package main

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
)

func main() {
	client, err := client.NewSSEMCPClient("https://olapextend.dev.cubewise.asia/mcp/UX_Samples/sse",
		client.WithHeaders(map[string]string{
			"Content-Type":  "application/json",
			"Authorization": "25b74ae9b66c9cc9d1a76994d6707b0a658c20f87c00002c470e918751244c3c",
		}))
	if err != nil {
		log.Fatalf("Failed to create client: %v", err)
	}
	defer client.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	log.Printf("Starting client...")
	start := time.Now()

	// Start
	if err := client.Start(ctx); err != nil {
		log.Fatalf("Failed to start client: %v", err)
	}

	// Initialize
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name:    "test-client",
		Version: "1.0.0",
	}
	init, err := client.Initialize(ctx, initRequest)
	if err != nil {
		log.Fatalf("Failed to initialize: %v", err)
	}
	initBytes, _ := json.Marshal(init)
	log.Printf("init done\n%s", string(initBytes))

	// ListTools
	toolsRequest := mcp.ListToolsRequest{}
	tools, err := client.ListTools(ctx, toolsRequest)
	if err != nil {
		log.Fatalf("ListTools failed: %v", err)
	}
	toolBytes, _ := json.Marshal(tools)
	log.Printf("tools:\n%s", string(toolBytes))
	// Summarize time-elapsed
	log.Printf("elapsed: %dms", time.Since(start).Milliseconds())
}
