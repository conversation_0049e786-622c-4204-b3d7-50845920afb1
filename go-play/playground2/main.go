package main

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"time"

	secapi "git.woa.com/sec-api/go/scurl"
)

func main(){
	url := "https://testwss.testsite.woa.com/v1/lke/mcp/list/tool?action=time"
	safeClient := secapi.NewSafeClient(
		// secapi.WithUnsafeDomain([]string{"devwss.testsite.woa.com", "testwss.testsite.woa.com"}),
		secapi.WithUnsafeDomain([]string{"devwss.testsite.woa.com"}),
		secapi.WithAllowPorts([]string{"80", "443"}),
	)
	httpReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Printf("create http request failed, url: %s, err:%+v", url, err)
	}
	timeoutCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	httpReq = httpReq.WithContext(timeoutCtx)
	// 基于安全请求的客户端，发起安全请求
	resp, err := safeClient.Do(httpReq)
	if err != nil {
		fmt.Printf("do http request failed, url: %s, err:%+v", url, err)
		return
	}
	if resp == nil {
		fmt.Printf("do http request failed, url: %s, resp is nil", url)
		return
	}
	defer func(respBody io.ReadCloser) {
		err := respBody.Close()
		if err != nil {
			fmt.Printf("close http response body failed, url: %s, err:%+v", url, err)
		}
	}(resp.Body)
	// 解析 JSON 数据
	listToolRespBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("read http response failed, url: %s, err:%+v", url, err)
		return
	}
	fmt.Printf("listToolRespBytes: %v", string(listToolRespBytes))
}
