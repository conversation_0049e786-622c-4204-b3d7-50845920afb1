2025-06-01 17:41:22.964	INFO	config/config0.go:61	loadYaml|main.yaml|rainbow
2025-06-01 17:41:22.965	INFO	config/config0.go:79	loadYaml0|config.Load|main.yaml|rainbow
2025-06-01 17:41:22.965	INFO	config/config0.go:61	loadYaml|main.yaml|rainbow
2025-06-01 17:41:22.965	INFO	config/config0.go:79	loadYaml0|config.Load|main.yaml|rainbow
2025-06-01 17:41:22.965	INFO	config/config0.go:61	loadYaml|placeholder.yaml|rainbow
2025-06-01 17:41:22.965	INFO	config/config0.go:79	loadYaml0|config.Load|placeholder.yaml|rainbow
2025-06-01 17:41:22.966	INFO	config/config0.go:61	loadYaml|placeholder.yaml|rainbow
2025-06-01 17:41:22.966	INFO	config/config0.go:79	loadYaml0|config.Load|placeholder.yaml|rainbow
2025-06-01 17:47:17.018	INFO	config/config0.go:61	loadYaml|main.yaml|rainbow
2025-06-01 17:47:17.018	INFO	config/config0.go:79	loadYaml0|config.Load|main.yaml|rainbow
2025-06-01 17:47:17.018	INFO	config/config0.go:61	loadYaml|main.yaml|rainbow
2025-06-01 17:47:17.018	INFO	config/config0.go:79	loadYaml0|config.Load|main.yaml|rainbow
2025-06-01 17:47:17.018	INFO	config/config0.go:61	loadYaml|placeholder.yaml|rainbow
2025-06-01 17:47:17.018	INFO	config/config0.go:79	loadYaml0|config.Load|placeholder.yaml|rainbow
2025-06-01 17:47:17.019	INFO	config/config0.go:61	loadYaml|placeholder.yaml|rainbow
2025-06-01 17:47:17.019	INFO	config/config0.go:79	loadYaml0|config.Load|placeholder.yaml|rainbow
2025-06-01 17:47:17.071	INFO	playground6/main.go:74	

--------------------------------------------------------------------------------
mainConfig: 
&{WorkflowVectorGroup:{EmbeddingModelName:embedding_1121_7500 LatestEmbeddingModelName:sn-large-multi-language-v0.2.5 Biz:cs Secret:e1168c1b14e31acd512ddd566bbe4390 OperationMaxIDs:100 WorkflowUseVdb:true EmbeddingVersionControl:map[embedding_1121_7500:{GroupSuffix: WorkflowNameTemplate:{{ .WorkflowName }} WorkflowExampleTemplate:{{ .Example }} WorkflowCombinationTemplate: WorkflowCombinationExampleNum:0} sn-large-multi-language-v0.2.5:{GroupSuffix:v1 WorkflowNameTemplate:问题:{{ .WorkflowName }} WorkflowExampleTemplate:问题:{{ .Example }} WorkflowCombinationTemplate:问题:{{ .WorkflowName }}
{{- if .WorkflowExampleList }}
{{- range $index, $q := $.WorkflowExampleList }}
相似问:{{ $q }}
{{- end }}
{{- end }}
答案:{{ .WorkflowDescription }} WorkflowCombinationExampleNum:10}]}}
================================================================================
2025-06-01 17:47:17.071	INFO	tnet/server_transport.go:98	service:trpc.example.hello_server.Greeting is using tnet transport, current number of pollers: 1
2025-06-01 17:47:17.072	INFO	server/service.go:175	process:67317, trpc service:trpc.example.hello_server.Greeting launch success, tcp:127.0.0.1:8000, serving ...
2025-06-01 17:47:17.073	INFO	playground6/main.go:74	

--------------------------------------------------------------------------------
mainConfig: 
&{WorkflowVectorGroup:{EmbeddingModelName:embedding_1121_7500 LatestEmbeddingModelName:sn-large-multi-language-v0.2.5 Biz:cs Secret:e1168c1b14e31acd512ddd566bbe4390 OperationMaxIDs:100 WorkflowUseVdb:true EmbeddingVersionControl:map[embedding_1121_7500:{GroupSuffix: WorkflowNameTemplate:{{ .WorkflowName }} WorkflowExampleTemplate:{{ .Example }} WorkflowCombinationTemplate: WorkflowCombinationExampleNum:0} sn-large-multi-language-v0.2.5:{GroupSuffix:v1 WorkflowNameTemplate:问题:{{ .WorkflowName }} WorkflowExampleTemplate:问题:{{ .Example }} WorkflowCombinationTemplate:问题:{{ .WorkflowName }}
{{- if .WorkflowExampleList }}
{{- range $index, $q := $.WorkflowExampleList }}
相似问:{{ $q }}
{{- end }}
{{- end }}
答案:{{ .WorkflowDescription }} WorkflowCombinationExampleNum:10}]}}
================================================================================
2025-06-01 17:48:14.090	INFO	config/config0.go:61	loadYaml|main.yaml|rainbow
2025-06-01 17:48:14.090	INFO	config/config0.go:79	loadYaml0|config.Load|main.yaml|rainbow
2025-06-01 17:48:14.090	INFO	config/config0.go:61	loadYaml|placeholder.yaml|rainbow
2025-06-01 17:48:14.090	INFO	config/config0.go:79	loadYaml0|config.Load|placeholder.yaml|rainbow
2025-06-01 17:48:14.091	INFO	playground6/main.go:74	

--------------------------------------------------------------------------------
mainConfig: 
&{WorkflowVectorGroup:{EmbeddingModelName:embedding_1121_7500 LatestEmbeddingModelName:sn-large-multi-language-v0.2.5 Biz:cs Secret:e1168c1b14e31acd512ddd566bbe4390 OperationMaxIDs:100 WorkflowUseVdb:true EmbeddingVersionControl:map[embedding_1121_7500:{GroupSuffix:v0 WorkflowNameTemplate:{{ .WorkflowName }} WorkflowExampleTemplate:{{ .Example }} WorkflowCombinationTemplate: WorkflowCombinationExampleNum:0} sn-large-multi-language-v0.2.5:{GroupSuffix:v1 WorkflowNameTemplate:问题:{{ .WorkflowName }} WorkflowExampleTemplate:问题:{{ .Example }} WorkflowCombinationTemplate:问题:{{ .WorkflowName }}
{{- if .WorkflowExampleList }}
{{- range $index, $q := $.WorkflowExampleList }}
相似问:{{ $q }}
{{- end }}
{{- end }}
答案:{{ .WorkflowDescription }} WorkflowCombinationExampleNum:10}]}}
================================================================================
2025-06-01 17:51:51.011	INFO	server/service.go:571	process:67317, trpc service:trpc.example.hello_server.Greeting, closing ...
2025-06-01 17:51:51.011	INFO	server/service.go:600	process:67317, trpc service:trpc.example.hello_server.Greeting, closed
2025-06-01 17:51:51.011	INFO	admin/admin.go:178	process:67317, admin server, closed
2025-06-01 17:52:01.365	INFO	config/config0.go:61	loadYaml|main.yaml|rainbow
2025-06-01 17:52:01.365	INFO	config/config0.go:79	loadYaml0|config.Load|main.yaml|rainbow
2025-06-01 17:52:01.365	INFO	config/config0.go:61	loadYaml|main.yaml|rainbow
2025-06-01 17:52:01.365	INFO	config/config0.go:79	loadYaml0|config.Load|main.yaml|rainbow
2025-06-01 17:52:01.365	INFO	config/config0.go:61	loadYaml|placeholder.yaml|rainbow
2025-06-01 17:52:01.365	INFO	config/config0.go:79	loadYaml0|config.Load|placeholder.yaml|rainbow
2025-06-01 17:52:01.366	INFO	config/config0.go:61	loadYaml|placeholder.yaml|rainbow
2025-06-01 17:52:01.366	INFO	config/config0.go:79	loadYaml0|config.Load|placeholder.yaml|rainbow
2025-06-01 17:52:01.419	INFO	playground6/main.go:81	

--------------------------------------------------------------------------------
mainConfig: 
workflow_vector_group:
    EmbeddingModelName: embedding_1121_7500
    LatestEmbeddingModelName: sn-large-multi-language-v0.2.5
    Biz: cs
    Secret: e1168c1b14e31acd512ddd566bbe4390
    OperationMaxIDs: 100
    WorkflowUseVdb: true
    EmbeddingVersionControl:
        embedding_1121_7500:
            GroupSuffix: v0
            WorkflowNameTemplate: '{{ .WorkflowName }}'
            WorkflowExampleTemplate: '{{ .Example }}'
            WorkflowCombinationTemplate: ""
            WorkflowCombinationExampleNum: 0
        sn-large-multi-language-v0.2.5:
            GroupSuffix: v1
            WorkflowNameTemplate: 问题:{{ .WorkflowName }}
            WorkflowExampleTemplate: 问题:{{ .Example }}
            WorkflowCombinationTemplate: |-
                问题:{{ .WorkflowName }}
                {{- if .WorkflowExampleList }}
                {{- range $index, $q := $.WorkflowExampleList }}
                相似问:{{ $q }}
                {{- end }}
                {{- end }}
                答案:{{ .WorkflowDescription }}
            WorkflowCombinationExampleNum: 10

================================================================================
2025-06-01 17:52:01.420	INFO	tnet/server_transport.go:98	service:trpc.example.hello_server.Greeting is using tnet transport, current number of pollers: 1
2025-06-01 17:52:01.420	INFO	server/service.go:175	process:70915, trpc service:trpc.example.hello_server.Greeting launch success, tcp:127.0.0.1:8000, serving ...
2025-06-01 17:52:01.421	INFO	playground6/main.go:81	

--------------------------------------------------------------------------------
mainConfig: 
workflow_vector_group:
    EmbeddingModelName: embedding_1121_7500
    LatestEmbeddingModelName: sn-large-multi-language-v0.2.5
    Biz: cs
    Secret: e1168c1b14e31acd512ddd566bbe4390
    OperationMaxIDs: 100
    WorkflowUseVdb: true
    EmbeddingVersionControl:
        embedding_1121_7500:
            GroupSuffix: v0
            WorkflowNameTemplate: '{{ .WorkflowName }}'
            WorkflowExampleTemplate: '{{ .Example }}'
            WorkflowCombinationTemplate: ""
            WorkflowCombinationExampleNum: 0
        sn-large-multi-language-v0.2.5:
            GroupSuffix: v1
            WorkflowNameTemplate: 问题:{{ .WorkflowName }}
            WorkflowExampleTemplate: 问题:{{ .Example }}
            WorkflowCombinationTemplate: |-
                问题:{{ .WorkflowName }}
                {{- if .WorkflowExampleList }}
                {{- range $index, $q := $.WorkflowExampleList }}
                相似问:{{ $q }}
                {{- end }}
                {{- end }}
                答案:{{ .WorkflowDescription }}
            WorkflowCombinationExampleNum: 10

================================================================================
2025-06-01 17:53:46.819	INFO	config/config0.go:61	loadYaml|main.yaml|rainbow
2025-06-01 17:53:46.819	INFO	config/config0.go:79	loadYaml0|config.Load|main.yaml|rainbow
2025-06-01 17:53:46.819	INFO	config/config0.go:61	loadYaml|placeholder.yaml|rainbow
2025-06-01 17:53:46.819	INFO	config/config0.go:79	loadYaml0|config.Load|placeholder.yaml|rainbow
2025-06-01 17:53:46.820	INFO	playground6/main.go:81	

--------------------------------------------------------------------------------
mainConfig: 
workflow_vector_group:
    EmbeddingModelName: embedding_1121_7500
    LatestEmbeddingModelName: sn-large-multi-language-v0.2.5
    Biz: cs
    Secret: e1168c1b14e31acd512ddd566bbe4390
    OperationMaxIDs: 100
    WorkflowUseVdb: true
    EmbeddingVersionControl:
        embedding_1121_7500:
            GroupSuffix: v0
            WorkflowNameTemplate: '{{ .WorkflowName }}'
            WorkflowExampleTemplate: '{{ .Example }}'
            WorkflowCombinationTemplate: ""
            WorkflowCombinationExampleNum: 0
        sn-large-multi-language-v0.2.5:
            GroupSuffix: v1
            WorkflowNameTemplate: 问题:{{ .WorkflowName }}
            WorkflowExampleTemplate: 问题:{{ .Example }}
            WorkflowCombinationTemplate: |-
                问题:{{ .WorkflowName }}
                {{- if .WorkflowExampleList }}
                {{- range $index, $q := $.WorkflowExampleList }}
                相似问:{{ $q }}
                {{- end }}
                {{- end }}
                答案:{{ .WorkflowDescription }}
            WorkflowCombinationExampleNum: 10

================================================================================
2025-06-01 17:54:16.142	INFO	server/service.go:571	process:70915, trpc service:trpc.example.hello_server.Greeting, closing ...
2025-06-01 17:54:16.142	INFO	admin/admin.go:178	process:70915, admin server, closed
2025-06-01 17:54:16.142	INFO	server/service.go:600	process:70915, trpc service:trpc.example.hello_server.Greeting, closed
