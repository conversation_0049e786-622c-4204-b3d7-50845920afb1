global:  # 全局配置
  namespace: Production  # 环境类型，分正式 production 和非正式 development 两种类型
  env_name: Default  # 环境名称，非正式环境下多环境的名称

server:  # 服务端配置
  app: example  # 业务的应用名
  server: hello_server  # 进程服务名
  bin_path: /usr/local/trpc/bin/  # 二进制可执行文件和框架配置文件所在路径
  conf_path: /usr/local/trpc/conf/  # 业务配置文件所在路径
  data_path: /usr/local/trpc/data/  # 业务数据文件所在路径
  filter:  # 针对所有 service 处理函数前后的拦截器列表
    - simpledebuglog
    - recovery  # 拦截框架创建的业务处理协程 panic
  service:  # 业务服务提供的 service，可以有多个
      # service 的路由名称
    - name: trpc.example.hello_server.Greeting
      ip: 127.0.0.1  # 服务监听 ip 地址 可使用占位符 ${ip},ip 和 nic 二选一，优先 ip
      # nic: eth0
      port: 8000  # 服务监听端口 可使用占位符 ${port}
      network: tcp  # 网络监听类型 tcp udp
      protocol: trpc  # 应用层协议 trpc http
      transport: tnet  # protocol 为 http 时需要删除本行, tnet 暂时不支持 http 协议
      timeout: 1000  # 请求最长处理时间 单位 毫秒

client:  # 客户端调用的后端配置
  timeout: 1000  # 针对所有后端的请求最长处理时间
  namespace: Development  # 针对所有后端的环境
  filter:  # 针对所有后端调用函数前后的拦截器列表
    - simpledebuglog
  service:  # 针对单个后端的配置
    - name: trpc.example.hello_server.Greeting  # 后端服务的 service name
      namespace: Development  # 后端服务的环境
      network: tcp  # 后端服务的网络类型 tcp udp 配置优先
      protocol: trpc  # 应用层协议 trpc http
      transport: tnet  # protocol 为 http 时需要删除本行, tnet 暂时不支持 http 协议
      target: ip://127.0.0.1:8000  # 请求服务地址
      timeout: 1000   # 请求最长处理时间

plugins:  # 插件配置
  log:  # 日志配置
    default:  # 默认日志的配置，可支持多输出
      - writer: console  # 控制台标准输出 默认
        level: debug  # 标准输出日志的级别
      - writer: file  # 本地文件日志
        level: info  # 本地文件滚动日志的级别
        writer_config:
          filename: ./trpc.log  # 本地文件滚动日志存放的路径
          max_size: 10  # 本地文件滚动日志的大小 单位 MB
          max_backups: 10  # 最大日志文件数
          max_age: 7  # 最大日志保留天数
          compress: false  # 日志文件是否压缩
  config:
    rainbow: # 七彩石配置中心
      providers:
        - name: rainbow
          appid: v1_rb3_b539bab0-79dc-4f7a-862f-d17f3 # appid
          group: greeting # 配置所属组
          type: kv   # 七彩石数据格式, kv(默认), table, file
          env_name: Default
          enable_client_provider: false
          timeout: 2000
