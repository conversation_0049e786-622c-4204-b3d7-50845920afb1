package main

import (
	"context"
	"fmt"

	pb "example.com/play/hello/pb/hello"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	trpc "git.code.oa.com/trpc-go/trpc-go"
	tconfig "git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/config"
	"gopkg.in/yaml.v3"
)

var mainConfig MainConfig

type MainConfig struct {
	WorkflowVectorGroup WorkflowVectorGroup `yaml:"workflow_vector_group"`
}

type WorkflowVectorGroup struct {
	EmbeddingModelName       string                           `yaml:"EmbeddingModelName"`       // 兼容镜像更新前的老版本(新版本废弃)
	LatestEmbeddingModelName string                           `yaml:"LatestEmbeddingModelName"` // 使用的最新模型名
	Biz                      string                           `yaml:"Biz"`                      // 调用方系统标识
	Secret                   string                           `yaml:"Secret"`                   // 调用方分配的Secret
	OperationMaxIDs          int                              `yaml:"OperationMaxIDs"`          // 向量接口单次批量操作最大ID数量
	WorkflowUseVdb           bool                             `yaml:"WorkflowUseVdb"`           // 是否启用Vdb
	EmbeddingVersionControl  map[string]VectorEmbeddingConfig `yaml:"EmbeddingVersionControl"`  // embedding模型版本控制，key为版本名（即embedding模型名称）
}

type VectorEmbeddingConfig struct {
	GroupSuffix                   string `yaml:"GroupSuffix"`                   // 向量库ID后缀标识
	WorkflowNameTemplate          string `yaml:"WorkflowNameTemplate"`          // 工作流名称embedding内容模板（go template语法）
	WorkflowExampleTemplate       string `yaml:"WorkflowExampleTemplate"`       // 工作流相似问embedding内容模板（go template语法）
	WorkflowCombinationTemplate   string `yaml:"WorkflowCombinationTemplate"`   // 工作流组合embedding内容模板（go template语法）
	WorkflowCombinationExampleNum int    `yaml:"WorkflowCombinationExampleNum"` // 工作流组合embedding内容模板中相似问的数量
}

func initConfig() {
	srvCfg := tconfig.Get("rainbow")
	if srvCfg == nil {
		panic("get rainbow config fail")
	}
	configCh, err := srvCfg.Watch(context.Background(), "main.yaml")
	if err != nil {
		panic(err)
	}
	go func() {
		for range configCh {
			mainConfigTmp := MainConfig{}
			err := initMainConfig(&mainConfigTmp)
			if err != nil {
				continue
			}
			mainConfig = mainConfigTmp
		}
	}()

	err = initMainConfig(&mainConfig)
	if err != nil {
		panic(err)
	}
}

func initMainConfig(mconfig *MainConfig) (err error) {
	// main config
	err = config.UnmarshalYAMLFromRainbowWithPlaceholder("main.yaml", &mconfig)
	if err != nil {
		log.Infof("init main config err:%v", err)
		return err
	}

	printMainConfig()
	return nil
}

func printMainConfig() {
	configBytes, _ := yaml.Marshal(mainConfig)
	log.Infof("\n\n--------------------------------------------------------------------------------\n" +
		fmt.Sprintf("mainConfig: \n%+v\n", string(configBytes)) +
		"================================================================================")
}

func main() {
	srv := trpc.NewServer()
	initConfig()
	impl := &greetingImpl{}
	pb.RegisterGreetingService(srv, impl)
	if err := srv.Serve(); err != nil {
		log.Fatal(err)
	}
}
