// Code generated by trpc-go/trpc-go-cmdline v2.8.13. DO NOT EDIT.
// source: hello.proto

package hello

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// START ======================================= Server Service Definition ======================================= START

// GreetingService defines service.
type GreetingService interface {
	SayHello(ctx context.Context, req *HelloRequest) (*HelloReply, error)
}

func GreetingService_SayHello_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &HelloRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(GreetingService).SayHello(ctx, reqbody.(*HelloRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// GreetingServer_ServiceDesc descriptor for server.RegisterService.
var GreetingServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.example.hello_server.Greeting",
	HandlerType: ((*GreetingService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.example.hello_server.Greeting/SayHello",
			Func: GreetingService_SayHello_Handler,
		},
	},
}

// RegisterGreetingService registers service.
func RegisterGreetingService(s server.Service, svr GreetingService) {
	if err := s.Register(&GreetingServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("Greeting register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedGreeting struct{}

func (s *UnimplementedGreeting) SayHello(ctx context.Context, req *HelloRequest) (*HelloReply, error) {
	return nil, errors.New("rpc SayHello of service Greeting is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// GreetingClientProxy defines service client proxy
type GreetingClientProxy interface {
	SayHello(ctx context.Context, req *HelloRequest, opts ...client.Option) (rsp *HelloReply, err error)
}

type GreetingClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewGreetingClientProxy = func(opts ...client.Option) GreetingClientProxy {
	return &GreetingClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *GreetingClientProxyImpl) SayHello(ctx context.Context, req *HelloRequest, opts ...client.Option) (*HelloReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.example.hello_server.Greeting/SayHello")
	msg.WithCalleeServiceName(GreetingServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("example")
	msg.WithCalleeServer("hello_server")
	msg.WithCalleeService("Greeting")
	msg.WithCalleeMethod("SayHello")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &HelloReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
