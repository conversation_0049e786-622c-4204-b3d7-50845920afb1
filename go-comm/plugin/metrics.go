package plugin

import (
	"git.code.oa.com/trpc-go/trpc-go"
	"git.woa.com/galileo/eco/go/sdk/base/components"
	metriconf "git.woa.com/galileo/eco/go/sdk/base/configs/metrics"
	"git.woa.com/galileo/eco/go/sdk/base/helper"
	"git.woa.com/galileo/eco/go/sdk/base/model"
	"git.woa.com/galileo/eco/go/sdk/base/semconv"
	modelv3 "git.woa.com/galileo/eco/go/sdk/base/v3/model"
	"strconv"
)

// 上报插件调用数据到伽利略

// target 统一上报到插件执行服务
var target = "STKE.KEP.plugin-exec-server"

// monitorName 监控项名称 插件调用
var monitorName = "plugin_call"

// metrics 监控处理器
var metrics components.MetricsProcessor

// Dimension 上报指标的维度
type Dimension struct {
	Uin        string // 客户 uin
	AppID      string // 应用 ID
	PluginID   string // 插件 ID
	PluginName string // 插件名称
	ToolID     string // 工具 ID
	ToolName   string // 工具名称
	PluginType int    // 插件类型  0: 自定义插件, 1: 官方插件, 2: 第三方插件
	CreateType int    // 创建方式  0: API, 1: 代码, 2: MCP
	ErrCode    int    // 错误码
}

// ReportData 上报数据结构
type ReportData struct {
	Dimension Dimension // 指标维度
	IsSuccess bool      // 调用是否成功
	Cost      int64     // 执行耗时，单位毫秒
}

// Init 初始化
func Init() {
	resv3 := modelv3.NewResource(
		target,
		model.Namespace(trpc.GlobalConfig().Global.Namespace),
		trpc.GlobalConfig().Global.EnvName,
		trpc.GlobalConfig().Global.LocalIP,
		trpc.GlobalConfig().Global.ContainerName,
		trpc.GlobalConfig().Global.FullSetName,
		"",
		"",
		"",
	)

	// 构造 metric, 初始化，只能执行一次
	metricConfig := metriconf.NewConfig(resv3, metriconf.WithSchemaURL(semconv.SchemaURL))
	var err error
	metrics, err = helper.GetMetricsProcessor(metricConfig) // 全局持有，不要重复创建。
	if err != nil {
		panic(err)
	}
}

// Report 上报插件调用数据
func Report(data *ReportData) {
	if data == nil {
		return
	}
	customLabels := fillCustomLabels(data)
	// 调用失败的计数
	errCountValue := 0
	if !data.IsSuccess {
		errCountValue = 1
	}
	Metrics := []model.Metric{
		{
			// 请求总数
			Name:        "total",
			Aggregation: model.Aggregation_AGGREGATION_SUM,
			Value:       1,
		},
		{
			// 异常数
			Name:        "error_count",
			Aggregation: model.Aggregation_AGGREGATION_SUM,
			Value:       float64(errCountValue),
		},
		{
			Name:        "cost",
			Value:       float64(data.Cost),
			Aggregation: model.Aggregation_AGGREGATION_HISTOGRAM,
		},
	}
	customMetrics := model.GetCustomMetrics(len(customLabels), len(Metrics))
	defer model.PutCustomMetrics(customMetrics)
	customMetrics.CustomLabels = customLabels
	customMetrics.Metrics = Metrics
	customMetrics.MonitorName = monitorName
	metrics.ProcessCustomMetrics(customMetrics)
}

// fillCustomLabels 填充上报的指标维度
func fillCustomLabels(data *ReportData) []model.Label {
	customLabels := []model.Label{
		{
			// 上报方服务名
			Name:  "server",
			Value: trpc.GlobalConfig().Server.Server,
		},
		{
			Name:  "uin",
			Value: data.Dimension.Uin,
		},
		{
			Name:  "app_id",
			Value: data.Dimension.AppID,
		},
		{
			Name:  "plugin_id",
			Value: data.Dimension.PluginID,
		},
		{
			Name:  "plugin_name",
			Value: data.Dimension.PluginName,
		},
		{
			Name:  "tool_id",
			Value: data.Dimension.ToolID,
		},
		{
			Name:  "tool_name",
			Value: data.Dimension.ToolName,
		},
		{
			Name:  "plugin_type",
			Value: strconv.Itoa(data.Dimension.PluginType),
		},
		{
			Name:  "create_type",
			Value: strconv.Itoa(data.Dimension.CreateType),
		},
		{
			Name:  "error_code",
			Value: strconv.Itoa(data.Dimension.ErrCode),
		},
	}
	return customLabels
}
