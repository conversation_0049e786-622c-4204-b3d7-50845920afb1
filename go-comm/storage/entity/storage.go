package entity

const (
	// StorageTypeCOS COS 存储
	StorageTypeCOS = "cos"
	// StorageTypeMinIO MinIO 存储
	StorageTypeMinIO = "minio"

	// SharePath 共享的文件夹
	SharePath = "/code/0/interpreter/"
	// PublicPath 共有读私有写文件夹 用来存储手动录入md文件附件
	PublicPath = "/public/"
)

// COS 操作权限
// 文档 https://cloud.tencent.com/document/product/436/31923
// iWiki https://iwiki.woa.com/pages/viewpage.action?pageId=4006971558
const (
	PutObject               = "name/cos:PutObject"               // 简单上传操作
	GetObject               = "name/cos:GetObject"               // 下载对象
	InitiateMultipartUpload = "name/cos:InitiateMultipartUpload" // 分块上传：初始化分块操作
	ListMultipartUploads    = "name/cos:ListMultipartUploads"    // 分块上传：List 进行中的分块上传
	ListParts               = "name/cos:ListParts"               // 分块上传：List 已上传分块操作
	UploadPart              = "name/cos:UploadPart"              // 分块上传：上传分块块操作
	CompleteMultipartUpload = "name/cos:CompleteMultipartUpload" // 分块上传：完成所有分块上传操作
	AbortMultipartUpload    = "name/cos:AbortMultipartUpload"    // 取消分块上传操作
)

// MinIO 操作权限
// 文档 https://min.io/docs/minio/linux/administration/identity-access-management/
// policy-based-access-control.html#minio-policy
const (
	// MinIOVersionID 策略版本
	MinIOVersionID = "2012-10-17"

	MinIOPutObject            = "s3:PutObject"                // 简单上传操作
	MinIOGetObject            = "s3:GetObject"                // 下载对象
	MinIOListMultipartUploads = "s3:ListMultipartUploadParts" // 分块上传：List 进行中的分块上传
	MinIOAbortMultipartUpload = "s3:AbortMultipartUpload"     // 取消分块上传操作
)

// COSUpAndDownload COS 上传与下载
var COSUpAndDownload = []string{
	PutObject,
	GetObject,
	InitiateMultipartUpload,
	ListMultipartUploads,
	ListParts,
	UploadPart,
	CompleteMultipartUpload,
	AbortMultipartUpload,
}

// MinIOUpAndDownload minio 上传与下载
var MinIOUpAndDownload = []string{
	MinIOPutObject,
	MinIOGetObject,
	MinIOListMultipartUploads,
	MinIOAbortMultipartUpload,
}

// CredentialResult 临时密钥
type CredentialResult struct {
	Credentials *Credentials
	ExpiredTime int64
	StartTime   int64
}

// Credentials 临时密钥
type Credentials struct {
	TmpSecretID  string
	TmpSecretKey string
	SessionToken string
}

// CredentialPolicyStatement 策略语句
type CredentialPolicyStatement struct {
	Action    []string                  `json:",omitempty"`
	Effect    string                    `json:",omitempty"`
	Resource  []string                  `json:",omitempty"`
	Condition map[string]map[string]any `json:",omitempty"`
}

// CredentialPolicy 密钥策略
type CredentialPolicy struct {
	Version   string                      `json:",omitempty"`
	Statement []CredentialPolicyStatement `json:",omitempty"`
}
