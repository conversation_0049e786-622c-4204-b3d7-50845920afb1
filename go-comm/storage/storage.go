// Package storage 存储interface
package storage

import (
	"context"
	"git.woa.com/dialogue-platform/go-comm/storage/config"
	"git.woa.com/dialogue-platform/go-comm/storage/entity"
)

// Storage 存储interface
type Storage interface {
	// GetDomain 获取domain
	GetDomain(ctx context.Context) string
	// GetType 获取对象存储类型
	GetType(ctx context.Context) string
	// GetBucket 获取存储桶
	GetBucket(ctx context.Context) string
	// GetRegion 获取存储桶地域
	GetRegion(ctx context.Context) string
	// GetCredential 获取临时密钥
	GetCredential(ctx context.Context, path []string) (*entity.CredentialResult, error)
	// GetPreSignedURL 获取 COS 预签名 URL
	GetPreSignedURL(ctx context.Context, key string) (string, error)
	// GetObject 获取 COS 文件
	GetObject(ctx context.Context, key string) ([]byte, error)
	// PutObject 上传 COS 文件
	PutObject(ctx context.Context, bs []byte, key string) error
	// DelObject 删除 COS 文件
	DelObject(ctx context.Context, key string) error
}

// New creates Storage instance
func New(cfg config.Storage) Storage {
	if cfg.Type == entity.StorageTypeMinIO {
		return newMinIO(cfg.MinIO)
	}
	return newCOS(cfg.Cos)
}

// NewTencentCOS creates Storage instance for hunyuan-turbo
func NewTencentCOS(cfg config.Storage) Storage {
	if cfg.Type == entity.StorageTypeMinIO {
		return newMinIO(cfg.MinIO)
	}
	return newCOSTencent(cfg.Cos)
}
