package config

import "time"

// Storage 对象存储配置
type Storage struct {
	Type  string `yaml:"type"`
	Cos   Cos    `yaml:"cos"`
	MinIO MinIO  `yaml:"minio"`
}

// Cos cos存储
type Cos struct {
	SecretID   string        `yaml:"secret_id"`
	SecretKey  string        `yaml:"secret_key"`
	Region     string        `yaml:"region"`
	AppID      string        `yaml:"app_id"`
	Bucket     string        `yaml:"bucket"`
	Domain     string        `yaml:"domain"`
	ExpireTime time.Duration `yaml:"expire_time"`
}

// MinIO minio存储
type MinIO struct {
	SecretID    string        `yaml:"secret_id"`
	SecretKey   string        `yaml:"secret_key"`
	Region      string        `yaml:"region"`
	Bucket      string        `yaml:"bucket"`
	ExpireTime  time.Duration `yaml:"expire_time"`
	STSEndpoint string        `yaml:"sts_endpoint"` // sts地址:用于分配临时密钥
	EndPoint    string        `yaml:"end_point"`    // minio服务地址
	UseHTTPS    bool          `yaml:"use_https"`    // 是否使用https
}
