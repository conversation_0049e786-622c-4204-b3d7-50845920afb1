package scheduler

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"gorm.io/gorm"
)

// NewUpgradeWorkflowVectorTask 创建升级工作流embedding向量任务
func NewUpgradeWorkflowVectorTask(ctx context.Context, param entity.TaskUpgradeWorkflowVectorParams) error {
	log.InfoContextf(ctx, "NewUpgradeWorkflowVectorTask, appID: %s, params: %+v", param.RobotID, param)
	task, _ := task_scheduler.NewTask(
		ctx, task_scheduler.UserID(uint64(encode.StringToInt64(param.RobotID))), entity.TaskUpgradeWorkflowVector,
		entity.TaskMutexNone, param)
	taskID, err := taskScheduler.CreateTask(ctx, task)
	if err != nil {
		log.ErrorContextf(ctx, "taskScheduler.CreateTask failed, appID: %s, task: %+v, err: %+v", param.RobotID, task, err)
		return err
	}
	log.InfoContextf(ctx, "NewUpgradeWorkflowVectorTask success, appID: %s, taskID: %d", param.RobotID, taskID)
	return nil
}

// NewUpgradeWorkflowVectorTasks 创建升级工作流embedding向量任务
func NewUpgradeWorkflowVectorTasks(ctx context.Context, params []entity.TaskUpgradeWorkflowVectorParams) ([]string, []string, []error) {
	var wg sync.WaitGroup
	var mu sync.Mutex
	succeededAppIDs := make([]string, 0)
	failedAppIDs := make([]string, 0)
	failedErrors := make([]error, 0)
	for _, param := range params {
		wg.Add(1)
		go func(p entity.TaskUpgradeWorkflowVectorParams) {
			defer wg.Done()
			log.InfoContextf(ctx, "NewUpgradeWorkflowVectorTask, appID: %s, params: %+v", p.RobotID, p)
			task, _ := task_scheduler.NewTask(
				ctx, task_scheduler.UserID(uint64(encode.StringToInt64(p.RobotID))), entity.TaskUpgradeWorkflowVector,
				entity.TaskMutexNone, p,
			)
			taskID, err := taskScheduler.CreateTask(ctx, task)
			if err != nil {
				log.ErrorContextf(ctx, "taskScheduler.CreateTask failed, appID: %s, task: %+v, err: %+v", p.RobotID, task, err)
				mu.Lock()
				failedAppIDs = append(failedAppIDs, p.RobotID)
				failedErrors = append(failedErrors, err)
				mu.Unlock()
				return
			}
			log.InfoContextf(ctx, "NewUpgradeWorkflowVectorTask success, appID: %s, taskID: %d", p.RobotID, taskID)
			mu.Lock()
			succeededAppIDs = append(succeededAppIDs, p.RobotID)
			mu.Unlock()
		}(param)
	}
	wg.Wait()
	return succeededAppIDs, failedAppIDs, failedErrors
}

// GetAllUpgradeWorkflowVectorTasks 获取升级工作流embedding向量任务列表
func GetAllUpgradeWorkflowVectorTasks(ctx context.Context, pageSize, pageNum int) ([]*entity.CommonSchedulerTaskFields, int64, error) {
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTasks, pageSize:%d, pageNum:%d", pageSize, pageNum)
	// 进行中的任务和已完成的任务需要合并查询
	db := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx)
	commonFields := entity.CommonSchedulerTaskFields{}.GetDBTags()
	fieldsStr := strings.Join(commonFields, ", ")
	// 构建UNION查询，确保两个SELECT返回相同的字段
	unionQuery := fmt.Sprintf(`
		SELECT %s FROM %s WHERE %s = %d
		UNION ALL
		SELECT %s FROM %s WHERE %s = %d`,
		fieldsStr, entity.SchedulerTask{}.TableName(), entity.TSchedulerTaskColumns.Type, entity.TaskUpgradeWorkflowVector,
		fieldsStr, entity.SchedulerTaskLog{}.TableName(), entity.TSchedulerTaskColumns.Type, entity.TaskUpgradeWorkflowVector,
	)
	// 先获取总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM (%s) as combined", unionQuery)
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTasks, countQuery: %s", countQuery)
	var totalCount int64
	err := db.Raw(countQuery).Scan(&totalCount).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTasks db.Count err: %+v", err)
		return nil, 0, err
	}
	// 获取分页数据
	var tasks []*entity.CommonSchedulerTaskFields
	offset := (pageNum - 1) * pageSize
	order := fmt.Sprintf("%s DESC", entity.TSchedulerTaskColumns.CreateTime)
	finalQuery := fmt.Sprintf("SELECT * FROM (%s) as combined ORDER BY %s LIMIT %d OFFSET %d",
		unionQuery, order, pageSize, offset)
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTasks, finalQuery: %s", finalQuery)
	err = db.Raw(finalQuery).Find(&tasks).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTasks db.Find err: %+v", err)
		return nil, 0, err
	}
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTasks success, total: %d, len(tasks): %d", totalCount, len(tasks))
	return tasks, totalCount, nil
}

// GetUpgradeWorkflowVectorTasks 获取升级工作流embedding向量任务列表
func GetUpgradeWorkflowVectorTasks(ctx context.Context, pageSize, pageNum int,
	isHistory bool) ([]*entity.CommonSchedulerTaskFields, int64, error) {
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTasks, pageSize:%d, pageNum:%d, isHistory:%t", pageSize, pageNum, isHistory)
	db := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx)
	if isHistory {
		db = db.Table(entity.SchedulerTaskLog{}.TableName())
	} else {
		db = db.Table(entity.SchedulerTask{}.TableName())
	}
	query := fmt.Sprintf("%s = ?", entity.TSchedulerTaskColumns.Type)
	db = db.Where(query, entity.TaskUpgradeWorkflowVector)
	// 先获取总数
	var totalCount int64
	err := db.Count(&totalCount).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTasks db.Count err: %+v", err)
		return nil, 0, err
	}
	// 获取分页数据
	var tasks []*entity.CommonSchedulerTaskFields
	offset := (pageNum - 1) * pageSize
	order := fmt.Sprintf("%s DESC", entity.TSchedulerTaskColumns.CreateTime)
	err = db.Order(order).Limit(pageSize).Offset(offset).Find(&tasks).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTasks db.Find err: %+v", err)
		return nil, 0, err
	}
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTasks success, total: %d, len(tasks): %d", totalCount, len(tasks))
	return tasks, totalCount, nil
}

// GetUpgradeWorkflowVectorTaskByAppIDAndTraceID 获取升级工作流embedding向量任务
func GetUpgradeWorkflowVectorTaskByAppIDAndTraceID(ctx context.Context, appID string, traceID string,
	all, isHistory bool) ([]*entity.CommonSchedulerTaskFields, error) {
	// task_scheduler 根据AppID查询向量升级任务
	log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID, appID: %s, traceID: %s", appID, traceID)
	if appID == "" && traceID == "" {
		return nil, nil
	}
	db := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx)
	query := fmt.Sprintf("%s = ?", entity.TSchedulerTaskColumns.Type)
	taskTable := db.Table(entity.SchedulerTask{}.TableName()).Where(query, entity.TaskUpgradeWorkflowVector)
	taskHistoryTable := db.Table(entity.SchedulerTaskLog{}.TableName()).Where(query, entity.TaskUpgradeWorkflowVector)
	if appID != "" {
		appIDQuery := fmt.Sprintf("%s = ?", entity.TSchedulerTaskColumns.UserID)
		taskTable = taskTable.Where(appIDQuery, appID)
		taskHistoryTable = taskHistoryTable.Where(appIDQuery, appID)
	}
	if traceID != "" {
		traceIDQuery := fmt.Sprintf("%s = ?", entity.TSchedulerTaskColumns.TraceID)
		taskTable = taskTable.Where(traceIDQuery, traceID)
		taskHistoryTable = taskHistoryTable.Where(traceIDQuery, traceID)
	}
	// 查询未完成的任务
	var tasks []*entity.CommonSchedulerTaskFields
	if all || !isHistory {
		err := taskTable.Find(&tasks).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID db.Find err: %+v", err)
			return nil, err
		}
		log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID success, len(task): %d", len(tasks))
		if !all {
			return tasks, nil
		}
	}
	// 查询已完成的任务
	var taskHistories []*entity.CommonSchedulerTaskFields
	if all || isHistory {
		err := taskHistoryTable.Find(&taskHistories).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.ErrorContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID db.Find err: %+v", err)
			return nil, err
		}
		log.InfoContextf(ctx, "GetUpgradeWorkflowVectorTaskByAppID success, len(taskHistories): %d", len(taskHistories))
		if !all {
			return tasks, nil
		}
	}
	// 合并tasks和taskHistories
	tasks = append(tasks, taskHistories...)
	return tasks, nil
}

// // StopUpgradeWorkflowVectorTaskByTaskID 停止升级工作流embedding向量任务
// func StopUpgradeWorkflowVectorTaskByTaskID(ctx context.Context, taskID uint64) error {
// 	log.InfoContextf(ctx, "StopUpgradeWorkflowVectorTaskByTaskID, taskID: %d", taskID)
// 	err := taskScheduler.StopTask(ctx, task_scheduler.TaskID(taskID))
// 	if err != nil {
// 		log.ErrorContextf(ctx, "StopUpgradeWorkflowVectorTaskByTaskID failed, taskID:%d, err:%+v", taskID, err)
// 		return err
// 	}
// 	log.InfoContextf(ctx, "StopUpgradeWorkflowVectorTaskByTaskID success, taskID:%d", taskID)
// 	return nil
// }

// // StopUpgradeWorkflowVectorTaskByAppID 停止升级工作流embedding向量任务
// func StopUpgradeWorkflowVectorTaskByAppID(ctx context.Context, appID string) error {
// 	log.InfoContextf(ctx, "StopUpgradeWorkflowVectorTaskByAppID, appID: %s", appID)
// 	task, err := GetUpgradeWorkflowVectorTaskByAppID(ctx, appID)
// 	if err != nil {
// 		log.ErrorContextf(ctx, "StopUpgradeWorkflowVectorTaskByAppID failed, appID:%s, err:%+v", appID, err)
// 		return err
// 	}
// 	taskID := task.ID
// 	err = taskScheduler.StopTask(ctx, task_scheduler.TaskID(taskID))
// 	if err != nil {
// 		log.ErrorContextf(ctx, "StopUpgradeWorkflowVectorTaskByAppID failed, appID:%s, taskID:%d, err:%+v", appID, taskID, err)
// 		return err
// 	}
// 	log.InfoContextf(ctx, "StopUpgradeWorkflowVectorTaskByAppID success, appID:%s, taskID:%d", appID, taskID)
// 	return nil
// }

// RestartUpgradeWorkflowVectorTaskByAppID 重启升级工作流embedding向量任务
func RestartUpgradeWorkflowVectorTaskByAppID(ctx context.Context, appID string) error {
	log.InfoContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID, appID: %s", appID)
	// 查询未完成的任务
	query := fmt.Sprintf("%s = %d AND %s = ?", entity.TSchedulerTaskColumns.Type, entity.TaskUpgradeWorkflowVector,
		entity.TSchedulerTaskColumns.UserID)
	var task entity.SchedulerTask
	err := database.GetLLMRobotTaskGORM().Debug().WithContext(ctx).Table(entity.SchedulerTask{}.TableName()).
		Where(query, appID).
		Take(&task).Error
	if err != nil {
		log.ErrorContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID db.Find err: %+v", err)
		return err
	}
	log.InfoContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID get one task success, task: %+v", task)
	taskID := task.ID
	err = taskScheduler.ContinueTerminatedTask(ctx, task_scheduler.TaskID(taskID), task.RetryTimes, 0)
	if err != nil {
		log.ErrorContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID failed, appID:%s, taskID:%d, err:%+v", appID, taskID, err)
		return err
	}
	log.InfoContextf(ctx, "RestartUpgradeWorkflowVectorTaskByAppID success, appID:%s, taskID:%d", appID, taskID)
	return nil
}
