// bot-task-config-server
//
// @(#)workflow_ref_knowledge.go  星期三, 五月 14, 2025
// Copyright(c) 2025, mikeljiang@Tencent. All rights reserved.

package db

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
)

// GetWorkflowListByDoc  批量获取文档被引用的工作流列表
func GetWorkflowListByDoc(ctx context.Context, docIDs []string) ([]entity.WorkflowRefKnowledge, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workFlowRefDocs []entity.WorkflowRefKnowledge
	if err := db.Table(entity.WorkflowRefKnowledge{}.TableName()).Select("distinct f_workflow_id,f_biz_id").
		Where("f_is_deleted = 0 AND f_type = 'DOC' ").
		Where("f_biz_id IN (?)", docIDs).
		Scan(&workFlowRefDocs).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowListByDoc:err:%v", err)
		return nil, err
	}
	return workFlowRefDocs, nil
}

//// GetWorkflowListByAttribute  批量获取属性标签被引用的工作流列表
//func GetWorkflowListByAttribute(ctx context.Context, attributeBizID string, labelBizIDs []string) (
//	[]entity.WorkflowRefKnowledge, error) {
//	var workFlowRefAttributeAndLabels []entity.WorkflowRefKnowledge
//	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
//	db = db.Table(entity.WorkflowRefKnowledge{}.TableName()).
//		Select("distinct f_workflow_id, f_biz_id, f_label_id").
//		Where("f_is_deleted = 0 AND f_type = 'TAG' ")
//	if len(attributeBizID) > 0 {
//		db = db.Where("f_biz_id = ? ", attributeBizID)
//	}
//	if len(labelBizIDs) > 0 {
//		db = db.Where("f_label_id IN (?)", labelBizIDs)
//	}
//	err := db.Scan(&workFlowRefAttributeAndLabels).Error
//	if err != nil {
//		log.ErrorContextf(ctx, "GetWorkflowListByAttribute:err:%v", err)
//		return nil, err
//	}
//	return workFlowRefAttributeAndLabels, nil
//}

// GetWorkflowListByAttribute  批量获取标签被引用的工作流列表
func GetWorkflowListByAttribute(ctx context.Context, attributeBizIDs []string) ([]entity.WorkflowRefKnowledge, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workFlowRefDocs []entity.WorkflowRefKnowledge
	if err := db.Table(entity.WorkflowRefKnowledge{}.TableName()).Select("distinct f_workflow_id,f_biz_id").
		Where("f_is_deleted = 0 AND f_type = 'TAG' ").
		Where("f_biz_id IN (?)", attributeBizIDs).
		Scan(&workFlowRefDocs).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowListByAttribute:err:%v", err)
		return nil, err
	}
	return workFlowRefDocs, nil
}

// GetWorkflowListByAttributeLabel  批量获取标签值被引用的工作流列表
func GetWorkflowListByAttributeLabel(ctx context.Context, attributeLabelBizIDs []string) (
	[]entity.WorkflowRefKnowledge, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workFlowRefDocs []entity.WorkflowRefKnowledge
	if err := db.Table(entity.WorkflowRefKnowledge{}.TableName()).Select("distinct f_workflow_id,f_label_id").
		Where("f_is_deleted = 0 AND f_type = 'TAG' ").
		Where("f_label_id IN (?)", attributeLabelBizIDs).
		Scan(&workFlowRefDocs).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowListByAttributeLabel:err:%v", err)
		return nil, err
	}
	return workFlowRefDocs, nil
}
