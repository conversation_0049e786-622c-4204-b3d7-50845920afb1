// bot-task-config-server
//
// @(#)workflow-ref.go  星期五, 4月 25, 2025
// Copyright(c) 2025, mikeljiang@Tencent. All rights reserved.

package db

import (
	"context"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/set"
)

// GetWorkflowRobotIdByWorkflow 获取指定工作流的应用ID
func GetWorkflowRobotIdByWorkflow(ctx context.Context, workflowId string) (*entity.Workflow, error) {
	var workflow entity.Workflow
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Table(entity.Workflow{}.TableName()).Select("f_workflow_id, f_robot_id").
		Where("f_is_deleted = 0 ").
		Where("f_workflow_id = ? ", workflowId).
		Take(&workflow).Error // 使用 Take 只获取一个记录
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowRobotIdByWorkflow err:%v", err)
		return nil, err
	}
	return &workflow, nil
}

// GetAllWorkflowRefs 获取当前应用下所有工作流引用关系
func GetAllWorkflowRefs(ctx context.Context, robotId string) ([]entity.WorkflowReference, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workFlowRef []entity.WorkflowReference
	if err := db.Table(entity.WorkflowReference{}.TableName()).Select("distinct f_workflow_id,f_workflow_ref_id").
		Where("f_is_deleted = 0 ").
		Where("f_robot_id = ? ", robotId).
		Scan(&workFlowRef).Error; err != nil {
		log.ErrorContextf(ctx, "GetAllWorkflowRefs err:%v", err)
		return nil, err
	}
	return workFlowRef, nil
}

// GetAllWorkflowBasics 获取当前应用下所有工作流的基本信息
func GetAllWorkflowBasics(ctx context.Context, robotId string) ([]*entity.Workflow, map[string]*entity.Workflow,
	error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	workflows := make([]*entity.Workflow, 0)
	err := db.Table(entity.Workflow{}.TableName()).Select("f_workflow_id, f_workflow_name, f_desc, "+
		"f_release_status, f_flow_state, f_update_time").
		Where("f_is_deleted = 0 ").
		Where("f_robot_id = ? ", robotId).Order("f_update_time desc, f_id desc").Find(&workflows).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetAllWorkflowBasics err:%v", err)
		return nil, nil, err
	}
	workflowMap := make(map[string]*entity.Workflow, 0)
	//for _, item := range workflows {
	//	workflowMap[item.WorkflowID] = item
	//}
	for i := range workflows {
		workflowMap[workflows[i].WorkflowID] = workflows[i]
	}
	return workflows, workflowMap, nil
}

// GetWorkflowInfoByRefFlowIds 批量查询工作流id和被引用的ID
func GetWorkflowInfoByRefFlowIds(ctx context.Context, flowIds []string) ([]entity.WorkflowReference, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workFlowRef []entity.WorkflowReference
	if err := db.Table(entity.WorkflowReference{}.TableName()).Select("distinct f_workflow_id,f_workflow_ref_id").
		Where("f_is_deleted = 0 ").
		Where("f_workflow_ref_id IN (?)", flowIds).
		Scan(&workFlowRef).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowInfoByRefFlowIds:err:%v", err)
		return nil, err
	}
	return workFlowRef, nil
}

//// GetWorkFLowCountByRefFlowIds 批量查询工作流是否被引用
//func GetWorkFLowCountByRefFlowIds(ctx context.Context, flowIds []string) (int, error) {
//	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
//	querySql := queryWorkFLowCountByRefFlowIds
//	querySql += ` and f_workflow_ref_id IN (?` + strings.Repeat(",?", len(flowIds)-1) + ")"
//	paramValues := make([]interface{}, 0)
//	for _, flowId := range flowIds {
//		paramValues = append(paramValues, flowId)
//	}
//	var count int
//	err := db.Raw(querySql, paramValues...).Scan(&count).Error
//	if err != nil && err != sql.ErrNoRows {
//		log.Errorf("GetWorkFLowCountByRefFlowIds query sql:%s|err:%v", querySql, err)
//		return count, err
//	}
//	return count, nil
//}

//// GetCanBeReferencedWorkflowList1 获取可以被引用的工作流的列表
//func GetCanBeReferencedWorkflowList1(ctx context.Context, appId, query, workflowId string, page, pageSize uint32) (
//	[]entity.Workflow, error) {
//	offset := (page - 1) * pageSize
//	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
//	workflows := make([]entity.Workflow, 0)
//	paramValues := make([]interface{}, 0)
//	querySql := `select distinct a.f_id, a.f_workflow_id, a.f_workflow_name, a.f_desc,a.f_update_time from t_workflow a
//					left join (select distinct f_workflow_id from t_workflow_reference where f_robot_id = ?
//					and f_is_deleted = 0) b on a.f_workflow_id = b.f_workflow_id
//					where a.f_is_deleted = 0 and a.f_robot_id = ? and a.f_workflow_id != ?
//					and (a.f_release_status = ? or a.f_flow_state IN (?` +
//		strings.Repeat(",?", 1) + " ))  and b.f_workflow_id is null "
//	paramValues = append(paramValues, appId, appId, workflowId, entity.WorkflowReleaseStatusPublished,
//		entity.WorkflowStateEnable, entity.WorkflowStatePublishedChange)
//	if len(query) > 0 {
//		querySql += " and a.f_workflow_name LIKE ? "
//		paramValues = append(paramValues, "%"+query+"%")
//	}
//	paramValues = append(paramValues, pageSize, offset)
//	querySql += " ORDER BY a.f_update_time desc, a.f_id desc LIMIT ? OFFSET ?  "
//	err := db.Raw(querySql, paramValues...).Scan(&workflows).Error
//	if err != nil && err != sql.ErrNoRows {
//		log.ErrorContextf(ctx, "GetCanBeReferencedWorkflowList query sql:%s|err:%v", querySql, err)
//		return workflows, err
//	}
//	return workflows, nil
//}

//// GetCanBeReferencedWorkflowTotal 获取可以被引用的工作流总数
//func GetCanBeReferencedWorkflowTotal(ctx context.Context, appId, query, workflowId string) (
//	int32, error) {
//	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
//	var total int32
//	paramValues := make([]interface{}, 0)
//	querySql := `select count(distinct a.f_workflow_id) from t_workflow a
//					left join (select distinct f_workflow_id  from t_workflow_reference where f_robot_id = ?
//					and f_is_deleted = 0) b on a.f_workflow_id = b.f_workflow_id
//					where a.f_is_deleted = 0 and a.f_robot_id = ? and a.f_workflow_id != ?
//					and (a.f_release_status = ? or a.f_flow_state IN (?` +
//		strings.Repeat(",?", 1) + " ))  and b.f_workflow_id is null "
//	paramValues = append(paramValues, appId, appId, workflowId, entity.WorkflowReleaseStatusPublished,
//		entity.WorkflowStateEnable, entity.WorkflowStatePublishedChange)
//	if len(query) > 0 {
//		querySql += " and a.f_workflow_name LIKE ? "
//		paramValues = append(paramValues, "%"+query+"%")
//	}
//	err := db.Raw(querySql, paramValues...).Scan(&total).Error
//	if err != nil && err != sql.ErrNoRows {
//		log.ErrorContextf(ctx, "GetCanBeReferencedWorkflowTotal query sql:%s|err:%v", querySql, err)
//		return total, err
//	}
//	return total, nil
//}

// BuildWorkflowDirectReferences 构建工作流直接上下引用关系
func BuildWorkflowDirectReferences(workflows []*entity.Workflow, references []entity.WorkflowReference) {
	for i := range workflows {
		for _, reference := range references {
			// 工作流向下引用的
			if reference.WorkflowID == workflows[i].WorkflowID {
				workflows[i].DownwardDirectReferencedWorkflows = append(workflows[i].DownwardDirectReferencedWorkflows,
					reference.WorkflowRefID)
			}
			// 工作流向上被引用的
			if reference.WorkflowRefID == workflows[i].WorkflowID {
				workflows[i].UpwardDirectByReferencedWorkflows = append(workflows[i].UpwardDirectByReferencedWorkflows,
					reference.WorkflowID)
			}
		}
	}
}

// GetWorkflowsDeep 获取工作流层数和上下全部引用关系
func GetWorkflowsDeep(ctx context.Context, workflows []*entity.Workflow, workflowMap map[string]*entity.Workflow) {
	workflowDeepMessage := new(strings.Builder)
	treeBuilderMap := make(map[string]string, 0)
	for i := range workflows {
		// 递归向上获取深度
		upDeep, message := getUpwardLevel(ctx, workflows[i], workflowMap, treeBuilderMap, "<<<<")

		// 打印当前工作流向上所有记录
		if message.String() != "" {
			workflowDeepMessage.WriteString(message.String())
		}

		workflowDeepDownMessage := new(strings.Builder)
		// 递归向下获取深度
		downDeep := getDownwardLevel(ctx, workflows[i], workflowMap, treeBuilderMap,
			workflowDeepDownMessage, "<<<<")

		// 打印当前工作流向上向下深度
		workflowDeepMessage.WriteString(fmt.Sprintf("%s(%s)(upDeep:%d)(downDeep:%d)\n",
			workflows[i].WorkflowName, workflows[i].WorkflowID, upDeep, downDeep))

		// 打印当前工作流向下所有记录
		if workflowDeepDownMessage.String() != "" {
			workflowDeepMessage.WriteString(workflowDeepDownMessage.String())
		}
		workflowDeepMessage.WriteString("\n\n")

		workflows[i].UpwardDeep = upDeep
		workflows[i].DownwardDeep = downDeep

		workflows[i].UpwardAllByReferencedWorkflows = set.RemoveDuplicatesAndEmpty(
			workflows[i].UpwardAllByReferencedWorkflows)
		workflows[i].DownwardAllReferencedWorkflows = set.RemoveDuplicatesAndEmpty(
			workflows[i].DownwardAllReferencedWorkflows)
	}
	log.InfoContextf(ctx, "GetCanBeReferencedWorkflowList GetWorkflowsDeep tree:\n%s",
		workflowDeepMessage.String())
}

// 递归检查工作流向上被引用的层数和全部引用工作流ID
func getUpwardLevel(ctx context.Context, workflow *entity.Workflow, workflowMap map[string]*entity.Workflow,
	treeBuilderMap map[string]string, messageStyle string) (int, *strings.Builder) {
	workflowDeepMessage := new(strings.Builder)
	if workflow.UpwardDeep != 0 {
		builderUpExistTree(treeBuilderMap, workflow, messageStyle, workflowDeepMessage)
		return workflow.UpwardDeep, workflowDeepMessage
	}
	if len(workflow.UpwardDirectByReferencedWorkflows) == 0 {
		return 0, workflowDeepMessage
	}
	maxDepth := 0
	for _, referencedWorkflowID := range workflow.UpwardDirectByReferencedWorkflows {
		referencedWorkflow := getWorkflowFromMapByID(referencedWorkflowID, workflowMap)
		var currentMessage = ""
		if referencedWorkflow != nil {
			currentMessage = fmt.Sprintf("%s%s(%s)", messageStyle,
				referencedWorkflow.WorkflowName, referencedWorkflow.WorkflowID)
			if util.ContainsList(workflow.UpwardAllByReferencedWorkflows, referencedWorkflowID) {
				continue
			}
			workflow.UpwardAllByReferencedWorkflows = append(workflow.UpwardAllByReferencedWorkflows,
				referencedWorkflowID)
			depth, workflowDeepSubMessage := getUpwardLevel(ctx, referencedWorkflow, workflowMap,
				treeBuilderMap, messageStyle+"<<<<")
			workflowDeepMessage.WriteString(workflowDeepSubMessage.String())
			currentMessage = currentMessage + fmt.Sprintf("(upDeep:%d)>>>>\n", depth)
			referencedWorkflow.UpwardDeep = depth
			workflow.UpwardAllByReferencedWorkflows = append(workflow.UpwardAllByReferencedWorkflows,
				referencedWorkflow.UpwardAllByReferencedWorkflows...)
			if depth > maxDepth {
				maxDepth = depth
			}
		}
		workflowDeepMessage.WriteString(currentMessage)
	}
	treeBuilderMap[workflow.WorkflowName+"up"] = workflowDeepMessage.String()
	return maxDepth + 1, workflowDeepMessage
}

// 递归检查工作流向下引用的层数和全部引用工作流ID
func getDownwardLevel(ctx context.Context, workflow *entity.Workflow, workflowMap map[string]*entity.Workflow,
	treeBuilderMap map[string]string, workflowDeepMessage *strings.Builder, messageStyle string) int {
	workflowDeepMessageMap := new(strings.Builder)
	if workflow.DownwardDeep != 0 {
		builderDownExistTree(treeBuilderMap, workflow, messageStyle, workflowDeepMessage)
		return workflow.DownwardDeep
	}
	if len(workflow.DownwardDirectReferencedWorkflows) == 0 {
		return 0
	}
	maxDepth := 0
	for _, referencedWorkflowID := range workflow.DownwardDirectReferencedWorkflows {
		referencedWorkflow := getWorkflowFromMapByID(referencedWorkflowID, workflowMap)
		if referencedWorkflow != nil {
			workflowDeepMessage.WriteString(fmt.Sprintf("%s%s(%s)", messageStyle,
				referencedWorkflow.WorkflowName, referencedWorkflow.WorkflowID))
			workflowDeepMessageMap.WriteString(fmt.Sprintf("%s%s(%s)", messageStyle,
				referencedWorkflow.WorkflowName, referencedWorkflow.WorkflowID))
			if util.ContainsList(workflow.DownwardAllReferencedWorkflows, referencedWorkflowID) {
				continue
			}
			workflow.DownwardAllReferencedWorkflows = append(workflow.DownwardAllReferencedWorkflows,
				referencedWorkflowID)
			workflowDeepMessageSub := new(strings.Builder)

			depth := getDownwardLevel(ctx, referencedWorkflow, workflowMap, treeBuilderMap,
				workflowDeepMessageSub, messageStyle+"<<<<")
			workflowDeepMessage.WriteString(fmt.Sprintf("(downDeep:%d)>>>>\n", depth))
			workflowDeepMessageMap.WriteString(fmt.Sprintf("(downDeep:%d)>>>>\n", depth))
			workflowDeepMessage.WriteString(workflowDeepMessageSub.String())
			workflowDeepMessageMap.WriteString(workflowDeepMessageSub.String())
			referencedWorkflow.DownwardDeep = depth
			workflow.DownwardAllReferencedWorkflows = append(workflow.DownwardAllReferencedWorkflows,
				referencedWorkflow.DownwardAllReferencedWorkflows...)
			// 取最深层级
			if depth > maxDepth {
				maxDepth = depth
			}
		}
	}
	treeBuilderMap[workflow.WorkflowName+"down"] = workflowDeepMessageMap.String()
	return maxDepth + 1
}

func builderUpExistTree(treeBuilderMap map[string]string, workflow *entity.Workflow, messageStyle string,
	workflowDeepMessage *strings.Builder) {
	existTreeBuilderJson, ok := treeBuilderMap[workflow.WorkflowName+"up"]
	if ok {
		messageStyleCount := 0
		for _, char := range messageStyle {
			if char == '<' {
				messageStyleCount++
			}
		}
		// 当前的和map中的比较
		lastAndMessageStyleDifference := 0
		// 去掉最后面的，防止换行多一行空的
		existTreeBuilderJson := strings.TrimRight(existTreeBuilderJson, ">>>>\n")
		lines := strings.Split(existTreeBuilderJson, ">>>>\n")

		if len(lines) > 0 {
			countLast := countLeadingDashes(lines[(len(lines) - 1)])
			// 计算当前的和map的差值
			lastAndMessageStyleDifference = messageStyleCount - countLast
			for _, line := range lines {
				// 计算map中每一行开头的"<"的个数
				count := countLeadingDashes(line)
				// 如果当前的多，那就要给map加"<"
				if lastAndMessageStyleDifference > 0 {
					line = strings.Repeat("<", lastAndMessageStyleDifference) + line
				}
				// 如果当前的少，而且map中的这一行足够减，那就要给map减"-"，否则不处理
				if lastAndMessageStyleDifference < 0 && count+lastAndMessageStyleDifference > 0 {
					line = strings.TrimLeft(line, "<")
					line = strings.Repeat("<", count+lastAndMessageStyleDifference) + line
				}
				workflowDeepMessage.WriteString(fmt.Sprintf("%s>>>>\n", line))
			}
		}
	}
}

func builderDownExistTree(treeBuilderMap map[string]string, workflow *entity.Workflow, messageStyle string,
	workflowDeepMessage *strings.Builder) {
	existTreeBuilderJson, ok := treeBuilderMap[workflow.WorkflowName+"down"]
	if ok {
		messageStyleCount := 0
		for _, char := range messageStyle {
			if char == '<' {
				messageStyleCount++
			}
		}
		// 当前的和map中的比较
		firstAndMessageStyleDifference := 0
		// 去掉最后面的，防止换行多一行空的
		existTreeBuilderJson := strings.TrimRight(existTreeBuilderJson, ">>>>\n")
		lines := strings.Split(existTreeBuilderJson, ">>>>\n")
		for i, line := range lines {
			// 计算map中每一行开头的"<"的个数
			count := countLeadingDashes(line)
			// 计算当前的和map的差值
			if i == 0 {
				firstAndMessageStyleDifference = messageStyleCount - count
			}
			// 如果当前的多，那就要给map加"<"
			if firstAndMessageStyleDifference > 0 {
				line = strings.Repeat("<", firstAndMessageStyleDifference) + line
			}
			// 如果当前的少，而且map中的这一行足够减，那就要给map减"-"，否则不处理
			if firstAndMessageStyleDifference < 0 && count+firstAndMessageStyleDifference > 0 {
				line = strings.TrimLeft(line, "<")
				line = strings.Repeat("<", count+firstAndMessageStyleDifference) + line
			}
			workflowDeepMessage.WriteString(fmt.Sprintf("%s>>>>\n", line))
		}
	}
}

func countLeadingDashes(line string) int {
	count := 0
	for _, char := range line {
		if char == '<' {
			count++
		} else {
			break
		}
	}
	return count
}

// GetCanBeReferencedWorkflows 分页模糊查找，并过滤指定工作流ID，找出可以被引用的工作流
func GetCanBeReferencedWorkflows(workflows []*entity.Workflow, workflowMap map[string]*entity.Workflow,
	keyword string, filterID string, pageSize, pageNumber uint32) (
	[]entity.Workflow, int) {
	// 参数校验
	if len(workflows) == 0 || len(workflowMap) == 0 {
		return []entity.Workflow{}, 0
	}

	var canBeReferencedWorkflows []entity.Workflow
	workflowReferenceDepth := config.GetMainConfig().VerifyWorkflow.WorkflowReferenceDepth
	filterWorkflow := getWorkflowFromMapByID(filterID, workflowMap)

	// 遍历所有工作流进行筛选
	for _, workflow := range workflows {
		// 跳过不符合条件的工作流
		if workflow.WorkflowID == filterID {
			continue
		}
		// 跳过没有调试通过的工作流
		if !(workflow.ReleaseStatus == entity.WorkflowReleaseStatusPublished ||
			workflow.WorkflowState == entity.WorkflowStateEnable ||
			workflow.WorkflowState == entity.WorkflowStatePublishedChange) {
			continue
		}
		// 过滤层级超过5层的 最高层级A-B-C-D-E-F；除开自身，上下加起来不超过5层
		if filterWorkflow == nil || filterWorkflow.UpwardDeep+workflow.DownwardDeep >= workflowReferenceDepth {
			continue
		}
		// 模糊搜索不匹配的跳过
		if keyword != "" && !strings.Contains(workflow.WorkflowName, keyword) {
			continue
		}
		// 指定filterID自身及上层包含workflow下层的工作流的跳过
		if IsCycle(filterWorkflow, workflow) {
			continue
		}
		// 所有条件都满足，添加到结果列表
		canBeReferencedWorkflows = append(canBeReferencedWorkflows, *workflow)
	}

	// 处理分页逻辑
	total := len(canBeReferencedWorkflows)
	if total == 0 {
		return []entity.Workflow{}, 0
	}
	// 计算起始索引和结束索引
	startIndex := (pageNumber - 1) * pageSize
	if startIndex >= uint32(total) {
		return []entity.Workflow{}, total
	}

	endIndex := startIndex + pageSize
	// 判断结束索引是否超出切片范围
	if endIndex > uint32(total) {
		endIndex = uint32(total)
	}
	// 返回分页结果
	return canBeReferencedWorkflows[startIndex:endIndex], len(canBeReferencedWorkflows)
}

// IsCycle 检查两个工作流之间是否存在循环引用
// 参数:
//
//	filterWorkflow: 过滤工作流(上游工作流)
//	workflow: 当前工作流(下游工作流)
//
// 返回值:
//
//	bool: true表示存在循环引用，false表示不存在
func IsCycle(filterWorkflow *entity.Workflow, workflow *entity.Workflow) bool {
	// 快速检查:如果任一工作流为空或没有引用关系，直接返回false
	if filterWorkflow == nil || workflow == nil ||
		len(workflow.DownwardAllReferencedWorkflows) == 0 {
		return false
	}
	// 检查filter自身有没有被workflow引用
	for _, item2 := range workflow.DownwardAllReferencedWorkflows {
		// 判断是否有共同元素
		if filterWorkflow.WorkflowID == item2 {
			return true
		}
	}
	// 检查filter上层有没有被workflow引用
	// 遍历第一个切片
	if len(filterWorkflow.UpwardAllByReferencedWorkflows) > 0 {
		for _, item1 := range filterWorkflow.UpwardAllByReferencedWorkflows {
			// 遍历第二个切片
			for _, item2 := range workflow.DownwardAllReferencedWorkflows {
				// 判断是否有共同元素
				if item1 == item2 {
					return true
				}
			}
		}
	}
	return false
}

//// getWorkflowByID 根据ID获取工作流
//func getWorkflowByID(workflowID string, workflows []*entity.Workflow) *entity.Workflow {
//	for i := range workflows {
//		if workflows[i].WorkflowID == workflowID {
//			return workflows[i]
//		}
//	}
//	return nil
//}

// getWorkflowFromMapByID 根据ID获取工作流
func getWorkflowFromMapByID(workflowID string, workflowMap map[string]*entity.Workflow) *entity.Workflow {
	workflow, ok := workflowMap[workflowID]
	if ok {
		return workflow
	}
	return nil
}

// GetWorkflowRefsByFlowIds 批量查询指定工作流向下引用的其他工作流详情
func GetWorkflowRefsByFlowIds(ctx context.Context, robotId string, workflowIds []string) (
	map[string]struct{}, error) {
	// 参数校验
	if len(workflowIds) == 0 || len(robotId) == 0 {
		log.WarnContextf(ctx, "GetWorkflowRefsByFlowIds workflowIds and robotId is empty "+
			"workflowIds:%+v, robotId:%s", workflowIds, robotId)
		return nil, errors.BadWorkflowReqError("请求参数没有应用ID或者没有工作流ID")
	}
	// 获取当前应用下所有工作流引用关系
	references, err := GetAllWorkflowRefs(ctx, robotId)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowRefsByFlowIds db.GetAllWorkflowRefs err|%v", err)
		return nil, err
	}
	// 构建工作流向下引用关系图
	workflowGraph := buildWorkflowGraph(references)
	// 递归获取涉及引用关系的工作流
	refWorkflowIdMap := make(map[string]struct{})
	for _, workflowID := range workflowIds {
		referencedWorkflows, ok := workflowGraph[workflowID]
		if ok {
			for _, referencedWorkflowID := range referencedWorkflows {
				getRelatedWorkflowsRecursive(referencedWorkflowID, workflowGraph, refWorkflowIdMap)
			}
		}
	}
	log.InfoContextf(ctx, "GetWorkflowRefsByFlowIds workflowIds:%v,references:%v,workflowGraph:%v,"+
		"refWorkflowIdMap:%v", workflowIds, references, workflowGraph, refWorkflowIdMap)
	return refWorkflowIdMap, nil
}

// buildWorkflowGraph 构建工作流向下引用关系图
func buildWorkflowGraph(references []entity.WorkflowReference) map[string][]string {
	workflowGraph := make(map[string][]string, 0)
	for _, reference := range references {
		workflowGraph[reference.WorkflowID] = append(workflowGraph[reference.WorkflowID], reference.WorkflowRefID)
	}
	return workflowGraph
}

// getRelatedWorkflowsRecursive 递归获取涉及引用关系的工作流
func getRelatedWorkflowsRecursive(referencedWorkflowID string, workflowGraph map[string][]string,
	refWorkflowIds map[string]struct{}) {
	// 检查是否已处理过该工作流
	if _, ok := refWorkflowIds[referencedWorkflowID]; ok {
		return
	}
	refWorkflowIds[referencedWorkflowID] = struct{}{}
	referencedWorkflows, ok := workflowGraph[referencedWorkflowID]
	if ok {
		for _, referencedWorkflowID := range referencedWorkflows {
			getRelatedWorkflowsRecursive(referencedWorkflowID, workflowGraph, refWorkflowIds)
		}
	}

}

//// GetAllWorkflowNodeRefs 获取当前应用下所有工作流具体节点引用关系
//func GetAllWorkflowNodeRefs(ctx context.Context, robotId string) ([]entity.WorkflowReference, error) {
//	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
//	var workFlowRef []entity.WorkflowReference
//	if err := db.Table(entity.WorkflowReference{}.TableName()).
//		Select("f_workflow_id, f_node_id, f_workflow_ref_id").
//		Where("f_is_deleted = 0 ").
//		Where("f_robot_id = ? ", robotId).
//		Scan(&workFlowRef).Error; err != nil {
//		log.ErrorContextf(ctx, "GetAllWorkflowRefs err:%v", err)
//		return nil, err
//	}
//	return workFlowRef, nil
//}

// GetWorkflowRefInfosByFlowIds 查询工作流引用的其他工作流详情；过滤掉引用工作流关系表中对应的源数据不存在的情况
func GetWorkflowRefInfosByFlowIds(ctx context.Context, robotId string, workflowIds []string) (
	[]*entity.ExportWorkflowRef, error) {
	if len(workflowIds) == 0 && len(robotId) == 0 {
		log.WarnContextf(ctx, "GetWorkflowRefInfosByFlowIds workflowIds and robotId is empty "+
			"workflowIds:%+v, robotId:%s", workflowIds, robotId)
		return nil, errors.BadWorkflowReqError("请求参数没有应用ID，且没有工作流ID")
	}
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workflowRefList []*entity.ExportWorkflowRef
	db = db.Table("t_workflow_reference").
		Select("t_workflow_reference.f_workflow_id, t_workflow_reference.f_node_id, " +
			"t_workflow_reference.f_workflow_ref_id").
		Joins("inner join t_workflow on t_workflow_reference.f_workflow_ref_id = t_workflow.f_workflow_id").
		Where("t_workflow_reference.f_is_deleted = 0 and t_workflow.f_is_deleted = 0")
	if len(robotId) > 0 {
		db = db.Where("t_workflow_reference.f_robot_id = ? and t_workflow.f_robot_id = ?", robotId, robotId)
	}
	if len(workflowIds) > 0 {
		db = db.Where("t_workflow_reference.f_workflow_id IN (?)", workflowIds)
	}
	err := db.Scan(&workflowRefList).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowRefInfosByFlowIds failed workflowIDs:%+v, "+
			"robotId:%s, err:%+v", workflowIds, robotId, err)
		return nil, err
	}
	return workflowRefList, nil
}
