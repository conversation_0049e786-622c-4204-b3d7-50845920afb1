package db

import (
	"context"
	"errors"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// GetWorkflowRunsByAppIDAndEnvWithPagination 根据 AppID 和 RunEnv 查询 WorkflowRun，支持分页
func GetWorkflowRunsByAppIDAndEnvWithPagination(ctx context.Context, appID string,
	runEnv string, page uint32, pageSize uint32) ([]entity.WorkflowRun, int64, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workflowRuns []entity.WorkflowRun

	var total int64
	query := db.Model(&entity.WorkflowRun{}).
		Where("f_app_id = ?", appID).
		Where("f_run_env = ?", runEnv).
		Where("f_is_deleted = ?", 0).
		Order("f_create_time DESC")
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	err = query.Offset(int(offset)).Limit(int(pageSize)).Find(&workflowRuns).Error
	if err != nil {
		return nil, 0, err
	}

	return workflowRuns, total, nil
}

// GetPendingWorkflowRunsByUIN 根据 UIN 查询排队中的 WorkflowRun
func GetPendingWorkflowRunsByUIN(ctx context.Context, uin string) ([]entity.WorkflowRun, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workflowRuns []entity.WorkflowRun

	var total int64
	// 当运行实例的更新时间早于当前时间的x分钟前，也属于"排队中"，需要重新提交一下任务
	interval := time.Duration(config.GetMainConfig().AsyncWorkflowConfig.RunningRetryInterval) * time.Second
	query := db.Model(&entity.WorkflowRun{}).
		Where("f_uin = ?", uin).
		Where("(f_state = ? OR (f_state = ? AND f_update_time < ?))",
			entity.WorkflowRunStatePending,
			entity.WorkflowRunStateRunning,
			time.Now().Add(-interval)).
		Where("f_is_deleted = ?", 0)
	err := query.Count(&total).Error
	if err != nil {
		return nil, err
	}
	if total == 0 {
		return workflowRuns, nil
	}

	// 根据自增id来加速
	offset := uint64(0)
	limit := 100
	count := int64(0)
	for count < total {
		var tempWorkflowRuns []entity.WorkflowRun
		err = query.Where("f_id > ?", offset).Limit(int(limit)).Order("f_id ASC").Find(&tempWorkflowRuns).Error
		if err != nil {
			return nil, err
		}
		if len(tempWorkflowRuns) == 0 {
			break
		}
		workflowRuns = append(workflowRuns, tempWorkflowRuns...)
		offset = tempWorkflowRuns[len(tempWorkflowRuns)-1].ID
		count += int64(len(tempWorkflowRuns))
	}

	return workflowRuns, nil
}

// GetPendingWorkflowRunUINs 获取排队中的工作流运行实例对应的UIN
func GetPendingWorkflowRunUINs(ctx context.Context) ([]string, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var uins []string
	// 当运行实例的更新时间早于当前时间的x分钟前，也属于"排队中"，需要重新提交一下任务
	interval := time.Duration(config.GetMainConfig().AsyncWorkflowConfig.RunningRetryInterval) * time.Second
	query := db.Model(&entity.WorkflowRun{}).
		Where("(f_state = ? OR (f_state = ? AND f_update_time < ?))",
			entity.WorkflowRunStatePending,
			entity.WorkflowRunStateRunning,
			time.Now().Add(-interval)).
		Where("f_is_deleted = ?", 0).
		Order("f_create_time ASC").
		Select("f_uin")
	err := query.Find(&uins).Error
	if err != nil {
		return nil, err
	}
	return uins, nil
}

// GetNodeRunByID 根据 nodeRunId 查询 NodeRun 记录
func GetNodeRunByID(ctx context.Context, nodeRunId string) (*entity.NodeRun, error) {
	var nodeRun entity.NodeRun
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	result := db.Model(&entity.NodeRun{}).Where("f_node_run_id = ?", nodeRunId).First(&nodeRun)
	if result.Error != nil {
		// 如果记录未找到，返回 nil 和空错误
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		// 其他数据库错误，返回错误
		return nil, result.Error
	}
	return &nodeRun, nil
}

// GetWorkflowRunAndNodesByID 根据 WorkflowRunId 查询 WorkflowRun 和相关的 NodeRun 记录
func GetWorkflowRunAndNodesByID(ctx context.Context, workflowRunId string) (*entity.WorkflowRun, []entity.NodeRun, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workflowRun entity.WorkflowRun
	err := db.Model(&entity.WorkflowRun{}).Where("f_workflow_run_id = ?", workflowRunId).First(&workflowRun).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil, nil // 未找到 WorkflowRun，返回 nil
		}
		return nil, nil, err // 其他数据库错误
	}

	// 查询相关的 NodeRun
	var nodeRuns []entity.NodeRun
	err = db.Where("f_workflow_run_id = ?", workflowRunId).Find(&nodeRuns).Error
	if err != nil {
		return nil, nil, err // 查询 NodeRun 失败
	}

	return &workflowRun, nodeRuns, nil
}

// SaveWorkflowRun 保存 WorkflowRun
func SaveWorkflowRun(ctx context.Context, workflowRun *entity.WorkflowRun) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	return db.Model(&entity.WorkflowRun{}).Create(workflowRun).Error
}

// GetWorkflowRunCounts 获取总工作流运行实例运行中的数量
func GetWorkflowRunCounts(ctx context.Context) (int64, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var count int64
	err := db.Model(&entity.WorkflowRun{}).Where("f_state = ?", entity.WorkflowRunStateRunning).
		Count(&count).Error
	return count, err
}

// GetWorkflowRunCountByUIN 获取主账号ID对应的工作流运行实例的运行中的数量
func GetWorkflowRunCountByUIN(ctx context.Context, uin string) (int64, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var count int64
	err := db.Model(&entity.WorkflowRun{}).Where("f_uin = ?", uin).
		Where("f_state = ?", entity.WorkflowRunStateRunning).Count(&count).Error
	return count, err
}

// SaveWorkflowRunRetryUIN 保存需要重试的 主账号ID 到 redis ，用于后续定时任务重试
func SaveWorkflowRunRetryUIN(ctx context.Context, uin string, nextRetryTime time.Time) error {
	return database.GetRedis().ZAdd(ctx, entity.GetWorkflowRunRetryKey(), &redis.Z{
		Score:  float64(nextRetryTime.Unix()),
		Member: uin,
	}).Err()
}

// GetWorkflowRunRetryUINs 从 redis 获取需要重试的UIN
func GetWorkflowRunRetryUINs(ctx context.Context) ([]string, error) {
	retryUins := make([]string, 0)
	offset := int64(0)
	stopIter := false
	for !stopIter {
		uins, err := database.GetRedis().ZRangeByScore(ctx, entity.GetWorkflowRunRetryKey(), &redis.ZRangeBy{
			Min:    "0",
			Max:    strconv.FormatInt(time.Now().Unix(), 10),
			Offset: offset,
			Count:  1000,
		}).Result()
		if err != nil {
			return nil, err
		}
		if len(uins) == 0 {
			stopIter = true
		} else {
			offset += 1000
			retryUins = append(retryUins, uins...)
		}
	}
	return retryUins, nil
}

// RemoveWorkflowRunRetryUIN 从 redis 移除需要重试的 主账号ID
func RemoveWorkflowRunRetryUIN(ctx context.Context, uin string) error {
	return database.GetRedis().ZRem(ctx, entity.GetWorkflowRunRetryKey(), uin).Err()
}

// IsWorkflowRunRetryUINExist 检查某个任务是否存在于延迟队列中
func IsWorkflowRunRetryUINExist(ctx context.Context, uin string) (time.Time, bool) {
	// ZScore 返回 *float64，如果 member 不存在则返回 nil
	score, err := database.GetRedis().ZScore(ctx, entity.GetWorkflowRunRetryKey(), uin).Result()
	if err != nil {
		return time.Time{}, false
	}
	timestampUnix := int64(score)
	return time.Unix(timestampUnix, 0), true
}

// SaveAppDebugMode 保存应用的调试配置
func SaveAppDebugMode(ctx context.Context, appID, debugMode string) error {
	workflowCustomConfig, err := DescribeAppDebugConfig(ctx, appID)
	if err != nil {
		log.WarnContextf(ctx, "SaveAppDebugMode db.DescribeAppDebugConfig err:%+v", err)
		return err
	}
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	// 判断是否已经存在数据，决定是插入还是更新
	if workflowCustomConfig != nil {
		// 更新
		if err := db.Table(workflowCustomConfig.TableName()).Where("f_id=?", workflowCustomConfig.ID).
			Updates(map[string]interface{}{
				"f_debug_mode": debugMode,
				"f_uin":        uin,
				"f_sub_uin":    subUin,
			}).Error; err != nil {
			log.ErrorContextf(ctx, "SaveAppDebugMode update err:%+v", err)
			return err
		}
	} else {
		// 创建
		insertWorkflowCustomConfig := &entity.WorkflowCustomConfig{
			AppID:           appID,
			DebugMode:       debugMode,
			CustomVariables: "null",
			Uin:             uin,
			SubUin:          subUin,
		}
		if err := db.Table(insertWorkflowCustomConfig.TableName()).Create(insertWorkflowCustomConfig).
			Error; err != nil {
			log.ErrorContextf(ctx, "SaveAppDebugMode insert err:%+v", err)
			return err
		}
	}
	return nil
}

// SaveAppDebugCustomVariables 保存应用的调试配置的AI参数的值
func SaveAppDebugCustomVariables(ctx context.Context, appID, customJSON string) error {
	workflowCustomConfig, err := DescribeAppDebugConfig(ctx, appID)
	if err != nil {
		log.WarnContextf(ctx, "SaveAppDebugCustomVariables db.DescribeAppDebugConfig err:%+v", err)
		return err
	}
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	// 判断是否已经存在数据，决定是插入还是更新
	if workflowCustomConfig != nil {
		// 更新
		if err := db.Table(workflowCustomConfig.TableName()).Where("f_id=?", workflowCustomConfig.ID).
			Updates(map[string]interface{}{
				"f_custom_variables": customJSON,
				"f_uin":              uin,
				"f_sub_uin":          subUin,
			}).Error; err != nil {
			log.ErrorContextf(ctx, "SaveAppDebugCustomVariables update err:%+v", err)
			return err
		}
	} else {
		// 创建
		insertWorkflowCustomConfig := &entity.WorkflowCustomConfig{
			AppID:           appID,
			DebugMode:       "SYNC",
			CustomVariables: customJSON,
			Uin:             uin,
			SubUin:          subUin,
		}
		if err := db.Table(insertWorkflowCustomConfig.TableName()).Create(insertWorkflowCustomConfig).
			Error; err != nil {
			log.ErrorContextf(ctx, "SaveAppDebugCustomVariables insert err:%+v", err)
			return err
		}
	}
	return nil
}

// DescribeAppDebugConfig 查看应用的调试配置
func DescribeAppDebugConfig(ctx context.Context, appID string) (*entity.WorkflowCustomConfig, error) {
	var workflowCustomConfig entity.WorkflowCustomConfig
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	// 查询符合条件的记录
	err := db.Table(workflowCustomConfig.TableName()).
		Where("f_app_id = ? AND f_is_deleted = 0", appID).
		Take(&workflowCustomConfig).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnContextf(ctx, "DescribeAppDebugConfig appId:%s is not exist", appID)
			return nil, nil // 没有找到记录
		}
		log.ErrorContextf(ctx, "DescribeAppDebugConfig appId:%s, err:%+v",
			appID, err)
		return &workflowCustomConfig, err
	}
	return &workflowCustomConfig, nil
}
