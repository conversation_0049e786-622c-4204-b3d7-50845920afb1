[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "TaskConfig", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.KEP.bot_task_config_server.TaskConfig", "MethodName": "ImportWorkflowExample", "Func": "/ImportWorkflowExample", "ReqBody": "trpc.KEP.bot_task_config_server.ImportWfExampleReq", "RspBody": "trpc.KEP.bot_task_config_server.ImportWfExampleRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"AppBizId": "1904432610691776512", "WorkflowId": "7a7d062c-076c-4f6b-bf1f-3c8eb5f0833b", "FileName": "示例问法2.xlsx", "CosUrl": "/corp/1904119389648846848/1904432610691776512/doc/goGqAWBKBYLPEcFxdeSK-1918140369726603264.xlsx", "CosHash": "14831881565223759390", "Size": 9048}, "CheckList": [{"JsonPath": "FlowId", "OP": "NE", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "import_workflow_example"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]