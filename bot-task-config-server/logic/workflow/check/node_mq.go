/*
 * 2024-10-15
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON>n@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func (c *WfContext) parseMQNode(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetMQNodeData()

	if node.GetMQActionType() != KEP_WF.MQNodeData_SEND {
		c.appendNodeError(nodeID, "目前仅支持发送消息到消息队列中")
		return
	}

	// 消息队列中消息内容不应该超过100KB
	// 小消息优先：消息队列更适合传递小而频繁的消息，建议单条消息在1KB~100KB之间。
	// 大消息处理：如果消息内容较大（如图片、文件等），建议将大数据存储在对象存储（如OSS、S3等），消息队列只传递存储地址或元数据。
	maxMessageSize := 100 * 1024
	if len(node.GetMessage()) > maxMessageSize {
		c.appendNodeError(nodeID, "消息内容不能超过100KB")
	}

	switch node.GetMQType() {
	case KEP_WF.MQNodeData_KAFKA:
		c.parseKafkaOptions(nodeID, node.GetKafkaOptions())
	case KEP_WF.MQNodeData_ROCKETMQ:
		c.parseRocketMQOptions(nodeID, node.GetRocketMQOptions())
	default:
		c.appendNodeError(nodeID, fmt.Sprintf("不支持的消息队列类型: %v", node.GetMQType()))
	}
}

func (c *WfContext) parseKafkaOptions(nodeID string, kafkaOptions *KEP_WF.MQNodeData_KafkaOption) {
	if kafkaOptions == nil {
		return
	}
	c.checkInputAndCollectBase(nodeID, nil, kafkaOptions.GetEndpoints())
	c.checkInputAndCollectBase(nodeID, nil, kafkaOptions.GetTopic())
	switch kafkaOptions.GetProtocol() {
	case KEP_WF.MQNodeData_KafkaOption_PLAINTEXT:
		return
	case KEP_WF.MQNodeData_KafkaOption_SASL_PLAINTEXT, KEP_WF.MQNodeData_KafkaOption_SASL_SSL:
		c.checkInputAndCollectBase(nodeID, nil, kafkaOptions.GetUserName())
		c.checkInputAndCollectBase(nodeID, nil, kafkaOptions.GetPassword())
	default:
		c.appendNodeError(nodeID, fmt.Sprintf("不支持的Kafka认证方式: %v", kafkaOptions.GetMechanism()))
	}

}

func (c *WfContext) parseRocketMQOptions(nodeID string, rocketMQOptions *KEP_WF.MQNodeData_RocketMQOption) {
	if rocketMQOptions == nil {
		return
	}
	if rocketMQOptions.GetVersion() != KEP_WF.MQNodeData_RocketMQOption_V4 &&
		rocketMQOptions.GetVersion() != KEP_WF.MQNodeData_RocketMQOption_V5 {
		c.appendNodeError(nodeID, fmt.Sprintf("不支持的RocketMQ版本: %v", rocketMQOptions.GetVersion()))
	}
	c.checkInputAndCollectBase(nodeID, nil, rocketMQOptions.GetEndpoints())
	c.checkInputAndCollectBase(nodeID, nil, rocketMQOptions.GetTopic())
	c.checkInputAndCollectBase(nodeID, nil, rocketMQOptions.GetAccessKey())
	c.checkInputAndCollectBase(nodeID, nil, rocketMQOptions.GetSecretKey())
}
