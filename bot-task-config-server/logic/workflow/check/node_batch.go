package check

import (
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func (c *WfContext) parseBatch(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetBatchNodeData()

	if len(node.GetWorkflowID()) == 0 {
		c.appendNodeError(nodeID, "引用的工作流为空")
		return
	}

	// 当前节点用到的"输入参数"的名字
	currentNodeInputParamNames := make([]string, 0)
	existArrays := make(map[string]bool)
	for _, param := range wfn.GetInputs() {
		// 目前除开始节点外，其它节点（循环节点）不支持输入参数（类型为array<object>及object）的子级，因此不需要处理
		currentNodeInputParamNames = append(currentNodeInputParamNames, param.GetName())
		switch param.GetType() {
		case KEP_WF.TypeEnum_ARRAY_STRING, KEP_WF.TypeEnum_ARRAY_INT,
			KEP_WF.TypeEnum_ARRAY_FLOAT, KEP_WF.TypeEnum_ARRAY_BOOL,
			KEP_WF.TypeEnum_ARRAY_OBJECT:
			existArrays[param.GetName()] = true
			currentNodeInputParamNames = append(currentNodeInputParamNames, param.GetName()+".Item")
		}
	}

	// 1. 检查被引用工作流的输入参数：
	for _, input := range node.GetRefInputs() {
		if input.GetName() == "" {
			c.appendNodeError(nodeID, "变量名称不能为空")
		}
		// a. 校验 Input； b. 收集引用的"API参数（自定义变量）"
		c.checkInputAndCollectForSupportInnerInputParams(nodeID, currentNodeInputParamNames, input.GetInput())
	}

	// 2. 引用的工作流的关联关系
	c.collectWorkflowReference(nodeID, node.GetWorkflowID())
	// 3. 收集引用的工作流的输入，用于最后统一检查与引用的工作流中定义的输入是否一致
	c.collectItemToRefWorkflowInputs(node.GetWorkflowID(), node.GetRefInputs())
	// 4. 收集引用的工作流的输出，用于最后统一检查与引用的工作流中定义的输出是否一致
	c.collectItemToRefWorkflowOutputs(node.GetWorkflowID(), wfn.GetOutputs())

	if node.GetSpecifiedTraversalVariable() == "" {
		c.appendNodeError(nodeID, "指定遍历的数组未选择")
	} else if !existArrays[node.GetSpecifiedTraversalVariable()] {
		c.appendNodeError(nodeID, "指定遍历数组不存在")
	}

}
