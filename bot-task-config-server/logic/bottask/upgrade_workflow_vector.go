package bottask

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service/migrator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/utils"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
	"gorm.io/gorm"
)

// UpgradeWorkflowVectorScheduler 升级工作流embedding向量任务
type UpgradeWorkflowVectorScheduler struct {
	task      task_scheduler.Task
	param     entity.TaskUpgradeWorkflowVectorParams
	err       error
	logPrefix string
}

func init() {
	task_scheduler.Register(
		entity.TaskUpgradeWorkflowVector,
		func(task task_scheduler.Task, params entity.TaskUpgradeWorkflowVectorParams) task_scheduler.TaskHandler {
			return &UpgradeWorkflowVectorScheduler{
				task:      task,
				param:     params,
				logPrefix: fmt.Sprintf("UpgradeWorkflowVector|Migrate|AppID:%s", params.RobotID),
			}
		})
}

// Prepare 数据准备
func (c *UpgradeWorkflowVectorScheduler) Prepare(_ context.Context) (task_scheduler.TaskKV, error) {
	return task_scheduler.TaskKV{}, nil
}

// Init 初始化
func (c *UpgradeWorkflowVectorScheduler) Init(ctx context.Context, _ task_scheduler.TaskKV) error {
	// 填充RequestID和日志信息
	util.WithRequestID(ctx, c.param.RequestID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	log.InfoContextf(ctx, "%s|Init", c.logPrefix)
	return nil
}

// Process 任务处理
func (c *UpgradeWorkflowVectorScheduler) Process(ctx context.Context, _ *task_scheduler.Progress) error {
	newCtx := trpc.CloneContext(ctx)
	err := c.migrateWorkflowVector(newCtx, c.param.RobotID, c.param.EmbeddingModelName)
	c.err = err
	return err
}

// Done 任务完成, 仅在任务完成时触发一次, 该方法返回失败会在下次重试中继续触发
func (c *UpgradeWorkflowVectorScheduler) Done(ctx context.Context) error {
	log.InfoContextf(ctx, "%s|Done", c.logPrefix)
	return nil
}

// Fail 任务失败, 仅在任务重试次数耗尽彻底终止时触发一次, 且无论 Fail 本身是否执行成功, 仅触发一次
func (c *UpgradeWorkflowVectorScheduler) Fail(ctx context.Context) error {
	log.InfoContextf(ctx, "%s|Failed", c.logPrefix)
	return nil
}

// Stop 任务终止, 仅在任务被终止时触发一次
func (c *UpgradeWorkflowVectorScheduler) Stop(ctx context.Context) error {
	return nil
}

// 实现embedding特征升级灰度主逻辑，以应用为维度
func (c *UpgradeWorkflowVectorScheduler) migrateWorkflowVector(ctx context.Context, appID string, modelName string) error {
	var err error
	var upgrading bool
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	vdb := vdao.NewDao()
	// 1. 检查是否需要升级
	vectorGroups, err := vdb.GetWorkflowVectorInfo(ctx, db, appID, entity.SaveWorkflowType)
	if err != nil {
		return err
	}
	needUpgrade := false
	for _, vectorGroup := range vectorGroups {
		if vectorGroup.EmbeddingModeName != modelName {
			needUpgrade = true
			break
		}
	}
	if !needUpgrade {
		log.InfoContextf(ctx, "%s|no need to upgrade, modelName:%s", c.logPrefix, modelName)
		return nil
	}
	// 2. 标记工作流embedding向量正在升级中
	err = vdb.MarkWorkflowVectorUpgrade(ctx, db, appID, entity.SaveWorkflowType)
	if err != nil {
		log.ErrorContextf(ctx, "%s|invoke.MarkWorkflowVectorUpgrade|err:%+v", c.logPrefix, err)
		return err
	}
	defer func() {
		// 出现错误，重置升级状态
		if err != nil && upgrading {
			log.InfoContextf(ctx, "%s|upgrade workflow vector failed, reset upgrade status", c.logPrefix)
			err = vdb.MarkWorkflowVectorUpgradeDone(ctx, db, appID, entity.SaveWorkflowType)
			if err != nil {
				log.ErrorContextf(ctx, "%s|invoke.MarkWorkflowVectorUpgradeDone|err:%+v", c.logPrefix, err)
				return
			}
		}
	}()
	upgrading = true
	// 3. 获取模型名对应的配置项
	embeddingModelConfig := c.getUsingVectorModelInfo(ctx, modelName)
	if embeddingModelConfig == nil {
		log.WarnContextf(ctx, "%s|getUsingVectorModelInfo|modelName:%s not found", c.logPrefix, modelName)
		return fmt.Errorf("model %s is not found in config", modelName)
	}
	// 4. 获取embedding向量库分组
	oldVectorGroupInfos, err := c.getWorkflowVectorGroups(ctx, db, appID)
	if err != nil {
		log.WarnContextf(ctx, "%s|getWorkflowVectorGroups|err:%v", c.logPrefix, err)
		return err
	}
	// 5. 构造新的embedding向量库分组
	sandboxGroupID, prodGroupID, newVectorGroupInfos := c.getNewWorkflowVectorGroups(ctx, appID,
		oldVectorGroupInfos, embeddingModelConfig)
	// 6. 调用向量库接口存储新的向量分组
	if !c.param.TestOnly {
		_, _, err = vdb.CreateBotWorkflowGroup(ctx, appID, newVectorGroupInfos)
		if err != nil {
			log.WarnContextf(ctx, "%s|invoke.CreateBotWorkflowGroup|err:%v", c.logPrefix, err)
			return err
		}
	} else {
		log.InfoContextf(ctx, "%s|test only, skip create new workflow vector group", c.logPrefix)
	}
	// 7. 处理评测库与正式库
	envs := []string{entity.SandboxEnv, entity.ProductEnv}
	for _, env := range envs {
		groupID := sandboxGroupID
		if env == entity.ProductEnv {
			groupID = prodGroupID
		}
		totalCount, err := migrator.GetWorkflowCount(ctx, appID, entity.SandboxEnv)
		if err != nil {
			log.WarnContextf(ctx, "%s|invoke.GetWorkflowCount|err:%+v", c.logPrefix, err)
			return err
		}
		pageSize := 5
		for currentPage := 1; ; currentPage++ {
			wfs, err := migrator.GetWorkflowList(ctx, appID, env, currentPage, pageSize)
			if err != nil {
				log.WarnContextf(ctx, "%s|invoke.GetWorkflowList|err:%+v", c.logPrefix, err)
				return err
			}
			for _, wf := range wfs {
				wfExams, err := migrator.GetFlowExampleByEnvWfId(ctx, appID, wf.WorkflowID, env)
				if err != nil {
					log.WarnContextf(ctx, "%s|invoke.GetFlowExampleByEnvWfId|err:%+v", c.logPrefix, err)
					return err
				}
				if c.param.TestOnly {
					log.InfoContextf(ctx, "%s|test only, skip save workflow to vector", c.logPrefix)
					continue
				}
				err = c.saveWorkflowToVector(ctx, db, vdb, appID, groupID, wf, wfExams, embeddingModelConfig)
				if err != nil {
					log.WarnContextf(ctx, "%s|saveWorkflowToVector|err:%+v", c.logPrefix, err)
					return err
				}
			}
			if currentPage*pageSize >= totalCount {
				break
			}
		}
		// 8. 更新向量库分组ID和模型名并通知DM，需要在一个transaction中执行，通知DM一定一定一定要成功
		if c.param.TestOnly {
			log.InfoContextf(ctx, "%s|test only, skip update workflow vector group id and model name", c.logPrefix)
			continue
		}
		if err = db.Transaction(func(tx *gorm.DB) error {
			// 更新向量库分组ID和模型名
			if env == entity.SandboxEnv {
				err = vdb.UpdateWorkflowVectorGroupIDAndModelName(ctx, tx, appID, entity.SaveWorkflowType,
					vdao.WorkflowSandboxGroupInfix, groupID, embeddingModelConfig.LatestEmbeddingModelName)
			} else {
				err = vdb.UpdateWorkflowVectorGroupIDAndModelName(ctx, tx, appID, entity.SaveWorkflowType,
					vdao.WorkflowProdGroupInfix, groupID, embeddingModelConfig.LatestEmbeddingModelName)
			}
			if err != nil {
				log.WarnContextf(ctx, "%s|invoke.UpdateWorkflowVectorGroupIDAndModelName|err:%+v", c.logPrefix, err)
				return err
			}
			// 通知DM
			var dmErr error
			startInvoke := time.Now()
			for {
				if env == entity.SandboxEnv {
					req := &KEP_WF_DM.UpsertAppToSandboxRequest{
						AppID:                    appID,
						RetrievalWorkflowGroupID: groupID,
						RetrievalWorkflowModel:   embeddingModelConfig.LatestEmbeddingModelName,
					}
					if _, dmErr = proxy.GetWfDmProxy().UpsertAppToSandbox(ctx, req); err != nil {
						log.ErrorContextf(ctx, "%s|invoke.UpsertAppToSandboxRequest|err:%+v", c.logPrefix, err)
						return err
					}
				} else {
					req := &KEP_WF_DM.ReleaseWorkflowAppRequest{
						AppID:                    appID,
						RetrievalWorkflowGroupID: groupID,
						RetrievalWorkflowModel:   embeddingModelConfig.LatestEmbeddingModelName,
					}
					if _, dmErr = proxy.GetWfDmProxy().ReleaseWorkflowApp(ctx, req); err != nil {
						log.ErrorContextf(ctx, "%s|invoke.ReleaseWorkflowAppRequest|err:%+v", c.logPrefix, err)
						return err
					}
				}
				if dmErr == nil {
					break
				}
				// 如果调用失败，且已经调用超过半小时，则不再重试，返回报错
				if time.Since(startInvoke) < 30*time.Minute {
					time.Sleep(20 * time.Second)
					continue
				}
				return dmErr
			}
			return nil
		}); err != nil {
			log.WarnContextf(ctx, "%s|UpgradeTransaction|err:%+v", c.logPrefix, err)
			return err
		}
	}
	// 9. 标记工作流embedding向量升级完成
	err = vdb.MarkWorkflowVectorUpgradeDone(ctx, db, appID, entity.SaveWorkflowType)
	if err != nil {
		log.ErrorContextf(ctx, "%s|invoke.MarkWorkflowVectorUpgradeDone|err:%+v", c.logPrefix, err)
		return err
	}
	// 10. 异步删除旧的向量库分组
	if c.param.TestOnly {
		log.InfoContextf(ctx, "%s|test only, skip delete old workflow vector group", c.logPrefix)
		return nil
	}
	go func() {
		newCtx := trpc.CloneContext(ctx)
		oldGroupIDs := make([]string, 0)
		for _, oldVectorGroupInfo := range oldVectorGroupInfos {
			if oldVectorGroupInfo.VectorGroupID == sandboxGroupID || oldVectorGroupInfo.VectorGroupID == prodGroupID {
				// 避免误删
				continue
			}
			oldGroupIDs = append(oldGroupIDs, oldVectorGroupInfo.VectorGroupID)
		}
		err = c.deleteOldWorkflowVectorGroup(newCtx, appID, oldGroupIDs, embeddingModelConfig.Biz, embeddingModelConfig.Secret)
		if err != nil {
			log.WarnContextf(newCtx, "%s|deleteOldWorkflowVectorGroup|err:%+v", c.logPrefix, err)
			return
		}
	}()
	log.InfoContextf(ctx, "%s|upgrade workflow vector success", c.logPrefix)
	return nil
}

func (c *UpgradeWorkflowVectorScheduler) getUsingVectorModelInfo(ctx context.Context, modelName string) *config.UsingVectorModelInfo {
	vectorGroup := config.GetMainConfig().WorkflowVectorGroup
	info := &config.UsingVectorModelInfo{
		Biz:             vectorGroup.Biz,
		Secret:          vectorGroup.Secret,
		OperationMaxIDs: vectorGroup.OperationMaxIDs,
		WorkflowUseVdb:  vectorGroup.WorkflowUseVdb,

		EmbeddingModelName:       vectorGroup.EmbeddingModelName,
		LatestEmbeddingModelName: modelName,
	}
	eVersionConfig, ok := vectorGroup.EmbeddingVersionControl[modelName]
	if !ok {
		log.ErrorContextf(ctx, "%s|getUsingVectorModelInfo|modelName:%s not found in config", c.logPrefix, modelName)
		return nil
	}
	info.GroupSuffix = eVersionConfig.GroupSuffix
	info.WorkflowNameTemplate = eVersionConfig.WorkflowNameTemplate
	info.WorkflowExampleTemplate = eVersionConfig.WorkflowExampleTemplate
	info.WorkflowCombinationTemplate = eVersionConfig.WorkflowCombinationTemplate
	info.WorkflowCombinationExampleNum = eVersionConfig.WorkflowCombinationExampleNum
	return info
}

func (c *UpgradeWorkflowVectorScheduler) getWorkflowVectorGroups(ctx context.Context, db *gorm.DB,
	appID string) ([]entity.WorkflowVectorGroup, error) {
	// 升级需要的APPID在库中必须有
	var vectorGroupInfos []entity.WorkflowVectorGroup
	err := db.Model(&entity.WorkflowVectorGroup{}).
		Where("f_robot_id = ? and f_is_deleted = 0 and f_save_type = ?", appID, entity.SaveWorkflowType).
		Find(&vectorGroupInfos).Error
	if err != nil {
		log.WarnContextf(ctx, "%s|getWorkflowVectorGroups|err:%+v", c.logPrefix, err)
		return nil, err
	}
	if len(vectorGroupInfos) != 2 {
		log.WarnContextf(ctx, "%s|getWorkflowVectorGroups|expect 2 groups, but got %d", c.logPrefix, len(vectorGroupInfos))
		return nil, fmt.Errorf("get vector group from db by appid %s failed, expect 2 groups, but got %d",
			appID, len(vectorGroupInfos))
	}
	return vectorGroupInfos, nil
}

func (c *UpgradeWorkflowVectorScheduler) getNewWorkflowVectorGroups(ctx context.Context, appID string,
	oldWorkflowVectorGroups []entity.WorkflowVectorGroup,
	embeddingModelConfig *config.UsingVectorModelInfo) (sandboxGroupID string, prodGroupID string,
	newWorkflowVectorGroups []entity.WorkflowVectorGroup) {
	newWorkflowVectorGroups = make([]entity.WorkflowVectorGroup, 0)
	sandboxGroupID, prodGroupID = vdao.GetWorkFlowVectorGroupIDStr(ctx, appID, entity.SaveWorkflowType,
		embeddingModelConfig.Biz, embeddingModelConfig.GroupSuffix)
	for _, vectorGroupInfo := range oldWorkflowVectorGroups {
		if vectorGroupInfo.VectorGroupType == vdao.WorkflowSandboxGroupInfix {
			newWorkflowVectorGroups = append(newWorkflowVectorGroups, entity.WorkflowVectorGroup{
				VectorGroupID:     sandboxGroupID,
				VectorGroupType:   vdao.WorkflowSandboxGroupInfix,
				RobotID:           appID,
				SaveType:          entity.SaveWorkflowType,
				EmbeddingModeName: embeddingModelConfig.LatestEmbeddingModelName,
				UIN:               vectorGroupInfo.UIN,
				SubUIN:            vectorGroupInfo.SubUIN,
			})
		}
		if vectorGroupInfo.VectorGroupType == vdao.WorkflowProdGroupInfix {
			newWorkflowVectorGroups = append(newWorkflowVectorGroups, entity.WorkflowVectorGroup{
				VectorGroupID:     prodGroupID,
				VectorGroupType:   vdao.WorkflowProdGroupInfix, // 修正为正确的类型
				RobotID:           appID,
				SaveType:          entity.SaveWorkflowType,
				EmbeddingModeName: embeddingModelConfig.LatestEmbeddingModelName,
				UIN:               vectorGroupInfo.UIN,
				SubUIN:            vectorGroupInfo.SubUIN,
			})
		}
	}
	return
}

func (c *UpgradeWorkflowVectorScheduler) saveWorkflowToVector(ctx context.Context, db *gorm.DB,
	vdb vdao.Dao, appID string, embeddingGroupID string,
	wf *entity.Workflow, wfExams []*entity.WorkflowExample, info *config.UsingVectorModelInfo) error {
	// 渲染embedding内容模版
	name := strings.TrimSpace(wf.WorkflowName)
	desc := strings.TrimSpace(wf.WorkflowDesc)
	examStrs := make([]string, 0, info.WorkflowCombinationExampleNum)
	if len(wfExams) > 0 {
		// 按照UpdateTime进行排序（最新的在前）
		sort.Slice(wfExams, func(i, j int) bool {
			return wfExams[i].UpdateTime.After(wfExams[j].UpdateTime)
		})
		// 安全地获取指定数量的示例，避免数组越界
		endIndex := info.WorkflowCombinationExampleNum
		if endIndex > len(wfExams) {
			endIndex = len(wfExams)
		}
		for _, v := range wfExams[0:endIndex] {
			if v != nil {
				examStrs = append(examStrs, v.Example)
			}
		}
	}
	embeddingRenderWorkflow := &entity.EmbeddingRenderWorkflow{
		WorkflowName:        name,
		WorkflowDescription: desc,
		WorkflowExampleList: examStrs,
	}
	wfNameTemplateStr, err := util.Render(ctx, info.WorkflowNameTemplate, embeddingRenderWorkflow)
	if err != nil {
		log.WarnContextf(ctx, "%s|getWorkflowNameTemplate|err:%+v", c.logPrefix, err)
		return err
	}
	wfExamplesDescTemplateStr := ""
	if len(strings.TrimSpace(info.WorkflowCombinationTemplate)) > 0 {
		wfExamplesDescTemplateStr, err = util.Render(ctx, info.WorkflowCombinationTemplate, embeddingRenderWorkflow)
		if err != nil {
			log.WarnContextf(ctx, "%s|getWorkflowCombinationTemplate|err:%+v", c.logPrefix, err)
			return err
		}
	}
	// 构造embedding提取相关配置
	workflowVectorOrg := &entity.WorkflowVectorOrg{
		WorkflowID:                 wf.WorkflowID,
		WorkflowName:               wf.WorkflowName,
		FlowNameExamsDescID:        fmt.Sprintf(entity.FlowNameExamsDescID, wf.WorkflowID),
		WorkflowNameVectorOrg:      wfNameTemplateStr,
		WorkflowDesc:               wf.WorkflowDesc,
		WorkflowState:              wf.WorkflowState,
		RobotId:                    wf.RobotId,
		ReleaseStatus:              wf.ReleaseStatus,
		IsEnable:                   wf.IsEnable,
		FlowNameExamsDescVectorOrg: wfExamplesDescTemplateStr,
		Action:                     entity.ActionInsert,
	}
	appInfo := &vector_db_manager.AppInfo{
		Biz:    info.Biz,
		AppKey: appID,
		Secret: info.Secret,
	}
	dataList := make([]*vector_db_manager.AddVectorReq_Index, 0)
	// 工作流名称embedding
	workflowEmbed, err := vdb.GetWorkflowEmbedding(ctx, db, workflowVectorOrg, appInfo, info.LatestEmbeddingModelName)
	if err != nil {
		return err
	}
	fieldValueWorkflow := &vector_db_manager.FieldValue{
		FieldName:        vdao.FieldWorkflowId,
		FieldType:        vector_db_manager.FieldValue_STRING,
		FieldValueString: wf.WorkflowID,
	}
	fieldValueEnable := &vector_db_manager.FieldValue{
		FieldName:        vdao.FieldWorkflowEnable,
		FieldType:        vector_db_manager.FieldValue_UINT64,
		FieldValueUint64: vdao.GetWfVectorEnableByFlowState(wf.WorkflowState, wf.IsEnable), // 1:启用，0：禁用
	}
	dataList = append(dataList, &vector_db_manager.AddVectorReq_Index{
		Id:        wf.WorkflowID,
		Embedding: workflowEmbed[wf.WorkflowID],
		AttributeFields: &vector_db_manager.AttributeFields{
			Fields: []*vector_db_manager.FieldValue{fieldValueWorkflow, fieldValueEnable},
		},
		EntityId: wf.WorkflowID,
	})
	// 工作流名称、示例问法、工作流描述组合embedding
	if workflowVectorOrg.FlowNameExamsDescVectorOrg != "" {
		workflowNameExamsDescEmbed, err := vdb.GetWorkflowExamDescEmbedding(ctx, db, workflowVectorOrg,
			appInfo, info.LatestEmbeddingModelName)
		if err != nil {
			return err
		}
		if workflowNameExamsDescEmbed != nil {
			dataList = append(dataList, &vector_db_manager.AddVectorReq_Index{
				Id:        workflowVectorOrg.FlowNameExamsDescID,
				Embedding: workflowNameExamsDescEmbed[workflowVectorOrg.FlowNameExamsDescID],
				AttributeFields: &vector_db_manager.AttributeFields{
					Fields: []*vector_db_manager.FieldValue{fieldValueWorkflow, fieldValueEnable},
				},
				EntityId: workflowVectorOrg.FlowNameExamsDescID, // 私有化弃用
			})
		}
	}
	// 示例问法embedding
	if len(wfExams) > 0 {
		wfExamIDs := make([]string, 0)
		wfExamMap := make(map[string]string)
		examVectorOrgs := make([]*entity.WorkflowExampleVectorOrg, 0)
		for _, exam := range wfExams {
			wfExamIDs = append(wfExamIDs, exam.ExampleID)
			wfExampleTemplateStr, err := util.Render(ctx, info.WorkflowExampleTemplate, exam)
			if err != nil {
				log.WarnContextf(ctx, "%s|getWorkflowExampleTemplate|err:%+v", c.logPrefix, err)
				return err
			}
			wfExamMap[exam.ExampleID] = wfExampleTemplateStr
			examVectorOrgs = append(examVectorOrgs, &entity.WorkflowExampleVectorOrg{
				FlowID:           exam.FlowID,
				ExampleID:        exam.ExampleID,
				Example:          exam.Example,
				ExampleVectorOrg: wfExampleTemplateStr,
				RobotId:          exam.RobotId,
			})
		}
		examplesEmbed, err := vdb.GetWorkflowExampleEmbedding(ctx, db, examVectorOrgs,
			appInfo, info.LatestEmbeddingModelName)
		if err != nil {
			return err
		}
		for _, examIDs := range types.SplitStringSlice(wfExamIDs, info.OperationMaxIDs-2) {
			for _, wfExamID := range examIDs {
				fieldValueExample := &vector_db_manager.FieldValue{
					FieldName:        vdao.FieldExampleId,
					FieldType:        vector_db_manager.FieldValue_STRING,
					FieldValueString: wfExamID,
				}
				fieldValueExampleOrg := &vector_db_manager.FieldValue{
					FieldName:        vdao.FieldExampleValue,
					FieldType:        vector_db_manager.FieldValue_STRING,
					FieldValueString: wfExamMap[wfExamID], // 示例问法原文
				}
				dataList = append(dataList, &vector_db_manager.AddVectorReq_Index{
					Id:        wfExamID,                // 示例问法ID
					Embedding: examplesEmbed[wfExamID], // 示例问法向量化数据
					AttributeFields: &vector_db_manager.AttributeFields{
						Fields: []*vector_db_manager.FieldValue{fieldValueWorkflow,
							fieldValueEnable, fieldValueExample, fieldValueExampleOrg},
					},
					EntityId: wf.WorkflowID,
				})
			}
		}
	}
	reqID := fmt.Sprintf("wf:embed:upgrade:%s", appID)
	if err = vdb.AddVectorByBatch(ctx, dataList, appInfo, reqID, embeddingGroupID); err != nil {
		log.ErrorContextf(ctx, "%s|AddVectorByBatch|err:%v", c.logPrefix, err)
		return err
	}
	return nil
}

func (c *UpgradeWorkflowVectorScheduler) deleteOldWorkflowVectorGroup(ctx context.Context, appID string,
	oldVectorGroupIDs []string, biz string, secret string) error {
	proxy := vector_db_manager.NewVectorObjClientProxy()
	// 删除旧的向量库分组
	var errs []error
	for _, oldVectorGroupID := range oldVectorGroupIDs {
		appInfo := &vector_db_manager.AppInfo{
			Biz:    biz,
			AppKey: appID,
			Secret: secret,
		}
		reqID := fmt.Sprintf("wf:embed:upgrade:%s", appID)
		req := &vector_db_manager.DeleteGroupReq{
			RequestId: reqID,
			GroupId:   oldVectorGroupID,
			AppInfo:   appInfo,
		}
		rsp, err := proxy.DeleteGroup(ctx, req)
		if err != nil || rsp == nil || (rsp.Code != 0 && rsp.Code != 71014 && rsp.Code != 71016) {
			err = fmt.Errorf("req:%v,rsp:%v,err:%v", utils.ToJsonString(req), utils.ToJsonString(rsp), utils.ToJsonString(err))
			log.WarnContextf(ctx, "%s|invoke.DeleteGroup|%v", c.logPrefix, err)
			errs = append(errs, err)
			continue // 继续处理下一个分组
		}
	}
	if len(errs) > 0 {
		return fmt.Errorf("failed to delete groups: %w", errors.Join(errs...))
	}
	return nil
}
