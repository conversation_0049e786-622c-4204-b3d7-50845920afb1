package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	workflowLogic "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/workflow"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/lock"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// CheckPendingWorkflowRun 兜底检查UIN下是否存在排队中的工作流运行实例
func CheckPendingWorkflowRun() {
	ctx := trpc.BackgroundContext()
	// 只有一个实例做定时兜底的操作，抢不到锁就退出定时任务
	instanceIP := trpc.GlobalConfig().Global.LocalIP
	instancePID := os.Getpid()
	instanceKey := fmt.Sprintf("%s:%d", instanceIP, instancePID)
	lockKey := entity.GetWorkflowRunFallbackTimerLockKey()
	locker := lock.NewDefaultLocker(lockKey, instanceKey, database.GetRedis())
	locked, err := locker.Lock(ctx, false)
	if err != nil {
		log.WarnContextf(ctx, "CheckPendingWorkflowRun|Lock|err:%v", err)
		return
	}
	if !locked {
		log.WarnContextf(ctx, "CheckPendingWorkflowRun|Lock|lock has been acquired by other instances, skip timer")
		return
	}
	defer func() {
		if err := locker.UnLock(ctx); err != nil {
			log.WarnContextf(ctx, "CheckPendingWorkflowRun|UnLock|err:%v", err)
		}
	}()
	// 默认5分钟执行一次兜底逻辑
	fallbackTimerInterval := 5 * 60
	if config.GetMainConfig().AsyncWorkflowConfig.FallbackTimerInterval > 0 {
		fallbackTimerInterval = config.GetMainConfig().AsyncWorkflowConfig.FallbackTimerInterval
	}
	fallbackTicker := time.NewTicker(time.Duration(fallbackTimerInterval) * time.Second)
	log.Infof("CheckPendingWorkflowRun|start:%s|interval:%d", time.Now().String(), fallbackTimerInterval)
	go func() {
		defer fallbackTicker.Stop()
		for range fallbackTicker.C {
			log.Debugf("CheckPendingWorkflowRun|tick")
			checkPendingWorkflowRun(ctx)
		}
	}()
}

func checkPendingWorkflowRun(ctx context.Context) {
	// 获取需要重试的UIN
	uins, err := db.GetPendingWorkflowRunUINs(ctx)
	if err != nil {
		log.WarnContextf(ctx, "CheckPendingWorkflowRun|GetPendingWorkflowRunUINs|err:%+v", err)
		return
	}
	log.Infof("CheckPendingWorkflowRun|totalUINs:%d", len(uins))
	for _, uin := range uins {
		// 有UIN粒度的定时任务存在，兜底仅需要同步至 redis 即可
		if _, exist := db.IsWorkflowRunRetryUINExist(ctx, uin); exist {
			// redis中已有，不需要重复添加
			continue
		}
		// 保存需要重试的 主账号ID 到 redis ，用于后续定时任务重试
		if err := db.SaveWorkflowRunRetryUIN(ctx, uin, time.Now()); err != nil {
			log.WarnContextf(ctx, "CheckPendingWorkflowRun|SaveWorkflowRunRetryUIN|UIN:%s|err:%+v", uin, err)
			continue
		}
	}
}

// RetryPendingWorkflowRun 按UIN粒度的定时重试提交排队中的工作流运行实例
func RetryPendingWorkflowRun() {
	ctx := trpc.BackgroundContext()
	// 只有一个实例做定时重试的操作，抢不到锁就退出定时任务
	instanceIP := trpc.GlobalConfig().Global.LocalIP
	instancePID := os.Getpid()
	instanceKey := fmt.Sprintf("%s:%d", instanceIP, instancePID)
	lockKey := entity.GetWorkflowRunRetryTimerLockKey()
	locker := lock.NewDefaultLocker(lockKey, instanceKey, database.GetRedis())
	locked, err := locker.Lock(ctx, false)
	if err != nil {
		log.WarnContextf(ctx, "RetryPendingWorkflowRun|Lock|err:%v", err)
		return
	}
	if !locked {
		log.WarnContextf(ctx, "RetryPendingWorkflowRun|Lock|lock has been acquired by other instances, skip timer")
		return
	}
	defer func() {
		if err := locker.UnLock(ctx); err != nil {
			log.WarnContextf(ctx, "RetryPendingWorkflowRun|UnLock|err:%v", err)
		}
	}()
	// 默认10秒钟进行一次重试逻辑
	retryTimerInterval := 10
	if config.GetMainConfig().AsyncWorkflowConfig.RetryTimerInterval > 0 {
		retryTimerInterval = config.GetMainConfig().AsyncWorkflowConfig.RetryTimerInterval
	}
	retryTicker := time.NewTicker(time.Duration(retryTimerInterval) * time.Second)
	log.Infof("RetryPendingWorkflowRun|start:%s|interval:%d", time.Now().String(), retryTimerInterval)
	go func() {
		defer retryTicker.Stop()
		for range retryTicker.C {
			log.Debugf("RetryPendingWorkflowRun|tick")
			retryPendingWorkflowRun(ctx)
		}
	}()
}

func retryPendingWorkflowRun(ctx context.Context) {
	// 获取需要重试的UIN
	uins, err := db.GetWorkflowRunRetryUINs(ctx)
	if err != nil {
		log.WarnContextf(ctx, "RetryPendingWorkflowRun|GetWorkflowRunRetryUINs|err:%+v", err)
		return
	}
	log.Infof("RetryPendingWorkflowRun|total:%d", len(uins))
	for _, uin := range uins {
		retryTime, exist := db.IsWorkflowRunRetryUINExist(ctx, uin)
		if !exist {
			continue
		}
		// 获取排队状态的工作流运行实例
		workflowRuns, err := db.GetPendingWorkflowRunsByUIN(ctx, uin)
		if err != nil {
			log.WarnContextf(ctx, "RetryPendingWorkflowRun|GetPendingWorkflowRunsByUIN|err:%+v", err)
			continue
		}
		log.Infof("RetryPendingWorkflowRun|UIN:%s|total:%d", uin, len(workflowRuns))
		for _, workflowRun := range workflowRuns {
			dmRunEnv := KEP_WF_DM.RunEnvType_SANDBOX
			if workflowRun.RunEnv == entity.RunEnvProduct {
				dmRunEnv = KEP_WF_DM.RunEnvType_PRODUCT
			}
			customVariables := make([]*KEP_WF.CustomVariable, 0)
			customVariableMap := make(map[string]string, len(customVariables))
			for _, v := range customVariables {
				customVariableMap[v.GetName()] = v.GetValue()
			}
			if workflowRun.CustomVariables != "" {
				err := json.Unmarshal([]byte(workflowRun.CustomVariables), &customVariables)
				if err != nil {
					log.WarnContextf(ctx, "RetryPendingWorkflowRun|UIN:%s|json.Unmarshal|err:%+v",
						uin, err)
					continue
				}
			}
			// 重新提交启动工作流运行实例，如果执行失败，UIN对应的重试时间会被更新
			workflowLogic.AsyncStartWorkflowRun(ctx, workflowRun, &KEP_WF_DM.StartWorkflowRunRequest{
				WorkflowRunID:   workflowRun.WorkflowRunID,
				RunEnv:          dmRunEnv,
				WorkflowID:      workflowRun.WorkflowID,
				AppID:           workflowRun.AppID,
				Query:           workflowRun.Query,
				MainModelName:   workflowRun.MainModelName,
				CustomVariables: customVariableMap,
			}, "RetryPendingWorkflowRun")
		}
		// 再次获取重试时间，如果全部都执行成功，即重试时间没有更新，则移除
		nextRetryTime, exist := db.IsWorkflowRunRetryUINExist(ctx, uin)
		if exist && nextRetryTime.Equal(retryTime) {
			if err := db.RemoveWorkflowRunRetryUIN(ctx, uin); err != nil {
				log.WarnContextf(ctx, "CheckPendingWorkflowRunByUIN|RemoveWorkflowRunRetryUIN|UIN:%s|err:%+v", uin, err)
				continue
			}
		}
	}
}
