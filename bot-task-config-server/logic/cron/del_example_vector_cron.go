// bot-task-config-server
//
// @(#)del_example_vector_corn.go  星期二, 十月 29, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package cron

import (
	"context"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/go-comm/trpc-cloud-api/cloudparam"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
	"github.com/robfig/cron"
)

// DelExampleVectorJob 查询发布状态的job
type DelExampleVectorJob struct {
	ctx context.Context
}

// StartDelExampleVectorJob 刷示例问法
func StartDelExampleVectorJob() {
	log.Info("StartDelExampleVectorJob|disable", config.GetMainConfig().TaskFlowConfig.DelVectorFromDelExample.Enable)
	if !config.GetMainConfig().TaskFlowConfig.DelVectorFromDelExample.Enable {
		log.Info("StartDelExampleVectorJob|disable")
		return
	}

	c := cron.New()
	spec := config.GetMainConfig().TaskFlowConfig.DelVectorFromDelExample.Spec
	if len(spec) == 0 {
		spec = "0 */2 * * * *"
	}
	log.Infof("StartDelExampleVectorJob|spec:%s", spec)
	err := c.AddJob(spec, &DelExampleVectorJob{})
	if err != nil {
		log.Errorf("StartDelExampleVectorJob|err", err)
		return
	}
	go c.Start()
}

// Run ...
func (a *DelExampleVectorJob) Run() {

	log.Infof("---------------- [StartDelExampleVectorJob] ------------")
	var examples []entity.IntentCorpus
	var err error
	reqID := idgenerator.NewUUID()
	a.ctx = cloudparam.NewContext(trpc.BackgroundContext(), cloudparam.BaseParam{
		RequestId: reqID,
	})
	log.WithContextFields(a.ctx,
		cloudparam.RequestId, reqID,
		"upgrade", "refresh del example to vector",
	)
	util.WithRequestID(a.ctx, reqID)

	// 全量
	if config.GetMainConfig().TaskFlowConfig.DelVectorFromDelExample.RefreshAll {
		examples, err = db.GetDeleteExamples(a.ctx)
		if err != nil {
			log.ErrorContextf(a.ctx, "StartDelExampleVectorJob|err:%v", err)
			return
		}
		a.RunExamples(a.ctx, examples)
	}

	// 只刷白名单
	robotIds := config.GetMainConfig().TaskFlowConfig.DelVectorFromDelExample.WhiteRobotIdsList
	if len(robotIds) > 0 {
		examples, err = db.GetDeleteExamplesByRobotId(a.ctx, robotIds)
		if err != nil {
			log.ErrorContextf(a.ctx, "StartDelExampleVectorJob|err:%v", err)
			return
		}
		a.RunExamples(a.ctx, examples)
	}

}

// RunExamples ...
func (a *DelExampleVectorJob) RunExamples(ctx context.Context, examples []entity.IntentCorpus) {
	var err error
	sid := util.RequestID(ctx)
	eIds := make(map[string][]string, 0)
	prodIds := make(map[string][]string, 0)
	mm := make(map[string]struct{}, 0)
	robotIds := make([]string, 0)

	log.Infof("StartDelExampleVectorJob|examples:%+v", examples)
	//获取全量的 示例问法Ids
	for _, v := range examples {
		if _, ok := mm[v.CorpusID]; !ok {
			robotIds = append(robotIds, v.RobotId)
			eIds[v.RobotId] = append(eIds[v.RobotId], v.CorpusID)
			mm[v.CorpusID] = struct{}{}
			// 获取发布且删除的示例问法
			if v.ReleaseStatus == entity.ReleaseStatusPublished {
				prodIds[v.RobotId] = append(prodIds[v.RobotId], v.CorpusID)
			}
		}
	}

	// 删除向量操作
	vdb := vdao.NewDao()
	for _, id := range robotIds {
		appInfo := &vector_db_manager.AppInfo{
			Biz:    config.GetMainConfig().VectorGroup.Biz,
			AppKey: id,
			Secret: config.GetMainConfig().VectorGroup.Secret,
		}
		sandboxGroupID := fmt.Sprintf("%s-%s-%s",
			config.GetMainConfig().VectorGroup.Biz, "sandbox", id)

		prodGroupID := fmt.Sprintf("%s-%s-%s",
			config.GetMainConfig().VectorGroup.Biz, "prod", id)

		if len(eIds[id]) > 0 {
			err = vdb.DeleteVectors(ctx, sid, sandboxGroupID, appInfo, eIds[id])
			if err != nil && !strings.Contains(err.Error(), "group not exist") {
				log.ErrorContextf(ctx, "DeleteVectors sandbox fail sandboxGroupID:%+s|eIds:%+v|err:%+v",
					sandboxGroupID, eIds[id], err)
			}
		}

		if len(prodIds[id]) > 0 {
			err = vdb.DeleteVectors(ctx, sid, prodGroupID, appInfo, prodIds[id])
			if err != nil && !strings.Contains(err.Error(), "group not exist") {
				log.ErrorContextf(ctx, "DeleteVectors prod fail prodGroupID:%+s|prodIds:%+v|err:%+v",
					prodGroupID, prodIds[id], err)
			}
		}
	}

}
