// bot-task-config-server
//
// @(#)parse.go  星期三, 四月 16, 2025
// Copyright(c) 2025, mikeljiang@Tencent. All rights reserved.

package types

import (
	"context"
	"encoding/json"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

// TypeParseValid 校验字符串是否是对应类型
func TypeParseValid(ctx context.Context, valueJson, valueType string) bool {
	// 空值不做任何校验
	if valueJson == "" {
		return true
	}
	switch valueType {
	case KEP_WF.TypeEnum_STRING.String():
		return true
	case KEP_WF.TypeEnum_INT.String():
		_, err := strconv.Atoi(valueJson)
		if err != nil {
			log.WarnContextf(ctx, " valueType:%s,valueJson:%s", valueType, valueJson)
			return false
		}
		return true
	case KEP_WF.TypeEnum_FLOAT.String():
		_, err := strconv.ParseFloat(valueJson, 64)
		if err != nil {
			log.WarnContextf(ctx, " valueType:%s,valueJson:%s", valueType, valueJson)
			return false
		}
		return true
	case KEP_WF.TypeEnum_BOOL.String():
		_, err := strconv.ParseBool(valueJson)
		if err != nil {
			log.WarnContextf(ctx, " valueType:%s,valueJson:%s", valueType, valueJson)
			return false
		}
		return true
	case KEP_WF.TypeEnum_OBJECT.String():
		var obj map[string]interface{}
		err := json.Unmarshal([]byte(valueJson), &obj)
		if err != nil {
			log.WarnContextf(ctx, " valueType:%s,valueJson:%s", valueType, valueJson)
			return false
		}
		return true
	case KEP_WF.TypeEnum_ARRAY_STRING.String():
		var arr []string
		err := json.Unmarshal([]byte(valueJson), &arr)
		if err != nil {
			log.WarnContextf(ctx, " valueType:%s,valueJson:%s", valueType, valueJson)
			return false
		}
		return true
	case KEP_WF.TypeEnum_ARRAY_INT.String():
		var arrInt []int
		err := json.Unmarshal([]byte(valueJson), &arrInt)
		if err != nil {
			log.WarnContextf(ctx, " valueType:%s,valueJson:%s", valueType, valueJson)
			return false
		}
		return true
	case KEP_WF.TypeEnum_ARRAY_FLOAT.String():
		var arrFloat []float64
		err := json.Unmarshal([]byte(valueJson), &arrFloat)
		if err != nil {
			log.WarnContextf(ctx, " valueType:%s,valueJson:%s", valueType, valueJson)
			return false
		}
		return true
	case KEP_WF.TypeEnum_ARRAY_BOOL.String():
		var arrBool []bool
		err := json.Unmarshal([]byte(valueJson), &arrBool)
		if err != nil {
			log.WarnContextf(ctx, " valueType:%s,valueJson:%s", valueType, valueJson)
			return false
		}
		return true
	case KEP_WF.TypeEnum_ARRAY_OBJECT.String():
		var arrArrayObject []map[string]interface{}
		err := json.Unmarshal([]byte(valueJson), &arrArrayObject)
		if err != nil {
			log.WarnContextf(ctx, " valueType:%s,valueJson:%s", valueType, valueJson)
			return false
		}
		return true
	default:
		return true
	}
}
