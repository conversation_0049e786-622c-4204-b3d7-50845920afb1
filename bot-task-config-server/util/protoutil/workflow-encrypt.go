package protoutil

import (
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/security"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

// EncryptWorkflowJson 加密工作流中的密钥
func EncryptWorkflowJson(workflowJson string) (string, error) {
	logPrefix := "EncryptWorkflowJson"
	if len(workflowJson) == 0 {
		// 创建任务流程时没有画布信息，且多处用到了这里；暂时不改
		return workflowJson, nil
	}
	workflow, err := JsonToWorkflow(workflowJson)
	if err != nil {
		log.Errorf("%s|JsonToWorkflow|err:%s", logPrefix, err.Error())
		return "", err
	}
	if workflow == nil {
		return "", fmt.Errorf("workflow is nil")
	}
	for _, node := range workflow.Nodes {
		if node == nil {
			continue
		}
		switch node.GetNodeType() {
		case KEP_WF.NodeType_MQ:
			err = encryptMQNode(logPrefix, node.GetMQNodeData())
			if err != nil {
				log.Errorf("%s|encryptMQNode|err:%s", logPrefix, err.Error())
				return "", err
			}
		}
	}
	encryptedJson, err := WorkflowToJson(workflow)
	if err != nil {
		log.Errorf("%s|WorkflowToJson|err:%s", logPrefix, err.Error())
		return "", err
	}
	return encryptedJson, nil
}

// DecryptWorkflowJson 解密工作流中的密钥
func DecryptWorkflowJson(workflowJson string) (string, error) {
	logPrefix := "DecryptWorkflowJson"
	if len(workflowJson) == 0 {
		// 创建任务流程时没有画布信息，且多处用到了这里；暂时不改
		return workflowJson, nil
	}
	workflow, err := JsonToWorkflow(workflowJson)
	if err != nil {
		log.Errorf("%s|JsonToWorkflow|err:%s", logPrefix, err.Error())
		return "", err
	}
	if workflow == nil {
		return "", fmt.Errorf("workflow is nil")
	}
	for _, node := range workflow.Nodes {
		if node == nil {
			continue
		}
		switch node.GetNodeType() {
		case KEP_WF.NodeType_MQ:
			err = decryptMQNode(logPrefix, node.GetMQNodeData())
			if err != nil {
				log.Errorf("%s|decryptMQNode|err:%s", logPrefix, err.Error())
				return "", err
			}
		}
	}
	decryptedJson, err := WorkflowToJson(workflow)
	if err != nil {
		log.Errorf("%s|WorkflowToJson|err:%s", logPrefix, err.Error())
		return "", err
	}
	return decryptedJson, nil
}

func encryptMQNode(logPrefix string, nodeData *KEP_WF.MQNodeData) error {
	prefix := fmt.Sprintf("%s|encryptMQNode", logPrefix)
	if nodeData == nil {
		return nil
	}
	var err error
	switch nodeData.GetMQType() {
	case KEP_WF.MQNodeData_KAFKA:
		if nodeData.GetKafkaOptions() != nil {
			err = encryptInput(prefix, nodeData.GetKafkaOptions().GetPassword())
		}
	case KEP_WF.MQNodeData_ROCKETMQ:
		if nodeData.GetRocketMQOptions() != nil {
			err = encryptInput(prefix, nodeData.GetRocketMQOptions().GetSecretKey())
		}
	}
	return err
}

func decryptMQNode(logPrefix string, nodeData *KEP_WF.MQNodeData) error {
	prefix := fmt.Sprintf("%s|decryptMQNode", logPrefix)
	if nodeData == nil {
		return nil
	}
	var err error
	switch nodeData.GetMQType() {
	case KEP_WF.MQNodeData_KAFKA:
		if nodeData.GetKafkaOptions() != nil {
			err = decryptInput(prefix, nodeData.GetKafkaOptions().GetPassword())
		}
	case KEP_WF.MQNodeData_ROCKETMQ:
		if nodeData.GetRocketMQOptions() != nil {
			err = decryptInput(prefix, nodeData.GetRocketMQOptions().GetSecretKey())
		}
	}
	return err
}

func encryptInput(logPrefix string, input *KEP_WF.Input) error {
	if input != nil {
		if input.GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT {
			userInput := input.GetUserInputValue()
			if userInput == nil {
				return nil
			}
			newValues := make([]string, 0)
			for _, value := range userInput.GetValues() {
				encrypted, err := security.AESEncryptBase64StringDefault(value)
				if err != nil {
					log.Errorf("encryptInput|AESEncryptBase64StringDefault|err:%s", logPrefix, err.Error())
					return err
				}
				newValues = append(newValues, encrypted)
			}
			userInput.Values = newValues
		}
	}
	return nil
}

func decryptInput(logPrefix string, input *KEP_WF.Input) error {
	if input != nil {
		if input.GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT {
			userInput := input.GetUserInputValue()
			if userInput == nil {
				return nil
			}
			newValues := make([]string, 0)
			for _, value := range userInput.GetValues() {
				decrypted, err := security.AESDecryptBase64StringDefault(value)
				if err != nil {
					log.Errorf("%s|decryptInput|AESDecryptBase64StringDefault|err:%s", logPrefix, err.Error())
					return err
				}
				newValues = append(newValues, decrypted)
			}
			userInput.Values = newValues
		}
	}
	return nil
}
