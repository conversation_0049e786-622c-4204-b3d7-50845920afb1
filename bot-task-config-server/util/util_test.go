// bot-task-config-server
//
// @(#)util_test.go  星期一, 五月 19, 2025
// Copyright(c) 2025, leoxxxu@Tencent. All rights reserved.

package util

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

func TestRender_Run(t *testing.T) {

	type WorkflowInfo struct {
		WorkflowName        string
		WorkflowExampleList []string
		WorkflowDescription string
	}

	workflow := WorkflowInfo{
		WorkflowName:        "测试",
		WorkflowExampleList: []string{"111", "222"},
		WorkflowDescription: "描述",
	}
	ctx := context.Background()
	tpl := "问题:{{ .WorkflowName }}"

	tplCom := `问题:{{ .WorkflowName }}
      {{- if .WorkflowExampleList }}
      {{- range $index, $q := $.WorkflowExampleList }}
      相似问:{{ $q }}
      {{- end }}
      {{- end }}
      答案:{{ .WorkflowDescription }}`
	workflowStr1, err := Render(ctx, tpl, workflow)
	if err != nil {
		log.ErrorContextf(ctx, "err:%+v", err)
	}
	log.InfoContextf(ctx, "%s", workflowStr1)

	workflowStr2, err := Render(ctx, tplCom, workflow)
	if err != nil {
		log.ErrorContextf(ctx, "err:%+v", err)
	}
	log.InfoContextf(ctx, "%s", workflowStr2)
}
