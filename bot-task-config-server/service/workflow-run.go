package service

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/workflow"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/asaskevich/govalidator"
)

// SaveAppDebugMode 保存应用的调试配置的调试模式
func (imp TaskConfigImp) SaveAppDebugMode(ctx context.Context, req *KEP_WF.SaveAppDebugModeReq) (
	*KEP_WF.SaveAppDebugModeRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "SaveAppDebugMode start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.SaveAppDebugMode(ctx, req)
		log.InfoContextf(ctx, "SaveAppDebugMode|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "SaveAppDebugMode|RESP|%s|%v", sid, err0)
	return nil, err0
}

// SaveAppDebugCustomVariables 保存应用的调试配置的AI参数的值
func (imp TaskConfigImp) SaveAppDebugCustomVariables(ctx context.Context, req *KEP_WF.SaveAppDebugCustomVariablesReq) (
	*KEP_WF.SaveAppDebugCustomVariablesRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "SaveAppDebugCustomVariables start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.SaveAppDebugCustomVariables(ctx, req)
		log.InfoContextf(ctx, "SaveAppDebugCustomVariables|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "SaveAppDebugCustomVariables|RESP|%s|%v", sid, err0)
	return nil, err0
}

// DescribeAppDebugConfig 查看应用的调试配置
func (imp TaskConfigImp) DescribeAppDebugConfig(ctx context.Context, req *KEP_WF.DescribeAppDebugConfigReq) (
	*KEP_WF.DescribeAppDebugConfigRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "DescribeAppDebugConfig start sid|req:|%s|%+v", sid, req)
	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		// 业务处理
		resp, err := workflow.DescribeAppDebugConfig(ctx, req)
		log.InfoContextf(ctx, "DescribeAppDebugConfig|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.WarnContextf(ctx, "DescribeAppDebugConfig|RESP|%s|%v", sid, err0)
	return nil, err0
}

// CreateWorkflowRun 创建工作流运行实例
func (imp TaskConfigImp) CreateWorkflowRun(ctx context.Context, req *KEP_WF.CreateWorkflowRunReq) (*KEP_WF.CreateWorkflowRunRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "CreateWorkflowRun|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		resp, err := workflow.CreateWorkflowRun(ctx, req)
		log.InfoContextf(ctx, "CreateWorkflowRun|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("CreateWorkflowRun|RESP|%s|%v", sid, err0)
	return nil, err0
}

// CanCreateWorkflowRun 判断是否能创建工作流运行实例
func (imp TaskConfigImp) CanCreateWorkflowRun(ctx context.Context, req *KEP_WF.CanCreateWorkflowRunReq) (*KEP_WF.CanCreateWorkflowRunRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "CanCreateWorkflowRun|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		resp, err := workflow.CanCreateWorkflowRun(ctx, req)
		log.InfoContextf(ctx, "CanCreateWorkflowRun|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("CanCreateWorkflowRun|RESP|%s|%v", sid, err0)
	return nil, err0
}

// ListWorkflowRuns 查询工作流运行实例列表
func (imp TaskConfigImp) ListWorkflowRuns(ctx context.Context, req *KEP_WF.ListWorkflowRunsReq) (*KEP_WF.ListWorkflowRunsRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "ListWorkflowRuns|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		resp, err := workflow.ListWorkflowRuns(ctx, req)
		log.InfoContextf(ctx, "ListWorkflowRuns|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("ListWorkflowRuns|RESP|%s|%v", sid, err0)
	return nil, err0
}

// DescribeWorkflowRun 查询工作流运行实例详情
func (imp TaskConfigImp) DescribeWorkflowRun(ctx context.Context, req *KEP_WF.DescribeWorkflowRunReq) (*KEP_WF.DescribeWorkflowRunRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "DescribeWorkflowRun|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		resp, err := workflow.DescribeWorkflowRun(ctx, req)
		log.InfoContextf(ctx, "DescribeWorkflowRun|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("DescribeWorkflowRun|RESP|%s|%v", sid, err0)
	return nil, err0
}

// DescribeNodeRun 查看工作流节点运行详情
func (imp TaskConfigImp) DescribeNodeRun(ctx context.Context, req *KEP_WF.DescribeNodeRunReq) (*KEP_WF.DescribeNodeRunRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "DescribeNodeRun|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		resp, err := workflow.DescribeNodeRun(ctx, req)
		log.InfoContextf(ctx, "DescribeNodeRun|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("DescribeNodeRun|RESP|%s|%v", sid, err0)
	return nil, err0
}

// StopWorkflowRun 停止工作流运行实例
func (imp TaskConfigImp) StopWorkflowRun(ctx context.Context, req *KEP_WF.StopWorkflowRunReq) (*KEP_WF.StopWorkflowRunRsp, error) {
	sid := util.RequestID(ctx)
	log.InfoContextf(ctx, "StopWorkflowRun|REQ|%s|%v", sid, req)

	// 请求参数校验
	ok, gve := govalidator.ValidateStruct(req)
	if ok {
		resp, err := workflow.StopWorkflowRun(ctx, req)
		log.InfoContextf(ctx, "StopWorkflowRun|RESP|%s|%v|%v", sid, resp, err)
		return resp, err
	}

	err0 := errors.BadRequestError(gve.Error())
	log.Warnf("StopWorkflowRun|RESP|%s|%v", sid, err0)
	return nil, err0
}
