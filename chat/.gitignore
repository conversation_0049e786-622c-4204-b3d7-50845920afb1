# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Editor files
.vscode/
.idea/

# Logs
*.log

# Temp files
.DS_Store
*.temp
*.tmp

# Vendor
vendor/

# Project's binary
/cmd/cmd
/bin/chat
/chat/bin/*

# TRPC configuration
config/*.yaml
!config/*.template.yaml
chat/config/application.yaml
chat/config/*.yaml

gotest/*.out