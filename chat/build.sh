#!/bin/sh
set -e

platform=$1
version=$2 # 私有化版本号
# Mac 系统名
OS_DARWIN="Darwin"
# 记录当前系统名
WORKING_OS="$(uname -s)"
# 兼容 Linux 和 Mac 命令
sed0="sed"
if [ "$OS_DARWIN" == "$WORKING_OS" ]; then
  sed0="gsed"
fi
apps='chat'


buildVersion () {
  app=$1
  tag=$2

  if [ ! -f "$app/bin/version.txt" ]; then
      echo "$app/bin/version.txt no exists "
      exit
  fi
  echo "recover"
  # 还原
  "$sed0" -i "s/buildVersion *= *\"[^\"]*\"/buildVersion = \"服务编译版本号, 勿动~~\"/g" ./$app/bin/version.txt
  PATCH_USER="$(git config --get user.name)"
  if [ -z "$PATCH_USER" ]; then
  	PATCH_USER="$(USER)"
  fi
  COMMIT_VERSION="$(git log -1 --pretty=format:"%H" | cut -c 1-8)"
  # 二进制版本
  BINARY_VERSION="${PATCH_USER}-$tag-$COMMIT_VERSION@$(date +'%Y.%m.%d.%H.%M.%S')"
  echo "BINARY_VERSION=$BINARY_VERSION"
  echo "current_time: $current_time"
  "$sed0" -i "s/buildVersion *= *\"[^\"]*\"/buildVersion = \"${BINARY_VERSION}\"/g" ./$app/bin/version.txt
}

build_and_push () {
  echo "image name: $3"
  if [ "$platform" == "arm" ]; then
    docker buildx build --push --platform=$1 --build-arg app=$2 --build-arg GOPRIVATE=$GOPRIVATE --build-arg GOPROXY=$GOPROXY --build-arg GOSUMDB=$GOSUMDB -f Dockerfile-arm -t $3 .
  else
    docker buildx build --push --platform=$1 --build-arg app=$2 --build-arg GOPRIVATE=$GOPRIVATE --build-arg GOPROXY=$GOPROXY --build-arg GOSUMDB=$GOSUMDB -f Dockerfile -t $3 .
  fi
}

build () {
    app=$1
    dockertag=$2
    platform=$3
    echo "building $app:$dockertag ..."
    buildVersion $app $dockertag
    if [ "$platform" == "amd" ]; then
        build_and_push linux/amd64 $app lmbke.tencentcloudcr.com/llm_customer/qbot.qbot.${app}_private:$dockertag
    elif [ "$platform" == "arm" ]; then
        build_and_push linux/arm64 $app lmbke.tencentcloudcr.com/llm_customer/qbot.qbot.${app}_private:$dockertag-aarch64
    elif [ "$platform" == "all" ]; then
        build_and_push linux/amd64 $app lmbke.tencentcloudcr.com/llm_customer/qbot.qbot.${app}_private:$dockertag
        build_and_push linux/arm64 $app lmbke.tencentcloudcr.com/llm_customer/qbot.qbot.${app}_private:$dockertag-aarch64
    else
        echo "error platform: $3 "
    fi
    echo "recover"
    # 还原
    "$sed0" -i "s/buildVersion *= *\"[^\"]*\"/buildVersion = \"服务编译版本号, 勿动~~\"/g" ./$app/bin/version.txt
}

dockertag=""
if [ -z "$version" ]
then
    dockertag=$(date +'%Y%m%d')_$(git rev-parse --short HEAD)
else
      dockertag=$version"_"$(date +'%Y%m%d')_$(git rev-parse --short HEAD)
fi


IFS=',' read -ra values <<< "$apps"
for app in "${values[@]}"; do
    build $app $dockertag $platform
done

