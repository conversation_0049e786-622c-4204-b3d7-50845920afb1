FROM --platform=$BUILDPLATFORM golang:1.22.3-alpine3.18 as go-builder
ARG app
ARG GOPROXY
ARG GOSUMDB
ARG TARGETARCH
ARG TARGETOS

WORKDIR /build

COPY . ./

RUN apk add --no-cache git git-lfs && \
#    go install github.com/go-delve/delve/cmd/dlv@latest && \
#    cp /go/bin/dlv ./$app/bin/ && \
    go env -w GOPROXY=$GOPROXY && \
    go env -w GOPRIVATE="" && \
    go env -w GOSUMDB=$GOSUMDB && \
    go env -w GO111MODULE=on

RUN go mod tidy -C $app && \
    go mod verify -C $app && \
    GOOS=${TARGETOS} GOARCH=${TARGETARCH} go build -C $app -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=ignore" -o bin/$app cmd/main.go;

FROM arm64v8/alpine:3.18
ARG app
ENV APP_NAME=$app

WORKDIR /$app

COPY --from=go-builder /build/$app/bin/* ./

RUN apk add --no-cache tzdata strace curl tcpdump && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

ENTRYPOINT ["/bin/sh", "-c", "./$APP_NAME -conf ./config/trpc_go.yaml"]
