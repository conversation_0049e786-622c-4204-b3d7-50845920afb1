package helper

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"
)

var testStr = []string{
	"答案在此\n引用:【passage 123】【passage 456】【passage 789】",
	"引用:【passage 1】【passage 2】【passage 】",
	"个人所得税专项附加扣除信息的确认已经开始。纳税人可以享受的专项附加扣除包括子女教育、继续教育、大病医疗、住房贷款利息、住房租金、赡养老人等6类。如果您的情况有以下变化，需要修改专项附加扣除信息：1. 赡养老人、子女教育、住房贷款利息的扣除比例需要修改；2. 有老人在2021年去世，2022年不能再申请赡养老人专项附加扣除；3. 夫妻一方不再申请住房贷款利息专项附加扣除，在2022年由另一方申报；4. 房租和房贷需要替换扣除的情况，即2022年不再申报住房租金，改为申报住房贷款利息，或2022年不再申报住房贷款利息，改为申报住房租金。在填报专项附加扣除信息时，请注意以下易错点：1. 同一子女的子女教育扣除项目，父母双方的扣除比例应符合政策规定标准；2. 夫妻双方非婚前分别购买的住房，只能选择一方扣除住房贷款利息；3. 纳税人填报的赡养老人项目，共同扣除人的合计扣除金额要符合标准；4. 同一专项附加扣除项目，纳税人只能选择一处扣缴义务人扣除；5. 纳税人与其配偶主要工作城市相同的，只能由一方扣除住房租金；6. 纳税人与其配偶不能同时扣除住房租金和住房贷款利息支出；7. 纳税人应当确保填报子女、配偶、赡养老人身份信息准确；8. 纳税人填报职业资格继续教育的，应当在取得相关证书的当年享受扣除；9. 纳税人填报学历继续教育的，应当为中国境内接受的学历(学位)继续教育。",
	"混元答案1\n以上信息来源于【passage 1】和【passage 2】。",
	"混元答案2\n以上信息来源于【passage 1】。",
	"badecase1\n【引用passage 1】",
	"支持工业企业增加投资政策的联系电话为82632730。【passage 2】\n\n\n\n引用形式：在答案最后给出引用的passage，格式引用:【passage x】。",
	"具体的部门名称未在已知信息中提及。引用:【passage 1】",
	"test引用:【passage 1, passage 2, passage 3, passage 4, passage 5, passage 6】",
	"个投保人当年的激励总额不超过20万元，且获得省、市、区保险激励的总额不超过实际发生保费的90%。\n\n   引用：【passage 2】\n\n\n\n3. **知识产权质押融资**：\n\n",
}

/*
{"request_id":"b4f81719a9c76da8f4cd4c09623fea15:318fc7fa-1855-40da-b8bc-977a937d811d:9i6_20240705_234815_039_s0KBHuNb",
"message":{"role":1,"content":"根据提供的信息，无法直接得出云智能2024年的人力数量。但我们可以从相关部分进行推测：\n\n1. 腾讯云智能相关部门2023年平均在岗集团员工544人，云智员工152人。
\n2. 2024年预估人效126万/人，同比+43%。\n\n由于缺乏具体的2024年人力数量的数据，无法准确回答云智能2024年的人力数量。建议您查阅相关的详细报告或数据以获取准确信息。
\n\n以上信息来源于【passage 1】和【passage 2】。"},
"finished":true,"statistic_info":{"first_token_cost":2227,"total_cost":10767,"input_tokens":2018,"output_tokens":136,"total_tokens":2154},
"hy_paas_id":"a606366e-23de-4831-8d70-5642a098c7a6"},time cost:10.79553044s,ERR: <nil>
*/

func TestGetAll(t *testing.T) {
	t.Run("test get all reference", func(t *testing.T) {
		res := GetAllReference(testStr[0])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []string{"【passage 123】", "【passage 456】", "【passage 789】"}, res)

		res = GetAllReference(testStr[1])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []string{"【passage 1】", "【passage 2】"}, res)
	})
	t.Run("test get all sub match", func(t *testing.T) {
		res := GetAllSubMatch(0, testStr[0])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{123, 456, 789}, res)

		res = GetAllSubMatch(0, testStr[1])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1, 2}, res)

		res = GetAllSubMatch(0, testStr[2])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{}, res)

		res = GetAllSubMatch(0, testStr[3])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1, 2}, res)

		res = GetAllSubMatch(0, testStr[4])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1}, res)

		res = GetAllSubMatch(0, testStr[5])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1}, res)

		res = GetAllSubMatch(0, testStr[6])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{2}, res)

		res = GetAllSubMatch(0, testStr[7])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1}, res)

		res = GetAllSubMatch(0, testStr[8])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1, 2, 3, 4, 5, 6}, res)

		res = GetAllSubMatch(0, testStr[9])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{2}, res)
	})

	t.Run("test remove reference", func(t *testing.T) {
		res := RemoveReference(0, testStr[0])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, "答案在此\n", res)

		res = RemoveReference(0, testStr[1])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, "【passage 】", res)

		res = RemoveReference(0, testStr[3])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, "混元答案1", res)

		res = RemoveReference(0, testStr[4])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, "混元答案2", res)

		res = RemoveReference(0, testStr[5])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, "badecase1\n", res)

		res = RemoveReference(0, testStr[6])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, "支持工业企业增加投资政策的联系电话为82632730。\n\n\n\n", res)

		res = RemoveReference(0, testStr[7])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, "具体的部门名称未在已知信息中提及。", res)

		res = RemoveReference(0, testStr[8])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, "test", res)

		res = RemoveReference(0, testStr[9])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, "个投保人当年的激励总额不超过20万元，且获得省、市、区保险激励的总额不超过实际发生保费的90%。\n\n   \n\n\n\n3. **知识产权质押融资**：\n\n", res)
	})

}

var hunYuanSearchResults = []string{
	"截至**2024年7月28日[2,3,6](@ref)，中国代表团在2024年巴黎奥运会上已经斩获了**5金5银2铜**合计**12枚奖牌**[2](@ref)[3](@ref)。以下是部分中国代表团获得的金牌项目：\n\n- **黄雨婷和盛李豪**在射击项目上斩获首金[4](@ref)。\n- **安琦轩、李佳蔓、杨晓蕾**在射箭女子团体赛上获得银牌[7,8,9](@ref)\n - **张博恒、肖若腾、邹敬园、苏炜德、刘洋**在体操男子团体赛上获得银牌[5](@ref)[6](@ref)。",
}

func TestHunYuanSearch(t *testing.T) {
	t.Run("match reference", func(t *testing.T) {
		t.Logf("原始字符串:\n %v\n", hunYuanSearchResults[0])
		content, positions, indexes := MatchSearchResults(context.Background(), hunYuanSearchResults[0])
		t.Logf("替换后的字符串:\n %v\n 位置: %v\n 索引: %v\n", content, positions, indexes)
	})
}

var hunyuan = []string{
	"根据文档内容，关于成都市旅游业补贴“文旅消费新业态示范区”“文旅消费新业态示范项目”的咨询方式如下：\n\n市文广旅局市场管理处电话:61882997,负责星级酒店评定有关咨询工作；\n资源开发处电话:61884416,负责 A级林盘、“四改一提升”、主题旅游目的地评定有关咨询工作;\n文旅产业处电话:61882968,主要是服务和指导各区(市)县文旅行政管理部门做好政策咨询、项目申报等各项协调保障工作。\n\n申报审核过程中技术问题可咨询“天府蓉易享”平台技术人员(何老师:028-63911260;徐老师:18180949850)。\n\n引用的passage编号是：",
	"1. 根据《成都市2022年现代林业产业项目申报指南》《成都市2022年现代林业产业项目评定办法》的通知，评定原则包括公平、公正、公开评审原则（passage 1）。\n2. 评分标准包括项目具备基本条件、项目建设内容、资金筹措、现场核查情况和工作综合情况（passage 2）。\n3. 评定程序包括会议评审（得分占80%）、实地核查（得分占10%）和综合评分（得分占10%）（passage 4）。\n4. 项目绩效评价情况包括经济效益、社会效益、环境效益、带动农民增收等方面（passage 6）。\n5. 资金安排根据最终得分对产业项目进行排名，依据项目绩效、工程量、可行性等要素进行综合平衡（passage 7）。",
	"1. 根据《成都市温江区促进服务业高质量发展支持政策的实施细则》，支持对象包括工商注册、税收管辖及统计关系等均在温江区的市场主体。（passage 1）\n2. 根据《成都市温江区促进服务业高质量发展支持政策的实施细则》，支持对象包括具有独立法人资格、财务制度健全、实行独立核算的市场主体。（passage 2）\n3. 根据《成都市温江区促进服务业高质量发展支持政策的实施细则》，支持对象包括工商注册、税收管辖及统计关系等均在温江区，具有独立法人资格、财务制度健全、实行独立核算的企业；或执业许可登记、税收管辖在温江区,财务制度健全、实行独立核算的机构（含事业单位、社会组织、行业协会）。（passage 4）\n4. 根据《关于开展“2024-02〔2023〕2-1-01-示范性消费场景”项目申报的通知》，支持对象包括工商注册登记、税收解缴关系、统计关系均在温江区的企业。（passage 6）\n5. 根据《关于开展“2024-02〔2022〕21-03-02-01-支持金融人才集聚（金融人才奖励）”等项目申报的通知》，支持对象包括注册登记、税务、统计关系在温江区的企业或其核心管理团队。（passage 7）\n6. 根据《关于开展“2024-02〔2022〕21-03-02-01-支持金融人才集聚（金融人才奖励）”等项目申报的通知》，支持对象包括注册登记、税务、统计关系在温江区的企业或其核心管理团队；海外上市主体通过协议或股权安排实际控制的境内经营实体的注册登记、税务、统计关系在温江区的企业或其核心管理团队。（passage 9）\n7. 根据《项目申报指南》，政策咨询包括区财政局（区金融局）资本市场科和区政务服务中心产业政策服务科。（passage 10）",
	"根据文档内容，成都制造业高端人才奖励申报条件如下：\n(一)与所在单位签订了 3年(含)以上的劳动合同;\n(二)所在单位属市域内先进制造业重点产业链企业;\n(三)申报人从事该产业领域相关工作连续从业满 1年;\n(四)申报人经认定符合《成都市人才分类目录》A、B、C、D类的人才;\n(五)申报人上一年度缴纳个人所得税后年收入 40万元(含)以上;\n(六)申报人已足额缴纳了个人所得税;\n(七)申报人没有受到刑事处罚,或申报时刑事处罚已经执行完毕;\n(八)申报人未享受过市、区(市)县同类奖励。\n\n引用的passage编号：1",
	"1. 成都市制造业重点产业重点领域高端人才奖励申报书；\n2. 申请人身份证或者护照(正反面)；\n3. 成都市人才认定证书；\n4. 所在单位社保缴纳证明(申报人需至少提供2023年6月至2024年6月时间段内连续缴纳社保证明)；\n5. 所在单位提供的2023年度(1月12月)工资薪金、奖金发放说明(按照附件3说明填写签字盖章，并提供与薪酬发放说明每月金额相对应的银行流水)；\n6. 申报人2023年度(1月-12月)工资薪金、奖励收入个税完税证明；\n7. 所在单位的营业执照(正、副本)或多证合一证(正、副本)复印件(加盖所在单位鲜章)；\n8. 申报人与所在单位签订的劳动合同复印件(加盖所在单位鲜章)；\n9. 申报人突出的业绩(成果)、论文著作、授权专利或表彰奖励等证明材料；\n10. 申报承诺书。\n\n引用的passage编号：1",
	"根据文档内容，成都申报采购区内生物医药企业产品补贴的项目包括：\n2024-02〔2021〕8-16-02-关于采购区内生物医药企业产品的补贴\n2024-02〔2021〕8-07-02-特医保健销售奖励\n2024-02〔2021〕7-13-02-工业企业参会参展补贴\n2024-02〔2021〕7-12-02-企业采购“温江造”产品补贴\n2024-02〔2021〕8-27-02-百强奖励\n2024-02〔2021〕7-19-02-企业上云奖励\n2024-02〔2021〕7-19-01-工业服务平台奖励\n\n引用的passage编号为：",
	"1. 根据《成都市温江区推动先进制造业高质量发展的若干政策措施的实施细则（2022修订版）》，对于租用非关联第三方标准厂房且年地方经济发展实际贡献达到200元/平方米、300元/平方米的规模以上工业企业，分别给予100元/年/平方米、200元/年/平方米的租金补贴，补贴金额不超过实际价格。（passage 9）\n2. 根据《成都市温江区促进服务业高质量发展支持政策的实施细则》，对于入驻纳入区楼宇智慧服务平台统计的楼宇满一年的企业且租用面积达到200平方米及以上，申报年度经济贡献不低于1000元/平方米，且总量不低于50万元/年的企业，按照每月35元/平方米的标准补助，时间最长三年，支持金额不超过支持面积内的实际租金总额。（passage 2）\n3. 根据《成都市温江区租赁市场发展试点财政奖补资金使用管理实施细则》，对出租自持租赁住房的住房租赁企业，按自持租赁住房出租面积，每年按100元/平方米的标准进行经营奖补。（passage 3）\n4. 根据《中共成都市温江区委成都市温江区人民政府关于促进民营经济健康发展的实施意见》，鼓励人力资源服务机构为民营企业提供招用工服务，对人力资源服务机构新入驻温江设点执业并为区内企业提供上述服务的，对其在温租赁自用办公用房（200平方米以内），按租金实际价格给予最高10万元/年的房租补贴，补贴期限不超过3年。（passage 4）\n5. 根据《关于开展“2023-01 〔2023〕8-4-03-01-人才住房奖励支持”等项目申报的通知》，对于经温江区认定的A类人才，符合条件的可免费租住一套不超过200平方米的人才住房，入住期间所产生的租金、物业服务费用由房源业主或代管单位按年申报，租金的市场标准由房源业主或代管单位委托房地产评估机构评估确定。（passage 5）\n6. 根据《成都市温江区推动先进制造业高质量发展的若干政策措施》，对新引进落户天府商务服务区等指导区域的金融机构，对新购置自用办公用房的，按购房价格的8%一次性给予最高500万元补贴；对租用载体面积不超过800平方米的部分，连续三年按最高50元/平方米/月的标准给予租金补贴，补贴单价不超过实际租用单价。（passage 8）",
	"锦江区建筑企业提质增效扶持政策内容包括：\n\n1. **适用范围**：工商注册、税务登记在锦江区，并纳入国家统计网报企业库的，且具有《资质证书》和独立法人资格的施工企业。\n\n2. **政策内容**：\n- **强化建设项目推介**：鼓励区内总承包企业专业分包选择区内施工企业。\n- **鼓励企业上规入库**：达到上规入库标准，首次纳入国家统计网报企业库，且当年建筑业产值达到5000万元以上的资质等级以上的区内施工企业，给予5万元的一次性奖励。\n- **支持区外企业迁入**：总承包特级、一级资质的区外施工企业新迁入并入库锦江区后，其入库当年建筑业产值达到20亿元的，给予300万元的一次性奖励；第二年建筑业产值达到4亿元的，给予40万元的一次性奖励。\n- **支持企业提升资质**：区内施工企业新晋升（新办）为施工总承包特级、一级资质企业的施工企业，其资质晋升或新办的当年建筑业产值达到20亿元的，给予200万元的一次性奖励；第二年建筑业产值达到4亿元的，给予40万元的一次性奖励。\n- **支持企业壮大发展**：对区内年度建筑业产值首次达到0.5亿元、1亿元、5亿元、10亿元、30亿元、50亿元、100亿元、300亿元、500亿元、1000亿元的施工企业，且当年建筑业产值增幅超过锦江区目标增幅，分别对应给予0.5万元、5万元、10万元、20万元、60万元、100万元、200万元、400万元、600万元、1000万元的一次性奖励。\n- **支持企业入库**：区内施工企业入库当年建筑业产值达到0.2亿元、0.5亿元、1亿元、2亿元、5亿元、10亿元、20亿元、50亿元的，分别对应给予0.5万元、1万元、5万元、8万元、15万元、30万元、60万元、120万元的一次性奖励。\n- **支持企业“走出去”**：对年度区外建筑业产值达到1亿元、5亿元、10亿元、20亿元、50亿元、100亿元、300亿元的施工一级及以下资质的区内施工企业分别对应给予2万元、10万元、20万元、40万元、100万元、150万元、300万元的一次性奖励；对年度区外建筑业产值达到100亿元、300亿元、500亿元、1000亿元、2000亿元的施工总承包特级的区内施工企业分别对应给予100万元、200万元、300万元、400万元、500万元的一次性奖励。\n\n3. **附则**：\n- 申报时限为次年三月底前。\n- 获得财政扶持资金支持的企业需配合做好绩效评价工作。\n- 申报企业对数据的真实性负责。\n- 享受扶持的企业在锦江区实际经营期限原则上不低于10年或达到协议年限。\n- 同类性质政策企业可就高不就低选择申报，不可叠加享受。\n- 对企业晋升资质后5年内被撤销等情形，企业所获奖励资金须全额退回。\n\n**引用passage编号**：[1]",
	"1. 企业应为登记注册且纳税在大邑县的企业、事业单位、社会团体及其他组织机构。\n2. 企业需开设“蓉漂人才巴士”，并设立统一标识标记，用于企业各类人才的上下班通勤。\n3. “蓉漂人才巴士”车辆应为企业自有或租用车辆，配有司机，专用于运送企业人才，不得对外经营盈利。\n4. 车辆核定载客人数应在15座以上（含司机），原则上每车每日往返县外发车不多于2次（多于2次的按2次计费），每次开行应做好相关记录并存档。\n5. 运行线路起讫点一端为大邑县内，另一端为大邑县外（与已开通公交线路起讫点一致的，不纳入补贴范围）。\n6. 企业应根据实际出行需求人数，单独或联合周边企业开设“蓉漂人才巴士”。联合开设“蓉漂人才巴士”应出具联合开设证明材料，并协商确定一家企业为申报补贴的主体。\n7. 申报企业需在规定时间内报名，并提交相关资料至所在产业功能区（功能区外的企业报所在镇街），再报县交通运输局审核认定。\n8. 企业应在规定时间内统计汇总车辆开行记录和相关费用支出，出具相关证明材料，整理形成正式文本后，报送所在产业功能区或镇（街道）。\n9. 经逐级审定后，给予企业专项补贴，补贴金额为运送人才上下班产生车辆能源消耗费的50%（若为企业自有车辆）或租车费的50%（若为租用车辆），每家企业每年最高补贴10万元。\n10. 申报资料包括《大邑县“蓉漂人才巴士”补贴报名表》、登记注册地和纳税关系证明材料、“蓉漂人才巴士”车辆相关证明、驾驶员基本信息、企业无重大违法记录和不良信用记录的承诺函等。\n11. 年末结算时，企业需提交《大邑县“蓉漂人才巴士”补贴结算表》、“蓉漂人才巴士”开行记录、燃油费用（电动车用电费用）相关证明材料等。\n12. 补贴资金由县财政局按程序向县交通运输局划拨，再由县交通运输局按程序向各产业功能区或镇街划拨补贴资金，最后向企业兑现补助资金。\n参考条款：\n1. 《大邑县交通运输局关于印发《大邑县“蓉漂人才巴士”人才通勤保障实施细则》的通知》（passage 1）\n2. 《大邑县“蓉漂人才巴士”补贴报名表》（passage 2）\n3. 《大邑县“蓉漂人才巴士”补贴结算表》（passage 6）",
	"锦江区为支持软件企业上规入库，在房租方面的补贴政策如下：\n\n- **月度入库企业**：可按不超过2000平方米、30元/月/平方米的标准，或实际租金的60%，给予一次性不超过50万元的房租补贴。\n- **年度入库企业**：可按不超过1000平方米、30元/月/平方米的标准，或实际租金的40%，给予一次性不超过30万元的房租补贴。\n\n这些政策分别来源于以下两篇文档：\n\n1. 《锦江区经济和信息化局贯彻落实成都市锦江区关于推动“1+3+3”产业体系高质量发展的若干政策的实施细则》（passage 1）\n2. 《成都市锦江区关于推动“1+3+3”产业体系高质量发展的若干政策》（passage 2）",
	"金堂县的科技行业人才政策包括：\n- **支持科技成果转移转化**：对获得中国专利金奖、银奖、优秀奖等，并在县内落地转化的项目，给予专利权人不超过50万元一次性经费支持（passage 3）。\n- **支持科技孵化载体发展**：对县内新认定的国家级孵化器、省级孵化器等创新创业载体，分别给予运营机构不超过100万元、50万元、30万元的经费资助（passage 3）。\n- **支持关键核心技术攻关**：对获得国家立项的关键核心技术攻关项目并在县内落地转化产生效益的，按照所获国家专项支持资金的30%给予支持（passage 3）。\n\n邛崃市的科技行业人才政策包括：\n- **企业“育才奖”**：鼓励企业培育在职人员提升职业能力，对推荐人员成功入选国家海外高层次人才引进计划等的企业给予奖励（passage 2）。\n- **企业“伯乐奖”**：鼓励用人企业、第三方机构、行业专家等引荐人才来邛创新创业，对成功举荐高层次人才的企业给予一次性奖励（passage 5）。",
	"在成都温江，针对企业经营困难问题，政府采取了一系列措施来缓解企业的压力，具体包括：\n\n**防疫物资保障**\n- 加强防疫物资保供，鼓励企业采购并给予奖励。\n- 支持防疫物资生产，纳入战略储备采购名单。\n- 支持防疫项目攻关，给予研发补贴。\n- 支持防疫体系建设，提供复产复业补助。\n\n**降低经营成本**\n- 降低企业用能成本，提供电费补贴。\n- 减轻企业租金负担，减免租金和物业费。\n\n**财税金融支持**\n- 给予信贷支持，确保信贷规模，降低融资成本。\n- 鼓励社会资本投资，给予股权投资奖励。\n- 给予贴息支持，下调贷款利率。\n- 给予税收支持，落实税收优惠政策。\n\n**援企稳岗**\n- 降低用工成本，延期缴纳社保费、公积金。\n- 提供人力资源服务，搭建招聘平台。\n\n**企业服务**\n- 帮助企业拓展市场，提供交易奖励。\n- 提供疫情防控法律服务。\n- 提升企业服务质效，简化审批流程。\n\n**支持市场主体纾困**\n- 落实退税减税缓税政策。\n- 加大金融信贷惠企力度。\n- 降低企业闭环生产管理成本。\n- 加快推进企业复工复产。\n\n**低效工业用地提质增效**\n- 鼓励重组增效，提供并购奖励。\n- 鼓励工业上楼，提供厂房建设奖励。\n- 鼓励多投快建，给予项目投资奖励。\n- 鼓励产出增效，提供经济发展贡献奖励。\n\n**服务业高质量发展**\n- 支持对象包括温江区内的企业和机构。\n- 提供政策咨询服务。\n\n以上措施涵盖了财税减免、金融支持、人力资源服务、市场拓展等多个方面，旨在帮助企业渡过难关，保障经济的平稳运行。具体的实施细节和申请流程，企业可以联系相关部门获取详细信息。\n\n**引用passage编号：**\n1. 成都市温江区关于应对新型冠状病毒肺炎疫情缓解中小企业生产经营困难的16条政策措施\n2. 关于印发《成都市温江区关于支持市场主体纾困加快经济恢复的若干政策措施》的通知\n5. 成都市温江区关于加快促进低效工业用地企业提质增效的政策措施\n6. 成都市温江区商务局关于印发《成都市温江区促进服务业高质量发展支持政策的实施细则》的通知",
	"根据文档内容，成都市对住宿企业的补贴要求如下：\n\n对2022年第四季度营业额在100万元以下的限额以上住宿业市场主体，给予1万元水电气费补贴。\n对2022年第四季度营业额在100万元(含)-300万元的限额以上住宿业市场主体，给予3万元水电气费补贴。\n对2022年第四季度营业额在300万元(含)-500万元的限额以上住宿业市场主体，给予5万元水电气费补贴。\n对2022年第四季度营业额在500万元(含)以上的限额以上住宿业市场主体，给予10万元水电气费补贴。\n此外，成都市温江区商务局的政策规定，年度营业额达到200万元、500万元、1000万元，且同比增长25%及以上的限额以上住宿企业也有资格申报补贴。\n\n引用文档：\n[passage 1] 成都市文化广电旅游局关于印发《成都市支持市场主体纾困加快经济恢复的政策措施有关实施细则》。\n[passage 2] 成都市温江区商务局关于印发《成都市温江区促进服务业高质量发展支持政策的实施细则》的通知。",
}

var hunyuanAns = []string{
	"根据文档内容，关于成都市旅游业补贴“文旅消费新业态示范区”“文旅消费新业态示范项目”的咨询方式如下：\n\n市文广旅局市场管理处电话:61882997,负责星级酒店评定有关咨询工作；\n资源开发处电话:61884416,负责 A级林盘、“四改一提升”、主题旅游目的地评定有关咨询工作;\n文旅产业处电话:61882968,主要是服务和指导各区(市)县文旅行政管理部门做好政策咨询、项目申报等各项协调保障工作。\n\n申报审核过程中技术问题可咨询“天府蓉易享”平台技术人员(何老师:028-63911260;徐老师:18180949850)。\n",
	"1. 根据《成都市2022年现代林业产业项目申报指南》《成都市2022年现代林业产业项目评定办法》的通知，评定原则包括公平、公正、公开评审原则。\n2. 评分标准包括项目具备基本条件、项目建设内容、资金筹措、现场核查情况和工作综合情况。\n3. 评定程序包括会议评审（得分占80%）、实地核查（得分占10%）和综合评分（得分占10%）。\n4. 项目绩效评价情况包括经济效益、社会效益、环境效益、带动农民增收等方面。\n5. 资金安排根据最终得分对产业项目进行排名，依据项目绩效、工程量、可行性等要素进行综合平衡。",
	"1. 根据《成都市温江区促进服务业高质量发展支持政策的实施细则》，支持对象包括工商注册、税收管辖及统计关系等均在温江区的市场主体。\n2. 根据《成都市温江区促进服务业高质量发展支持政策的实施细则》，支持对象包括具有独立法人资格、财务制度健全、实行独立核算的市场主体。\n3. 根据《成都市温江区促进服务业高质量发展支持政策的实施细则》，支持对象包括工商注册、税收管辖及统计关系等均在温江区，具有独立法人资格、财务制度健全、实行独立核算的企业；或执业许可登记、税收管辖在温江区,财务制度健全、实行独立核算的机构（含事业单位、社会组织、行业协会）。\n4. 根据《关于开展“2024-02〔2023〕2-1-01-示范性消费场景”项目申报的通知》，支持对象包括工商注册登记、税收解缴关系、统计关系均在温江区的企业。\n5. 根据《关于开展“2024-02〔2022〕21-03-02-01-支持金融人才集聚（金融人才奖励）”等项目申报的通知》，支持对象包括注册登记、税务、统计关系在温江区的企业或其核心管理团队。\n6. 根据《关于开展“2024-02〔2022〕21-03-02-01-支持金融人才集聚（金融人才奖励）”等项目申报的通知》，支持对象包括注册登记、税务、统计关系在温江区的企业或其核心管理团队；海外上市主体通过协议或股权安排实际控制的境内经营实体的注册登记、税务、统计关系在温江区的企业或其核心管理团队。\n7. 根据《项目申报指南》，政策咨询包括区财政局（区金融局）资本市场科和区政务服务中心产业政策服务科。",
	"根据文档内容，成都制造业高端人才奖励申报条件如下：\n(一)与所在单位签订了 3年(含)以上的劳动合同;\n(二)所在单位属市域内先进制造业重点产业链企业;\n(三)申报人从事该产业领域相关工作连续从业满 1年;\n(四)申报人经认定符合《成都市人才分类目录》A、B、C、D类的人才;\n(五)申报人上一年度缴纳个人所得税后年收入 40万元(含)以上;\n(六)申报人已足额缴纳了个人所得税;\n(七)申报人没有受到刑事处罚,或申报时刑事处罚已经执行完毕;\n(八)申报人未享受过市、区(市)县同类奖励。\n",
	"1. 成都市制造业重点产业重点领域高端人才奖励申报书；\n2. 申请人身份证或者护照(正反面)；\n3. 成都市人才认定证书；\n4. 所在单位社保缴纳证明(申报人需至少提供2023年6月至2024年6月时间段内连续缴纳社保证明)；\n5. 所在单位提供的2023年度(1月12月)工资薪金、奖金发放说明(按照附件3说明填写签字盖章，并提供与薪酬发放说明每月金额相对应的银行流水)；\n6. 申报人2023年度(1月-12月)工资薪金、奖励收入个税完税证明；\n7. 所在单位的营业执照(正、副本)或多证合一证(正、副本)复印件(加盖所在单位鲜章)；\n8. 申报人与所在单位签订的劳动合同复印件(加盖所在单位鲜章)；\n9. 申报人突出的业绩(成果)、论文著作、授权专利或表彰奖励等证明材料；\n10. 申报承诺书。\n",
	"根据文档内容，成都申报采购区内生物医药企业产品补贴的项目包括：\n2024-02〔2021〕8-16-02-关于采购区内生物医药企业产品的补贴\n2024-02〔2021〕8-07-02-特医保健销售奖励\n2024-02〔2021〕7-13-02-工业企业参会参展补贴\n2024-02〔2021〕7-12-02-企业采购“温江造”产品补贴\n2024-02〔2021〕8-27-02-百强奖励\n2024-02〔2021〕7-19-02-企业上云奖励\n2024-02〔2021〕7-19-01-工业服务平台奖励\n",
	"1. 根据《成都市温江区推动先进制造业高质量发展的若干政策措施的实施细则（2022修订版）》，对于租用非关联第三方标准厂房且年地方经济发展实际贡献达到200元/平方米、300元/平方米的规模以上工业企业，分别给予100元/年/平方米、200元/年/平方米的租金补贴，补贴金额不超过实际价格。\n2. 根据《成都市温江区促进服务业高质量发展支持政策的实施细则》，对于入驻纳入区楼宇智慧服务平台统计的楼宇满一年的企业且租用面积达到200平方米及以上，申报年度经济贡献不低于1000元/平方米，且总量不低于50万元/年的企业，按照每月35元/平方米的标准补助，时间最长三年，支持金额不超过支持面积内的实际租金总额。\n3. 根据《成都市温江区租赁市场发展试点财政奖补资金使用管理实施细则》，对出租自持租赁住房的住房租赁企业，按自持租赁住房出租面积，每年按100元/平方米的标准进行经营奖补。\n4. 根据《中共成都市温江区委成都市温江区人民政府关于促进民营经济健康发展的实施意见》，鼓励人力资源服务机构为民营企业提供招用工服务，对人力资源服务机构新入驻温江设点执业并为区内企业提供上述服务的，对其在温租赁自用办公用房（200平方米以内），按租金实际价格给予最高10万元/年的房租补贴，补贴期限不超过3年。\n5. 根据《关于开展“2023-01 〔2023〕8-4-03-01-人才住房奖励支持”等项目申报的通知》，对于经温江区认定的A类人才，符合条件的可免费租住一套不超过200平方米的人才住房，入住期间所产生的租金、物业服务费用由房源业主或代管单位按年申报，租金的市场标准由房源业主或代管单位委托房地产评估机构评估确定。\n6. 根据《成都市温江区推动先进制造业高质量发展的若干政策措施》，对新引进落户天府商务服务区等指导区域的金融机构，对新购置自用办公用房的，按购房价格的8%一次性给予最高500万元补贴；对租用载体面积不超过800平方米的部分，连续三年按最高50元/平方米/月的标准给予租金补贴，补贴单价不超过实际租用单价。",
	"锦江区建筑企业提质增效扶持政策内容包括：\n\n1. **适用范围**：工商注册、税务登记在锦江区，并纳入国家统计网报企业库的，且具有《资质证书》和独立法人资格的施工企业。\n\n2. **政策内容**：\n- **强化建设项目推介**：鼓励区内总承包企业专业分包选择区内施工企业。\n- **鼓励企业上规入库**：达到上规入库标准，首次纳入国家统计网报企业库，且当年建筑业产值达到5000万元以上的资质等级以上的区内施工企业，给予5万元的一次性奖励。\n- **支持区外企业迁入**：总承包特级、一级资质的区外施工企业新迁入并入库锦江区后，其入库当年建筑业产值达到20亿元的，给予300万元的一次性奖励；第二年建筑业产值达到4亿元的，给予40万元的一次性奖励。\n- **支持企业提升资质**：区内施工企业新晋升（新办）为施工总承包特级、一级资质企业的施工企业，其资质晋升或新办的当年建筑业产值达到20亿元的，给予200万元的一次性奖励；第二年建筑业产值达到4亿元的，给予40万元的一次性奖励。\n- **支持企业壮大发展**：对区内年度建筑业产值首次达到0.5亿元、1亿元、5亿元、10亿元、30亿元、50亿元、100亿元、300亿元、500亿元、1000亿元的施工企业，且当年建筑业产值增幅超过锦江区目标增幅，分别对应给予0.5万元、5万元、10万元、20万元、60万元、100万元、200万元、400万元、600万元、1000万元的一次性奖励。\n- **支持企业入库**：区内施工企业入库当年建筑业产值达到0.2亿元、0.5亿元、1亿元、2亿元、5亿元、10亿元、20亿元、50亿元的，分别对应给予0.5万元、1万元、5万元、8万元、15万元、30万元、60万元、120万元的一次性奖励。\n- **支持企业“走出去”**：对年度区外建筑业产值达到1亿元、5亿元、10亿元、20亿元、50亿元、100亿元、300亿元的施工一级及以下资质的区内施工企业分别对应给予2万元、10万元、20万元、40万元、100万元、150万元、300万元的一次性奖励；对年度区外建筑业产值达到100亿元、300亿元、500亿元、1000亿元、2000亿元的施工总承包特级的区内施工企业分别对应给予100万元、200万元、300万元、400万元、500万元的一次性奖励。\n\n3. **附则**：\n- 申报时限为次年三月底前。\n- 获得财政扶持资金支持的企业需配合做好绩效评价工作。\n- 申报企业对数据的真实性负责。\n- 享受扶持的企业在锦江区实际经营期限原则上不低于10年或达到协议年限。\n- 同类性质政策企业可就高不就低选择申报，不可叠加享受。\n- 对企业晋升资质后5年内被撤销等情形，企业所获奖励资金须全额退回。\n",
	"1. 企业应为登记注册且纳税在大邑县的企业、事业单位、社会团体及其他组织机构。\n2. 企业需开设“蓉漂人才巴士”，并设立统一标识标记，用于企业各类人才的上下班通勤。\n3. “蓉漂人才巴士”车辆应为企业自有或租用车辆，配有司机，专用于运送企业人才，不得对外经营盈利。\n4. 车辆核定载客人数应在15座以上（含司机），原则上每车每日往返县外发车不多于2次（多于2次的按2次计费），每次开行应做好相关记录并存档。\n5. 运行线路起讫点一端为大邑县内，另一端为大邑县外（与已开通公交线路起讫点一致的，不纳入补贴范围）。\n6. 企业应根据实际出行需求人数，单独或联合周边企业开设“蓉漂人才巴士”。联合开设“蓉漂人才巴士”应出具联合开设证明材料，并协商确定一家企业为申报补贴的主体。\n7. 申报企业需在规定时间内报名，并提交相关资料至所在产业功能区（功能区外的企业报所在镇街），再报县交通运输局审核认定。\n8. 企业应在规定时间内统计汇总车辆开行记录和相关费用支出，出具相关证明材料，整理形成正式文本后，报送所在产业功能区或镇（街道）。\n9. 经逐级审定后，给予企业专项补贴，补贴金额为运送人才上下班产生车辆能源消耗费的50%（若为企业自有车辆）或租车费的50%（若为租用车辆），每家企业每年最高补贴10万元。\n10. 申报资料包括《大邑县“蓉漂人才巴士”补贴报名表》、登记注册地和纳税关系证明材料、“蓉漂人才巴士”车辆相关证明、驾驶员基本信息、企业无重大违法记录和不良信用记录的承诺函等。\n11. 年末结算时，企业需提交《大邑县“蓉漂人才巴士”补贴结算表》、“蓉漂人才巴士”开行记录、燃油费用（电动车用电费用）相关证明材料等。\n12. 补贴资金由县财政局按程序向县交通运输局划拨，再由县交通运输局按程序向各产业功能区或镇街划拨补贴资金，最后向企业兑现补助资金。\n参考条款：\n1. 《大邑县交通运输局关于印发《大邑县“蓉漂人才巴士”人才通勤保障实施细则》的通知》\n2. 《大邑县“蓉漂人才巴士”补贴报名表》\n3. 《大邑县“蓉漂人才巴士”补贴结算表》",
	"锦江区为支持软件企业上规入库，在房租方面的补贴政策如下：\n\n- **月度入库企业**：可按不超过2000平方米、30元/月/平方米的标准，或实际租金的60%，给予一次性不超过50万元的房租补贴。\n- **年度入库企业**：可按不超过1000平方米、30元/月/平方米的标准，或实际租金的40%，给予一次性不超过30万元的房租补贴。\n\n这些政策分别来源于以下两篇文档：\n\n1. 《锦江区经济和信息化局贯彻落实成都市锦江区关于推动“1+3+3”产业体系高质量发展的若干政策的实施细则》\n2. 《成都市锦江区关于推动“1+3+3”产业体系高质量发展的若干政策》",
	"金堂县的科技行业人才政策包括：\n- **支持科技成果转移转化**：对获得中国专利金奖、银奖、优秀奖等，并在县内落地转化的项目，给予专利权人不超过50万元一次性经费支持。\n- **支持科技孵化载体发展**：对县内新认定的国家级孵化器、省级孵化器等创新创业载体，分别给予运营机构不超过100万元、50万元、30万元的经费资助。\n- **支持关键核心技术攻关**：对获得国家立项的关键核心技术攻关项目并在县内落地转化产生效益的，按照所获国家专项支持资金的30%给予支持。\n\n邛崃市的科技行业人才政策包括：\n- **企业“育才奖”**：鼓励企业培育在职人员提升职业能力，对推荐人员成功入选国家海外高层次人才引进计划等的企业给予奖励。\n- **企业“伯乐奖”**：鼓励用人企业、第三方机构、行业专家等引荐人才来邛创新创业，对成功举荐高层次人才的企业给予一次性奖励。",
	"在成都温江，针对企业经营困难问题，政府采取了一系列措施来缓解企业的压力，具体包括：\n\n**防疫物资保障**\n- 加强防疫物资保供，鼓励企业采购并给予奖励。\n- 支持防疫物资生产，纳入战略储备采购名单。\n- 支持防疫项目攻关，给予研发补贴。\n- 支持防疫体系建设，提供复产复业补助。\n\n**降低经营成本**\n- 降低企业用能成本，提供电费补贴。\n- 减轻企业租金负担，减免租金和物业费。\n\n**财税金融支持**\n- 给予信贷支持，确保信贷规模，降低融资成本。\n- 鼓励社会资本投资，给予股权投资奖励。\n- 给予贴息支持，下调贷款利率。\n- 给予税收支持，落实税收优惠政策。\n\n**援企稳岗**\n- 降低用工成本，延期缴纳社保费、公积金。\n- 提供人力资源服务，搭建招聘平台。\n\n**企业服务**\n- 帮助企业拓展市场，提供交易奖励。\n- 提供疫情防控法律服务。\n- 提升企业服务质效，简化审批流程。\n\n**支持市场主体纾困**\n- 落实退税减税缓税政策。\n- 加大金融信贷惠企力度。\n- 降低企业闭环生产管理成本。\n- 加快推进企业复工复产。\n\n**低效工业用地提质增效**\n- 鼓励重组增效，提供并购奖励。\n- 鼓励工业上楼，提供厂房建设奖励。\n- 鼓励多投快建，给予项目投资奖励。\n- 鼓励产出增效，提供经济发展贡献奖励。\n\n**服务业高质量发展**\n- 支持对象包括温江区内的企业和机构。\n- 提供政策咨询服务。\n\n以上措施涵盖了财税减免、金融支持、人力资源服务、市场拓展等多个方面，旨在帮助企业渡过难关，保障经济的平稳运行。具体的实施细节和申请流程，企业可以联系相关部门获取详细信息。\n\n1. 成都市温江区关于应对新型冠状病毒肺炎疫情缓解中小企业生产经营困难的16条政策措施\n2. 关于印发《成都市温江区关于支持市场主体纾困加快经济恢复的若干政策措施》的通知\n5. 成都市温江区关于加快促进低效工业用地企业提质增效的政策措施\n6. 成都市温江区商务局关于印发《成都市温江区促进服务业高质量发展支持政策的实施细则》的通知",
	"根据文档内容，成都市对住宿企业的补贴要求如下：\n\n对2022年第四季度营业额在100万元以下的限额以上住宿业市场主体，给予1万元水电气费补贴。\n对2022年第四季度营业额在100万元(含)-300万元的限额以上住宿业市场主体，给予3万元水电气费补贴。\n对2022年第四季度营业额在300万元(含)-500万元的限额以上住宿业市场主体，给予5万元水电气费补贴。\n对2022年第四季度营业额在500万元(含)以上的限额以上住宿业市场主体，给予10万元水电气费补贴。\n此外，成都市温江区商务局的政策规定，年度营业额达到200万元、500万元、1000万元，且同比增长25%及以上的限额以上住宿企业也有资格申报补贴。\n\n引用文档：\n成都市文化广电旅游局关于印发《成都市支持市场主体纾困加快经济恢复的政策措施有关实施细则》。\n成都市温江区商务局关于印发《成都市温江区促进服务业高质量发展支持政策的实施细则》的通知。",
}

func TestHunyuan(t *testing.T) {
	t.Run("test get all sub match", func(t *testing.T) {
		res := GetAllSubMatch(0, hunyuan[0])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{}, res)

		res = GetAllSubMatch(0, hunyuan[1])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1, 2, 4, 6, 7}, res)

		res = GetAllSubMatch(0, hunyuan[2])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1, 2, 4, 6, 7, 9, 10}, res)

		res = GetAllSubMatch(0, hunyuan[3])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1}, res)

		res = GetAllSubMatch(0, hunyuan[4])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1}, res)

		res = GetAllSubMatch(0, hunyuan[5])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{}, res)

		res = GetAllSubMatch(0, hunyuan[6])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{9, 2, 3, 4, 5, 8}, res)

		res = GetAllSubMatch(0, hunyuan[7])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1}, res)

		res = GetAllSubMatch(0, hunyuan[8])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1, 2, 6}, res)

		res = GetAllSubMatch(0, hunyuan[9])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1, 2}, res)

		res = GetAllSubMatch(0, hunyuan[10])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{3, 2, 5}, res)

		res = GetAllSubMatch(0, hunyuan[11])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{}, res)

		res = GetAllSubMatch(0, hunyuan[12])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []int{1, 2}, res)
	})

	t.Run("test remove reference", func(t *testing.T) {
		res := RemoveReference(0, hunyuan[0])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, hunyuanAns[0], res)

		res = RemoveReference(0, hunyuan[1])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, hunyuanAns[1], res)

		res = RemoveReference(0, hunyuan[2])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, hunyuanAns[2], res)

		res = RemoveReference(0, hunyuan[3])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, hunyuanAns[3], res)

		res = RemoveReference(0, hunyuan[4])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, hunyuanAns[4], res)

		res = RemoveReference(0, hunyuan[5])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, hunyuanAns[5], res)

		res = RemoveReference(0, hunyuan[6])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, hunyuanAns[6], res)

		res = RemoveReference(0, hunyuan[7])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, hunyuanAns[7], res)

		res = RemoveReference(0, hunyuan[8])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, hunyuanAns[8], res)

		res = RemoveReference(0, hunyuan[9])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, hunyuanAns[9], res)

		res = RemoveReference(0, hunyuan[10])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, hunyuanAns[10], res)

		res = RemoveReference(0, hunyuan[11])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, hunyuanAns[11], res)

		res = RemoveReference(0, hunyuan[12])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, hunyuanAns[12], res)

	})
}

var testMatchLLMRefsInput = "[1](@ref)、[2](@ref)、[2,3](@ref)\n" +
	// "[1,2]、[2,3,4]，[6,5]\n" +
	"Passage1到2abcd, 123Passage 1到3abc111，Passage   8到9测试，Passage22到23abcd, 123Passage 22到23abc111，Passage   22到24测试\n" +
	"passage1到2abcd, 123passage 1到3abc111，passage   8到9测试，passage22到23abcd, 123passage 22到23abc111，passage   22到24测试\n" +
	"Passage1abcd, 123Passage 1abc111，Passage   1测试，Passage22abcd, 123Passage 22abc111，Passage   22测试\n" +
	"passage1abcd, 123passage 1abc111，passage   1测试，passage22abcd, 123passage 22abc111，passage   22测试\n" +
	"Passage[1]abcd, 123Passage [1]abc111，Passage   [1]测试，Passage[22]abcd, 123Passage [22]abc111，Passage   [22]测试\n" +
	"passage[1]abcd, 123passage [1]abc111，passage   [1]测试，passage[22]abcd, 123passage [22]abc111，passage   [22]测试\n" +
	"Passage【1】abcd, 123Passage 【1】abc111，Passage   【1】测试，Passage【22】abcd, 123Passage 【22】abc111，Passage   【22】测试\n" +
	"passage【1】abcd, 123passage 【1】abc111，passage   【1】测试，passage【22】abcd, 123passage 【22】abc111，passage   【22】测试\n" +
	// "[1]abcd,123[11]" +
	"[^1]asd，[^123]"

var testMatchLLMRefsResult = "<1>(@ref)、<2>(@ref)、<2>(@ref)<3>(@ref)\n" +
	// "<1>(@ref)<2>(@ref)、<2>(@ref)<3>(@ref)<4>(@ref)，<6>(@ref)<5>(@ref)\n" +
	"<1>(@ref)<2>(@ref)abcd, 123<1>(@ref)<2>(@ref)<3>(@ref)abc111，<8>(@ref)<9>(@ref)测试，<22>(@ref)<23>(@ref)abcd, 123<22>(@ref)<23>(@ref)abc111，<22>(@ref)<23>(@ref)<24>(@ref)测试\n" +
	"<1>(@ref)<2>(@ref)abcd, 123<1>(@ref)<2>(@ref)<3>(@ref)abc111，<8>(@ref)<9>(@ref)测试，<22>(@ref)<23>(@ref)abcd, 123<22>(@ref)<23>(@ref)abc111，<22>(@ref)<23>(@ref)<24>(@ref)测试\n" +
	"<1>(@ref)abcd, 123<1>(@ref)abc111，<1>(@ref)测试，<22>(@ref)abcd, 123<22>(@ref)abc111，<22>(@ref)测试\n" +
	"<1>(@ref)abcd, 123<1>(@ref)abc111，<1>(@ref)测试，<22>(@ref)abcd, 123<22>(@ref)abc111，<22>(@ref)测试\n" +
	"<1>(@ref)abcd, 123<1>(@ref)abc111，<1>(@ref)测试，<22>(@ref)abcd, 123<22>(@ref)abc111，<22>(@ref)测试\n" +
	"<1>(@ref)abcd, 123<1>(@ref)abc111，<1>(@ref)测试，<22>(@ref)abcd, 123<22>(@ref)abc111，<22>(@ref)测试\n" +
	"<1>(@ref)abcd, 123<1>(@ref)abc111，<1>(@ref)测试，<22>(@ref)abcd, 123<22>(@ref)abc111，<22>(@ref)测试\n" +
	"<1>(@ref)abcd, 123<1>(@ref)abc111，<1>(@ref)测试，<22>(@ref)abcd, 123<22>(@ref)abc111，<22>(@ref)测试\n" +
	// "<1>(@ref)abcd,123<11>(@ref)" +
	"<1>(@ref)asd，<123>(@ref)"

func TestMatchLLMRefs(t *testing.T) {
	res := MatchLLMRefs(testMatchLLMRefsInput)
	t.Logf("res: %v\n", res)
	require.EqualValues(t, testMatchLLMRefsResult, res)
}

var testNoMatchLLMRefsInput = "[1(@ref)、2](@ref)、[2,3(@ref)af2,3](@ref)\n" +
	"[1、2]、[2,3a]\n" +
	"Passage1到ab2，Passage2到1Passage 1到ab2，Passage  2到1\n" +
	"passage1到ab2，passage2到1passage 1到ab2，passage  2到1\n" +
	"PassageabcdPassage abc111\n" +
	"passageabcdpassage abc111\n" +
	"Passage[]abcd, 123Passage [a1]Passage [22b]Passage   [22测试]\n" +
	"passage[]abcd, 123passage [a1]passage [22b]passage   [22测试]\n" +
	"Passage【】abcd, 123Passage 【a1】Passage 【22b】Passage   【22测试】Passage【1]Passage[1】\n" +
	"passage【】abcd, 123passage 【a1】passage 【22b】passage   【22测试】passage【1]passage[1】\n" +
	"[1abcd,123[11，22],[^1asd[^1,^1][^123,^123]"

var testNoMatchLLMRefsResult = "[1(@ref)、2](@ref)、[2,3(@ref)af2,3](@ref)\n" +
	"[1、2]、[2,3a]\n" +
	"<1>(@ref)到ab2，<2>(@ref)到1<1>(@ref)到ab2，<2>(@ref)到1\n" +
	"<1>(@ref)到ab2，<2>(@ref)到1<1>(@ref)到ab2，<2>(@ref)到1\n" +
	"PassageabcdPassage abc111\n" +
	"passageabcdpassage abc111\n" +
	"Passage[]abcd, 123Passage [a1]Passage [22b]Passage   [22测试]\n" +
	"passage[]abcd, 123passage [a1]passage [22b]passage   [22测试]\n" +
	"Passage【】abcd, 123Passage 【a1】Passage 【22b】Passage   【22测试】Passage【1]Passage[1】\n" +
	"passage【】abcd, 123passage 【a1】passage 【22b】passage   【22测试】passage【1]passage[1】\n" +
	"[1abcd,123[11，22],[^1asd[^1,^1][^123,^123]"

func TestNoMatchLLMRefs(t *testing.T) {
	res := MatchLLMRefs(testNoMatchLLMRefsInput)
	t.Logf("res: %v\n", res)
	require.EqualValues(t, testNoMatchLLMRefsResult, res)
}

func TestMatchLLMRefs1(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "1",
			args: args{str: "abc[1](@ref)a"},
			want: "abc<1>(@ref)a",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MatchLLMRefs(tt.args.str); got != tt.want {
				t.Errorf("MatchLLMRefs() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMatchSearchResults(t *testing.T) {
	type args struct {
		ctx context.Context
		str string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 []int
		want2 []int
	}{
		{
			name: "1",
			args: args{
				ctx: context.Background(),
				str: "abc[1](@ref)a",
			},
			want: "abca",
		},
		{
			name: "2",
			args: args{
				ctx: context.Background(),
				str: "abc[1,2](@ref)a",
			},
			want: "abca",
		},
		{
			name: "3",
			args: args{
				ctx: context.Background(),
				str: "abc[^1]a",
			},
			want: "abca",
		},
		{
			name: "3",
			args: args{
				ctx: context.Background(),
				str: "abc[1]a",
			},
			want: "abc[1]a",
		},
		{
			name: "4",
			args: args{
				ctx: context.Background(),
				str: "根据提供的材料信息，左转相关交通标志分类如下：\n\n---\n\n### 一、允许左转的标志\n1. **向左转弯标志（图75）**  \n   - **含义**：表示一切车辆只准向左转弯[1](@ref)。  \n   - **",
			},
			want: "根据提供的材料信息，左转相关交通标志分类如下：\n\n---\n\n### 一、允许左转的标志\n1. **向左转弯标志（图75）**  \n   - **含义**：表示一切车辆只准向左转弯。  \n   - **",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2 := MatchSearchResults(tt.args.ctx, tt.args.str)
			if got != tt.want {
				t.Errorf("MatchSearchResults() got = %v, want %v", got, tt.want)
			}
			fmt.Println(got1)
			fmt.Println(got2)
			// if !reflect.DeepEqual(got1, tt.want1) {
			//	t.Errorf("MatchSearchResults() got1 = %v, want %v", got1, tt.want1)
			// }
			// if !reflect.DeepEqual(got2, tt.want2) {
			//	t.Errorf("MatchSearchResults() got2 = %v, want %v", got2, tt.want2)
			// }
		})
	}
}
