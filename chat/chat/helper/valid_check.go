// chat
//
// @(#)valid_check.go  星期二, 一月 14, 2025
// Copyright(c) 2025, randal<PERSON>@Tencent. All rights reserved.

package helper

import (
	"fmt"
	"regexp"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
)

var (
	recordIDRegx  = regexp.MustCompile(`^[a-zA-Z0-9_]{32}$`)
	appKeyRegx    = regexp.MustCompile(`^[a-zA-Z0-9]{8}$`)
	appKeyRegxNew = regexp.MustCompile(`^[a-zA-Z]{128}$`)
	appidRegx     = regexp.MustCompile(`^[0-9]+$`)
)

// CheckShareCode 校验分享码是否合法
func CheckShareCode(shareCode string) bool {
	if config.App().App.ShareCodeLen == 0 {
		return true
	}
	pattern := fmt.Sprintf("^[a-zA-Z]{%d}$", config.App().App.ShareCodeLen)
	regx, err := regexp.Compile(pattern)
	if err != nil {
		log.Warnf("[param invalid] CheckShareCode pattern:%s,err:%+v", pattern, err)
		return false
	}
	return regx.MatchString(shareCode)
}

// CheckSessionID 校验sessionID是否合法
func CheckSessionID(sessionID string) bool {
	sessionRegx := config.GetSessionRegx()
	if len(sessionRegx) == 0 {
		return len(sessionID) != 0 && len(sessionID) < 65
	}
	regx, err := regexp.Compile(sessionRegx)
	if err != nil {
		log.Warnf("[param invalid] CheckSessionID sessionRegx:%s,err:%+v", sessionRegx, err)
		return false
	}
	if !regx.MatchString(sessionID) {
		log.Warnf("[param invalid] sessionID doesn't match regex, sessionID:%s", sessionID)
		return false
	}
	return true
}

// CheckRecordID 校验recordID是否合法
func CheckRecordID(recordID string) bool {
	return recordIDRegx.MatchString(recordID)
}

// CheckBotAppKey 校验bot的appkey是否合法
func CheckBotAppKey(appkey string, isAppid bool) bool {
	if !isAppid {
		if len(appkey) > 8 {
			return appKeyRegxNew.MatchString(appkey)
		}
		return appKeyRegx.MatchString(appkey)
	} else {
		return appidRegx.MatchString(appkey)
	}
}
