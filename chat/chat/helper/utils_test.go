package helper

import (
	"testing"

	"github.com/stretchr/testify/require"
)

var intSlice = []int{1, 2, 3, 4, 5}

var summaryList = []string{
	"UNI-T使用说明书.doc【因阅读长度限制，仅阅读3.7%的字数】中核心观点包含：\n 这篇文档是【产品说明书】，主要关注【长安汽车使用说明】。以下是核心要点总结：\n\n1. **使用说明**\\n   - 本说",
	"UNI-T使用说明书.doc【因阅读长度限制，仅阅读3.7%的字数】中核心观点包含：\n 这篇文档是【产品说明书】，主要关注【长安汽车使用说明】。以下是核心要点总结：1. **使用说明**   - 本说明书适用于所有车型，但具体配置可能有所不同，以实际车辆为准。\\n   - 使用说明书中的“※”表示该配置不包含在所有车型中。\\n   - 说明书内容可能因产品改进而变更，不另行通知。\\n   - 说明书属于车辆的有效组成部分，需详细阅读，特别是“危险”、“警告”和“注意”标记。\\n\\n2. **注意事项**\\n   - 不要改装车辆，以免影响安全、操纵、性能和寿命，且改装引起的问题不属于质量保修范围。\\n   - 更换零件应使用公司配套供应商生产的零配件，否则不属于质量保修范围。\\n\\n3. **符号信息**\\n   - 提供了常用符号及其代表的单位和含义，如L代表升，km/h代表千米/小时等。\\n\\n4. **企业标准**\\n   - 列出了车型号与对应的企业标准，如SC7151AA B6对应Q/JD 7660。\\n\\n5. **操作方法及故障处理**\\n   - 详细列出了各种车辆状态指示灯的含义、操作方法及故障处理，如远光灯、近光灯、自动远近光灯、后雾灯、位置灯、转向灯、机油压力低、蓄电池充电、门开指示灯、发动机、燃油低、安全气囊、前后排安全带、水温高、胎压监测、下坡辅助、定速巡航、自适应巡航(ACC)、电子稳定控制故障(ESC)、电子稳定控制关闭(ESC_OFF)、电子助力转向故障(EPS)、电子驻车制动故障(EPB)、电子驻车制动(EPB)、自动驻车(AUTO HOLD)、变速箱故障等。",
}

var summaryAns = []string{
	" 这篇文档是【产品说明书】，主要关注【长安汽车使用说明】。以下是核心要点总结：",
	" 这篇文档是【产品说明书】，主要关注【长安汽车使用说明】。以下是核心要点总结：1. **使用说明**   - 本说明书适用于所有车型，但具体配置可能有所不同，以实际车辆为准。\\n   - 使用说明书中",
}

// TestAll test
func TestAll(t *testing.T) {
	t.Run("Contains In Slice", func(t *testing.T) {
		res := ContainsInt(intSlice, 1)
		t.Logf("res: %v\n", res)
		require.EqualValues(t, true, res)

	})

	t.Run("TruncatedSummary", func(t *testing.T) {
		res := TruncatedSummary(summaryList[0])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, summaryAns[0], res)

		res = TruncatedSummary(summaryList[1])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, summaryAns[1], res)
	})
}

var strSlice = []string{"a", "b", "c", "d", "e"}
var uintSlice = []uint{1, 2, 3, 4, 5}
var floatSlice = []float64{1.1, 2.2, 3.3, 4.4, 5.5}

// TestContains test
func TestContains(t *testing.T) {
	t.Run("Contains", func(t *testing.T) {
		res := Contains(strSlice, "a")
		t.Logf("res: %v\n", res)
		require.EqualValues(t, true, res)

		res = Contains(uintSlice, 1)
		t.Logf("res: %v\n", res)
		require.EqualValues(t, true, res)

		res = Contains(floatSlice, 1.1)
		t.Logf("res: %v\n", res)
		require.EqualValues(t, true, res)

		res = Contains(floatSlice, 1)
		t.Logf("res: %v\n", res)
		require.EqualValues(t, false, res)
	})

}

// TestSnakeToPascalCase 测试下划线转驼峰
func TestSnakeToPascalCase(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"code_interpreter", "CodeInterpreter"},
		{"code_interpreter_test", "CodeInterpreterTest"},
		{"code", "Code"},
	}

	for _, testCase := range testCases {
		t.Run(testCase.input, func(t *testing.T) {
			result := SnakeToPascalCase(testCase.input)
			require.Equal(t, testCase.expected, result)
			t.Logf("result: %s\n", result)
		})
	}
}

// TestSnakeToCamelCase 测试下划线转小驼峰
func TestSnakeToCamelCase(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"code_interpreter", "codeInterpreter"},
		{"code_interpreter_test", "codeInterpreterTest"},
		{"code", "code"},
	}

	for _, testCase := range testCases {
		t.Run(testCase.input, func(t *testing.T) {
			result := SnakeToCamelCase(testCase.input)
			require.Equal(t, testCase.expected, result)
			t.Logf("result: %s\n", result)
		})
	}
}

// TestTruncateHistories
func TestTruncateHistories(t *testing.T) {
	t.Run("TruncateHistoriesTest", func(t *testing.T) {
		// arguments := `{"name": "test", "age": 18}`
		content := "![](https://lke-realtime-1251316161.cos.ap-guangzhou.myqcloud.com/public/1807766118357270528/1911331072150339584/image/XgmXVinYIOCEmklvfJvL-1912153817580306432.jpg)我的卡罗拉车灯坏了，我该怎么更换\",\"RewriteQuery\":\"我的卡罗拉车灯坏了，我该怎么更换\",\"Message\":[{\"Content\":\"![](https://lke-realtime-1251316161.cos.ap-guangzhou.myqcloud.com/public/1807766118357270528/1911331072150339584/image/XgmXVinYIOCEmklvfJvL-1912153817580306432.jpg)我的卡罗拉车灯坏了，我该怎么更换\",\"Role\":0},{\"Content\":\"以下是卡罗拉锐放车灯更换的详细步骤及对应图示，请根据具体车灯类型操作：\\n\\n### 前照灯（灯泡型）更换步骤\\n1. **断开连接器**  \\n  按下锁定释放装置的同时断开连接器。  \\n  ![](https://lke.cloud.tencent.com/s/ilTUT2Jf?size=mid|160.7*108.7|0.13)\\n\\n2. **拆下旧灯泡**  \\n  逆时针转动灯泡并将其取出。  \\n  ![](https://lke.cloud.tencent.com/s/tTVvN7WZ?size=mid|160.7*109.3|0.13)\\n\\n3. **安装新灯泡**  \\n  对准插槽插入新灯泡。  \\n  ![](https://lke.cloud.tencent.com/s/qvRKv8rc?size=mid|160.7*109.3|0.13)\\n\\n4. **固定灯泡**  \\n  顺时针转动灯泡至锁定位置。  \\n  ![](https://lke.cloud.tencent.com/s/5CXrzav3?size=mid|160.7*109.0|0.13)\\n\\n5. **检查安装**  \\n  连接连接器后，轻晃灯泡确认牢固，并目视检查无漏光。  \\n  ![](https://lke.cloud.tencent.com/s/32QU2QlH?size=mid|160.7*109.3|0.13)\\n\\n---\\n\\n### 日间行车灯/前位灯（灯泡型）更换步骤\\n1. **拆下灯座**  \\n  逆时针转动灯座。  \\n  ![](https://lke.cloud.tencent.com/s/u5BNWPkk?size=min)\\n\\n2. **更换灯泡**  \\n  拆下旧灯泡后安装新灯泡。  \\n  ![](https://lke.cloud.tencent.com/s/GYDaGnau?size=mid|160.7*110.0|0.13)\\n\\n3. **重新安装**  \\n  顺时针转动灯座固定。  \\n  ![](https://lke.cloud.tencent.com/s/zZo8LcuW?size=mid|160.3*110.3|0.13)\\n\\n---\\n\\n### 后转向信号灯更换步骤\\n1. **拆下车灯装置**  \\n  打开背门，拆下2个螺钉后拉出车灯装置。  \\n  ![](https://lke.cloud.tencent.com/s/2xOWgiLh?size=mid|161.0*109.7|0.13)\\n\\n2. **更换灯泡**  \\n  逆时针转动灯座拆下旧灯泡，安装新灯泡后顺时针固定。  \\n  ![](https://lke.cloud.tencent.com/s/FJpeHZEh?size=mid|161.0*109.3|0.13)\\n\\n---\\n\\n### 注意事项\\n- **安全警告**  \\n  ![](https://lke.cloud.tencent.com/s/eoSYXo8E?size=min)  \\n  - 操作前关闭发动机，避免烫伤或夹伤。  \\n  - 勿用手直接触摸灯泡玻璃部分，需用干布包裹。  \\n  - 确保灯泡瓦数与原厂一致，防止过热损坏。\\n\\n- **LED车灯提示**  \\n  若车灯为LED型（如高位刹车灯、牌照灯等），需由丰田经销商更换，不可自行操作。\\n\\n如需其他车灯（如倒车灯、雾灯）的更换指南，请提供具体类型以便进一步说明。\\n请选择是否要推荐附近的服务中心"
		content1 := "![](https://lke-realtime-1251316161.cos.ap-guangzhou.myqcloud.com/public/1807766118357270528/1911331072150339584/image/XgmXVinYIOCEmklvfJvL-1912153817580306432.jpg)我的卡罗拉车灯坏了，我该怎么更换\",\"RewriteQuery\":\"我的卡罗拉车灯坏了，我该怎么更换\",\"Message\":[{\"Content\":\"![](https://lke-realtime-1251316161.cos.ap-guangzhou.myqcloud.com/public/1807766118357270528/1911331072150339584/image/XgmXVinYIOCEmklvfJvL-1912153817580306432.jpg)我的卡罗拉车灯坏了，我该怎么更换\",\"Role\":0},{\"Content\":\"以下是卡罗拉锐放车灯更换的详细步骤及对应图示，请根据具体车灯类型操作：\\n\\n### 前照灯（灯泡型）更换步骤\\n1. **断开连接器**  \\n  按下锁定释放装置的同时断开连接器。  \\n  ![](https://lke.cloud.tencent.com/s/ilTUT2Jf?size=mid|160.7*108.7|0.13)\\n\\n2. **拆下旧灯泡**  \\n  逆时针转动灯泡并将其取出。  \\n  ![](https://lke.cloud.tencent.com/s/tTVvN7WZ?size=mid|160.7*109.3|0.13)\\n\\n3. **安装新灯泡**  \\n  对准插槽插入新灯泡。  \\n  ![](https://lke.cloud.tencent.com/s/qvRKv8rc?size=mid|160.7*109.3|0.13)\\n\\n4. **固定灯泡**  \\n  顺时针转动灯泡至锁定位置。  \\n  ![](https://lke.cloud.tencent.com/s/5CXrzav3?size=mid|160.7*109.0|0.13)\\n\\n5. **检查安装**  \\n  连接连接器后，轻晃灯泡确认牢固，并目视检查无漏光。  \\n  ![](https://lke.cloud.tencent.com/s/32QU2QlH?size=mid|160.7*109.3|0.13)\\n\\n---\\n\\n### 日间行车灯/前位灯（灯泡型）更换步骤\\n1. **拆下灯座**  \\n  逆时针转动灯座。  \\n  ![](https://lke.cloud.tencent.com/s/u5BNWPkk?size=min)\\n\\n2. **更换灯泡**  \\n  拆下旧灯泡后安装新灯泡。  \\n  ![](https://lke.cloud.tencent.com/s/GYDaGnau?size=mid|160.7*110.0|0.13)\\n\\n3. **重新安装**  \\n  顺时针转动灯座固定。  \\n  ![](https://lke.cloud.tencent.com/s/zZo8LcuW?size=mid|160.3*110.3|0.13)\\n\\n---\\n\\n### 后转向信号灯更换步骤\\n1. **拆下车灯装置**  \\n  打开背门，拆下2个螺钉后拉出车灯装置。  \\n  ![](https://lke.cloud.tencent.com/s/2xOWgiLh?size=mid|161.0*109.7|0.13)\\n\\n2. **更换灯泡**  \\n  逆时针转动灯座拆下旧灯泡，安装新灯泡后顺时针固定。  \\n  ![](https://lke.cloud.tencent.com/s/FJpeHZEh?size=mid|161.0*109.3|0.13)\\n\\n---\\n\\n### 注意事项\\n- **安全警告**  \\n  ![](https://lke.cloud.tencent.com/s/eoSYXo8E?size=min)  \\n  - 操作前关闭发动机，避免烫伤或夹伤。  \\n  - 勿用手直接触摸灯泡玻璃部分，需用干布包裹。  \\n  - 确保灯泡瓦数与原厂一致，防止过热损坏。\\n\\n- **LED车灯提示**  \\n  若车灯为LED型（如高位刹车灯、牌照灯等），需由丰田经销商更换，不可自行操作。\\n\\n如需其他车灯（如倒车灯、雾灯）的更换指南，请提供具体类型以便进一步说明。\\n请选择是否要推荐附近的服务中心。"

		// 提取前75个字符和后75个字符
		res := TruncateSentence([]rune(content)[:150/2]) +
			string([]rune(content)[len([]rune(content))-150/2:])
		res1 := TruncateSentence([]rune(content1)[:150/2]) +
			TruncateSentence([]rune(content1)[len([]rune(content1))-150/2:])
		t.Logf("res: %v\n", res)
		t.Logf("res: %v\n", res1)
	})
}
