package helper

import (
	"reflect"
	"testing"

	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"github.com/stretchr/testify/require"
)

var queryList = []string{
	"![image-2021100816340001](https://test.com)![](http://test.com)",
	"![imag2222e-2021100816340002](http://test.com)",
	"您好，![](https://test.com)这是什么？",
	"图片中的人![](https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/public/1798372414582423552/1798560965043159040/image/WXYVUqSjqFBwqXmiocRo-1798620949861105664.jpeg)头发戴眼镜了吗？",
	"西红柿炒鸡蛋是一道经典的家常菜，以其色泽鲜艳、酸甜爽口而深受喜爱。以下是制作西红柿炒鸡蛋的步骤:11.准备食材:需要鸡蛋三到四个，西红柿2个(可根据口味酌情增加)。同时准备盐1克、糖3克、食用油适量。2.食材处理:将鸡蛋去壳打散放入碗中备用，西红柿切小块备用。3.煎鸡蛋:锅中倒入适量食用油，油热之后，倒入鸡蛋液，待鸡蛋稍稍凝固炒散后，把鸡蛋推到一边(或盛出备用)。4.炒西红柿:然后放入西红柿，煸炒均匀，加少许糖、少许盐、少许生抽煸炒均匀，然后大火收汁。5.装盘:放盐翻炒匀均后关火，即可装盘。烹饪技巧包括:![](https://lke.cloud.tencent.com/s/KuEnQZrB?size=min)喜欢吃西红柿汤汁多的可以把西红柿切小一点，烹饪的时候汁水更容易被烹饪出来。![](https://lke.cloud.tencent.com/s/zrlBYDLZ?size=min)鸡蛋里加少量清水，炒出来的鸡蛋非常嫩。![](https://lke.cloud.tencent.com/s/9NgARveE?size=min)夏季的西红柿非常多汁，稍微炒下就很多汤汁出来了，所以最后不需要加水。![](https://lke.cloud.tencent.com/s/KuEnQZrB?size=min)西红柿炒至番茄酱状，在炒至过程中可加入适量白糖中和。通过以上步骤，你可以轻松制作出美味的西红柿炒鸡蛋，享受这道经典家常菜带来的美味和满足感。",
	"您好，![](https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/public/1798372.jpg)这是什么？![](https://cos-internal.ap-guangzhou.tencentcos.cn/public/1798372.jpg)呢",
	"![](https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/public/1798372.jpg)",
	"![](https://cos-internal.ap-guangzhou.tencentcos.cn/public/1798372.jpg)",
}

// Test strings
var placeHolderStrings = []string{
	"https://I123",
	"https://I4567",
	"https://Iabc",
	"https://(I|L)",
	"https://I",
	"https://L",
	"https://I0",
	"https://L0",
}

// TestImageFile test
func TestImageFile(t *testing.T) {
	config.App().MultiModal.PlaceholderV2 = "Picture %d: <image>[Picture %d]"
	t.Run("test get all image", func(t *testing.T) {
		res := GetAllImage(queryList[0])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []string([]string(nil)), res)
		res = GetAllImage(queryList[1])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []string(nil), res)
		res = GetAllImage(queryList[2])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []string(nil), res)

		res = GetAllImage(queryList[3])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []string{"![](https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/public/1798372414582423552/1798560965043159040/image/WXYVUqSjqFBwqXmiocRo-1798620949861105664.jpeg)"}, res)

		res = GetAllImage(queryList[5])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []string{"![](https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/public/1798372.jpg)", "![](https://cos-internal.ap-guangzhou.tencentcos.cn/public/1798372.jpg)"}, res)
	})

	t.Run("test get all image urls", func(t *testing.T) {
		res := GetAllImageURLs(queryList[0])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []string(nil), res)
		res = GetAllImageURLs(queryList[1])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []string(nil), res)
		res = GetAllImageURLs(queryList[2])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []string(nil), res)

		res = GetAllImageURLs(queryList[3])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []string{"https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/public/1798372414582423552/1798560965043159040/image/WXYVUqSjqFBwqXmiocRo-1798620949861105664.jpeg"}, res)

		res = GetAllImageURLs(queryList[5])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, []string{"https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/public/1798372.jpg", "https://cos-internal.ap-guangzhou.tencentcos.cn/public/1798372.jpg"}, res)

	})

	t.Run("test is query only image", func(t *testing.T) {
		res := IsQueryOnlyImage(queryList[0])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, false, res)
		res = IsQueryOnlyImage(queryList[1])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, false, res)
		res = IsQueryOnlyImage(queryList[2])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, false, res)

		res = IsQueryOnlyImage(queryList[3])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, false, res)
		res = IsQueryOnlyImage(queryList[6])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, true, res)
		res = IsQueryOnlyImage(queryList[7])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, true, res)
	})

	t.Run("test extractLinkWithPlaceholder", func(t *testing.T) {
		c, p := ExtractLinkWithPlaceholder(queryList[4], 0, 10)
		t.Logf("res: %v\n", c)
		t.Logf("res: %v\n", p)
		require.EqualValues(t, []string{"https://lke.cloud.tencent.com/s/KuEnQZrB?size=min", "https://lke.cloud.tencent.com/s/zrlBYDLZ?size=min", "https://lke.cloud.tencent.com/s/9NgARveE?size=min"}, p)
		require.EqualValues(t, "西红柿炒鸡蛋是一道经典的家常菜，以其色泽鲜艳、酸甜爽口而深受喜爱。以下是制作西红柿炒鸡蛋的步骤:11.准备食材:需要鸡蛋三到四个，西红柿2个(可根据口味酌情增加)。同时准备盐1克、糖3克、食用油适量。2.食材处理:将鸡蛋去壳打散放入碗中备用，西红柿切小块备用。3.煎鸡蛋:锅中倒入适量食用油，油热之后，倒入鸡蛋液，待鸡蛋稍稍凝固炒散后，把鸡蛋推到一边(或盛出备用)。4.炒西红柿:然后放入西红柿，煸炒均匀，加少许糖、少许盐、少许生抽煸炒均匀，然后大火收汁。5.装盘:放盐翻炒匀均后关火，即可装盘。烹饪技巧包括:Picture 1: <image>[Picture 1]喜欢吃西红柿汤汁多的可以把西红柿切小一点，烹饪的时候汁水更容易被烹饪出来。Picture 2: <image>[Picture 2]鸡蛋里加少量清水，炒出来的鸡蛋非常嫩。Picture 3: <image>[Picture 3]夏季的西红柿非常多汁，稍微炒下就很多汤汁出来了，所以最后不需要加水。[Picture 1]西红柿炒至番茄酱状，在炒至过程中可加入适量白糖中和。通过以上步骤，你可以轻松制作出美味的西红柿炒鸡蛋，享受这道经典家常菜带来的美味和满足感。", c)

	})

	t.Run("test extractLinkWithPlaceholder2", func(t *testing.T) {
		c, p := ExtractLinkWithPlaceholder(queryList[4], 0, 2)
		t.Logf("content res: %v\n", c)
		t.Logf("placeholder res: %v\n", p)
		require.EqualValues(t, []string{"https://lke.cloud.tencent.com/s/KuEnQZrB?size=min", "https://lke.cloud.tencent.com/s/zrlBYDLZ?size=min"}, p)
		require.EqualValues(t, "西红柿炒鸡蛋是一道经典的家常菜，以其色泽鲜艳、酸甜爽口而深受喜爱。以下是制作西红柿炒鸡蛋的步骤:11.准备食材:需要鸡蛋三到四个，西红柿2个(可根据口味酌情增加)。同时准备盐1克、糖3克、食用油适量。2.食材处理:将鸡蛋去壳打散放入碗中备用，西红柿切小块备用。3.煎鸡蛋:锅中倒入适量食用油，油热之后，倒入鸡蛋液，待鸡蛋稍稍凝固炒散后，把鸡蛋推到一边(或盛出备用)。4.炒西红柿:然后放入西红柿，煸炒均匀，加少许糖、少许盐、少许生抽煸炒均匀，然后大火收汁。5.装盘:放盐翻炒匀均后关火，即可装盘。烹饪技巧包括:Picture 1: <image>[Picture 1]喜欢吃西红柿汤汁多的可以把西红柿切小一点，烹饪的时候汁水更容易被烹饪出来。Picture 2: <image>[Picture 2]鸡蛋里加少量清水，炒出来的鸡蛋非常嫩。![](https://lke.cloud.tencent.com/s/9NgARveE?size=min)夏季的西红柿非常多汁，稍微炒下就很多汤汁出来了，所以最后不需要加水。[Picture 1]西红柿炒至番茄酱状，在炒至过程中可加入适量白糖中和。通过以上步骤，你可以轻松制作出美味的西红柿炒鸡蛋，享受这道经典家常菜带来的美味和满足感。", c)

	})
}

func TestPlaceholder(t *testing.T) {
	t.Run("test isPlaceholderEnd", func(t *testing.T) {

		ok := IsPlaceholderEnd(placeHolderStrings[0])
		t.Logf("query is: %s, res: %v\n", placeHolderStrings[0], ok)
		require.EqualValues(t, true, ok)

		ok = IsPlaceholderEnd(placeHolderStrings[1])
		t.Logf("query is: %s, res: %v\n", placeHolderStrings[1], ok)
		require.EqualValues(t, true, ok)

		ok = IsPlaceholderEnd(placeHolderStrings[2])
		t.Logf("query is: %s, res: %v\n", placeHolderStrings[2], ok)
		require.EqualValues(t, false, ok)

		ok = IsPlaceholderEnd(placeHolderStrings[3])
		t.Logf("query is: %s, res: %v\n", placeHolderStrings[3], ok)
		require.EqualValues(t, false, ok)

		ok = IsPlaceholderEnd(placeHolderStrings[4])
		t.Logf("query is: %s, res: %v\n", placeHolderStrings[4], ok)
		require.EqualValues(t, true, ok)

		ok = IsPlaceholderEnd(placeHolderStrings[5])
		t.Logf("query is: %s, res: %v\n", placeHolderStrings[5], ok)
		require.EqualValues(t, true, ok)

		ok = IsPlaceholderEnd(placeHolderStrings[6])
		t.Logf("query is: %s, res: %v\n", placeHolderStrings[6], ok)
		require.EqualValues(t, true, ok)

		ok = IsPlaceholderEnd(placeHolderStrings[7])
		t.Logf("query is: %s, res: %v\n", placeHolderStrings[7], ok)
		require.EqualValues(t, true, ok)

	})
}

// Test strings
var calcStrings = []string{
	"1+1等于 [Calculator",
	"1+1 等于 [Calculator(1",
	"test[Calculator(1+",
	"test[Calculator(1+1",
	"test[Calculator(1+1)→", // 后面这两个case命中了也没事儿，实际处理的时候 已经进入POT了
	"test[Calculator(123)→",
}

func TestIsEndWithCalc(t *testing.T) {
	t.Run("test isEndWithCalc", func(t *testing.T) {
		ok := IsEndWithCalc(calcStrings[0])
		t.Logf("query is: %s, res: %v\n", calcStrings[0], ok)
		require.EqualValues(t, true, ok)

		ok = IsEndWithCalc(calcStrings[1])
		t.Logf("query is: %s, res: %v\n", calcStrings[1], ok)
		require.EqualValues(t, true, ok)

		ok = IsEndWithCalc(calcStrings[2])
		t.Logf("query is: %s, res: %v\n", calcStrings[2], ok)
		require.EqualValues(t, true, ok)

		ok = IsEndWithCalc(calcStrings[3])
		t.Logf("query is: %s, res: %v\n", calcStrings[3], ok)
		require.EqualValues(t, true, ok)

		ok = IsEndWithCalc(calcStrings[4])
		t.Logf("query is: %s, res: %v\n", calcStrings[4], ok)
		require.EqualValues(t, true, ok)

		ok = IsEndWithCalc(calcStrings[5])
		t.Logf("query is: %s, res: %v\n", calcStrings[5], ok)
		require.EqualValues(t, true, ok)
	})
}

var resultImages = []string{
	"在[Picture 5]中，标准模式下的布局窗口的命令栏位于窗口的底部。",
}

func TestResultImage(t *testing.T) {
	t.Run("test isResultImage", func(t *testing.T) {
		res, indexes := MatchAllImage(resultImages[0])
		t.Logf("query is: %s, res: %v\n", resultImages[0], res)
		require.EqualValues(t, []string{"[Picture 5]"}, res)
		require.EqualValues(t, []int{5}, indexes)
		t.Logf("query  res: %v\n", resultImage.FindAllString(resultImages[0], -1))
	})

}

var captionStrings = []string{
	"【图1】这张图片是一张垂直条形图，显示了从2016年到2023年（其中有一个“2023e”代表2023年的估计值）的母婴用品消费线上线和线下占比情况。\n\n在每个年份旁边，有两个条形：一个蓝色代表“线上母婴消费占比”，另一个绿色代表“线下母婴消费占比”。这些条形的高度表示各年度相应比例的具体数值。\n\n根据图表数据，我们可以看到：\n\n在2016年，线上母婴消费占比为77.4%，线下母婴消费占比为22.6%。\n随着时间推移，线上母婴消费的占比逐渐上升，而线下母婴消费的占比则相应下降。\n到了2023年预计，线上母婴消费占比将达到64.0%，而线下母婴消费占比将降至36.0%。\n总体来看，这个趋势表明越来越多的人选择通过互联网购买母婴用品，而非传统的实体店购物方式。【图2】这张图片是一个饼状图，标题为“2020年中国生鲜零售渠道分布”。图中展示了两个部分的数据：菜市场和其他。\n\n菜市场的占比为56.0%，即占据了整个生鲜零售市场的五分之六左右。\n其他渠道的占比为44.0%，这意味着除了菜市场之外，其他生鲜零售渠道的市场份额相对较小。\n从这个饼状图可以看出，中国的生鲜零售市场主要由菜市场主导，而其他渠道的发展空间相对有限。",
	"第1张图片捕捉了中国上海的标志性建筑——东方明珠电视塔的壮丽景色。这座由三根柱子支撑的塔楼高耸入云，其独特的球形结构在城市天际线中显得格外醒目。天空被一层薄雾笼罩，为场景增添了一种神秘感。背景中的其他建筑物虽然不如东方明珠电视塔那么显眼，但它们的存在增强了整个图像的都市氛围。图像的整体色调偏冷，以蓝色和灰色为主，突出了东方明珠电视塔作为城市地标的地位。\n第2张图片捕捉了法国巴黎的标志性建筑——埃菲尔铁塔的壮丽景色。这座由金属制成的高大结构在清澈蓝天的背景下显得格外醒目，点缀着几朵云彩。从低角度拍摄的照片强调了它的高度和威严。前景中有一片草地，为城市景观增添了一抹自然的气息。远处可以看到一些树木和建筑物，暗示了这个地标周围的繁华都市生活。图像中的颜色鲜艳，天空的蓝色与埃菲尔铁塔的金属灰色形成了美丽的对比。这张照片完美地展示了埃菲尔铁塔作为巴黎和世界著名旅游景点的地位。\n",
	"第1张图片捕捉了中国上海的标志性建筑——东方明珠电视塔的壮丽景色。这座由三根柱子支撑的塔楼高耸入云，其独特的球形结构在城市天际线中显得格外醒目。天空被一层薄雾笼罩，为场景增添了一种神秘感。背景中的其他建筑物虽然不如东方明珠电视塔那么显眼，但它们的存在增强了整个图像的都市氛围。图像的整体色调偏冷，以蓝色和灰色为主，突出了东方明珠电视塔作为城市地标的地位。\\n第2张图片捕捉了法国巴黎的标志性建筑——埃菲尔铁塔的壮丽景色。这座由金属制成的高大结构在清澈蓝天的背景下显得格外醒目，点缀着几朵云彩。从低角度拍摄的照片强调了它的高度和威严。前景中有一片草地，为城市景观增添了一抹自然的气息。远处可以看到一些树木和建筑物，暗示了这个地标周围的繁华都市生活。图像中的颜色鲜艳，天空的蓝色与埃菲尔铁塔的金属灰色形成了美丽的对比。这张照片完美地展示了埃菲尔铁塔作为巴黎和世界著名旅游景点的地位。\\n",
}

func TestCaption(t *testing.T) {
	// 按照【图1】【图2】这种格式提取图片描述
	t.Run("test extract caption", func(t *testing.T) {
		res := ExtractCaption(2, captionStrings[0])
		t.Logf("query is: %s, res: %v\n", captionStrings[0], res)
		require.EqualValues(t, []string{"这张图片是一张垂直条形图，显示了从2016年到2023年（其中有一个“2023e”代表2023年的估计值）的母婴用品消费线上线和线下占比情况。\n\n在每个年份旁边，有两个条形：一个蓝色代表“线上母婴消费占比”，另一个绿色代表“线下母婴消费占比”。这些条形的高度表示各年度相应比例的具体数值。\n\n根据图表数据，我们可以看到：\n\n在2016年，线上母婴消费占比为77.4%，线下母婴消费占比为22.6%。\n随着时间推移，线上母婴消费的占比逐渐上升，而线下母婴消费的占比则相应下降。\n到了2023年预计，线上母婴消费占比将达到64.0%，而线下母婴消费占比将降至36.0%。\n总体来看，这个趋势表明越来越多的人选择通过互联网购买母婴用品，而非传统的实体店购物方式。", "这张图片是一个饼状图，标题为“2020年中国生鲜零售渠道分布”。图中展示了两个部分的数据：菜市场和其他。\n\n菜市场的占比为56.0%，即占据了整个生鲜零售市场的五分之六左右。\n其他渠道的占比为44.0%，这意味着除了菜市场之外，其他生鲜零售渠道的市场份额相对较小。\n从这个饼状图可以看出，中国的生鲜零售市场主要由菜市场主导，而其他渠道的发展空间相对有限。"}, res)

		res = ExtractCaption(2, captionStrings[1])
		t.Logf("query is: %s, res: %v\n", captionStrings[1], res)
		require.EqualValues(t, []string{"第1张图片捕捉了中国上海的标志性建筑——东方明珠电视塔的壮丽景色。这座由三根柱子支撑的塔楼高耸入云，其独特的球形结构在城市天际线中显得格外醒目。天空被一层薄雾笼罩，为场景增添了一种神秘感。背景中的其他建筑物虽然不如东方明珠电视塔那么显眼，但它们的存在增强了整个图像的都市氛围。图像的整体色调偏冷，以蓝色和灰色为主，突出了东方明珠电视塔作为城市地标的地位。", "第2张图片捕捉了法国巴黎的标志性建筑——埃菲尔铁塔的壮丽景色。这座由金属制成的高大结构在清澈蓝天的背景下显得格外醒目，点缀着几朵云彩。从低角度拍摄的照片强调了它的高度和威严。前景中有一片草地，为城市景观增添了一抹自然的气息。远处可以看到一些树木和建筑物，暗示了这个地标周围的繁华都市生活。图像中的颜色鲜艳，天空的蓝色与埃菲尔铁塔的金属灰色形成了美丽的对比。这张照片完美地展示了埃菲尔铁塔作为巴黎和世界著名旅游景点的地位。"}, res)

	})
}

var imagePlaceholders = []string{
	"【图1】",
	"【图2】",
	"【图3】和【图4】",
}

func TestImagePlaceholders(t *testing.T) {
	t.Run("test Is Query Rewrite Image Placeholder", func(t *testing.T) {
		ok := IsQueryRewriteImagePlaceholder(imagePlaceholders[0])
		t.Logf("query is: %s, res: %v\n", imagePlaceholders[0], ok)
		require.EqualValues(t, true, ok)

		ok = IsQueryRewriteImagePlaceholder(imagePlaceholders[1])
		t.Logf("query is: %s, res: %v\n", imagePlaceholders[1], ok)
		require.EqualValues(t, true, ok)

		ok = IsQueryRewriteImagePlaceholder(imagePlaceholders[2])
		t.Logf("query is: %s, res: %v\n", imagePlaceholders[2], ok)
		require.EqualValues(t, true, ok)
	})

	t.Run("test get query rewrite image placeholders", func(t *testing.T) {
		res := GetQueryRewriteImagePlaceholder(imagePlaceholders[0])
		t.Logf("query is: %s, res: %v\n", imagePlaceholders[0], res)
		require.EqualValues(t, []string{"【图1】"}, res)

		res = GetQueryRewriteImagePlaceholder(imagePlaceholders[1])
		t.Logf("query is: %s, res: %v\n", imagePlaceholders[1], res)
		require.EqualValues(t, []string{"【图2】"}, res)

		res = GetQueryRewriteImagePlaceholder(imagePlaceholders[2])
		t.Logf("query is: %s, res: %v\n", imagePlaceholders[2], res)
		require.EqualValues(t, []string{"【图3】", "【图4】"}, res)

	})

	t.Run("test get query rewrite image index", func(t *testing.T) {
		index := GetQueryRewriteImageIndex(imagePlaceholders[0])
		t.Logf("query is: %s, index: %v\n", imagePlaceholders[0], index)
		require.EqualValues(t, []int{1}, index)

		index = GetQueryRewriteImageIndex(imagePlaceholders[1])
		t.Logf("query is: %s, index: %v\n", imagePlaceholders[1], index)
		require.EqualValues(t, []int{2}, index)

		index = GetQueryRewriteImageIndex(imagePlaceholders[2])
		t.Logf("query is: %s, index: %v\n", imagePlaceholders[2], index)
		require.EqualValues(t, []int{3, 4}, index)
	})
}

// Test strings
var fileNameStrings = []string{
	"帮我重新总结一下《中国汽车工业协会信息发布会》这篇文章",
	"帮我重新总结一下《中国汽车工业协会信息发布会》《文章2》这2篇文章",
	"帮我重新总结一下《中国汽车工业协会信息发布会》和《文章2》这2篇文章",
}

func TestFileName(t *testing.T) {
	t.Run("test get file name", func(t *testing.T) {
		res := GetFileName(fileNameStrings[0])
		t.Logf("query is: %s, res: %v\n", fileNameStrings[0], res)
		require.EqualValues(t, []string{"中国汽车工业协会信息发布会"}, res)
	})

	t.Run("test get file name", func(t *testing.T) {
		res := GetFileName(fileNameStrings[1])
		t.Logf("query is: %s, res: %v\n", fileNameStrings[1], res)
		require.EqualValues(t, []string{"中国汽车工业协会信息发布会", "文章2"}, res)
	})

	t.Run("test get file name", func(t *testing.T) {
		res := GetFileName(fileNameStrings[2])
		t.Logf("query is: %s, res: %v\n", fileNameStrings[1], res)
		require.EqualValues(t, []string{"中国汽车工业协会信息发布会", "文章2"}, res)
	})

}

// Test strings
var picStrings = []string{
	"这是图片[",
	"这是图片[Picture",
	"这是图片[Picture ",
	"这是图片[Picture 2",
	"这是图片[Picture 5",
	"这是图片[Picture 3",
	"这是图片[Picture 3]", // 不匹配，这个时候要替换为原始链接了
}

// TestIsEndWithPicture test
func TestIsEndWithPicture(t *testing.T) {
	t.Run("test isEndWithPicture", func(t *testing.T) {
		ok := IsEndWithPicture(picStrings[0])
		t.Logf("query is: %s, res: %v\n", picStrings[0], ok)
		require.EqualValues(t, true, ok)

		ok = IsEndWithPicture(picStrings[1])
		t.Logf("query is: %s, res: %v\n", picStrings[1], ok)
		require.EqualValues(t, true, ok)

		ok = IsEndWithPicture(picStrings[2])
		t.Logf("query is: %s, res: %v\n", picStrings[2], ok)
		require.EqualValues(t, true, ok)

		ok = IsEndWithPicture(picStrings[3])
		t.Logf("query is: %s, res: %v\n", picStrings[3], ok)
		require.EqualValues(t, true, ok)

		ok = IsEndWithPicture(picStrings[4])
		t.Logf("query is: %s, res: %v\n", picStrings[4], ok)
		require.EqualValues(t, true, ok)

		ok = IsEndWithPicture(picStrings[5])
		t.Logf("query is: %s, res: %v\n", picStrings[5], ok)
		require.EqualValues(t, true, ok)

		ok = IsEndWithPicture(picStrings[6])
		t.Logf("query is: %s, res: %v\n", picStrings[6], ok)
		require.EqualValues(t, false, ok)
	})
}

var testStrings1 = []string{
	"玉米得到的100元，所以老奶奶一共卖了10+50+50+100= [Calculator",
	"玉米得到的100元，所以老奶奶一共卖了10+50+50+100= [Calculato",
	"玉米得到的100元，所以老奶奶一共卖了10+50+50+100= [Calcul",
	"玉米得到的100元，所以老奶奶一共卖了10+50+50+100= [Calcu",
	"玉米得到的100元，所以老奶奶一共卖了10+50+50+100= [Calc",
	"玉米得到的100元，所以老奶奶一共卖了10+50+50+100= [Cal",
	"玉米得到的100元，所以老奶奶一共卖了10+50+50+100= [Ca",
	"玉米得到的100元，所以老奶奶一共卖了10+50+50+100= [C",
	"玉米得到的100元，所以老奶奶一共卖了10+50+50+100= [",
	"玉米得到的100元，所以老奶奶一共卖了10+50+50+100= 其他内容", // 不匹配的情况
}
var prefix = "[Calculator"

// Test strings
var picStrings1 = []string{
	"这是图片[",
	"这是图片[Pi",
	"这是图片[Picture ",
	"这是图片[Picture 2",
	"这是图片[Picture 5",
	"这是图片[Picture 3",
	"这是图片[Picture 3]", // 不匹配，这个时候要替换为原始链接了
}
var prefix2 = "[Picture"

// Test strings
func TestEndsWithPlaceholderPrefix(t *testing.T) {
	t.Run("test EndsWithPlaceholderPrefix", func(t *testing.T) {
		ok := EndsWithPlaceholderPrefix(testStrings1[0], prefix)
		t.Logf("query is: %s, res: %v\n", testStrings1[0], ok)
		require.EqualValues(t, true, ok)

		ok = EndsWithPlaceholderPrefix(picStrings1[0], prefix2)
		t.Logf("query is: %s, res: %v\n", picStrings1[0], ok)
		require.EqualValues(t, true, ok)

		ok = EndsWithPlaceholderPrefix(testStrings1[1], prefix)
		t.Logf("query is: %s, res: %v\n", testStrings1[1], ok)
		require.EqualValues(t, true, ok)

		ok = EndsWithPlaceholderPrefix(picStrings1[1], prefix2)
		t.Logf("query is: %s, res: %v\n", picStrings1[1], ok)
		require.EqualValues(t, true, ok)

		ok = EndsWithPlaceholderPrefix(testStrings1[2], prefix)
		t.Logf("query is: %s, res: %v\n", testStrings1[2], ok)
		require.EqualValues(t, true, ok)

		ok = EndsWithPlaceholderPrefix(testStrings1[3], prefix)
		t.Logf("query is: %s, res: %v\n", testStrings1[3], ok)
		require.EqualValues(t, true, ok)

		ok = EndsWithPlaceholderPrefix(testStrings1[4], prefix)
		t.Logf("query is: %s, res: %v\n", testStrings1[4], ok)
		require.EqualValues(t, true, ok)

		ok = EndsWithPlaceholderPrefix(testStrings1[5], prefix)
		t.Logf("query is: %s, res: %v\n", testStrings1[5], ok)
		require.EqualValues(t, true, ok)

		ok = EndsWithPlaceholderPrefix(testStrings1[6], prefix)
		t.Logf("query is: %s, res: %v\n", testStrings1[6], ok)
		require.EqualValues(t, true, ok)

		ok = EndsWithPlaceholderPrefix(testStrings1[7], prefix)
		t.Logf("query is: %s, res: %v\n", testStrings1[7], ok)
		require.EqualValues(t, true, ok)

		ok = EndsWithPlaceholderPrefix(testStrings1[8], prefix)
		t.Logf("query is: %s, res: %v\n", testStrings1[8], ok)
		require.EqualValues(t, true, ok)

		ok = EndsWithPlaceholderPrefix(testStrings1[9], prefix)
		t.Logf("query is: %s, res: %v\n", testStrings1[9], ok)
		require.EqualValues(t, false, ok)
	})
}

func TestHasOtherCharacters(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "1",
			args: args{str: "【图1】啊啊啊"},
			want: true,
		},
		{
			name: "2",
			args: args{str: "【图1】"},
			want: false,
		},
		{
			name: "2",
			args: args{str: "【图1】【图3】"},
			want: false,
		},
		{
			name: "2",
			args: args{str: "【图1】a【图3】"},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsQueryHasOtherCharacters(tt.args.str); got != tt.want {
				t.Errorf("HasOtherCharacters() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestExtractLinkWithPlaceholder(t *testing.T) {
	type args struct {
		content       string
		startIndex    int
		maxImageCount int
	}
	config.App().MultiModal.PlaceholderV2 = "Picture %d: <image>[Picture %d]"
	tests := []struct {
		name  string
		args  args
		want  string
		want1 []string
	}{
		{
			name: "1",
			args: args{
				content:       "西红柿炒鸡均后关火，即可装盘。烹饪技巧包括:![](https://lke.cloud.tencent.com/s/KuEnQZrB?size=min)喜欢吃西红柿汤汁多的可以把西红柿切小一点，烹饪的时候汁水更容易被烹饪出来。![](https://lke.cloud.tencent.com/s/zrlBYDLZ?size=min)鸡蛋里加少量清水，炒出来的鸡蛋非常嫩。![](https://lke.cloud.tencent.com/s/9NgARveE?size=min)夏季的西红柿非常多汁，稍微炒下就很多汤汁出来了，所以最后不需要加水。![](https://lke.cloud.tencent.com/s/KuEnQZrF?size=min)西红柿炒至番茄酱状。",
				startIndex:    0,
				maxImageCount: 5,
			},
			want: "西红柿炒鸡均后关火，即可装盘。烹饪技巧包括:Picture 1: <image>[Picture 1]喜欢吃西红柿汤汁多的可以把西红柿切小一点，烹饪的时候汁水更容易被烹饪出来。Picture 2: <image>[Picture 2]鸡蛋里加少量清水，炒出来的鸡蛋非常嫩。Picture 3: <image>[Picture 3]夏季的西红柿非常多汁，稍微炒下就很多汤汁出来了，所以最后不需要加水。Picture 4: <image>[Picture 4]西红柿炒至番茄酱状。",
			want1: []string{
				"https://lke.cloud.tencent.com/s/KuEnQZrB?size=min",
				"https://lke.cloud.tencent.com/s/zrlBYDLZ?size=min",
				"https://lke.cloud.tencent.com/s/9NgARveE?size=min",
				"https://lke.cloud.tencent.com/s/KuEnQZrF?size=min",
			},
		},
		{
			name: "2",
			args: args{
				content:       "西红柿炒鸡均后关火，即可装盘。烹饪技巧包括:![](https://lke.cloud.tencent.com/s/KuEnQZrB?size=min)喜欢吃西红柿汤汁多的可以把西红柿切小一点，烹饪的时候汁水更容易被烹饪出来。![](https://lke.cloud.tencent.com/s/zrlBYDLZ?size=min)鸡蛋里加少量清水，炒出来的鸡蛋非常嫩。![](https://lke.cloud.tencent.com/s/9NgARveE?size=min)夏季的西红柿非常多汁，稍微炒下就很多汤汁出来了，所以最后不需要加水。![](https://lke.cloud.tencent.com/s/KuEnQZrB?size=min)西红柿炒至番茄酱状。",
				startIndex:    0,
				maxImageCount: 5,
			},
			want: "西红柿炒鸡均后关火，即可装盘。烹饪技巧包括:Picture 1: <image>[Picture 1]喜欢吃西红柿汤汁多的可以把西红柿切小一点，烹饪的时候汁水更容易被烹饪出来。Picture 2: <image>[Picture 2]鸡蛋里加少量清水，炒出来的鸡蛋非常嫩。Picture 3: <image>[Picture 3]夏季的西红柿非常多汁，稍微炒下就很多汤汁出来了，所以最后不需要加水。[Picture 1]西红柿炒至番茄酱状。",
			want1: []string{
				"https://lke.cloud.tencent.com/s/KuEnQZrB?size=min",
				"https://lke.cloud.tencent.com/s/zrlBYDLZ?size=min",
				"https://lke.cloud.tencent.com/s/9NgARveE?size=min",
			},
		},
		{
			name: "3",
			args: args{
				content:       "西红柿炒鸡均后关火，即可装盘。烹饪技巧包括:![](https://lke.cloud.tencent.com/s/KuEnQZrB?size=min)喜欢吃西红柿汤汁多的可以把西红柿切小一点，烹饪的时候汁水更容易被烹饪出来。![](https://lke.cloud.tencent.com/s/zrlBYDLZ?size=min)鸡蛋里加少量清水，炒出来的鸡蛋非常嫩。![](https://lke.cloud.tencent.com/s/9NgARveE?size=min)夏季的西红柿非常多汁，稍微炒下就很多汤汁出来了，所以最后不需要加水。![](https://lke.cloud.tencent.com/s/KuEnQZrF?size=min)西红柿炒至番茄酱状。",
				startIndex:    0,
				maxImageCount: 2,
			},
			want: "西红柿炒鸡均后关火，即可装盘。烹饪技巧包括:Picture 1: <image>[Picture 1]喜欢吃西红柿汤汁多的可以把西红柿切小一点，烹饪的时候汁水更容易被烹饪出来。Picture 2: <image>[Picture 2]鸡蛋里加少量清水，炒出来的鸡蛋非常嫩。![](https://lke.cloud.tencent.com/s/9NgARveE?size=min)夏季的西红柿非常多汁，稍微炒下就很多汤汁出来了，所以最后不需要加水。![](https://lke.cloud.tencent.com/s/KuEnQZrF?size=min)西红柿炒至番茄酱状。",
			want1: []string{
				"https://lke.cloud.tencent.com/s/KuEnQZrB?size=min",
				"https://lke.cloud.tencent.com/s/zrlBYDLZ?size=min",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := ExtractLinkWithPlaceholder(tt.args.content, tt.args.startIndex, tt.args.maxImageCount)
			if got != tt.want {
				t.Errorf("ExtractLinkWithPlaceholder() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("ExtractLinkWithPlaceholder() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestGetImagePlaceholder(t *testing.T) {
	type args struct {
		query string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "1",
			args: args{query: "【图3】是什么"},
			want: []string{
				"【图3】",
			},
		},
		{
			name: "1",
			args: args{query: "【图3】是什么【图4】"},
			want: []string{
				"【图3】",
				"【图4】",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetImagePlaceholder(tt.args.query); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetImagePlaceholder() = %v, want %v", got, tt.want)
			}
		})
	}
}
