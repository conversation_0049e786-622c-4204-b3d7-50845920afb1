// Package helper md内容处理
package helper

import (
	"bytes"
	"fmt"
	"strings"

	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"github.com/gomarkdown/markdown"
	"github.com/gomarkdown/markdown/ast"
	"github.com/gomarkdown/markdown/parser"
)

// Markdown .
type Markdown struct {
	imgPlaceholder string
	imgMap         map[string]string
}

// Placeholder 占位符
type Placeholder struct {
	Key   string
	Value string
}

// Option Markdown 参数
type Option func(*Markdown)

// WithImgPlaceholder 图片占位符
func WithImgPlaceholder(p string) func(*Markdown) {
	return func(e *Markdown) {
		e.imgPlaceholder = p
	}
}

// New .
func New(opts ...Option) *Markdown {
	m := &Markdown{
		imgPlaceholder: "<ut_im##age_here_index>%d</ut_im##age_here_index>",
		imgMap:         make(map[string]string),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// ExtractLinkWithPlaceholder 使用占位符提取链接
func (m *Markdown) ExtractLinkWithPlaceholder(content []byte) ([]byte, []Placeholder) {
	x := make([]byte, len(content))
	copy(x, content)
	doc := markdown.Parse(
		x,
		parser.NewWithExtensions(parser.CommonExtensions|parser.HardLineBreak),
	)
	// 无法直接从 ast 渲染, 因为 markdown 的渲染器未完全实现
	// issue:
	//   https://github.com/gomarkdown/markdown/blob/c89a3d3cd9b5aca0ce0499d3868d94b1cf56f87b/md/md_renderer.go#L286
	// code:
	//   https://github.com/gomarkdown/markdown/issues/285
	// 完全实现后可以通过下面的方式直接渲染
	// doc, placeholders := m.extractLinkWithPlaceholder(m.ast)
	// return markdown.Render(doc, md.NewRenderer()), placeholders

	// 目前通过文本替换的方式临时处理
	// 可能导致的问题有2
	// 1. 图片和链接地址完全一致时, 链接占位符被替换为图片占位符的情况
	// 2. 图片 / 链接 带 title 时, 无法准确判断中间的空格数量, 现在默认为1个空格
	_, placeholders := m.extractLinkWithPlaceholder(doc)
	var kv []string
	for i, v := range placeholders {
		if i > config.GetMaxImageCount() {
			break
		}
		kv = append(kv, v.Value, v.Key) // 问题1
	}
	r := strings.NewReplacer(kv...)
	return []byte(r.Replace(string(content))), placeholders
}

func (m *Markdown) extractLinkWithPlaceholder(doc ast.Node) (ast.Node, []Placeholder) {
	var placeholders []Placeholder
	var placeholderKeyMap = make(map[string]struct{})
	ast.WalkFunc(doc, func(node ast.Node, entering bool) ast.WalkStatus {
		if img, ok := node.(*ast.Image); ok && entering && len(img.Destination) > 0 {
			var placeholder string
			if placeholder, ok = m.imgMap[string(img.Destination)]; !ok {
				placeholder = fmt.Sprintf(m.imgPlaceholder, len(m.imgMap))
				m.imgMap[string(img.Destination)] = placeholder
			}
			if _, ok = placeholderKeyMap[placeholder]; !ok {
				destination := img.Destination
				if isInTable(node) {
					destination = escape(destination)
				}
				title := ""
				if img.Title != nil {
					title = fmt.Sprintf(` "%s"`, string(img.Title)) // 问题2
				}
				placeholders = append(placeholders, Placeholder{
					Key:   placeholder,
					Value: string(destination) + title,
				})
				placeholderKeyMap[placeholder] = struct{}{}
			}
			img.Destination = []byte(placeholder)
		}
		return ast.GoToNext
	})

	return doc, placeholders
}

// escape replaces instances of backslash with escaped backslash in text.
func escape(text []byte) []byte {
	return bytes.Replace(text, []byte(`|`), []byte(`\|`), -1)
}

func isInTable(node ast.Node) bool {
	maxDepth := 10
	for i := 0; i < maxDepth; i++ {
		if node.GetParent() != nil {
			if _, ok := node.GetParent().(*ast.TableCell); ok {
				return true
			}
			node = node.GetParent()
		} else {
			break
		}
	}
	return false
}

// GetImagesFromPlaceholders 从占位符中获取图片
func GetImagesFromPlaceholders(placeholders []Placeholder) []string {
	images := make([]string, 0)
	for i, p := range placeholders {
		if i >= config.GetMaxImageCount() {
			return images
		}
		images = append(images, p.Value)
	}
	return images
}
