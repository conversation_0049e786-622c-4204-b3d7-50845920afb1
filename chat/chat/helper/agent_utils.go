package helper

import (
	"fmt"
	"net/url"
	"path"
	"reflect"
	"regexp"
	"slices"
	"strings"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	jsoniter "github.com/json-iterator/go"
	"golang.org/x/exp/maps"
)

var placeHolderRegexp = regexp.MustCompile(`\{\{([^{}]*?)\}\}`)

// emptyLinkRegexp 正则：(!)? 捕获可选的 '!'；\[\] 匹配空的方括号；\(([^)]+)\) 捕获 URL
var emptyLinkRegexp = regexp.MustCompile(`(!)?\[\]\(([^)]+)\)`)

// 匹配以 http:// 或 https:// 开头的 URL，直到遇到空白字符、回车、换行、反斜杠、双/单引号或右圆括号为止
var urlRegexp = regexp.MustCompile(`https?://[^\s\r\n\]"')]+`)

// ReplacePlaceholders 替换文本中的 {{name}} 占位符为对应的值
func ReplacePlaceholders(text string, values map[string]any) (allNames []string, result string) {
	// 使用正则表达式查找并替换占位符
	result = placeHolderRegexp.ReplaceAllStringFunc(text, func(match string) string {
		// 提取占位符中的键名
		key := placeHolderRegexp.FindStringSubmatch(match)[1]
		key = strings.TrimSpace(key)
		// 查找对应的值，如果存在则替换，否则保留原占位符
		if val, ok := values[key]; ok {
			allNames = append(allNames, key)
			return Object2String(val)
		}
		return ""
	})
	return
}

// Arguments2Map 参数转换为map
func Arguments2Map(arguments string) map[string]any {
	args := make(map[string]any)
	err := jsoniter.Unmarshal([]byte(arguments), &args)
	if err != nil {
		return nil
	}
	return args
}

// ReplaceInvalidHTML 替换非法的HTML
func ReplaceInvalidHTML(input string) string {
	// 定义正则：匹配 ![alt](url) 格式
	re := regexp.MustCompile(`!\[([^\]]*)\]\(([^)]+)\)`)

	result := re.ReplaceAllStringFunc(input, func(match string) string {
		// 获取alt文本与URL
		submatches := re.FindStringSubmatch(match)
		if len(submatches) < 3 {
			return match
		}
		altText := submatches[1]
		linkURL := submatches[2]

		// 解析URL，获取路径后缀
		parsedURL, err := url.Parse(linkURL)
		if err != nil {
			return match
		}
		ext := path.Ext(parsedURL.Path)
		// 如果是 HTML 文件，则认为格式不对，替换为普通链接格式
		if ext == ".html" || ext == "" { // 如果没有后缀，则认为不是图片
			if altText == "" {
				altText = "链接"
			}
			return fmt.Sprintf("[%s](%s)", altText, linkURL)
		}
		return match
	})

	return result
}

// ConvertToMap 转换为map
func ConvertToMap(data any) (map[string]any, error) {
	// 尝试将data断言为map[string]any
	m, ok := data.(map[string]any)
	if !ok {
		// 如果 data 是 []any，尝试处理数组中的每个元素
		if array, ok := data.([]any); ok {
			result := make(map[string]any)
			for _, item := range array {
				if itemMap, ok := item.(map[string]any); ok {
					// 递归处理每个 map 元素
					convertedMap, err := ConvertToMap(itemMap)
					if err != nil {
						return nil, err
					}
					for k, v := range convertedMap {
						result[k] = v
					}
				} else {
					// 如果数组中有非 map 类型的元素，直接返回错误
					return nil, fmt.Errorf("array item is not a map[string]any")
				}
			}
			return result, nil
		}
	}

	// 遍历map，递归处理嵌套的结构
	for key, value := range m {
		switch v := value.(type) {
		case []any:
			// 如果值是切片，递归处理切片中的每个元素
			for i, item := range v {
				if itemMap, ok := item.(map[string]any); ok {
					convertedMap, err := ConvertToMap(itemMap)
					if err != nil {
						return nil, err
					}
					v[i] = convertedMap
				}
			}
		case map[string]any:
			// 如果值是map，递归处理
			convertedMap, err := ConvertToMap(v)
			if err != nil {
				return nil, err
			}
			m[key] = convertedMap
		}
	}
	return m, nil
}

// HiddenSubParams 隐藏参数
func HiddenSubParams(subParams []*agent_config_server.AgentToolRspParam,
	data map[string]any) map[string]any {
	for _, param := range subParams {
		if param == nil {
			continue
		}
		if param.GetAgentHidden() { // 模型不可见
			if data != nil && data[param.GetName()] != nil {
				delete(data, param.GetName())
			}
		} else { // 模型可见，判断子参数
			if param.GetSubParams() != nil {
				// 检查 data[param.Name] 是否是 map 或 array
				if value, ok := data[param.Name]; ok && value != nil {
					switch reflect.TypeOf(value).Kind() {
					case reflect.Map:
						// 如果是 map，递归处理
						dataTemp, err := ConvertToMap(value)
						if err == nil && dataTemp != nil {
							dataTemp = HiddenSubParams(param.GetSubParams(), dataTemp)
							data[param.Name] = dataTemp
						}
					case reflect.Slice, reflect.Array:
						// 如果是数组或切片，递归处理每个元素
						if reflect.TypeOf(value).Kind() == reflect.Slice {
							arrayValue := value.([]any)
							for i, item := range arrayValue {
								if reflect.TypeOf(item).Kind() == reflect.Map {
									itemMap := item.(map[string]any)
									itemMap = HiddenSubParams(param.SubParams, itemMap)
									arrayValue[i] = itemMap
								}
							}
							data[param.Name] = arrayValue
						}
					}
				}
			}
		}
	}
	return data
}

// ExtractAllURLs 提取文本中的所有 http:// 或 https:// URL
func ExtractAllURLs(input string, urlSet map[string]struct{}) {
	if urlSet == nil {
		return
	}
	urls := urlRegexp.FindAllString(input, -1)

	for _, k := range urls {
		urlSet[k] = struct{}{}
	}
}

// ReplaceURLsWithPlaceholders 将输入文本中的所有 http:// 或 https:// URL
// 替换为 <scheme>I0、<scheme>I1…（如果 URL 有后缀，则保留后缀）
// 返回替换后的文本，以及占位符->原始 URL 的映射。
func ReplaceURLsWithPlaceholders(input string, mapping map[string]string, filter func(string) bool) string {
	if mapping == nil {
		return input
	}
	idx := len(mapping)
	replaced := urlRegexp.ReplaceAllStringFunc(input, func(rawURL string) string {
		if filter != nil && !filter(rawURL) {
			// 过滤掉不符合要求的 URL
			return rawURL
		}

		// SET, 如果对应长链的占位符已存在，则不重复生成
		for ph, v := range mapping {
			if v == rawURL {
				return ph
			}
		}
		rawURL = strings.TrimRight(rawURL, "\\")

		// 尝试解析 URL，以提取路径后缀
		u, err := url.Parse(rawURL)
		if err != nil || u == nil {
			// 解析失败，返回原始 URL
			return rawURL
		}
		var ext string
		if err == nil {
			ext = path.Ext(u.Path) // 如 ".html"；如果没有后缀，则 ext=""
		}

		// 构造占位符：保留协议、序号、再加后缀
		scheme := u.Scheme + "://" // 如果解析失败，u.Scheme 也为空，但 rawURL 本身起码含协议
		if scheme == "://" {
			// 解析失败时退回到 rawURL 的前缀
			parts := strings.SplitN(rawURL, "://", 2)
			scheme = parts[0] + "://"
		}

		ph := fmt.Sprintf("%sI%d%s", scheme, idx, ext)

		mapping[ph] = rawURL
		idx++
		return ph
	})

	return replaced
}

// ReplaceURLPlaceholders 将文本中的占位符替换为原始 URL
func ReplaceURLPlaceholders(content string, placeholders map[string]string) string {
	orderedKeys := maps.Keys(placeholders)
	slices.SortFunc(orderedKeys, func(a, b string) int { return len(b) - len(a) })
	for _, k := range orderedKeys {
		content = strings.ReplaceAll(content, k, placeholders[k])
	}
	return content
}

// FillEmptyLinkText 将 Markdown 文本中所有空文字链接（即 [](<url>)，但不含图片 ![](...)）
// 的链接文字补为“链接”，返回处理后的字符串。
func FillEmptyLinkText(input string) string {

	return emptyLinkRegexp.ReplaceAllStringFunc(input, func(match string) string {
		parts := emptyLinkRegexp.FindStringSubmatch(match)
		// parts[1]：如果是图片格式则为"!"，否则为""
		// parts[2]：匹配到的 URL
		if parts[1] == "!" {
			// 保留图片格式不做修改
			return match
		}
		// 普通链接且文字为空，补充“链接”
		return fmt.Sprintf("[链接](%s)", parts[2])
	})
}

// GetVNCURL 获取 VNC 地址
func GetVNCURL(text string) string {
	// 在整个字符串中查找第一个匹配
	match := urlRegexp.FindString(text)
	if match == "" {
		return ""
	}
	if strings.Contains(match, "token=") && strings.Contains(match, "vnc") {
		return match
	}
	return ""
}

// GetFileNameAndType 解析 rawURL，返回文件名和文件后缀（包括点号），如 "abc.txt", ".txt"
func GetFileNameAndType(rawURL string) (fileName, fileType string) {
	u, err := url.Parse(rawURL)
	if err != nil {
		return "", ""
	}
	fileName = path.Base(u.Path)            // 取最后一个斜杠之后的部分
	ext := path.Ext(fileName)               // 包含点号的后缀，如 ".txt"
	fileType = strings.TrimPrefix(ext, ".") // 去掉前导点号
	return
}

// MCPResponse 部署HTML响应
type MCPResponse struct {
	Content []MCPToolContent `json:"content"`
}

// MCPToolContent TODO
type MCPToolContent struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

// TrimVNCURL 修剪 VNC 地址
func TrimVNCURL(raw string) string {
	var p MCPResponse
	if err1 := jsoniter.Unmarshal([]byte(raw), &p); err1 != nil {
		return raw
	}
	// 如果第一个 content 包含 token=，则去掉它
	if len(p.Content) >= 2 && strings.Contains(p.Content[0].Text, "token=") {
		p.Content = p.Content[1:]
	}
	out, err := jsoniter.Marshal(p)
	if err != nil {
		return raw
	}
	return string(out)
}

// BrowserResponse 浏览器工具返回
type BrowserResponse struct {
	ActionResult   string `json:"action_result"`
	PageScreenshot string `json:"page_screenshot"`
}

// TrimPageContentSection 接受原始 JSON 字符串 raw，
// 在第二个 content 的 Text 中去掉 "\n\n>>>>> Page Content" 及之后的所有内容。
// 返回修剪后的 JSON 字符串或错误。
func TrimPageContentSection(raw string) string {

	var p MCPResponse
	if err := jsoniter.Unmarshal([]byte(raw), &p); err != nil {
		marker1 := " Page Content"
		if idx := strings.Index(raw, marker1); idx != -1 {
			raw = raw[:idx] + "\"}]}"
			return TrimVNCURL(raw)
		}
		return raw
	}

	// 若至少有两个 content，则修剪第二个的 Text
	if len(p.Content) >= 2 {
		const marker = "\n\n>>>>> Page Content"

		var br BrowserResponse
		if err := jsoniter.UnmarshalFromString(p.Content[1].Text, &br); err != nil {
			return ""
		}

		if idx := strings.Index(br.ActionResult, marker); idx != -1 {
			p.Content[1].Text = br.ActionResult[:idx]
		}

		// 如果第一个 content 包含 token=，则去掉它
		if len(p.Content) >= 1 && strings.Contains(p.Content[0].Text, "token=") {
			p.Content = p.Content[1:]
		}
	}

	out, err := jsoniter.Marshal(p)
	if err != nil {
		return raw
	}
	return string(out)
}
