package helper

// Throttle 节流器
type Throttle interface {
	Hit(l int, force bool) bool
	IsFirstReply() bool
}

// NewThrottle 构造节流器
func NewThrottle(step int) Throttle {
	return &throttle{step: step, counter: step}
}

// NewThrottleList 构造节流器切片
func NewThrottleList() []Throttle {
	return []Throttle{}
}

// Throttle 节流器
type throttle struct {
	step    int
	counter int
	total   int
}

// Hit 判断是否需要输出
func (t *throttle) Hit(l int, force bool) bool {
	defer func() {
		t.total += l
	}()
	if l >= 0 && (l >= t.counter || force) {
		if t.step > 0 {
			t.counter = (l - l%t.step) + t.step
		}
		return true
	}
	return false
}

// IsFirstReply 是否是第一次回复
func (t *throttle) IsFirstReply() bool {
	return t.total == 0
}
