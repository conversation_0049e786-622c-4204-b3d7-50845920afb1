package helper

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestMarkdown_ExtractLinkWithPlaceholder(t *testing.T) {
	t.Run("extract link with placeholder", func(t *testing.T) {
		m := New(
			WithImgPlaceholder("<ut_im##age_here_index>%d</ut_im##age_here_index>"),
		)
		c, p := m.ExtractLinkWithPlaceholder([]byte(`[link0](http://link0.com)
![img0](http://img0.com)
[link1](http://link1.com)
[](http://link0.com)
![img1](http://img1.com)

[]()
![]()

[![img1](http://img1.com)](http://link2.com)

| key | value               |
| ---- | ------------------ |
| 链接 | [![](http://img3.com?size=max\|154.8*37.0\|+Inf "test")](http://link3.com) |
| 图片 | ![](http://img3.com?size=max\|154.8*37.0\|+Inf "test") |
`))
		require.EqualValues(t, []Placeholder{
			{Key: "<ut_im##age_here_index>0</ut_im##age_here_index>", Value: "http://img0.com"},
			{Key: "<ut_im##age_here_index>1</ut_im##age_here_index>", Value: "http://img1.com"},
			{Key: "<ut_im##age_here_index>2</ut_im##age_here_index>", Value: `http://img3.com?size=max\|154.8*37.0\|+Inf "test"`},
		}, p)
		require.EqualValues(t, `[link0](http://link0.com)
![img0](<ut_im##age_here_index>0</ut_im##age_here_index>)
[link1](http://link1.com)
[](http://link0.com)
![img1](<ut_im##age_here_index>1</ut_im##age_here_index>)

[]()
![]()

[![img1](<ut_im##age_here_index>1</ut_im##age_here_index>)](http://link2.com)

| key | value               |
| ---- | ------------------ |
| 链接 | [![](<ut_im##age_here_index>2</ut_im##age_here_index>)](http://link3.com) |
| 图片 | ![](<ut_im##age_here_index>2</ut_im##age_here_index>) |
`, string(c))
	})
	t.Run("sample 1", func(t *testing.T) {
		m := New(
			WithImgPlaceholder("<ut_im##age_here_index>%d</ut_im##age_here_index>"),
		)
		c, p := m.ExtractLinkWithPlaceholder([]byte("角色介绍: \n\n角色介绍\n 播报\n\n\n|  |  " +
			"（おかべ りんたろう）  配音： 宫野真守  LabMem No." +
			"001  1991年12月14日出生，18岁。本作的主人公。东京机电大学1" +
			"年级生。  自称狂妄的疯狂科学家，抱着作为恶人般目中无人的态度。时常穿着白大褂。  日常生活中，" +
			"常唐突地听电话并说着“机关的阴谋”之类的话，他的设定几乎就是所谓的“中二病”。在秋叶原成立了“未来道具研究所”，" +
			"发明用途不明的道具。不识气氛，本质上是个好人。非常重视实验室的伙伴们。  第二个名字是“凤凰院凶真（ほうおういん きょうま）”。" +
			"但是，桥田跟真由里直呼本名“冈伦（オカリン）”。喜欢喝被称作“智慧饮料”的Dr Pepper。 " +
			" |\n| --- | --- |\n| ![](https://oaqbot.qidian.qq.com/s/CVJDjVs1?size=max\\|487.8*586.4\\|0.61) | " +
			"着巫女服。被冈部称呼为“琉华子（ルカ子）”，还被收为弟子。受冈伦鼓励下拿着名为“妖刀·五月雨”的素振刀不停练习。" +
			"是个相当害羞的人，经常被真由理拜托去COSPLAY，但总是拒绝。 |\n\n"))
		fmt.Println(string(c))
		fmt.Println(p)
	})
	t.Run("sample 2", func(t *testing.T) {
		m := New(
			WithImgPlaceholder("<ut_im##age_here_index>%d</ut_im##age_here_index>"),
		)
		c, p := m.ExtractLinkWithPlaceholder([]byte(`可以通过插入图片、图表、插图等方式将文字和图像结合起来呈现。\n\n![tR2eTLU0veyHqBD0y7fV-2777594556.jpg](https://qidian-qbot-test-1251316161.cos.ap-guangzhou.myqcloud.com/public/tR2eTLU0veyHqBD0y7fV-2777594556.jpg)`))
		fmt.Println(string(c))
		fmt.Println(p)
	})
	t.Run("xx", func(t *testing.T) {
		m := New(
			WithImgPlaceholder("<ut_im##age_here_index>%d</ut_im##age_here_index>"),
		)
		c, p := m.ExtractLinkWithPlaceholder([]byte("150万测试数据: \n尽管也曾有过分裂割据的状态，但时间较短，而国家统一的局面则一直居于主导地位。这样一种持续数千年不散的大一统中央集权的政治格局，有其深刻的原因：（1）农耕文化及在其基础上形成的儒家“大一统”观念，奠定了中央集权的文化底蕴；（2）宗法制度和官僚制度加固了中央集权的政治基础；（3）民族融合和对中华民族的认同，形成了中央集权国家的民族凝聚力；（4）较为封闭的地理环境和抵御江河泛滥的需要，为中央集权国家提供了自然条件和驱动力；（5）近代以来的外族入侵，危及了中华民族的生存，自保求存的民族生命本能，进一步加强了各族人民的团结。长期的历史传统，决定了我们必须建立单一制的国家结构形式。\r\n\n2． 民族原因。我国是一个多民族国家，各民族的历史状况和民族关系决定了在我国的具体条件下，不适宜采取联邦制，而应该采取单一制的国家结构形式。"))
		fmt.Println(string(c))
		fmt.Println(p)
	})
}
