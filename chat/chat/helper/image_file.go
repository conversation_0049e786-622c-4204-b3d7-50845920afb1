// Package helper 工具函数包
// Package eventbus eventbus/image_file.go
// by gaussguan
// 请求仅仅包含图片和文件的情况
package helper

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
)

// 计算器正则
var calcRegexp = regexp.MustCompile(`\[Calculator.*`)

// Picture正则,详细见单侧用例
var pictureRegexp = regexp.MustCompile(`\[(Picture(\s(\d+)?)?)?$`)

// 占位符正则
var placeholder = regexp.MustCompile(`https://(I|L)\d*$`)

// 正则匹配，校验query的markdown格式的图片，只有是cos的图片才需要提取
var mdImage = regexp.MustCompile(`!\[.*?\]\((http|https)://[^)]*(myqcloud.com|tencentcos.cn|tencent.com)[^)]*\)`)

// 正则匹配，markdown格式的图片
var privateMDImage = regexp.MustCompile(`!\[.*?\]\((http|https)://.*?\)`)

// 正则匹配，匹配结果中的图片 例如： 在[Picture 5]中，标准模式下的布局窗口的命令栏位于窗口的底部。
var resultImage = regexp.MustCompile(`\[Picture \d+\]`)

// 正则匹配，匹配结果中的图片的索引
var resultImageIndex = regexp.MustCompile(`\d+`)

// 正则匹配，匹配markdown里面的URL
var mdURL = regexp.MustCompile(`\((http|https)://.*?\)`)

// queryRewriteImagePlaceholder query改写后图片占位符
var queryRewriteImagePlaceholder = regexp.MustCompile(`【图(\d+)】`)

// 正则匹配，除占位符以外，是否还有其他文本
var queryRewriteImageStr = regexp.MustCompile(`^【图\d+】(?:【图\d+】)*$`)

// 正则匹配，包含文件名
var hasFileName = regexp.MustCompile(`《(.+?)》`)

var contentPlaceholder = regexp.MustCompile(`<ut_im##age_here_index>(.*?)</ut_im##age_here_index>`)

// ExtractLinkWithPlaceholder 提取带有占位符的链接
func ExtractLinkWithPlaceholder(content string, startIndex, maxImageCount int) (string, []string) {
	var images []string                   // 图片url
	mdImages := GetAllImage(content)      // md 图片链接
	mdImages = RemoveDuplicates(mdImages) // 去重
	for i, item := range mdImages {       // ![](http://www.baidu.com/img.jpg)
		if i >= maxImageCount {
			break
		}
		num := startIndex + i + 1
		// 替换第一个: Picture %d: <image>[Picture %d]
		content = strings.Replace(content, item, fmt.Sprintf(config.App().MultiModal.PlaceholderV2, num, num), 1)
		// 替换剩下的: [Picture %d]
		content = strings.Replace(content, item, fmt.Sprintf("[Picture %d]", num), -1)
		imageURL := mdURL.FindString(item) // (http://www.baidu.com/img.jpg)
		images = append(images, imageURL[1:len(imageURL)-1])
	}
	return content, images
}

// GetAllImage 获取所有的图片
func GetAllImage(query string) []string {
	if config.App().IsPrivate {
		return privateMDImage.FindAllString(query, -1)
	}
	return mdImage.FindAllString(query, -1)
}

// IsQueryContainsImage Query中是否包含图片
func IsQueryContainsImage(query string) bool {
	if len(query) < 1 {
		return false
	}
	return len(GetAllImage(query)) > 0
}

// GetAllImageURLs 获取所有的图片URL
func GetAllImageURLs(query string) []string {
	var imageURLs []string
	for _, item := range GetAllImage(query) {
		url := mdURL.FindString(item)
		imageURLs = append(imageURLs, url[1:len(url)-1])
	}
	return imageURLs
}

// IsQueryOnlyImage 判断是否是仅仅包含图片的情况
func IsQueryOnlyImage(query string) bool {
	if len(query) < 1 {
		return false
	}
	imageURL := GetAllImage(query)
	temp := ""
	for _, url := range imageURL {
		temp += url
	}
	return temp == query
}

// IsImageOrFile 判断是否是图片或者文件，没有Query的情况
func IsImageOrFile(imageURLs, fileURLs []string) bool {
	return IsImage(imageURLs) && IsFile(fileURLs)

}

// IsImage 判断是否是图片
func IsImage(imageURLs []string) (yes bool) {
	yes = len(imageURLs) > 0
	for _, url := range imageURLs {
		if !strings.HasPrefix(url, "http") {
			return false
		}
	}
	return yes
}

// IsFile 判断是否是文件
func IsFile(fileURLs []string) (yes bool) {
	yes = len(fileURLs) > 0
	for _, url := range fileURLs {
		if !strings.HasPrefix(url, "http") {
			return false
		}
	}
	return yes
}

// IsPlaceholderEnd 是否以占位符结尾
func IsPlaceholderEnd(query string) bool {
	return placeholder.MatchString(query)
}

// IsEndWithCalc 是否以计算器结尾
func IsEndWithCalc(query string) bool {
	return calcRegexp.MatchString(query)
}

// IsEndWithPicture 是否以图片结尾
func IsEndWithPicture(query string) bool {
	return pictureRegexp.MatchString(query)
}

// MatchAllImage 匹配所有的图片
func MatchAllImage(query string) (res []string, indexes []int) {
	res = resultImage.FindAllString(query, -1)
	for _, item := range res {
		strIndex := resultImageIndex.FindAllString(item, -1)
		if len(strIndex) == 0 {
			indexes = append(indexes, 0)
			continue
		}
		index, _ := strconv.Atoi(strIndex[0])
		indexes = append(indexes, index)
	}
	return res, indexes
}

// ExtractCaption 提取caption
func ExtractCaption(count int, query string) (res []string) {
	log.InfoContextf(trpc.BackgroundContext(), "ExtractCaption query: %s, count: %d", query, count)
	for i := 1; i <= count; i++ {
		tagStart := fmt.Sprintf("【图%d】", i)
		if !strings.Contains(query, tagStart) {
			break
		}

		tagEnd := fmt.Sprintf("【图%d】", i+1)
		start := strings.Index(query, tagStart) + len(tagStart)
		end := strings.Index(query, tagEnd)

		if end == -1 {
			end = len(query)
			res = append(res, query[start:end])
			break
		}

		res = append(res, query[start:end])

	}

	if len(res) == 0 {
		res = strings.Split(query, "\n")
	}
	finalRes := make([]string, 0)
	for _, item := range res {
		if item == "" {
			continue
		}
		finalRes = append(finalRes, item)
	}
	log.InfoContextf(trpc.BackgroundContext(), "ExtractCaption res: %s", Object2String(finalRes))
	return finalRes
}

// IsQueryRewriteImagePlaceholder 是否是query改写后图片占位符
func IsQueryRewriteImagePlaceholder(query string) bool {
	return queryRewriteImagePlaceholder.MatchString(query)
}

// GetQueryRewriteImagePlaceholder 获取query改写后图片占位符
func GetQueryRewriteImagePlaceholder(query string) (res []string) {
	return queryRewriteImagePlaceholder.FindAllString(query, -1)
}

// GetQueryRewriteImageIndex 获取query改写后图片占位符的索引
func GetQueryRewriteImageIndex(query string) (indexs []int) {
	matches := queryRewriteImagePlaceholder.FindAllStringSubmatch(query, -1)
	// fmt.Printf("matches: %v\n", matches)
	for _, match := range matches {
		// 提取数字部分并转为int
		if len(match) < 2 {
			continue
		}
		num, _ := strconv.Atoi(match[1])
		indexs = append(indexs, num)
	}
	return indexs
}

// GetImagePlaceholder 获取图片占位符
func GetImagePlaceholder(query string) []string {
	matches := queryRewriteImagePlaceholder.FindAllString(query, -1)
	return matches
}

// IsQueryContainsFileName 是否包含文件名
func IsQueryContainsFileName(query string) bool {
	return hasFileName.MatchString(query)
}

// GetFileName 获取文件名
func GetFileName(query string) []string {
	matchs := hasFileName.FindAllStringSubmatch(query, -1)
	var res []string
	for _, match := range matchs {
		if len(match) < 1 {
			continue
		}
		res = append(res, match[1])
	}
	return res
}

// EndsWithPlaceholderPrefix 判断字符串是否以占位符前缀结尾
func EndsWithPlaceholderPrefix(s, placeholder string) bool {
	n := len(s)
	m := len(placeholder)
	minLen := n
	if m < n {
		minLen = m
	}
	for i := 1; i <= minLen; i++ {
		if s[n-i:] == placeholder[:i] {
			return true
		}
	}
	return false
}

// ReplaceDomain 替换域名
func ReplaceDomain(links []string) []string {
	var newLinks []string
	for _, link := range links {
		newLink := strings.Replace(link, "myqcloud.com", "tencentcos.cn", -1)
		newLinks = append(newLinks, newLink)
	}
	return newLinks
}

// RemoveContentPlaceholder 移除文本占位符
func RemoveContentPlaceholder(content string) string {
	newContent := contentPlaceholder.ReplaceAllString(content, "")
	return newContent
}

// IsQueryHasOtherCharacters 除图片占位符还有其他字符
func IsQueryHasOtherCharacters(str string) bool {
	match := queryRewriteImageStr.MatchString(str)
	return !match
}

// RemoveDuplicates 去重
func RemoveDuplicates(arr []string) []string {
	uniqueMap := make(map[string]bool)
	uniqueArr := []string{}

	for _, str := range arr {
		if !uniqueMap[str] {
			uniqueMap[str] = true
			uniqueArr = append(uniqueArr, str)
		}
	}

	return uniqueArr
}
