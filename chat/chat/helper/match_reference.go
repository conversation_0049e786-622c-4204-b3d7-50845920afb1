package helper

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
)

// 正则匹配，markdown格式的图片
var refer = regexp.MustCompile(`【passage (\d+)】`)

// 处理[x](@ref)标号
var squareBracketsRefRegx = regexp.MustCompile(`\[(\d+)\]\(@ref\)`)

// 处理[^1][^2]标号
var squareBracketsABCRegx = regexp.MustCompile(`\[\^(\d+)\]`)

// 处理[1,2,3](@ref)标号
var squareBracketsABCRefRegx = regexp.MustCompile(`\[(\d+(?:,\d+)*)\]\(@ref\)`)

// 处理passage标号
var passageRegx = regexp.MustCompile(`(?i)passage\s*(?:【(\d+)】|\[(\d+)\]|(\d+))`)

// 处理passage 1到2标号
var passageAtoBRegx = regexp.MustCompile(`(?i)passage\s*(\d+)\s*到\s*(\d+)`)

// 处理<x>(@ref)标号
var refRegx = regexp.MustCompile(`<(\d+)>\(@ref\)`)

// GetAllReference 获取所有的引用
func GetAllReference(query string) []string {
	return refer.FindAllString(query, -1)
}

// GetAllSubMatch 获取所有子匹配
func GetAllSubMatch(botBizID uint64, query string) (ans []int) {
	ans = make([]int, 0)
	patterns := config.GetReferencePattern(botBizID)
	// mock test
	// patterns := []string{
	//	`【passage (\d+)】`,
	//	`【引用passage (\d+)】`,
	//	`（passage (\d+)）`,
	//	`\n以上信息来源于【passage (\d+)】(。|和【passage (\d+)】。)`,
	//	`引用:【(?:passage\s+\d+(?:,\s*passage\s+\d+)*)】`,
	//	`passage\s+(\d+)`,
	//	`\n引用的passage编号：(\d+)`,
	//	`\n引用的passage编号是：(\d+)`,
	//	`\n\*\*引用passage编号\*\*：\[(\d+)\]`,
	//	`\[passage (\d+)\] `,
	//	`\n引用的passage编号是：`,
	//	`\n引用的passage编号为：`,
	//	`\n\*\*引用passage编号：\*\*`,
	// }
	for _, pattern := range patterns {
		refer = regexp.MustCompile(pattern)
		matches := refer.FindAllStringSubmatch(query, -1)
		for _, match := range matches {
			// 提取数字部分并转为int
			if len(match) < 2 {
				continue
			}
			num, _ := strconv.Atoi(match[1])
			if !ContainsInt(ans, num) { // 去重
				ans = append(ans, num)
			}
		}
	}

	return ans
}

// RemoveReference 移除引用
// 特殊处理一下case
// https://tapd.woa.com/qrobot_case/bugtrace/bugs/view?bug_id=1070108476127821993&jump_count=1
func RemoveReference(botBizID uint64, query string) string {
	patterns := config.GetReferencePattern(botBizID)
	// mock test
	// patterns := []string{
	//	`【passage (\d+)】`,
	//	`【引用passage (\d+)】`,
	//	`（passage (\d+)）`,
	//	`\n以上信息来源于(。|和。)`,
	//	`引用:【(?:passage\s+\d+(?:,\s*passage\s+\d+)*)】`,
	//	`passage\s+(\d+)`,
	//	`\n引用的passage编号：(\d+)`,
	//	`\n引用的passage编号是：(\d+)`,
	//	`\n\*\*引用passage编号\*\*：\[(\d+)\]`,
	//	`\[passage (\d+)\] `,
	//	`\n引用的passage编号是：`,
	//	`\n引用的passage编号为：`,
	//	`引用形式：在答案最后给出引用的passage，格式引用:【passage x】。`,
	//	`\n\*\*引用passage编号：\*\*`,
	//	`引用:`,
	//	`引用：`,
	// }

	for _, pattern := range patterns {
		refer = regexp.MustCompile(pattern)
		query = refer.ReplaceAllString(query, "")
	}
	// refer = regexp.MustCompile(`\n以上信息来源于【passage (\d+)】(。|和【passage (\d+)】。)`) // 混元答案
	// query = refer.ReplaceAllString(query, "")
	//
	// refer = regexp.MustCompile(`【引用passage (\d+)】`) // badcase1
	// query = refer.ReplaceAllString(query, "")
	//
	// refer = regexp.MustCompile(`【passage (\d+)】`)
	// return refer.ReplaceAllString(query, "")
	return query
}

// splitAndReplaceReferencesABCCommon 处理多个引用, [1]、[1,2,3]和[1,2,3](@ref)都转化成<x>(@ref)的形式
func splitAndReplaceReferencesABCCommon(str string, re *regexp.Regexp) string {
	replaceFunc := func(s string) string {
		matches := re.FindStringSubmatch(s)
		if len(matches) < 2 {
			return s
		}
		numbersStr := matches[1]
		numbers := strings.Split(numbersStr, ",")
		newStrs := make([]string, len(numbers))
		for i, number := range numbers {
			newStrs[i] = fmt.Sprintf("<%s>(@ref)", number)
		}
		return strings.Join(newStrs, "")
	}
	return re.ReplaceAllStringFunc(str, replaceFunc)
}

// MatchSearchResults 匹配搜索结果
func MatchSearchResults(ctx context.Context, str string) (string, []int, []int) {
	tik := time.Now()
	defer func() {
		timeSince := time.Since(tik)
		if timeSince > time.Millisecond*10 {
			log.WarnContextf(ctx, "MatchSearchResults cost: %s", timeSince)
		} else if timeSince > time.Millisecond*1 {
			log.InfoContextf(ctx, "MatchSearchResults cost: %s", timeSince)
		} else {
			log.DebugContextf(ctx, "MatchSearchResults cost: %s", timeSince)
		}
	}()
	str = MatchLLMRefs(str)
	return getPositionsAndIndexs(refRegx, str)
}

func getPositionsAndIndexs(re *regexp.Regexp, str string) (string, []int, []int) {
	var indexes []int
	replaceFunc := func(s string) string {
		matches := re.FindStringSubmatch(s)
		if len(matches) < 2 {
			return s
		}
		number, _ := strconv.Atoi(matches[1])
		indexes = append(indexes, number)
		return fmt.Sprintf("%d", number)
	}
	re.ReplaceAllStringFunc(str, replaceFunc)
	subst := re.Split(str, -1)

	var result []string
	positions := make([]int, 0, len(subst))
	for i, substr := range subst {
		result = append(result, substr)
		if i == 0 {
			positions = append(positions, utf8.RuneCountInString(substr))
		} else {
			positions = append(positions, positions[i-1]+utf8.RuneCountInString(substr))
		}
	}
	content := strings.Join(result, "")
	// positions长度会比indexes多1
	return content, positions, indexes
}

// splitAndReplaceReferencesAtoBCommon 处理多个引用，[1]到[2] 转化成<x>(@ref)的形式
func splitAndReplaceReferencesAtoBCommon(str string, re *regexp.Regexp) string {
	output := re.ReplaceAllStringFunc(str, func(match string) string {
		// 提取数字
		parts := re.FindStringSubmatch(match)
		if len(parts) == 3 {
			a, _ := strconv.Atoi(parts[1])
			b, _ := strconv.Atoi(parts[2])
			// 检查 a <= b 的条件
			if a <= b {
				var sb strings.Builder
				for i := a; i <= b; i++ {
					sb.WriteString(fmt.Sprintf("<%d>(@ref)", i))
				}
				return sb.String()
			}
		}
		return match // 如果不符合条件，返回原始字符串
	})
	return output
}

// MatchLLMRefs 匹配大模型返回的引用标号，统一转化成<x>(@ref)的形式
func MatchLLMRefs(str string) string {
	// 1. 处理[2,4,7](@ref)标号
	str = splitAndReplaceReferencesABCCommon(str, squareBracketsABCRefRegx)
	// 2. 处理[1](@ref)标号
	str = squareBracketsRefRegx.ReplaceAllString(str, "<$1>(@ref)")
	// 3. 处理 passage 1到2标号
	str = splitAndReplaceReferencesAtoBCommon(str, passageAtoBRegx)
	// 4. 处理passage 1，passage [2] passage 【3】标号
	str = passageRegx.ReplaceAllStringFunc(str, func(match string) string {
		// 提取数字
		if passageRegx.FindStringSubmatch(match)[1] != "" {
			return fmt.Sprintf("<%s>(@ref)", passageRegx.FindStringSubmatch(match)[1])
		} else if passageRegx.FindStringSubmatch(match)[2] != "" {
			return fmt.Sprintf("<%s>(@ref)", passageRegx.FindStringSubmatch(match)[2])
		}
		return fmt.Sprintf("<%s>(@ref)", passageRegx.FindStringSubmatch(match)[3])
	})

	// 5. 处理[^1][^2]标号
	str = splitAndReplaceReferencesABCCommon(str, squareBracketsABCRegx)
	return str
}
