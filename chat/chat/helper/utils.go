// Package helper 对象转Json字符串
package helper

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	secapi "git.woa.com/sec-api/go/scurl"
	jsoniter "github.com/json-iterator/go"
)

// Object2String 对象转Json字符串
func Object2String(req any) string {
	b, _ := jsoniter.Marshal(req)
	return string(b)
}

// Object2StringEscapeHTML 对象转Json字符串
func Object2StringEscapeHTML(req any) string {
	buff := &bytes.Buffer{}
	enc := jsoniter.NewEncoder(buff)
	enc.SetEscapeHTML(false)
	_ = enc.Encode(req)
	return buff.String()
}

// UnEscapeHTMLJson HTML中使用的特殊字符(如'<','>','&'等)不被转义 大小写敏感
func UnEscapeHTMLJson() jsoniter.API {
	return jsoniter.Config{EscapeHTML: false, CaseSensitive: true}.Froze()
}

// Filter returns a new slice containing all elements of slice s that satisfy all filters.
func Filter[E any](s []E, filters ...func(E) bool) (result []E) {
next:
	for _, v := range s {
		for _, fn := range filters {
			if !fn(v) {
				continue next
			}
		}
		result = append(result, v)
	}
	return
}

// GetUint64FromString 字符串转uint64
func GetUint64FromString(str string) uint64 {
	uInt64Data, err := strconv.ParseUint(str, 10, 64)
	if err != nil {
		log.WarnContextf(trpc.BackgroundContext(), "strconv.ParseUint failed, error: %v", err)
	}
	return uInt64Data
}

// GetUint64FromStringWithError 字符串转uint64
func GetUint64FromStringWithError(str string) (uint64, error) {
	uInt64Data, err := strconv.ParseUint(str, 10, 64)
	if err != nil {
		return 0, err
	}
	return uInt64Data, nil
}

// ContainsInt 判断是否在 slice 中
func ContainsInt(items []int, item int) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}

// TruncatedSummary 截断摘要，取前100个字
func TruncatedSummary(summary string) string {
	tag := "核心观点包含：\n"
	start := strings.Index(summary, tag)
	if start != -1 {
		summary = summary[start+len(tag):]
	}
	summary1, summary2 := summary, summary
	newlineIndex := strings.Index(summary, "\n")
	if newlineIndex != -1 {
		summary1 = summary[:newlineIndex]
	}
	if utf8.RuneCountInString(summary) > 100 {
		b := []rune(summary)
		summary2 = string(b[:100])
	}
	if len(summary1) > len(summary2) {
		return summary2
	}
	return summary1
}

// Contains 判断是否在 slice 中
func Contains[T comparable](arr []T, elem T) bool {
	for _, v := range arr {
		if v == elem {
			return true
		}
	}
	return false
}

// When 判断条件是否成立，成立返回yes，不成立返回no
func When[V any](cond bool, yes, no V) V {
	if cond {
		return yes
	}
	return no
}

const timeFormat = "2006-01-02 15:04:05"

// GetStringTime 获取时间格式
func GetStringTime() string {
	return time.Now().Format(timeFormat)
}

// SnakeToCamelCase 将蛇形命名转换为小驼峰命名。
// 示例：code_interpreter -> codeInterpreter
func SnakeToCamelCase(s string) string {
	parts := strings.Split(s, "_")
	if len(parts) == 0 {
		return s
	}
	// 第一个单词全部小写
	camel := strings.ToLower(parts[0])
	// 后续单词首字母大写，其余小写
	for i := 1; i < len(parts); i++ {
		if len(parts[i]) > 0 {
			camel += strings.ToUpper(parts[i][:1]) + strings.ToLower(parts[i][1:])
		}
	}
	return camel
}

// SnakeToPascalCase 将蛇形命名转换为大驼峰命名。
// 示例：code_interpreter -> CodeInterpreter
func SnakeToPascalCase(s string) string {
	parts := strings.Split(s, "_")
	if len(parts) == 0 {
		return s
	}
	var pascal string
	for _, part := range parts {
		if len(part) > 0 {
			pascal += strings.ToUpper(part[:1]) + strings.ToLower(part[1:])
		}
	}
	return pascal
}

// TruncateSentence 按标点符号截断句子
func TruncateSentence(sentence []rune) string {
	// 标点符号map
	punctuationMarksMap := map[rune]bool{'。': true, '，': true, '！': true, '？': true, '；': true, '：': true,
		'、': true, ',': true, '.': true, '!': true, '?': true, ';': true, ':': true, '…': true, ' ': true}
	length := len(sentence)
	for i := length; i > 0; i-- {
		if _, ok := punctuationMarksMap[sentence[i-1]]; ok { // 如果没有标点 原样返回
			sentence = sentence[:i-1] // 丢弃标点符号
			break
		}
	}
	return string(sentence)
}

// FetchJSONFromURL 从URL获取JSON
func FetchJSONFromURL(ctx context.Context, url string) string {
	// 发送HTTP请求获取HTML内容
	safeClient := secapi.NewSafeClient(secapi.WithUnsafeDomain(config.GetUnsafeDomain()),
		secapi.WithAllowPorts([]string{"80", "443"}))

	httpReq, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.ErrorContextf(ctx, "failed to create request: %v", err)
		return ""
	}

	// 基于安全请求的客户端，发起安全请求
	resp, err := safeClient.Do(httpReq)
	if err != nil {
		log.ErrorContextf(ctx, "failed to fetch URL: %v", err)
		return ""
	}

	defer resp.Body.Close()

	// 读取HTML响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.ErrorContextf(ctx, "failed to read body: %v", err)
		return ""
	}

	// 使用正则表达式提取JSON部分
	re := regexp.MustCompile(`(?s)option_\w+\s*=\s*(\{.*?\});`)
	matches := re.FindSubmatch(body)
	if len(matches) < 2 {
		log.ErrorContextf(ctx, "failed to parse JSON from URL: %s", url)
		return ""
	}
	return string(matches[1])
}

// ParseRFC3339ToUnix 将 RFC3339 时间字符串转换为 Unix 时间戳（秒）
func ParseRFC3339ToUnix(s string) int64 {
	t, err := time.Parse(time.RFC3339, s)
	if err != nil {
		return 0
	}
	return t.Unix()
}

// ParseRawQuery 解析rawQuery
func ParseRawQuery(rawQuery string) (map[string]string, error) {
	if rawQuery == "" {
		return map[string]string{}, nil
	}
	values, err := url.ParseQuery(rawQuery)
	if err != nil {
		return nil, err
	}
	result := make(map[string]string, len(values))
	for k, v := range values {
		if len(v) > 0 {
			result[k] = v[0]
		}
	}
	return result, nil
}
