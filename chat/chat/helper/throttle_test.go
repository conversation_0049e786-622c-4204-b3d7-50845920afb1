package helper

import "testing"

func Test_throttle_Hit(t *testing.T) {
	type seq struct {
		l     int
		force bool
		want  bool
	}
	tests := []struct {
		name string
		step int
		seqs []seq
	}{
		{"Normal", 5, []seq{
			{0, false, false},
			{3, false, false},
			{6, false, true},
			{9, false, false},
			{12, false, true},
			{20, false, true},
			{23, false, false},
			{33, false, true},
			{34, false, false},
			{37, false, true},
			{38, false, false},
			{39, true, true},
		}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tr := NewThrottle(tt.step)
			for _, s := range tt.seqs {
				if got := tr.Hit(s.l, s.force); got != s.want {
					t.Errorf("l = %d, force = %v, throttle.Hit() = %v, want %v", s.l, s.force, got, s.want)
				}
			}
		})
	}
}
