package helper

import (
	"regexp"
	"strconv"
	"strings"
)

// 意图序号正则
var intentRegexp = regexp.MustCompile(`"idx"\s*:\s*"(-?\d+)"`) // 兼容空格
var multiIntentRegexp = regexp.MustCompile(`['\"]?(-?\d+)['\"]?`)

// 相关性正则
var relatedRegexp = regexp.MustCompile(`"related"\s*:\s*"(\d+)"`) // 兼容空格

// ResponseFormat 意图响应格式
func ResponseFormat(intentRsp string) bool {
	return strings.Contains(intentRsp, "related") && strings.Contains(intentRsp, "idx")
}

// ResponseFormatNew 意图响应格式
func ResponseFormatNew(intentRsp string) bool {
	return !strings.Contains(intentRsp, "related")
}

// ParseIntent 解析意图序号
func ParseIntent(intentRsp string) (indexs int) {
	matches := intentRegexp.FindAllStringSubmatch(intentRsp, -1)
	for _, match := range matches {
		// 提取数字部分并转为int
		if len(match) < 2 {
			continue
		}
		num, _ := strconv.Atoi(match[1])
		indexs = num
		break
	}
	return indexs
}

// ParseMultiIntent 解析意图序号
func ParseMultiIntent(intentRsp string) (indexs []int) {
	matches := multiIntentRegexp.FindAllStringSubmatch(intentRsp, -1)
	for _, match := range matches {
		num, _ := strconv.Atoi(match[1])
		indexs = append(indexs, num)
	}
	return indexs
}

// ParseRelated 解析相关性
func ParseRelated(intentRsp string) (related bool) {
	matches := relatedRegexp.FindAllStringSubmatch(intentRsp, -1)
	for _, match := range matches {
		// 提取数字部分并转为int
		if len(match) < 2 {
			continue
		}
		num, _ := strconv.Atoi(match[1])
		if num == 1 {
			related = true
		}
		break
	}
	return related
}
