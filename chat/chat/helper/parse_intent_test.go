package helper

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/require"
)

var intentRspList = []string{
	"\t{\"question\":\"【问题】\", \"related\":\"1\",\"intention\":\"【意图名称】\",\"idx\":\"1\", \"reason\":\"【意图原因】\"}",
	"\t{\"question\":\"【问题】\", \"related\":\"0\",\"intention\":\"【意图名称】\",\"idx\":\"11\", \"reason\":\"【意图原因】\"}",
	"{\"question\":\"今天北京的天气怎么样\", \"related\":\"0\",\"intention\":\"天气\",\"idx\":\"8\", \"reason\":\"问题询问的是特定地点（北京）的当前天气状况，这属于天气信息的查询。\"}",
	"\t{\"intention\":\"其他意图\", \"idx\":\"-1\"}",
	"{\"intention\":\"其他意图\", \"idx\":\"-1\\\"}",
}

// Test_ParseIntent test
func Test_ParseIntent(t *testing.T) {
	t.Run("test response format", func(t *testing.T) {
		res := ResponseFormat(intentRspList[0])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, true, res)

		res = ResponseFormat(intentRspList[1])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, true, res)

		res = ResponseFormat(intentRspList[2])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, true, res)

	})

	t.Run("test parse intent", func(t *testing.T) {

		res := ParseIntent(intentRspList[0])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, 1, res)

		res = ParseIntent(intentRspList[1])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, 11, res)

		res = ParseIntent(intentRspList[2])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, 8, res)

		res = ParseIntent(intentRspList[3])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, -1, res)

		res = ParseIntent(intentRspList[4])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, 0, res) // 解析会失败
	})

	t.Run("test parse related", func(t *testing.T) {
		res := ParseRelated(intentRspList[0])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, true, res)

		res = ParseRelated(intentRspList[1])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, false, res)

		res = ParseRelated(intentRspList[2])
		t.Logf("res: %v\n", res)
		require.EqualValues(t, false, res)
	})
}

func TestParseMultiIntent(t *testing.T) {
	type args struct {
		intentRsp string
	}
	tests := []struct {
		name       string
		args       args
		wantIndexs []int
	}{
		{
			name:       "1",
			args:       args{intentRsp: "\"idx\": ['-1','2']"},
			wantIndexs: []int{-1, 2},
		},
		{
			name:       "2",
			args:       args{intentRsp: "{\"idx\": \"5\"}"},
			wantIndexs: []int{5},
		},
		{
			name:       "3",
			args:       args{intentRsp: "{\"idx\": 5}"},
			wantIndexs: []int{5},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotIndexs := ParseMultiIntent(tt.args.intentRsp); !reflect.DeepEqual(gotIndexs, tt.wantIndexs) {
				t.Errorf("ParseMultiIntent() = %v, want %v", gotIndexs, tt.wantIndexs)
			}
		})
	}
}
