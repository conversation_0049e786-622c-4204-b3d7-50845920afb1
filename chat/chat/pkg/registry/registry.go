// Package registry 是用来做 service 注册和解注册的
// 把注册单独提出来写，方便类似于私有化部署等需要服务合并的场景
package registry

import (
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/timer"
	"git.code.oa.com/trpc-go/trpc-go"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus"
	"git.woa.com/ivy/qbot/qbot/chat/internal/service"
	"git.woa.com/ivy/qbot/qbot/chat/pkg/rate"
	"gopkg.in/yaml.v2"
)

// Registry service 注册器
type Registry struct {
	s *service.Service
}

// New 新建注册器
func New() *Registry {
	return &Registry{}
}

// Register 注册服务
func (r *Registry) Register(srv *server.Server) error {
	// 监听应用配置
	config.Init()
	config.WorkflowInit()
	config.AgentInit()
	logConfig()
	dao.InitGlobalOutputCache()

	// 初始化业务 service
	r.s = service.New()
	pb.RegisterChatService(srv.Service("qbot.qbot.chat.Chat"), r.s)
	pb.RegisterStreamChatService(srv.Service("qbot.qbot.chat.StreamChat"), service.NewStream())

	// http服务注册
	service.NewHTTP()
	thttp.HandleFunc("/qbot/chat/receiveRTCEvent", service.RTCEventHandle)
	thttp.RegisterNoProtocolService(srv.Service("qbot.qbot.chat.ChatHttp"))

	timerRegister(srv)
	// todo 没用代码注释掉
	rateConfig := config.App().RateLimiter
	if err := rate.RegisterRedisRateLimiter(
		rateConfig.RedisAddress,
		rateConfig.Period,
		rateConfig.CleanUpInterval,
		rateConfig.Limit); err != nil {
		return err
	}
	eventbus.Run()
	return nil
}

// Deregister 解注册
func (r *Registry) Deregister() {

}

func logConfig() {
	// log config
	cfg := trpc.GlobalConfig()
	fmt.Println("\n-------------------------------------------------------------------------------")
	g0, _ := yaml.Marshal(cfg.Global)
	fmt.Printf("Global:\n%v", string(g0))
	fmt.Println("\n-------------------------------------------------------------------------------")
	s0, _ := yaml.Marshal(cfg.Server)
	fmt.Printf("Server:\n%v", string(s0))
	fmt.Println("\n-------------------------------------------------------------------------------")
	c0, _ := yaml.Marshal(cfg.Client)
	fmt.Printf("Client:\n%v", string(c0))
	fmt.Println("\n-------------------------------------------------------------------------------")
	p0, _ := yaml.Marshal(cfg.Plugins)
	fmt.Printf("Plugins:\n%v", string(p0))
	fmt.Println("\n-------------------------------------------------------------------------------")
	a0, _ := yaml.Marshal(config.App())
	fmt.Printf("[application.yaml]:\n%v", string(a0))
	fmt.Println("\n===============================================================================")
	fmt.Println(time.Now().String())
	fmt.Println("===============================================================================")
}

// timerRegister 注册定时任务
func timerRegister(srv *server.Server) {
	// 注册调度策略
	timer.RegisterScheduler("redis", service.NewScheduler())

	// 注册 DeleteRealtimeDoc 任务
	timer.RegisterHandlerService(srv.Service("qbot.chat.chat.DeleteRealtimeDoc"),
		service.New().DeleteRealtimeDoc)

	// 注册 DeleteRealtimeDoc 任务
	timer.RegisterHandlerService(srv.Service("qbot.chat.chat.LoadPromptVersionFromDB"),
		service.New().LoadPromptVersionFromDB)

	// 注册 GetRejectAnswer 任务
	timer.RegisterHandlerService(srv.Service("qbot.chat.chat.GetRejectAnswer"),
		service.New().GetRejectAnswer)

	// 注册应用关键词同步任务
	timer.RegisterHandlerService(srv.Service("qbot.chat.chat.UpdateRobotKeywords"),
		service.New().UpdateRobotKeywords)

	// 注册消息记录清理任务
	timer.RegisterHandlerService(srv.Service("qbot.chat.chat.ClearMsgRecord"),
		service.New().ClearMsgRecord)

	// 注册获取agent tools任务
	timer.RegisterHandlerService(srv.Service("qbot.chat.chat.GetAgentTools"),
		service.New().GetAgentToolConfig)
}
