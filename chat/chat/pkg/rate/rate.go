// Package rate rate limiter
package rate

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	libredis "github.com/redis/go-redis/v9"
	limiter "github.com/ulule/limiter/v3"
	sredis "github.com/ulule/limiter/v3/drivers/store/redis"
)

// RateLimit rate limiter
var RateLimit *limiter.Limiter

// RegisterRedisRateLimiter 注册 a new rate limiter
func RegisterRedisRateLimiter(redisAddress string, period, cleanUpInterval time.Duration, limit int64) error {
	option, err := libredis.ParseURL(redisAddress)
	if err != nil {
		return err
	}
	client := libredis.NewClient(option)
	// Create a store with the redis client.
	store, err := sredis.NewStoreWithOptions(client, limiter.StoreOptions{
		Prefix:          "limiter_chat",
		CleanUpInterval: cleanUpInterval,
	})
	if err != nil {
		return err
	}
	rate := limiter.Rate{
		Formatted: "",
		Period:    period,
		Limit:     limit,
	}
	RateLimit = limiter.New(store, rate)
	return nil
}

// Increment 增加限制
func Increment(ctx context.Context, key string, count int64) (bool, error) {
	if !config.App().RateLimiter.Enable {
		return false, nil
	}
	rctx, err := RateLimit.Increment(ctx, key, count)
	if err != nil {
		log.ErrorContextf(ctx, "Increment error: %+v, key: %s, count: %d", err, key, count)
		return false, err
	}
	log.DebugContextf(ctx, "Increment success, key: %s, count: %d, rctx: %+v", key, count, rctx)
	return rctx.Reached, nil
}
