package limiter

import (
	"context"
	"fmt"
	"math/rand"
	"testing"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
)

const (
	timeout         = 7 * time.Second
	concurrence     = 10
	maxConcurrence  = 5
	llmResponseTime = 30
)

var (
	ErrMock         = errs.New(460000, "model error")
	ErrMockOverload = errs.New(460000, "model overload")
)

// recordID
var l Limiter

func Test_limiter(t *testing.T) {
	rdb := redis.NewClientProxy(
		"redis.qbot.qbot",
		client.WithNetwork("tcp"),
		client.WithProtocol("redis"),
		client.WithTarget("ip://*************:6380/0"),
		client.WithPassword("evf_hun_fcz2HVP6tne"),
		client.WithTimeout(800*time.Millisecond),
	)

	l = New(rdb)
	ctx := context.Background()
	g := errgroupx.Group{}
	for i := 0; i < concurrence; i++ {
		i := i
		g.Go(func() error {
			for {
				request := fmt.Sprintf("request_%d", i)
				if err := mock(ctx, "bot", request, maxConcurrence); err != nil {
					log.Debugf("[xxx] %s: %+v", request, errs.Msg(err))
				}
			}
		})
	}
	if err := g.Wait(); err != nil {
		log.Debugf("[---] error: %s:", err)
	}
}

func mock(ctx context.Context, bot, request string, concurrence uint) error {
	log.Debugf("[>>>] %s", request)
	defer func() {
		log.Debugf("[<<<] %s", request)
	}()

	return l.Do(
		ctx, bot, request, concurrence, timeout,
		func() error { return invoke(request) },
		func(err error) bool { return err == ErrMockOverload },
	)
}

func invoke(request string) error {
	isModelOverload := rand.Intn(10) < 1 // 10%
	if isModelOverload {
		log.Debugf("[+++] %s: mock model overload", request)
		t := rand.Intn(200) + 1
		time.Sleep(time.Duration(t) * time.Millisecond)
		log.Debugf("[---] %s: model overload", request)
		return ErrMockOverload
	}

	t := rand.Intn(llmResponseTime) + 1
	hasError := rand.Intn(10) < 2 // 20%
	var timeError int
	if hasError {
		timeError = rand.Intn(t)
	}

	log.Debugf("[+++] %s: time: %d, hasError: %+v, timeError: %d", request, t, hasError, timeError)
	defer func() {
		log.Debugf("[---] %s: time: %d, hasError: %+v, timeError: %d", request, t, hasError, timeError)
	}()
	if !hasError {
		time.Sleep(time.Duration(t) * time.Second)
	} else {
		time.Sleep(time.Duration(timeError) * time.Second)
		return ErrMock
	}
	return nil
}
