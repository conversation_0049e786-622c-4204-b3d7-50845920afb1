// Package limiter 并发限制器
package limiter

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
)

// ErrConcurrenceExceeded 并发超限错误
var ErrConcurrenceExceeded = errs.New(10600, "concurrency exceeded")

// KEYS [lock]
// ARGV [holder, now, limit, expire]
const lua = `
	redis.call('ZREMRANGEBYSCORE', KEYS[1], '-inf', ARGV[4])
	if ((redis.call('ZCARD', KEYS[1]) >= tonumber(ARGV[3])) and (redis.call('ZSCORE', KEYS[1], ARGV[1]) == false))
	then
		return 0
	end
	redis.call('ZADD', KEYS[1], ARGV[2], ARGV[1])
	return 1
`

// Judger 频控错误判断函数
type Judger func(error) bool

// Callback 需要并发执行的函数
type Callback func() error

// CallbackWithReturn 带有返回值的需要并发执行的函数
type CallbackWithReturn func() (bool, any, error)

// Limiter 并发限制器
type Limiter interface {
	// Do 以给定并发执行 cb，如果并发超过限制或者下游返回频控错误，则会重试直到超时。
	Do(
		ctx context.Context,
		lock, holder string, concurrence uint,
		timeout time.Duration, cb Callback, isOverload Judger,
	) error

	// TryDo 尝试执行 try，如果 try 不能返回数据，则以单并发让某个协程执行 do，其它协程等待后再次 try。
	TryDo(ctx context.Context, lock string, do Callback, try CallbackWithReturn) (any, bool, error)

	// Lock 锁定
	Lock(ctx context.Context, lock string) (bool, error)

	// Unlock 解锁
	Unlock(ctx context.Context, lock string) error

	// GetLimit 获取并发数
	GetLimit(ctx context.Context, lock string) (int, error)

	// LimitByTimePeriod 根据时间片限制请求数量
	LimitByTimePeriod(ctx context.Context, lock, holder string, limit uint, period time.Duration) (bool, int, error)
}

type limiter struct {
	rdb             redis.Client
	mutex           sync.Mutex
	scriptDigest    string
	expire          time.Duration
	maxUnlockRetry  int
	tryLockInterval time.Duration
}

var (
	inst *limiter
	once sync.Once
)

// New 构造并发限制器
func New(rdb redis.Client) Limiter {
	once.Do(func() {
		inst = &limiter{
			rdb:             rdb,
			mutex:           sync.Mutex{},
			expire:          -3 * time.Minute,
			maxUnlockRetry:  3,
			tryLockInterval: 50 * time.Millisecond,
		}
	})
	return inst
}

// loadScript 加载并发控制 lua 脚本
func (c *limiter) loadScript(ctx context.Context) error {
	if c.scriptDigest != "" {
		return nil
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()
	if c.scriptDigest != "" {
		return nil
	}

	r, err := redis.String(c.rdb.Do(ctx, "SCRIPT", "LOAD", lua))
	if err != nil {
		return err
	}
	c.scriptDigest = r
	return nil
}

// multiLock 多并发锁定
func (c *limiter) multiLock(ctx context.Context, lock, holder string, limit uint) (bool, error) {
	if err := c.loadScript(ctx); err != nil {
		return false, err
	}
	args := []any{c.scriptDigest, 1, lock, holder, time.Now().Unix(), limit, time.Now().Add(c.expire).Unix()}
	r, err := redis.Int(c.rdb.Do(ctx, "EVALSHA", args...))
	if err != nil {
		log.ErrorContextf(ctx, "multi lock error: %+v, args: %+v", err, args)
		return false, err
	}
	return r == 1, nil
}

// multiUnlock 多并发解锁
func (c *limiter) multiUnlock(ctx context.Context, lock, holder string) (err error) {
	for i := 0; i < c.maxUnlockRetry; i++ {
		if _, err := redis.Int(c.rdb.Do(ctx, "ZREM", lock, holder)); err == nil {
			return nil
		}
		log.ErrorContextf(ctx, "multi unlock error: %+v, lock: %s, holder: %s", err, lock, holder)
	}
	return err
}

// Do 以给定并发执行 cb，如果并发超过限制或者下游返回频控错误，则会重试直到超时。
func (c *limiter) Do(
	ctx context.Context,
	lock, holder string, concurrence uint, timeout time.Duration, cb Callback, isOverload Judger,
) error {
	timer := time.NewTimer(timeout)
	ticker := time.NewTicker(c.tryLockInterval)
	defer timer.Stop()
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return context.Canceled
		case <-timer.C:
			return ErrConcurrenceExceeded // 超时锁定失败，返回频控
		case <-ticker.C:
			if cb == nil {
				return nil
			}

			ok, err := c.multiLock(ctx, lock, holder, concurrence)
			if err != nil {
				return err
			}
			if !ok {
				metrics.ReportLimit(lock, holder)
				continue
			}

			defer func() {
				if err = c.multiUnlock(ctx, lock, holder); err != nil {
					log.ErrorContextf(ctx, "multi unlock error: %+v, lock: %s, holder: %s", err, lock, holder)
				}
			}()

			if err := cb(); err != nil {
				// if isOverload != nil && isOverload(err) {
				//	metrics.ReportLimit(lock, holder)
				//	c.multiUnlock(ctx, lock, holder)
				//	continue
				// }
				metrics.ReportLimit(lock, holder)
				if isOverload != nil && isOverload(err) {
					return ErrConcurrenceExceeded
				}
				return err
			}
			return nil
		}
	}
}

// TryDo 尝试执行 try，如果 try 不能返回数据，则以单并发让某个协程执行 do，其它协程等待后再次 try。
func (c *limiter) TryDo(ctx context.Context, lock string, do Callback, try CallbackWithReturn) (any, bool, error) {
	ticker := time.NewTicker(c.tryLockInterval)
	defer ticker.Stop()
	for {
		done, v, err := try()
		if err != nil {
			return nil, false, err
		}
		if done {
			return v, false, nil
		}

		ok, err := c.Lock(ctx, lock)
		if err != nil {
			return nil, false, err
		}
		if !ok {
			metrics.ReportLimit(lock, "")
			select {
			case <-ctx.Done():
				return nil, false, ctx.Err()
			case <-ticker.C:
				continue
			}
		}

		defer func() {
			if err = c.Unlock(ctx, lock); err != nil {
				log.ErrorContextf(ctx, "unlock error: %+v, lock: %s", err, lock)
			}
		}()
		done, v, err = try()
		if err != nil {
			return nil, false, err
		}
		if done {
			return v, false, nil
		}

		if err := do(); err != nil {
			return nil, false, err
		}

		_, v, err = try()
		if err != nil {
			return nil, false, err
		}
		return v, true, nil
	}
}

// Lock 锁定
func (c *limiter) Lock(ctx context.Context, lock string) (bool, error) {
	expire := int64(3000)
	now := time.Now()
	if t, ok := ctx.Deadline(); ok && t.After(now) {
		expire = t.Sub(now).Milliseconds()
	}
	if _, err := redis.String(c.rdb.Do(ctx, "SET", lock, 1, "PX", expire, "NX")); err != nil {
		if err == redis.ErrNil {
			return false, nil
		}
		log.ErrorContextf(ctx, "lock error: %+v, lock: %s", err, lock)
		return false, err
	}
	return true, nil
}

// Unlock 解锁
func (c *limiter) Unlock(ctx context.Context, lock string) error {
	if _, err := redis.Int64(c.rdb.Do(ctx, "DEL", lock)); err != nil {
		log.ErrorContextf(ctx, "unlock error: %+v, lock: %s", err, lock)
		return err
	}
	return nil
}

// GetLimit 获取锁定的数量
func (c *limiter) GetLimit(ctx context.Context, lock string) (int, error) {
	concurrence, err := redis.Int(c.rdb.Do(ctx, "ZCARD", lock))
	if err != nil {
		log.ErrorContextf(ctx, "unlock error: %+v, lock: %s", err, lock)
		return 0, err
	}
	return concurrence, nil
}

// LimitByTimePeriod 根据时间片限制请求数量
func (c *limiter) LimitByTimePeriod(ctx context.Context, lock, holder string, limit uint,
	period time.Duration) (bool, int, error) {
	luaWithNum := `
		redis.call('ZREMRANGEBYSCORE', KEYS[1], '-inf', ARGV[4])
		local nums = tonumber(redis.call('ZCARD', KEYS[1]))
		if (nums >= tonumber(ARGV[3]))
		then
			return {0, nums}
		end
		redis.call('ZADD', KEYS[1], ARGV[2], ARGV[1])
		return {1, nums+1}
	`
	script := redis.NewScript(1, luaWithNum)
	args := []interface{}{lock, holder, time.Now().Unix(), limit, time.Now().Add(-period).Unix()}
	v, err := redis.Ints(script.Do(ctx, c.rdb, args...))
	if err != nil {
		log.ErrorContextf(ctx, "LimitByTime error: %+v, args: %+v", err, args)
		return false, 0, err
	}
	if len(v) != 2 {
		log.ErrorContextf(ctx, "LimitByTime get invalid result args: %+v, result: %+v", args, v)
		return false, 0, fmt.Errorf("invalid limit result")
	}
	return v[0] == 1, v[1], nil
}
