// Package agentlogger TODO
package agentlogger

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/plugin"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"github.com/google/uuid"
)

// Init 注册 log
func Init() {
	plugin.Register("agent", log.DefaultLogFactory)
}

// New 创建一个新的Step
func New(ctx context.Context, key StepKey, input any) *Step {
	s := &Step{
		Key:   key,
		Input: input,
	}
	s.Init(ctx)
	return s
}

// StepStatus 步骤的结果
type StepStatus int

const (
	// StepStatusSuccess 正常
	StepStatusSuccess StepStatus = 0
	// StepStatusFailed 失败
	StepStatusFailed StepStatus = -1
	// StepKeyLLM LLM
	StepKeyLLM StepKey = "LLM"
	// StepKeyPlugin 插件
	StepKeyPlugin StepKey = "Plugin"
	// StepKeyMCP MCP插件
	StepKeyMCP StepKey = "MCP"
	// StepKeyWorkflowLLM 工作流LLM节点
	StepKeyWorkflowLLM StepKey = "Workflow-LLM"
	// StepKeyWorkflowTool 工作流工具节点
	StepKeyWorkflowTool StepKey = "Workflow-Tool"
	// StepKeyWorkflowPlugin 工作流插件节点
	StepKeyWorkflowPlugin StepKey = "Workflow-Plugin"
	// StepKeyWorkflowCode 工作流代码节点
	StepKeyWorkflowCode StepKey = "Workflow-Code"
)

// StepKey 步骤类型
type StepKey string

// Step 步骤
type Step struct {
	ID        int64  `json:"-"`
	RecordID  string `json:"-"`
	TraceID   string `json:"-"`
	SessionID string `json:"-"`
	AppID     string `json:"-"`
	RequestID string `json:"-"`

	Key         StepKey    `json:"key"`        // 步骤关键字
	Input       any        `json:"input"`      // 输入参数
	Output      any        `json:"output"`     // 输出参数
	Status      StepStatus `json:"status"`     // 状态值
	FailMessage string     `json:"fail_msg"`   // 失败消息
	ExtraData   any        `json:"extra_data"` // 附加信息
	StartTime   int64      `json:"start_time"` // 开始时间
	EndTime     int64      `json:"end_time"`   // 结束时间
}

// SetResult SetResult
func (s *Step) SetResult(err error) *Step {
	if err != nil {
		s.Status = StepStatusFailed
		s.FailMessage = err.Error()
	} else {
		s.Status = StepStatusSuccess
	}
	s.EndTime = time.Now().UnixMilli()
	return s
}

// Init 初始化
func (s *Step) Init(ctx context.Context) {
	s.RecordID = pkg.RecordID(ctx)
	s.TraceID = model.TraceID(ctx)
	s.StartTime = time.Now().UnixMilli()
	s.EndTime = 0
	s.SessionID = pkg.SessionID(ctx)
	s.AppID = pkg.AppID(ctx)
	s.RequestID = uuid.NewString()
}

// UpsertStep 记录步骤
//
//	@param isCompleted 	是否完成。日志的情况下，可能完成的时候才写入
func (s *Step) UpsertStep(isCompleted bool) {

	if isCompleted {
		// 一般tag会加索引（如loki），所以tag的方式更加高效
		log.Get("agent").
			With(log.Field{Key: "traceID", Value: s.TraceID}).
			With(log.Field{Key: "RequestID", Value: s.RequestID}).
			With(log.Field{Key: "RecordID", Value: s.RecordID}).
			With(log.Field{Key: "SessionID", Value: s.SessionID}).
			With(log.Field{Key: "AppID", Value: s.AppID}).
			With(log.Field{Key: "Key", Value: s.Key}).
			With(log.Field{Key: "Status", Value: s.Status}).
			Infof("%s", helper.Object2StringEscapeHTML(s))
	}
}
