// Package pkg 用来写需要对外导出的变量、常量、类型和方法
package pkg

import (
	"git.code.oa.com/trpc-go/trpc-go/errs"
)

// errors 服务内部定义的错误要暴露出去，调用方需要引用它来判断错误类型
var (
	ErrUserNotLogin              = errs.New(450006, "用户未登录")
	ErrIntentNotFound            = errs.New(450206, "意图不存在")
	ErrAdminAppKeyInvalid        = errs.New(4505004, "APPKEY无效，请核实APPKEY来源") // 兼容admin的错误码，不对外
	ErrBadRequest                = errs.New(400, "请求参数错误, 请参阅接入文档.")
	ErrAuthTokenFailed           = errs.New(460001, "Token 校验失败")
	ErrEventHandlerNotExist      = errs.New(460002, "事件处理器不存在")
	ErrSeatNotExist              = errs.New(460003, "坐席用户不存在")
	ErrAdminRobotNotExist        = errs.New(450011, "机器人不存在") // 兼容admin的错误码，不对外
	ErrRobotNotExist             = errs.New(460004, "机器人不存在")
	ErrSessionNotTransfered      = errs.New(460005, "会话未被转人工")
	ErrInvalidMsgRecord          = errs.New(460006, "消息不存在或没有操作权限")
	ErrCreateSessionFailed       = errs.New(460007, "会话创建失败")
	ErrRenderTemplate            = errs.New(460008, "Prompt 渲染失败")
	ErrVisitorNotExist           = errs.New(460009, "访客用户不存在")
	ErrSessionNotFound           = errs.New(460010, "会话不存在或没有操作权限") // 前端使用该错误码
	ErrConcurrenceExceeded       = errs.New(460011, "超出并发数限制")      // API 使用该错误码
	ErrLLMTimeout                = errs.New(460020, "模型请求超时")
	ErrKnowledgeNotPublished     = errs.New(460021, "知识库未发布")
	ErrCreateVisitorFailed       = errs.New(460022, "访客创建失败")
	ErrRateMsgRecordFailed       = errs.New(460023, "消息点赞点踩失败")
	ErrInvalidVisitorLabel       = errs.New(460024, "标签不合法")
	ErrImageRecognitionFailed    = errs.New(460025, "图像识别失败")
	ErrRobotStopOrArrearage      = errs.New(460026, "机器人停用或者欠费")
	ErrAppTypeNotSupported       = errs.New(460030, "该应用类型不支持当前请求")
	ErrAppRateLimiter            = errs.New(460031, "当前应用连接数超出请求限制，请稍后再试")
	ErrNoBalance                 = errs.New(460032, "当前应用模型余额不足")
	ErrInvalidApp                = errs.New(460033, "应用不存在或没有操作权限")
	ErrContentTooLong            = errs.New(460034, "输入内容过长")
	ErrCalcContentTooLong        = errs.New(460035, "计算内容过长, 已经停止")
	ErrTaskFlowNodePreview       = errs.New(460036, "任务流程节点预览参数异常")
	ErrNoSearchEngineBalance     = errs.New(460037, "搜索资源已用尽，调用失败")
	ErrAppIDInBlacklist          = errs.New(460038, "该AppID请求存在异常行为，调用失败")
	ErrMsgRecordErr              = errs.New(460039, "消息记录不合法")
	ErrAudioInteractNotEnabled   = errs.New(460040, "语音互动功能未开启")
	ErrRTCNotEnabled             = errs.New(460041, "语音通话功能未开启")
	ErrRTCResourceInsufficient   = errs.New(460042, "语音通话线路繁忙，请稍后再试")
	ErrRTCVoiceNotConfig         = errs.New(460043, "语音通话声音未配置")
	ErrRTCDigitalHumanNotConfig  = errs.New(460044, "语音通话形象未配置")
	ErrRTCDigitalHumanNotEnabled = errs.New(460045, "语音通话形象未启用")
	ErrRTCConcurrencyLimiter     = errs.New(460046, "当前语音通话数超出请求限制，请稍后再试")
	// ErrWorkflowDisable 工作流相关
	// -----------------------------------------------------
	ErrWorkflowDisable   = errs.New(460100, "工作流功能未启用")
	ErrWorkflowRunError  = errs.New(460101, "工作流运行异常")
	ErrWorkflowClose     = errs.New(460102, "您选择的工作流未开启，请开启后再调试")
	ErrWorkflowDebug     = errs.New(460103, "您选择的工作流待调试，请先在工作流中对其进行调试")
	ErrWorkflowDelete    = errs.New(460104, "您选择的工作流已被删除，请重新选择")
	ErrWorkflowRelease   = errs.New(460105, "您选择的工作流未发布，请先在工作流中对其进行开启")
	ErrWorkflowReference = errs.New(460106, "该工作流中引用了其他工作流，其他工作流因‘未启用’或‘待调试’导致不可用，请’启用‘或’调试‘后再进行测试。")
	// ErrAgentImageUnsupported 智能体相关
	// -----------------------------------------------------
	ErrAgentImageUnsupported = errs.New(460200, "Agent暂时不支持图片")
	ErrAgentFileUnsupported  = errs.New(460201, "Agent暂时不支持文件")
	ErrAgentRunError         = errs.New(460202, "Agent运行异常")
	ErrAgentNoToolsError     = errs.New(460203, "Agent当前没有可执行的工具")
	ErrAgentToolsRunError    = errs.New(460204, "Agent工具失败")
	// ErrAgentLocalToolOuputsRedundant 运行时检测到参数错误
	ErrAgentLocalToolOuputsRedundant = errs.New(460205, "请求参数错误，上次聊天没有请求本地工具调用，请不要配置 LocalToolOuputs 参数。")
	// ErrAgentLocalToolsNotFound 本地工具未找到
	ErrAgentLocalToolsNotFound = errs.New(460206, "请求参数错误，LocalToolOuputs 上报的工具名称不存在")
	// ErrCustomVariableErrorID 自定义变量相关的错误
	ErrCustomVariableErrorID = 460207

	// ErrRedisNotFound redis相关
	// -----------------------------------------------------
	ErrRedisNotFound = errs.New(460300, "Redis数据不存在")
)

var (
	// ErrForbidden 拒绝访问。服务器已经理解请求，但是拒绝执行
	ErrForbidden = errs.New(403, "Forbidden")

	// ErrInternalServerError 服务器内部错误。可能是访问存储失败、内部服务调用失败等原因
	ErrInternalServerError = errs.New(500, "Internal Server Error")
)
