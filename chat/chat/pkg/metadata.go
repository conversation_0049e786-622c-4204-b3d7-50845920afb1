package pkg

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	trpc "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"github.com/spf13/cast"
)

const (
	metaToken              = "token"
	metaUin                = "uin"
	metaSubAccountUin      = "sub_account_uin"
	metaLoginUin           = "login_uin"
	metaLoginSubAccountUin = "login_sub_account_uin"
	metaRequestID          = "request_id"
	metaSessionID          = "session_id"
	metaTraceID            = "trace_id"
	metaAction             = "action"
	metaStaffID            = "StaffID"
	metaRecordID           = "record_id"       // 消息记录 ID
	metaAppID              = "app_id"          // 应用 ID
	metaMessageID          = "message_id"      // chat 收到的 message id
	metaLoginUserType      = "login_user_type" // 账户登录类型
	metaStatistics         = "ppl_statistics"  // 统计信息
	metaInterfaceType      = "interface_type"  // 接入接口类型，sse或websocket
)

// Uin AKSK 对应的云主账号 Uin
func Uin(ctx context.Context) string {
	return string(codec.Message(ctx).ServerMetaData()[metaUin])
}

// SubAccountUin AKSK 对应的云子账号 Uin
func SubAccountUin(ctx context.Context) string {
	return string(codec.Message(ctx).ServerMetaData()[metaSubAccountUin])
}

// LoginUin 登录态对应的云主账号 Uin
func LoginUin(ctx context.Context) string {
	return string(codec.Message(ctx).ServerMetaData()[metaLoginUin])
}

// LoginSubAccountUin 登录态对应的云子账号 Uin
func LoginSubAccountUin(ctx context.Context) string {
	return string(codec.Message(ctx).ServerMetaData()[metaLoginSubAccountUin])
}

// RequestID 云请求 ID
func RequestID(ctx context.Context) string {
	return string(codec.Message(ctx).ServerMetaData()[metaRequestID])
}

// LoginUserType 获取登录的用户类型
// func LoginUserType(ctx context.Context) uint32 {
//	userType := metadata.Metadata(ctx).Get(metaLoginUserType)
//	if len(userType) == 0 {
//		return 0
//	}
//	return cast.ToUint32(userType)
// }

// WithRequestID 云请求 ID
func WithRequestID(ctx context.Context, requestID string) {
	id := RequestID(ctx)
	if len(id) > 0 {
		return
	}
	trpc.SetMetaData(ctx, metaRequestID, []byte(requestID))
}

// RecordID 消息记录 ID
func RecordID(ctx context.Context) string {
	return string(codec.Message(ctx).ServerMetaData()[metaRecordID])
}

// WithRecordID 消息记录 ID
func WithRecordID(ctx context.Context, recordID string) {
	id := RecordID(ctx)
	if len(id) > 0 {
		return
	}
	trpc.SetMetaData(ctx, metaRecordID, []byte(recordID))
}

// MessageID 消息记录 ID
func MessageID(ctx context.Context) string {
	return string(codec.Message(ctx).ServerMetaData()[metaMessageID])
}

// WithMessageID 消息记录 ID
func WithMessageID(ctx context.Context, messageID string) {
	id := MessageID(ctx)
	if len(id) > 0 {
		return
	}
	trpc.SetMetaData(ctx, metaMessageID, []byte(messageID))
}

// SessionID 云请求 ID
func SessionID(ctx context.Context) string {
	sid := string(codec.Message(ctx).ServerMetaData()[metaSessionID])
	if len(sid) == 0 {
		sid = string(codec.Message(ctx).ServerMetaData()["_sid_"])
	}
	return sid
}

// WithSessionID 云请求 ID
func WithSessionID(ctx context.Context, sessionID string) {
	id := codec.Message(ctx).ServerMetaData()[metaSessionID]
	if len(id) > 0 {
		return
	}
	trpc.SetMetaData(ctx, metaSessionID, []byte(sessionID))
}

// TraceID 云请求 ID
func TraceID(ctx context.Context) string {
	return string(codec.Message(ctx).ServerMetaData()[metaTraceID])
}

// WithTraceID 云请求 ID
func WithTraceID(ctx context.Context, traceID string) {
	id := TraceID(ctx)
	if len(id) > 0 {
		return
	}
	trpc.SetMetaData(ctx, metaTraceID, []byte(traceID))
}

// AppID 应用 ID
func AppID(ctx context.Context) string {
	return string(codec.Message(ctx).ServerMetaData()[metaAppID])
}

// WithAppID 机器人 ID
func WithAppID(ctx context.Context, appID uint64) {
	id := AppID(ctx)
	if len(id) > 0 {
		return
	}
	trpc.SetMetaData(ctx, metaAppID, []byte(fmt.Sprintf("%d", appID)))
}

// StaffID 获取staffID
func StaffID(ctx context.Context) uint64 {
	staffID := string(codec.Message(ctx).ServerMetaData()[metaStaffID])
	if staffID == "" {
		return 0
	}
	return cast.ToUint64(staffID)
}

// WithStaffID 携带员工 ID
func WithStaffID(ctx context.Context, staffID uint64) {
	id := codec.Message(ctx).ServerMetaData()[metaStaffID]
	if len(id) > 0 {
		return
	}
	trpc.SetMetaData(ctx, metaStaffID, []byte(strconv.FormatUint(staffID, 10)))
}

// WithLoginUserType 携带用户类型
func WithLoginUserType(ctx context.Context, userType uint32) {
	id := codec.Message(ctx).ServerMetaData()[metaLoginUserType]
	if len(id) > 0 {
		return
	}
	trpc.SetMetaData(ctx, metaLoginUserType, []byte(strconv.FormatUint(uint64(userType), 10)))
}

// WithUin 携带uin
func WithUin(ctx context.Context, uin string) {
	if len(Uin(ctx)) > 0 || len(uin) == 0 {
		return
	}
	trpc.SetMetaData(ctx, metaUin, []byte(uin))
}

// WithStatistics .
func WithStatistics(ctx context.Context, statistics *PPLStatistics) {
	if statistics == nil {
		return
	}
	buf, _ := json.Marshal(statistics)
	trpc.SetMetaData(ctx, metaStatistics, buf)
}

// GetStatistics .
func GetStatistics(ctx context.Context) *PPLStatistics {
	buf, found := codec.Message(ctx).ServerMetaData()[metaStatistics]
	if len(buf) == 0 || !found {
		return nil
	}
	statistics := &PPLStatistics{}
	_ = json.Unmarshal(buf, statistics)
	return statistics
}

// InterfaceType 获取接入的接口类型
func InterfaceType(ctx context.Context) string {
	return string(codec.Message(ctx).ServerMetaData()[metaInterfaceType])
}

// WithInterfaceType 接入接口类型，websocket或sse
func WithInterfaceType(ctx context.Context, interfaceType string) {
	id := TraceID(ctx)
	if len(id) > 0 {
		return
	}
	trpc.SetMetaData(ctx, metaInterfaceType, []byte(interfaceType))
}
