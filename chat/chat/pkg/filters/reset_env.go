// Package filters 开发环境set10的chat路由到set9的admin
package filters

import (
	"context"
	"os"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/filter"
)

func init() {
	filter.Register("reset_env", serverFilter, nil)
}

func serverFilter(ctx context.Context, req interface{}, next filter.ServerHandleFunc) (interface{}, error) {
	if os.Getenv("ENV") == "oa" && os.Getenv("QD_ENV") == "2" && os.Getenv("QD_SET") == "10" {
		trpc.SetMetaData(ctx, "ENV-SET", []byte("2-9"))
	}
	if os.Getenv("ENV") == "oa" && os.Getenv("QD_ENV") == "2" && os.Getenv("QD_SET") == "11" {
		trpc.SetMetaData(ctx, "ENV-SET", []byte("2-9"))
	}
	return next(ctx, req)
}
