package pkg

import "time"

// PPLStatistics .
type PPLStatistics struct {
	PPLStartTime        time.Time `json:"ppl_start_time"`
	FirstPkgCost        int64     `json:"first_pkg_cost"`
	FirstThoughtPkgCost int64     `json:"first_thought_pkg_cost"`
	FirstReplyPkgCost   int64     `json:"first_answer_cost"`
	TotalThoughtCost    int64     `json:"total_thought_cost"`
	TotalCost           int64     `json:"total_cost"`

	MainModel         string `json:"main_model"`
	IntentionCategory string `json:"intention_category"`
}
