package agent

import (
	"context"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	plugin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
)

// hitWorkflow 判断是否命中工作流
func (a *ChatAgentImpl) hitWorkflow(ctx context.Context, bs *botsession.BotSession,
	rsp *llmm.Response) (model.AgentTool, bool) {
	if !rsp.Finished {
		return model.AgentTool{}, false
	}
	for _, toolCall := range rsp.GetMessage().ToolCalls {
		if toolCall == nil {
			continue
		}
		// 去掉工具的 transfer_to_ 前缀
		pdl, isPdl := a.agentContainer.IsPdl(toolName2Handoff(toolCall.Function.Name))
		if !isPdl || pdl == nil {
			continue
		}
		// 发生转交，这里不需要处理 handoff 相关的其他信息看，因为前置判断一定命中了 hitHandoff
		bs.AgentStatus.AgentSwitch = true
		bs.AgentStatus.AgentType = model.AgentTypeWorkflow
		bs.WorkflowID = pdl.WorkflowId
		bs.AgentStatus.WorkflowID = pdl.WorkflowId
		bs.AgentStatus.WorkflowName = pdl.Name
		return model.AgentTool{
			IsWorkflow:   true,
			WorkflowID:   pdl.WorkflowId,
			WorkflowName: pdl.Name,
			PDLContent:   pdl.Instructions,
		}, true
	}
	return model.AgentTool{}, false
}

// processWorkflowThought 处理工作流思考
func (a *ChatAgentImpl) processWorkflowThought(ctx context.Context, bs *botsession.BotSession,
	rsp *llmm.Response, workflow model.AgentTool) (isEvil, isFinish bool, err error) {
	// toolName := rsp.GetMessage().GetToolCalls()[0].GetFunction().GetName()
	// 处理思考过程
	procedure := event.AgentProcedure{
		Index:        0,
		Name:         workflow.WorkflowName,
		Title:        workflow.WorkflowName,
		Status:       event.ProcedureStatusSuccess,
		Icon:         config.AgentConfig.WorkflowIcon,
		Debugging:    event.AgentProcedureDebugging{Content: "执行工作流：" + workflow.WorkflowName},
		WorkflowName: workflow.GetPluginName(),
		PluginType:   event.PluginTypeWorkflow,
	}
	a.addProcedure(procedure)

	bs.AgentStatus.AgentType = model.AgentTypeWorkflow
	bs.AgentStatus.WorkflowID = workflow.WorkflowID
	bs.AgentStatus.WorkflowName = workflow.WorkflowName
	return false, false, nil
}

// GetConversationFromMessage 从消息中获取对话
func (a *ChatAgentImpl) GetConversationFromMessage(ctx context.Context) (conversation string) {
	// a.agentMemory
	for _, memory := range a.agentMemory {
		if memory.Role == llmm.Role_USER {
			conversation += "[USER]" + memory.Content + "\n"
		}

		if memory.Role == llmm.Role_ASSISTANT {
			conversation += a.wrapBotReply(ctx, memory)
			// conversation += "[BOT_" + a.replaceAgentPrefix(memory.From) + "] "
			// if memory.CallType != "" && len(memory.ToolCalls) > 0 {
			//	conversation += "<Call " + memory.CallType + ">"
			//	conversation += a.replaceAgentPrefix(memory.ToolCalls[0].GetFunction().GetName())
			//	if len(memory.ToolCalls[0].GetFunction().GetArguments()) > 3 { // 过滤掉空的参数 "{}"
			//		conversation += "(" + memory.ToolCalls[0].GetFunction().GetArguments() + ")\n"
			//	} else {
			//		conversation += "\n"
			//	}
			// } else {
			//	conversation += memory.Content + "\n"
			// }

		}
		if memory.Role == llmm.Role_TOOL && memory.CallType == "API" {
			// conversation += "[SYSTEM] <API response> " + memory.Content + "\n"
			conversation += "[SYSTEM] <tool_response> " + memory.Content + "\n"
		}
	}
	conversation = strings.TrimSuffix(conversation, "\n")
	log.InfoContextf(ctx, "GetConversationFromMessage done. memory is: %s, conversation is: %s",
		helper.Object2String(a.agentMemory), conversation)
	return
}

// wrapBotReply 包装Bot回复
func (a *ChatAgentImpl) wrapBotReply(ctx context.Context, memory *model.AgentMemory) (conversation string) {
	conversation += "[BOT_" + a.replaceAgentPrefix(memory.From) + "] "

	if memory.CallType != "" && len(memory.ToolCalls) > 0 {
		toolName := memory.ToolCalls[0].GetFunction().GetName()
		if toolName == "response_to_user" {
			conversation += model.MatchResponseToUserReply(memory.ToolCalls[0].GetFunction().GetArguments()) + "\n"
			return
		}
		if memory.CallType == "API" {
			conversation += "<tool_call>"
		} else {
			conversation += "<Call " + memory.CallType + ">"
		}

		conversation += a.replaceAgentPrefix(toolName)

		// 拼工具结果
		if len(memory.ToolCalls[0].GetFunction().GetArguments()) > 3 { // 过滤掉空的参数 "{}"
			conversation += "(" + memory.ToolCalls[0].GetFunction().GetArguments() + ")\n"
		} else {
			conversation += "\n"
		}
	} else {
		conversation += memory.Content + "\n"
	}
	return
}

// replaceAgentPrefix 替换Agent前缀
func (a *ChatAgentImpl) replaceAgentPrefix(content string) string {
	return strings.ReplaceAll(content, "Agent-", "")
}

// wrapAPIParams 封装API参数信息
func (a *ChatAgentImpl) wrapAPIParams(ctx context.Context, bs *botsession.BotSession) string {
	var sb strings.Builder

	sb.WriteString("<INFO>\n")
	sb.WriteString("SYS.UserQuery: " + bs.OriginContent + "\n")
	sb.WriteString("SYS.RewriteQuery: " + bs.OriginContent + "\n")
	sb.WriteString("SYS.CurrentTime: " + helper.GetStringTime())

	for key, value := range bs.CustomVariables {
		sb.WriteString("\nAPI." + key + ": " + value)
	}

	for key, value := range bs.WorkflowInput {
		sb.WriteString("\nCUSTOM." + key + ": " + value)
	}

	sb.WriteString("\n</INFO>\n")

	res := sb.String()

	log.InfoContextf(ctx, "wrapAPIParams done. res is: %s", res)

	return res
}

// wrapWorkflowCtx 封装工作流上下文
func (a *ChatAgentImpl) wrapWorkflowCtx(ctx context.Context, bs *botsession.BotSession) botsession.WorkflowCtx {
	currentState := config.AgentConfig.CurrentStateUser
	if len(a.agentMemory) > 0 && a.agentMemory[len(a.agentMemory)-1].Role == llmm.Role_TOOL &&
		a.agentMemory[len(a.agentMemory)-1].CallType == "API" {
		currentState = config.AgentConfig.CurrentStateSys
	}
	openAPITools := a.getWorkflowTools(ctx, bs)
	pdlContent := a.wrapAPIParams(ctx, bs) + model.ConvertPDL(bs.ToolsInfo[bs.AgentStatus.WorkflowName].PDLContent)
	workflowCtx := botsession.WorkflowCtx{
		WorkflowName:              bs.AgentStatus.WorkflowName,
		APIInfos:                  model.ConvertAPIInfo2String(openAPITools),
		PDL:                       pdlContent,
		Conversation:              a.GetConversationFromMessage(ctx),
		CurrentTime:               helper.GetStringTime() + " (" + time.Now().Weekday().String() + ")",
		Query:                     bs.OriginContent,
		CurrentState:              currentState,
		UserAdditionalConstraints: bs.ToolsInfo[bs.AgentStatus.WorkflowName].UserAdditionalConstraints,
		UseMultiAgent:             !bs.WorkflowDebug, // 是debug模式，则不使用多Agent
	}
	return workflowCtx
}

// wrapPdlMcpServerInfo 封装PDL的MCP服务信息
func (a *ChatAgentImpl) wrapPdlMcpServerInfo(ctx context.Context,
	t *plugin.DescribeToolRsp) *agent_config_server.AgentMCPServerInfo {
	res := &agent_config_server.AgentMCPServerInfo{
		McpServerUrl:   t.MCPServer.McpServerUrl,
		Timeout:        t.MCPServer.Timeout,
		SseReadTimeout: t.MCPServer.SseReadTimeout,
		Headers:        convertMap2McpHeader(t.MCPServer.Headers),
	}
	return res
}

// convertMap2McpHeader 转换MCP头部
func convertMap2McpHeader(headers map[string]string) []*agent_config_server.AgentPluginHeader {
	convertedHeaders := make([]*agent_config_server.AgentPluginHeader, 0)
	for key, value := range headers {
		item := &agent_config_server.AgentPluginHeader{
			ParamName:  key,
			ParamValue: value,
		}
		convertedHeaders = append(convertedHeaders, item)
	}
	return convertedHeaders
}

// wrapUserHeaders 封装用户头部
func wrapUserHeaders(headers []*plugin.RequestParam) []*agent_config_server.AgentPluginHeader {
	userHeaders := make([]*agent_config_server.AgentPluginHeader, 0)
	for _, item := range headers {
		userHeader := &agent_config_server.AgentPluginHeader{
			ParamName:  item.GetName(),
			ParamValue: item.GetDefaultValue(),
		}
		userHeaders = append(userHeaders, userHeader)
	}
	return userHeaders
}
