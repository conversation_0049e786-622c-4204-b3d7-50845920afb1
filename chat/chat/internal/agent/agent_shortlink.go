package agent

import (
	"context"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"google.golang.org/protobuf/proto"
)

const (
	browserToolNamePrefix = "browser_" // 由于浏览器工具的链接太多，导致LLM可能产生幻觉，所以浏览器工具不进行短链接转换
)

// chatReqShortLinkDecorator chat请求装饰器，长链转短链
func chatReqShortLinkDecorator(f chatFunc, placeholders map[string]string) chatFunc {
	if placeholders == nil {
		return f // 不进行长链转短链
	}
	return func(
		ctx context.Context, req *llmm.Request, ch chan *llmm.Response, startTime time.Time, signal chan int) error {
		newReq := proto.Clone(req).(*llmm.Request)
		// 长链转短链

		var userUrls = make(map[string]struct{})
		excludeURLFilter := func(url string) bool {
			_, ok := userUrls[url]
			return !ok
		}
		var browserToolIds = make(map[string]struct{})
		for _, m := range newReq.GetMessages() {
			if m != nil {
				// 用户和system的URL保持不变，不进行转换
				if m.GetRole() == llmm.Role_USER || m.GetRole() == llmm.Role_SYSTEM {
					helper.ExtractAllURLs(m.GetContent(), userUrls)
					helper.ExtractAllURLs(m.GetReasoningContent(), userUrls)
					for _, tool := range m.GetToolCalls() {
						if tool != nil && tool.GetFunction() != nil {
							helper.ExtractAllURLs(tool.GetFunction().GetArguments(), userUrls)
						}
					}
					continue
				}
				// 浏览器插件的URL保持不变，不进行转换
				for _, tool := range m.GetToolCalls() {
					if tool != nil && tool.GetFunction() != nil &&
						strings.HasPrefix(tool.GetFunction().GetName(), browserToolNamePrefix) {
						browserToolIds[tool.GetId()] = struct{}{}
						helper.ExtractAllURLs(tool.GetFunction().GetArguments(), userUrls)
					}
				}
			}
		}

		for _, m := range newReq.GetMessages() {
			if m != nil {
				// 用户和system的URL保持不变，不进行转换
				if m.GetRole() == llmm.Role_USER || m.GetRole() == llmm.Role_SYSTEM {
					continue
				}

				// 浏览器插件的URL保持不变，不进行转换
				if m.GetRole() == llmm.Role_TOOL {
					if _, has := browserToolIds[m.GetToolCallId()]; has {
						continue
					}
				}

				// 其他角色（如机器人）的URL进行转换，但是排除用户和system输入的URL
				m.Content = helper.ReplaceURLsWithPlaceholders(m.GetContent(), placeholders, excludeURLFilter)
				m.ReasoningContent =
					helper.ReplaceURLsWithPlaceholders(m.GetReasoningContent(), placeholders, excludeURLFilter)
				for _, tool := range m.GetToolCalls() {
					if tool != nil && tool.GetFunction() != nil {
						tool.GetFunction().Arguments =
							helper.ReplaceURLsWithPlaceholders(tool.GetFunction().GetArguments(),
								placeholders, excludeURLFilter)
					}
				}
			}
		}
		log.InfoContextf(ctx, "chatReqShortLinkDecorator: placeholders: %s", helper.Object2String(placeholders))
		return f(ctx, newReq, ch, startTime, signal)
	}
}

// chatRspShortLinkDecorator chat响应装饰器，短链转长链
func chatRspShortLinkDecorator(rsp *llmm.Response, placeholders map[string]string) {
	if placeholders == nil {
		return // 不进行短链转长链
	}
	// 短链转长链
	if rsp != nil && rsp.GetMessage() != nil && rsp.GetMessage().GetRole() != llmm.Role_USER {
		rsp.GetMessage().Content = helper.ReplaceURLPlaceholders(rsp.GetMessage().GetContent(), placeholders)
		rsp.GetMessage().ReasoningContent =
			helper.ReplaceURLPlaceholders(rsp.GetMessage().GetReasoningContent(), placeholders)
		for _, toolCall := range rsp.GetMessage().GetToolCalls() {
			// if toolCall != nil && toolCall.GetFunction() != nil &&
			//	!strings.HasPrefix(toolCall.GetFunction().GetName(), browserToolNamePrefix) {
			if toolCall != nil && toolCall.GetFunction() != nil {
				toolCall.GetFunction().Arguments =
					helper.ReplaceURLPlaceholders(toolCall.GetFunction().GetArguments(), placeholders)
			}
		}
	}
}
