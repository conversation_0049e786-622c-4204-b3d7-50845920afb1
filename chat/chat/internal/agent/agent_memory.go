package agent

import (
	"context"
	"strconv"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// initAgentMemory 初始化一个Agent记忆
func (a *ChatAgentImpl) initAgentMemory(ctx context.Context, bs *botsession.BotSession) {
	// 先尝试从 redis 中获取 agent status
	status, _ := a.dao.GetAgentStatus(ctx, strconv.FormatUint(bs.App.GetAppBizId(), 10), bs.SessionID)
	currentAgent := a.agentContainer.EntryAgent // 当前默认在入口 agent
	if status != nil && status.CurrentAgent != "" {
		currentAgent = status.CurrentAgent // 如果状态缓存存在，当前 agent 配置成当前状态的 agent
	} else if status != nil {
		status.CurrentAgent = currentAgent // 兼容历史缓存数据，不存在 CurrentAgent 使用当前主 agent
	}
	m := a.agentContainer.GetAgentModel(currentAgent) // 获取当前模型配置
	m.BotBizID = bs.App.GetAppBizId()
	// 系统Prompt 作为扩展参数传入
	a.history, _ = a.memory.MakeMultiRoundHistories(ctx, bs, uint(m.GetHistoryLimit()), false)
	chatHistory := ""
	for _, h := range a.history {
		chatHistory += "用户输入：" + h[0].Content + "\n客服回答：" + h[1].Content + "\n"
	}
	bs.AgentSystemVar = model.SystemVar{
		UserQuery:    bs.OriginContent,
		RewriteQuery: bs.OriginContent,
		ChatHistory:  strings.TrimSuffix(chatHistory, "\n"),
		CurrentTime:  helper.GetStringTime(),
	}

	hasHistoryThought := status != nil && len(status.Memory) != 0 // redis中有历史思考
	if status != nil && len(status.WorkflowMemory) != 0 && status.AgentType == model.AgentTypeWorkflow {
		a.initAgentWorkflowMemory(ctx, bs, status)
		return
	}
	if hasHistoryThought {
		a.initAgentNormalMemory(ctx, bs, status)
		return
	}
	// 没有思考过程缓存，走下面的逻辑
	for _, his := range a.history {
		a.agentMemory = append(a.agentMemory, &model.AgentMemory{
			Message: &llmm.Message{Role: llmm.Role_USER, Content: his[0].Content},
			From:    "USER",
		})
		a.agentMemory = append(a.agentMemory, &model.AgentMemory{
			Message: &llmm.Message{Role: llmm.Role_ASSISTANT, Content: his[1].Content},
			From:    "MAIN", // 历史记录，都当做来自MAIN？否则存在哪里呢
		})
	}
	// 公共逻辑
	if bs.AgentStatus == nil || bs.AgentStatus.AgentType == "" {
		bs.AgentStatus = &model.AgentStatus{
			AgentType:      model.AgentTypeMain,
			Memory:         make([]*model.AgentMemory, 0),
			WorkflowMemory: make([]*model.AgentMemory, 0),
			UsePDL:         bs.App.GetKnowledgeQa().GetWorkflow().GetUsePdl(),
			CurrentAgent:   a.agentContainer.GetEntryAgent(), // 默认从主 agent 开始执行
			ParentAgents:   nil,                              // 父节点栈为空
		}
	}
	if bs.OriginContent != "" { // 用户问题
		message := &model.AgentMemory{
			Message:  &llmm.Message{Role: llmm.Role_USER, Content: bs.OriginContent},
			From:     "USER",
			CallType: "",
		}
		a.agentMemory = append(a.agentMemory, message)
		bs.AgentStatus.AddWorkflowMemory(message)
	}
	log.InfoContextf(ctx, "initAgentMemory: %s", helper.Object2String(a.agentMemory))
}

// initAgentNormalMemory 初始化一个标准版Agent记忆
func (a *ChatAgentImpl) initAgentNormalMemory(ctx context.Context,
	bs *botsession.BotSession, status *model.AgentStatus) {

	bs.AgentStatus = status
	// 思考当前轮数
	// 需要丢弃掉最后的 config.GetAgentThoughtRound() - 1 轮，使用agentMemory
	stop := len(a.history) - config.GetAgentThoughtRound() + 1
	// 保留 N 轮，传入的 memory 也算一轮，所以只需要再往前找 N-1 轮
	round := config.GetAgentThoughtRound() - 1
	count := len(status.Memory)

	stop = min(stop, count) // 工作流多用几轮思考？
	round = min(round, count)

	for i := 0; i < stop; i++ {
		a.agentMemory = append(a.agentMemory, &model.AgentMemory{
			Message: &llmm.Message{Role: llmm.Role_USER, Content: a.history[i][0].Content},
			From:    "USER",
		})
		a.agentMemory = append(a.agentMemory, &model.AgentMemory{
			Message: &llmm.Message{Role: llmm.Role_ASSISTANT, Content: a.history[i][1].Content},
			From:    "MAIN", // 历史记录，都当做来自MAIN？否则存在哪里呢
		})
	}

	// 添加思考过程  倒序遍历
	index := len(status.Memory) - 1
	for ; index >= 0 && round != 0; index-- {
		if status.Memory[index].Role == llmm.Role_SYSTEM { // 这里碰到系统消息，直接跳过
			break
		}
		if status.Memory[index].Role == llmm.Role_USER { // 把当前找到的那条用户消息也一起保留
			round--
			if round == 0 {
				break
			}
		}
	}
	if index >= 0 {
		a.agentMemory = append(a.agentMemory, status.Memory[index:]...)
	}

	if bs.OriginContent != "" { // 用户问题
		message := &model.AgentMemory{
			Message:  &llmm.Message{Role: llmm.Role_USER, Content: bs.OriginContent},
			From:     "USER",
			CallType: "",
		}
		a.agentMemory = append(a.agentMemory, message)
		bs.AgentStatus.AddWorkflowMemory(message)
	}
	log.InfoContextf(ctx, "initAgentNormalMemory: %s", helper.Object2String(a.agentMemory))
}

// initAgentWorkflowMemory 初始化一个工作流Agent记忆
func (a *ChatAgentImpl) initAgentWorkflowMemory(ctx context.Context,
	bs *botsession.BotSession, status *model.AgentStatus) {

	bs.AgentStatus = status
	// 思考当前轮数
	count := len(status.Memory)
	stop := len(a.history) - min(int(config.App().Bot.HistoryLimit)-1, count) // 工作流多用几轮思考？
	round := min(int(config.App().Bot.HistoryLimit)-1, count)

	for i := 0; i < stop; i++ {
		a.agentMemory = append(a.agentMemory, &model.AgentMemory{
			Message: &llmm.Message{Role: llmm.Role_USER, Content: a.history[i][0].Content},
			From:    "USER",
		})
		a.agentMemory = append(a.agentMemory, &model.AgentMemory{
			Message: &llmm.Message{Role: llmm.Role_ASSISTANT, Content: a.history[i][1].Content},
			From:    "MAIN", // 历史记录，都当做来自MAIN？否则存在哪里呢
		})
	}

	// 添加思考过程  倒序遍历
	index := len(status.WorkflowMemory) - 1
	for ; index > 0 && round != 0; index-- { // 这里Index 等于0要退出，因为0是不一定是用户问题
		if status.WorkflowMemory[index].Role == llmm.Role_USER { // 把当前找到的那条用户消息也一起保留
			round--
			if round == 0 {
				break
			}
		}
	}
	a.agentMemory = append(a.agentMemory, status.WorkflowMemory[index:]...)
	if bs.OriginContent != "" { // 用户问题
		message := &model.AgentMemory{
			Message:  &llmm.Message{Role: llmm.Role_USER, Content: bs.OriginContent},
			From:     "USER",
			CallType: "",
		}
		a.agentMemory = append(a.agentMemory, message)
		bs.AgentStatus.AddWorkflowMemory(message)
	}
	log.InfoContextf(ctx, "initAgentWorkflowMemory: %s", helper.Object2String(a.agentMemory))
}

// addAgentMemory 添加一个Agent记忆
func (a *ChatAgentImpl) addAgentMemory(message *model.AgentMemory) {
	a.agentMemory = append(a.agentMemory, message)
}

// updateAgentMemory 更新一个Agent记忆
func (a *ChatAgentImpl) updateAgentMemory(ctx context.Context, callID, content string) {
	// a.agentMemory倒序遍历
	for i := len(a.agentMemory) - 1; i >= 0; i-- {
		if a.agentMemory[i].Role == llmm.Role_TOOL && a.agentMemory[i].GetToolCallId() == callID {
			log.DebugContextf(ctx, "updateAgentMemory: %s", helper.Object2String(a.agentMemory[i]))
			a.agentMemory[i].Content = content
			break
		}
	}
}

// processAgentMemory 处理智能体的记忆
func (a *ChatAgentImpl) processAgentMemory(bs *botsession.BotSession, rsp *llmm.Response) {
	if len(rsp.GetMessage().GetToolCalls()) > 0 && !a.needOutputDirectly(bs, rsp) { // 直接回复的不管它
		from := "MAIN"
		callType := "tool"
		if bs.AgentStatus.AgentType == model.AgentTypeWorkflow {
			from = bs.AgentStatus.WorkflowName
			callType = "API"
		}
		message := &model.AgentMemory{
			Message: &llmm.Message{
				Role:      llmm.Role_ASSISTANT,
				Content:   rsp.GetMessage().GetContent(),
				ToolCalls: rsp.GetMessage().GetToolCalls(),
			},
			From:     from,
			CallType: callType,
		}

		bs.AgentStatus.AddWorkflowMemory(message)
		a.addAgentMemory(message)
		for _, toolCall := range rsp.GetMessage().GetToolCalls() {
			newMessage := &model.AgentMemory{
				Message: &llmm.Message{Role: llmm.Role_TOOL,
					Content:    "", // 这里留空，后面tool执行完了更新
					ToolCallId: toolCall.GetId()},
				From:     "TOOL",
				CallType: "tool",
			}
			if bs.AgentStatus.AgentType == model.AgentTypeWorkflow {
				newMessage.From = bs.AgentStatus.WorkflowName
				newMessage.CallType = "API"
			}
			a.addAgentMemory(newMessage)
			bs.AgentStatus.AddWorkflowMemory(newMessage)
		}
	}
}
