package agent

import (
	"context"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	openapipb "git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	jsoniter "github.com/json-iterator/go"
)

// GenerateDisplayStatus 生成最终的显示状态。
//
//	pluginLabel: 插件的展示名称，如 "图片生成"、"混元搜索"、"腾讯云 EdgeOne Pages"。
//	toolName:    工具内部名称，如 "TextToImage"、"HunyuanSearchSummary"。
//	tmpl:        模板，形如 "{工具名称} 图片生成中"、"{工具名称} 正在查询" 等。
//	paramValue:  可选参数值，若为空字符串或 nil，则只返回模板替换后的结果。
//
// 返回值示例：
//
//	图片生成/TextToImage 图片生成中[盛开的粉色樱花树……]
//	混元搜索/HunyuanSearchSummary 正在搜索[李白介绍]
//	代码生成/deploy-html 代码生成中
func (a *ChatAgentImpl) GenerateDisplayStatus(pluginName, toolName, arguments string) string {

	paramValue := make(map[string]interface{})
	if arguments != "" {
		// 解析参数值
		_ = jsoniter.Unmarshal([]byte(arguments), &paramValue)
	}

	var tmpl = "{工具名称} 正在执行中"
	displayParam := ""
	// 1. 拼接“插件名称/内部名称”
	fullName := fmt.Sprintf("%s/%s", pluginName, toolName)
	for _, item := range model.AgentToolConfigs {
		if item.ToolName == toolName && item.PluginName == pluginName {
			tmpl = item.MappingTemplate
			displayParam = item.DisplayParam
			break
		}
	}

	// 2. 将模板中的 {工具名称} 占位符替换为上一步结果
	status := strings.ReplaceAll(tmpl, "{工具名称}", fullName)

	// 3. 如果有参数值，则用方括号包裹追加到末尾
	if len(displayParam) > 0 {
		str := fmt.Sprintf("%v", paramValue[displayParam])
		if str != "" {
			status += ":" + str
		}
	}

	return status
}

// GenerateDisplayType 生成最终的显示类型
func (a *ChatAgentImpl) GenerateDisplayType(pluginName, toolName string) uint32 {
	switch toolName {
	case ToolNameHunYuanSearch:
		return event.DisplayTypeSearch
	case ToolNameKnowledge:
		return event.DisplayTypeKnowledge
	}

	for _, item := range model.AgentToolConfigs {
		if item.ToolName == toolName && item.PluginName == pluginName {
			if item.Category == "x5_use" || item.Category == "shell" || item.Category == "filesystem" {
				return event.DisplayTypeSandbox
			}
			break
		}
	}
	return 0
}

// IsToolsContainsBrowserTools 工具中是否包含浏览器工具
func (a *ChatAgentImpl) IsToolsContainsBrowserTools(tools []*openapipb.Tool) bool {
	for _, tool := range tools {
		if strings.HasPrefix(tool.GetFunction().GetName(), browserToolNamePrefix) {
			return true
		}
	}
	return false
}

// processBrowserTools 处理浏览器历史工具
func (a *ChatAgentImpl) processBrowserTools(ctx context.Context, req *llmm.Request) error {
	for i, message := range req.GetMessages() {
		if i == 0 || i == len(req.GetMessages())-1 {
			// 第一个 和最后一个 消息不处理
			continue
		}
		// 当前消息是工具调用，且上一个消息有工具调用且工具名称以浏览器工具前缀开头
		lastMessage := req.GetMessages()[i-1]
		messageIsWanted := message != nil && message.Role == llmm.Role_TOOL &&
			lastMessage != nil && lastMessage.Role == llmm.Role_ASSISTANT
		messageIsWanted = messageIsWanted && len(lastMessage.GetToolCalls()) > 0 &&
			strings.HasPrefix(lastMessage.GetToolCalls()[0].GetFunction().GetName(), browserToolNamePrefix)

		if messageIsWanted {
			// 处理浏览器历史工具
			log.DebugContextf(ctx, "processBrowserTools, message before: %s", message.GetContent())
			message.Content = helper.TrimPageContentSection(message.GetContent())
			log.DebugContextf(ctx, "processBrowserTools, message after: %s", message.GetContent())
		}
	}
	return nil
}
