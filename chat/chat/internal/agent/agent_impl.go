package agent

import (
	"context"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/limiter"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/go-comm/clues"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"git.woa.com/ivy/qbot/qbot/chat/pkg/agentlogger"
	jsoniter "github.com/json-iterator/go"
)

// AgentReply 智能体回复
func (a *ChatAgentImpl) AgentReply(ctx context.Context, bs *botsession.BotSession) (err error) {
	// SystemRole 在多 agent 体系下应该对应到 agent 的 instructions
	// if len(bs.SystemRole) < 1 {
	// 	bs.SystemRole = bs.App.RoleDescription()
	// }
	// 整合用户上传的端上配置和云上配置
	agentContainer, err := a.NewContainer(ctx, bs)
	if err != nil {
		return err
	}
	a.setContainer(*agentContainer)

	a.initAgentMemory(ctx, bs)
	a.initThoughtEvent(ctx, bs)

	// 处理上一次因为本地工具调用导致的中断（如果存在）
	lastStep, err := a.dealLastInterrupt(ctx, bs)
	if err != nil {
		return err
	}

	// bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedureAgent))
	// a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)

	// 判断当前需要用哪个Prompt
	// 启用PDL 且传了WorkflowID，则是PDL调试，使用WorkflowPrompt
	if a.useWorkflowPrompt(ctx, bs) {
		log.DebugContextf(ctx, "useWorkflowPrompt")
		bs.AgentStatus.AgentType = model.AgentTypeWorkflow
	}

	totalStep, maxIterations := lastStep, config.AgentConfig.MaxIterations // totalStep 从上次中断处开始计算
	if config.IsQBAppBizID(bs.App.GetAppBizId()) {
		maxIterations = config.GetAgentConfig().QBConfig.MaxIterations
	}
	for i := 0; i < maxIterations && totalStep < 2*maxIterations; i++ {
		totalStep++
		bs.AgentStatus.CurrentStep = totalStep
		log.InfoContextf(ctx, "current agent status: %s, session_id: %s",
			helper.Object2String(bs.AgentStatus), bs.SessionID)
		currentPrompt := a.getMainAgentPrompt(ctx, bs)
		if bs.AgentStatus.AgentType == model.AgentTypeWorkflow {
			currentPrompt = a.getWorkflowPrompt(ctx, bs)
		}
		// 如果中途切换，在这里判断
		if bs.AgentStatus.AgentSwitch {
			i = 0 // 重置迭代次数，这里如果一直切换有可能会导致无限循环.设置一个totalStep上限
			bs.AgentStatus.AgentSwitch = false
		}
		finish, err := a.agentReply(ctx, bs, currentPrompt)
		if err != nil {
			// bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(event.ProcedureAgent))
			// a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
			return err
		}
		if finish {
			return nil
		}
	}
	// 超过最大迭代次数 强制回复
	currentPrompt := a.getReachThinkingLimitPrompt(ctx, bs)
	a.mustReply = true
	_, err = a.agentReply(ctx, bs, currentPrompt)
	return err
}

// agentReply 智能体回复
func (a *ChatAgentImpl) agentReply(ctx context.Context,
	bs *botsession.BotSession, prompt string) (finish bool, err error) {

	// 封装请求
	req, err := a.NewAgentRequest(ctx, bs, prompt)
	if err != nil {
		log.ErrorContextf(ctx, "build NewAgentRequest error: %v", err)
		return finish, err
	}
	clues.AddTrackData(ctx, "ChatAgentImpl.NewAgentRequest", req)
	step := agentlogger.New(ctx, agentlogger.StepKeyLLM, req)
	isR1 := strings.HasPrefix(a.ThinkingModelName, DeepSeekModelPrefix)
	// 发起请求
	cfg := config.App().Bot
	ch := make(chan *llmm.Response, cfg.ResponseChannelSize)
	g, gCtx := errgroupx.WithContext(ctx)
	agentCtx, cancel := context.WithCancel(ctx)
	isTimeout, isEvil, last, signal := false, false, &llmm.Response{}, make(chan int, 10)
	placeholders := make(map[string]string)
	g.Go(func() error {
		bs.TokenStat.UpdateAgentProcedure(event.NewAgentProcessingTSProcedure(event.ProcedureThinkingModel))
		a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
		// chatDecorator 对函数 chat 调用做一次装饰，处理用户自定义参数
		err := a.chatDecorator(func(ctx context.Context, req *llmm.Request, ch chan *llmm.Response, startTime time.Time,
			signal chan int) error {
			step.Input = req
			return a.dao.Chat(ctx, req, ch, startTime, signal)
		}, bs, placeholders)(agentCtx, req, ch, bs.StartTime, signal) // 调用LLM
		if err != nil {
			log.ErrorContextf(ctx, "chat error: %v", err)
		}
		return err
	})
	g.Go(func() error {
		last, finish, isTimeout, isEvil, err = a.agentStreamDisplay(gCtx, cancel, bs, ch, signal, req, placeholders)
		step.Output = last
		step.ExtraData = placeholders

		if last != nil && !last.GetFinished() {
			last.Finished = true
			extra := event.ReplyExtraInfo{EChartsInfo: a.EChartsInfo}
			re := bs.NewAgentReplyEvent(ctx, last, isEvil, model.ReplyMethodAgent, bs.StartTime, extra)
			_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
		}

		return helper.When(isTimeout, pkg.ErrLLMTimeout, err)
	})
	err = g.Wait()
	step.SetResult(err)
	if !isR1 { // 两阶段的，已经处理过上报了。
		step.UpsertStep(true)
	}
	return finish, err
}

// SendTokenStat 发送 token 统计事件到前端
func (a *ChatAgentImpl) SendTokenStat(ctx context.Context, clientID string, tokenStat *event.TokenStatEvent) {
	if tokenStat == nil {
		return
	}
	ctx, cancel := context.WithCancel(ctx)
	err := a.dao.DoEmitWsClient(ctx, clientID, tokenStat, cancel)
	tse0 := *tokenStat
	tse0.Procedures = append([]event.Procedure{}, tokenStat.Procedures...)
	clues.AddTrackDataWithError(ctx, tokenStat.EventSource+".TokenStatEvent", tse0, err)
}

// NewAgentRequest 构造请求，请求Agent
func (a *ChatAgentImpl) NewAgentRequest(ctx context.Context, bs *botsession.BotSession,
	prompt string) (req *llmm.Request, err error) {
	openAPITools := make([]*openapi.Tool, 0)
	currentAgent := bs.AgentStatus.CurrentAgent
	// m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeAgentMain) // todo ModelType选择
	m := a.agentContainer.GetAgentModel(currentAgent) // 获取模型配置
	m.BotBizID = bs.App.GetAppBizId()
	m.ModelName = config.GetAgentModelMap(m.GetModelName())
	if config.IsQBAppBizID(bs.App.GetAppBizId()) {
		m.ModelName = config.GetAgentConfig().QBConfig.QBModelName
	}
	a.ThinkingModelName = m.GetModelName()
	requestID := model.RequestID(ctx, bs.SessionID, bs.RecordID)
	instructions, _ := a.agentContainer.GetAgentInstructions(bs.AgentStatus.CurrentAgent)
	if a.mustReply {
		req = m.NewAgentRequest(requestID, a.agentMemory, openAPITools, prompt)
		req.ExtraParams.ReachThinkingLimit = a.mustReply
		req.ExtraParams.TaskGoal = instructions
	} else if bs.AgentStatus.AgentType != model.AgentTypeWorkflow { // 超限回复不传tools; 工作流不传tools
		// 获取所有工具
		a.getAllTools(ctx, bs)
		// 获取当前 agent 的配置，包含 handoff 和 local tool 替换原来的 a.wrapOpenAPITools(bs)
		openAPITools, err := a.agentContainer.GetFunctionCalls(ctx, currentAgent, bs)
		if err != nil {
			log.ErrorContextf(ctx, "agent GetFunctionCalls error: %v", err)
			return nil, err
		}
		req = m.NewAgentRequest(requestID, a.agentMemory, openAPITools, prompt)
		req.ExtraParams.TaskGoal = instructions
	} else if bs.AgentStatus.AgentType == model.AgentTypeWorkflow {
		workflowCtx := a.wrapWorkflowCtx(ctx, bs)
		prompt, _ = model.Render(ctx, a.getWorkflowPrompt(ctx, bs), workflowCtx)
		memory := make([]*model.AgentMemory, 0)
		memory = append(memory, &model.AgentMemory{
			Message:  &llmm.Message{Role: llmm.Role_USER, Content: prompt},
			From:     bs.AgentStatus.WorkflowName,
			CallType: "",
		})
		openAPITools = model.GetPDLDefaultTools(bs.WorkflowDebug) // PDL模式，不需要传给下游Tools，只传两个作为示意
		req = m.NewAgentRequest(requestID, memory, openAPITools, prompt)
		req.ExtraParams.ReachThinkingLimit = a.mustReply
		req.ExtraParams.IsPdl = true
	}

	return req, nil
}

// agentStreamDisplay 智能体结果输出
func (a *ChatAgentImpl) agentStreamDisplay(ctx context.Context, cancel context.CancelFunc, bs *botsession.BotSession,
	ch chan *llmm.Response, signal chan int,
	req *llmm.Request, placeholders map[string]string,
) (last *llmm.Response, isFinish, isTimeout, isEvil bool, err error) {
	cfg := config.App().Bot
	throttles := cfg.Throttles
	ticker := time.NewTicker(time.Duration(cfg.StopGeneration.CheckInterval) * time.Millisecond)
	timeout := time.NewTimer(time.Duration(cfg.Timeout) * time.Second)
	timeout2 := time.NewTimer(time.Duration(cfg.Timeout/10) * time.Second)
	throttleCheck := helper.NewThrottle(throttles.Check)
	replyThrottleCheck := helper.NewThrottle(throttles.Check)
	throttleStreaming := helper.NewThrottle(helper.When(bs.StreamingThrottle > 0, bs.StreamingThrottle,
		throttles.Streaming))
	codeStreaming := helper.NewThrottle(helper.When(bs.StreamingThrottle > 0, bs.StreamingThrottle,
		throttles.Streaming))
	index, isFinish, isEvil, last, lastThought, preUseToken := 0, false, false, &llmm.Response{}, 0, uint32(0)
	defer func() {
		ticker.Stop()
		timeout.Stop()
	}()
	for {
		select {
		case <-timeout.C:
			signal <- 1
			// bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(event.ProcedureAgent))
			// a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
			return last, isFinish, true, false, nil
		case <-timeout2.C:
			a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
		case <-ticker.C:
			if ok, _ := a.dao.IsGenerationStopped(ctx, bs.To.CorpStaffID, bs.TokenStat.RecordID); ok {
				last.Finished = true
				extra := event.ReplyExtraInfo{EChartsInfo: a.EChartsInfo}
				re := bs.NewAgentReplyEvent(ctx, last, false, model.ReplyMethodAgent, bs.StartTime, extra)
				_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
				a.processAgentRecord(ctx, bs, last)
				signal <- 1
				return last, true, false, false, nil
			}
		case rsp, ok := <-ch:
			if a.needReportToken(ctx, bs, preUseToken, rsp) {
				preUseToken = rsp.GetStatisticInfo().GetTotalTokens()
			}
			if err = a.chatRspVarDecorator(ctx, rsp, bs); err != nil {
				return last, isFinish, false, false, err
			}
			chatRspShortLinkDecorator(rsp, placeholders) // 短链接转长链接
			timeout.Stop()
			last = rsp
			if !ok {
				return last, isFinish, false, false, pkg.ErrAgentRunError
			}
			if strings.TrimSpace(rsp.GetMessage().GetContent()) == "" &&
				strings.TrimSpace(rsp.GetMessage().GetReasoningContent()) == "" &&
				len(rsp.GetMessage().GetToolCalls()) == 0 {
				continue
			}
			if index == 0 && !a.mustReply { // 最后超限回复 不处理思考事件
				metrics.ReportThoughtFirstToken(ctx, time.Since(bs.StartTime).Milliseconds())
				a.addThoughtProcedure(rsp.GetMessage().GetContent())
				index++
			}
			if isEvil, isFinish, err = a.processAgentResponse(ctx, bs,
				rsp, throttleCheck, replyThrottleCheck, throttleStreaming, codeStreaming, &index,
				&lastThought, signal, req); isEvil || err != nil {
				signal <- 1
				return last, isFinish, false, isEvil, err
			}
			if isFinish || rsp.Finished {
				last = rsp // 在中间rsp被手工置为finished
				return last, isFinish, false, isEvil, nil
			}
		}
	}
}

// processAgentResponse 处理智能体的返回  1. 处理Thought事件; 2. 处理Memory; 3. tool调用; 4. 安全审核; 5. 最终结果输出
func (a *ChatAgentImpl) processAgentResponse(ctx context.Context, bs *botsession.BotSession,
	rsp *llmm.Response, throttleCheck, replyThrottleCheck, throttleStreaming, codeStreaming helper.Throttle,
	index, lastThought *int, signal chan int, req *llmm.Request) (isEvil, isFinish bool, err error) {
	if a.processAgentCheck(ctx, bs, rsp, throttleCheck) {
		return true, true, nil
	}

	if a.mustReply { // 强制回复,安全审核？
		if *index == 0 {
			metrics.ReportContentFirstToken(ctx, time.Since(bs.StartTime).Milliseconds())
			*index++
		}
		return a.processAgentMustReply(ctx, bs, rsp)
	}
	// 处理必须要回复的，都要处理思考事件
	r1ThoughtFinish := len(rsp.GetMessage().GetReasoningContent()) > 0 && len(rsp.GetMessage().GetContent()) > 0
	lastThoughtOutput := *lastThought == 0 && (r1ThoughtFinish || a.needOutputDirectly(bs, rsp) || a.needUseTool(rsp) ||
		rsp.GetFinished())
	a.processAgentThought(ctx, bs, rsp, throttleStreaming, lastThoughtOutput)
	if lastThoughtOutput {
		*lastThought++
		if strings.HasPrefix(a.ThinkingModelName, DeepSeekModelPrefix) {
			signal <- 1
			llmStep := agentlogger.New(ctx, agentlogger.StepKeyLLM, req)
			llmStep.Output = rsp
			llmStep.SetResult(nil)
			llmStep.UpsertStep(true)
			isFinish, err = a.thinkingModelCompletion(ctx, bs, req, rsp.GetMessage().GetReasoningContent())

			log.InfoContextf(ctx, "thinkingModelCompletion, isFinish: %v, err: %v", isFinish, err)
			rsp.Finished = true
			return false, isFinish, err
		}
	}
	return a.processFunctionCalls(ctx, bs, rsp, throttleStreaming, replyThrottleCheck, codeStreaming, index)
}

// processFunctionCalls 处理函数调用
func (a *ChatAgentImpl) processFunctionCalls(ctx context.Context, bs *botsession.BotSession, rsp *llmm.Response,
	throttleStreaming, replyThrottleCheck, codeStreaming helper.Throttle, index *int) (isEvil, isFinish bool, err error) {
	// Agent直接回复
	if a.needOutputDirectly(bs, rsp) && throttleStreaming.Hit(len([]rune(bs.GetAgentReply(ctx, rsp))), rsp.GetFinished()) {
		if a.processAgentReplyCheck(ctx, bs, rsp, replyThrottleCheck) {
			return true, true, nil
		}

		if rsp.Finished {
			d := event.ProcedureDebugging{CustomVariables: bs.CustomVariablesForDisplay}
			d.Agent.Output = rsp.GetMessage().GetContent()
			bs.TokenStat.UpdateAgentProcedure(event.NewAgentSuccessTSProcedure(event.ProcedureThinkingModel,
				nil, d, []*event.TokenUsage{}))
			a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
			if a.ExtractFileFromOutput(rsp.GetMessage().GetContent()) {
				a.updateThoughtFiles()
				a.ThoughtEventReply(ctx, bs)
			}
		}

		if *index == 1 {
			metrics.ReportContentFirstToken(ctx, time.Since(bs.StartTime).Milliseconds())
			*index++
		}
		return a.processAgentDirectlyReply(ctx, bs, rsp)
	}

	if rsp.Finished {
		d := event.ProcedureDebugging{CustomVariables: bs.CustomVariablesForDisplay}
		d.Agent.Output = helper.Object2StringEscapeHTML(rsp.GetMessage().GetToolCalls())
		bs.TokenStat.UpdateAgentProcedure(event.NewAgentSuccessTSProcedure(event.ProcedureThinkingModel,
			nil, d, []*event.TokenUsage{}))
		a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
	}
	// 优先处理 handoff
	if a.hitHandoff(ctx, bs, rsp) {
		message := &model.AgentMemory{
			Message: &llmm.Message{
				Role:      llmm.Role_ASSISTANT,
				Content:   rsp.GetMessage().GetContent(),
				ToolCalls: rsp.GetMessage().GetToolCalls(),
			},
			From:     "MAIN",
			CallType: "API",
		}
		bs.AgentStatus.AddMemory([]*model.AgentMemory{message})
		if !config.IsQBAppBizID(bs.App.AppBizId) {
			// 浏览器情况下，转交的消息不加入历史记录
			a.addAgentMemory(message)
		}
		// 如果命中了工作流
		if workflow, ok := a.hitWorkflow(ctx, bs, rsp); ok {
			message.CallType = "workflow"
			bs.AgentStatus.AddWorkflowMemory(message)
			return a.processWorkflowThought(ctx, bs, rsp, workflow)
		} else {
			bs.AgentStatus.AddWorkflowMemory(message)
			// TODO(willzhen) 这里是否还需要 AddWorkflowMemory， 是否还有其他处理？
			return false, false, nil
		}
	}

	// switch_to_main 会话切换到主Agent :switch_workflow
	if a.isHandoffToParent(bs, rsp, false) {
		a.thought.IsWorkflow = false
		bs.WorkflowID = ""
		bs.AgentStatus.AgentBacktrace()
		log.InfoContextf(ctx, "agent backtrace to %s", bs.AgentStatus.CurrentAgent)
		return false, false, nil
	}

	// 需要流式展示工具输入
	if a.needUseTool(rsp) && isCodeInterpreter(rsp.GetMessage().GetToolCalls()[0].GetFunction().GetName()) {
		return a.processStreamAgentTools(ctx, bs, rsp, codeStreaming)
	}

	// 需要调用工具
	if a.needUseTool(rsp) && rsp.GetFinished() {
		return a.processAgentTools(ctx, bs, rsp)
	}

	return isEvil, isFinish, nil
}

// needOutputDirectly 判断是否需要直接输出
func (a *ChatAgentImpl) needOutputDirectly(bs *botsession.BotSession, rsp *llmm.Response) bool {
	if config.IsQBAppBizID(bs.App.GetAppBizId()) && rsp.Finished && len(rsp.Message.ToolCalls) == 0 {
		// 浏览器的特殊处理逻辑，没有工具调用就结束循环
		return true
	}
	for _, toolCall := range rsp.GetMessage().ToolCalls {
		if model.IsDefaultTools(toolCall.GetFunction().GetName()) {
			return true
		}
	}
	return false
}

// isHandoffToParent 判断是否转交到主Agent
func (a *ChatAgentImpl) isHandoffToParent(bs *botsession.BotSession,
	rsp *llmm.Response, force bool) bool {
	if config.IsQBAppBizID(bs.App.GetAppBizId()) {
		// 浏览器不处理 task_done
		return false
	}
	if len(rsp.GetMessage().GetToolCalls()) > 0 && (force || rsp.Finished) &&
		(rsp.GetMessage().GetToolCalls()[0].GetFunction().GetName() == model.SysToolSwitch2Main ||
			rsp.GetMessage().GetToolCalls()[0].GetFunction().GetName() == model.SysToolSwitch2Parent) {
		// 处理模型幻觉，在入口 agent 回返回 task_done
		if bs.AgentStatus.AgentSwitch {
			return true
		}
		if bs.AgentStatus.CurrentAgent == a.agentContainer.EntryAgent {
			// 入口 agent 不能再往回转交了
			return false
		}
		return true
	}
	return false
}

// processAgentRecord 处理智能体的记录
func (a *ChatAgentImpl) processAgentRecord(ctx context.Context,
	bs *botsession.BotSession, rsp *llmm.Response) (isFinish bool) {
	if rsp.Finished { // 结果写DB 调用工具不写DB
		// bs.TokenStat.UpdateProcedure(event.NewSuccessTSProcedure(event.ProcedureAgent,
		//	nil, event.ProcedureDebugging{}, []*event.TokenUsage{}))
		// a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)

		reply := rsp.GetMessage().GetContent() // 超限回复时，取content
		if !a.mustReply {
			reply = bs.GetAgentReply(ctx, rsp) // 其他情况取 tools里面的回复
		}
		reply = helper.ReplaceInvalidHTML(reply)
		reply = helper.FillEmptyLinkText(reply)
		reply = strings.ReplaceAll(reply, "\\n", "\n") // todo 统一加到配置里
		reply = strings.ReplaceAll(reply, "\\\\", "\\")
		reply = strings.ReplaceAll(reply, "\\u0026", "\u0026")
		reply = strings.ReplaceAll(reply, "\\u003c", "\u003c")
		reply = strings.ReplaceAll(reply, "\\u003e", "\u003e")
		// 更新状态
		_ = a.dao.SetAgentStatus(ctx, strconv.FormatUint(bs.App.GetAppBizId(), 10),
			bs.SessionID, bs.AgentStatus)

		lastEvil := &infosec.CheckRsp{ResultCode: bs.Msg.ResultCode, ResultType: bs.Msg.ResultType}
		record := bs.NewBotRecord(ctx, reply,
			nil, model.ReplyMethodAgent, lastEvil, bs.StartTime)
		log.DebugContextf(ctx, "Token Stat: %v", helper.Object2String(bs.TokenStat))
		newRecord, newStat := event.GetMsgRecordAndTokenStat(ctx, record)
		newStat.AgentThought, _ = jsoniter.MarshalToString(a.thought)
		_, _ = a.dao.CreateMsgRecord(ctx, newRecord, newStat) // for answer
		isFinish = true
	}
	return isFinish
}

// processAgentMustReply 处理Agent超限回复
func (a *ChatAgentImpl) processAgentMustReply(ctx context.Context, bs *botsession.BotSession,
	rsp *llmm.Response) (isEvil, isFinish bool, err error) {

	m := &llmm.Message{Role: llmm.Role_ASSISTANT, Content: rsp.GetMessage().GetContent()}
	from, callType := "MAIN", "main"
	if bs.AgentStatus.AgentType == model.AgentTypeWorkflow {
		from = bs.AgentStatus.WorkflowName
		callType = "workflow"
	}
	if rsp.Finished {
		message := &model.AgentMemory{Message: m, From: from, CallType: callType}
		a.addAgentMemory(message)
		bs.AgentStatus.AddWorkflowMemory(message)
		bs.AgentStatus.AddMemory(a.agentMemory)
		d := event.ProcedureDebugging{CustomVariables: bs.CustomVariablesForDisplay}
		d.Agent.Output = helper.Object2StringEscapeHTML(rsp.GetMessage().GetToolCalls())
		bs.TokenStat.UpdateAgentProcedure(event.NewAgentSuccessTSProcedure(event.ProcedureThinkingModel,
			nil, d, []*event.TokenUsage{}))
		a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
	}
	rsp.GetMessage().Content = helper.ReplaceInvalidHTML(rsp.GetMessage().GetContent())
	ctx, cancel := context.WithCancel(ctx)
	re := bs.NewReplyEvent(ctx, rsp, isEvil, model.ReplyMethodAgent, bs.StartTime, nil)
	_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
	isFinish = a.processAgentRecord(ctx, bs, rsp)
	return false, isFinish, nil
}

// processAgentDirectlyReply 处理Agent直接回复
func (a *ChatAgentImpl) processAgentDirectlyReply(ctx context.Context, bs *botsession.BotSession,
	rsp *llmm.Response) (isEvil, isFinish bool, err error) {
	ctx, cancel := context.WithCancel(ctx)
	extra := event.ReplyExtraInfo{EChartsInfo: a.EChartsInfo}
	re := bs.NewAgentReplyEvent(ctx, rsp, isEvil, model.ReplyMethodAgent, bs.StartTime, extra)
	_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
	if !rsp.GetFinished() {
		return false, false, nil
	}
	toolcalls := rsp.GetMessage().GetToolCalls()
	m := &llmm.Message{
		Role:      llmm.Role_ASSISTANT,
		Content:   rsp.GetMessage().GetContent(),
		ToolCalls: toolcalls,
	}
	from, callType := "MAIN", "main"
	if len(toolcalls) > 0 && toolcalls[0].GetFunction().GetName() == "response_to_user" {
		from = bs.AgentStatus.WorkflowName
		callType = "workflow"
	}
	a.addAgentMemory(&model.AgentMemory{Message: m, From: from, CallType: callType})
	bs.AgentStatus.AddMemory(a.agentMemory)
	bs.AgentStatus.AddWorkflowMemory(&model.AgentMemory{Message: m, From: from, CallType: callType})

	isFinish = a.processAgentRecord(ctx, bs, rsp)
	return false, isFinish, nil
}

// needReportToken 上报使用token 给到统计
func (a *ChatAgentImpl) needReportToken(ctx context.Context, bs *botsession.BotSession, preToken uint32,
	last *llmm.Response) bool {
	currToken := last.GetStatisticInfo().GetTotalTokens()
	if currToken <= preToken {
		return false
	}
	need := currToken-preToken > uint32(config.App().Limiter.ReportJudgeLength)
	if !need && !last.GetFinished() { // 如果没有达到上报的阈值，并且不是最后一个包，则不上报
		return false
	}
	log.DebugContextf(ctx, "reportUseToken: preToken:%d, currToken:%d", preToken, currToken)
	uin, sin, _ := a.dao.GetUinByCorpID(ctx, bs.App.GetCorpId())
	a.dao.ReportToken(ctx, limiter.ReportTokenReq{
		Uin:       strconv.FormatUint(uin, 10),
		SID:       uint32(sin),
		AppBizID:  strconv.FormatUint(bs.App.GetAppBizId(), 10),
		ModelName: bs.App.GetAgentMainModelName(),
		Token:     uint64(currToken - preToken),
	})
	return true
}
