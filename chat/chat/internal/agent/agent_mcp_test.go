package agent

import (
	"context"
	"testing"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/stretchr/testify/require"
)

// mcp/call_tool_result.go
type CallToolResult2 struct {
	Content []mcp.TextContent `json:"content"`
}

// TestExtractDisplayURL 测试extractDisplayURL
func TestExtractDisplayURL(t *testing.T) {

	c1 := mcp.TextContent{
		Type: "text",
		Text: "运行环境实时查看链接：https://devwss.testsite.woa.com/novnc/vnc_lite.html?token=9902c569-8bd4-4436-83f4-e90d6898d26b1d7f02ae-80b4-42fb-9520-bbdf3b9d5dd1fe987f2e-82ad-4b3f-bded-05b13684b9e4ca1f99e0-0cbb-4029-8279-1d36b5455a38&traceId=6c55282c-0029-4fbf-888a-1f326501f1d3&env=dev",
	}
	c2 := mcp.TextContent{
		Type: "text",
		Text: "{\"action_result\":\"Action result: Success! Navigated to https://www.tencent.com/zh-cn/ir/financial-reports\\n\\n>>>>> Page Content\\nState of current webpage. NOTE that the following is one-time information!\\n[Start of state]\\nPrevious page:  (about:blank)\\nAction: go_to_url ({\\\"url\\\":\\\"https://www.tencent.com/zh-cn/ir/financial-reports\\\"})\\nAction Result: Your current page information has changed to 投资者 - Tencent 腾讯 (https://www.tencent.com/zh-cn/investors.html)\\n\\nDownloadable resources from top layer of current page inside the viewport: [Start of page]\\n[36]<a 业绩新闻/>points to a pdf.\\n[38]<a 业绩演示/>points to a pdf.\\n[41]<a />points to a pdf.\\n[45]<a PDF/>points to a pdf.\\n[48]<a />points to a pdf.\\n[52]<a />points to a pdf.\\n[56]<a />points to a pdf.\\n[62]<a PDF/>points to a pdf.\\n[65]<a PDF/>points to a pdf.\\n[73]<a />points to a pdf.\\n[77]<a />points to a pdf.\\n[81]<a />points to a xls.\\n[85]<a />points to a pdf.\\n[116]<a -\\t以电子方式发布公司通讯之最新安排/>points to a pdf.\\n[117]<a -\\t登记股东适用之申请表格/>points to a pdf.\\n[118]<a -\\t以电子方式发布公司通讯之最新安排/>points to a pdf.\\n[119]<a -\\t非登记持有人适用之申请表格/>points to a pdf.\\n[End of page]\\n\\nInteractive elements from top layer of current page inside the viewport: [Start of page]\\n[0]<a />\\n[1]<h1 Tencent腾讯/>\\n[2]<ul 简体\\n|\\n繁体\\n|\\nEnglish/>\\n[3]<li 公司简介\\n愿景及使命\\n发展历程\\n业务架构\\n管理团队\\n董事会成员\\n企业文化\\n办公地点/>\\n[4]<a 简介/>\\n[5]<li 面向用户\\n面向企业\\n创新科技/>\\n[6]<a 业务/>\\n[7]<li 人才发展\\n腾讯学堂\\n工作环境\\n员工活动/>\\n[8]<a 员工/>\\n[9]<li 环境\\n社会\\n治理\\nESG评级\\n报告/>\\n[10]<a ESG/>\\n[11]<li 季度业绩及投资者新闻\\n公告及财务报告\\n业绩电话会及投资者日历\\n投资者工具包\\n证券及债券信息\\n环境、社会及管治\\n股东资讯/>\\n[12]<a 投资者/>\\n[13]<li 企业动态\\n财务新闻\\n腾讯视角\\n媒体资料库/>\\n[14]<a 媒体/>\\n[15]<a 简/>\\n|\\n[16]<a 繁/>\\n|\\n[17]<a EN/>\\n连接投资者\\n开诚务实，为投资者创造长远价值\\n[18]<li />\\n[19]<a 季度业绩及投资者新闻/>\\n[20]<li />\\n[21]<a 公告及财务报告/>\\n[22]<li />\\n[23]<a 业绩电话会及投资者日历/>\\n[24]<li />\\n[25]<a 工具包/>\\n[26]<li />\\n[27]<a 证券及债券信息/>\\n[28]<li />\\n[29]<a 环境、社会及管治/>\\n[30]<li />\\n[31]<a 股东资讯/>\\n季度业绩\\n[32]<a 查看全部/>\\n[33]<a />\\n[34]<div />\\n[35]<h3 腾讯公布二零二五年第一季业绩/>\\n[36]<a 业绩新闻/>\\n[37]<img />\\n[38]<a 业绩演示/>\\n[39]<img />\\n电话会回放\\n港交所公告\\n投资者新闻\\n[40]<a 查看全部/>\\n1\\n[41]<a />\\n[42]<p 股东周年大会通告/>\\n[43]<span 2025.04.08/>\\n[44]<span PDF/>\\n2\\n[45]<a PDF/>\\n[46]<p 关于中国军工企业名单的自愿性公告\\n2025.01.07/>\\n3\\n截至二零二三年十二月三十一日止年度末期股息\\n2024.03.20\\nPDF\\n公告\\n[47]<a 查看全部/>\\n1\\n[48]<a />\\n[49]<p 二零二五年股东周年大会投票表决结果/>\\n[50]<span 2025.05.14/>\\n[51]<span PDF/>\\n2\\n[52]<a />\\n[53]<p 截至二零二五年三月三十一日止三个月业绩公布/>\\n[54]<span 2025.05.14/>\\n[55]<span PDF/>\\n3\\n[56]<a />\\n[57]<p 截至二零二五年四月三十日止之股份发行人的证券变动月报表/>\\n[58]<span 2025.05.08/>\\n[59]<span PDF/>\\n财务报告\\n[60]<input select;;text;2024/>\\n[61]<a 查看全部/>\\n[62]<a PDF/>\\n[63]<img />\\n[64]<p 2024 年度报告/>\\n[65]<a PDF/>\\n[66]<img />\\n[67]<p 2024 中期报告/>\\n[68]<a 播放/>\\n[69]<span />\\n[70]<a 查看全部/>\\n[71]<a 播放/>\\n[72]<span />\\n[73]<a />\\n[74]<img />\\n[75]<span PDF/>\\n[76]<h3 企业介绍/>\\n[77]<a />\\n[78]<img />\\n[79]<span PDF/>\\n[80]<h3 股东回报/>\\n[81]<a />\\n[82]<img />\\n[83]<span XLS/>\\n[84]<h3 历史运营数据/>\\n[85]<a />\\n[86]<img />\\n[87]<span PDF/>\\n[88]<h3 产品列表/>\\n[89]<a />\\n[90]<img />\\n[91]<p 股价互动资讯图及派息记录/>\\n[92]<a />\\n[93]<img />\\n[94]<p 分析师列表/>\\n[95]<a />\\n[96]<img />\\n[97]<p 债券信用评级/>\\n[98]<a />\\n[99]<img />\\n[100]<p 环境/>\\n[101]<a />\\n[102]<img />\\n[103]<p 社会/>\\n[104]<a />\\n[105]<img />\\n[106]<p 治理/>\\n[107]<a />\\n[108]<img />\\n[109]<p 政策/>\\n[110]<a />\\n[111]<img />\\n[112]<p 报告/>\\n[113]<a />\\n[114]<img />\\n[115]<p ESG评级/>\\n[116]<a -\\t以电子方式发布公司通讯之最新安排/>\\n[117]<a -\\t登记股东适用之申请表格/>\\n[118]<a -\\t以电子方式发布公司通讯之最新安排/>\\n[119]<a -\\t非登记持有人适用之申请表格/>\\n[120]<a <EMAIL>/>\\n投资者查询\\n[121]<a />\\n[122]<i />\\n[123]<img />\\n[124]<a />\\n[125]<i />\\n[126]<a />\\n[127]<i />\\n[128]<a />\\n[129]<i />\\n[130]<a />\\n[131]<i />\\n[132]<a 社会招聘/>\\n[133]<a 校园招聘/>\\n[134]<a 国际招聘/>\\n[135]<a 客户服务/>\\n[136]<a 合作洽谈/>\\n[137]<a 腾讯采购/>\\n[138]<a 诚信合规/>\\n[139]<a 媒体及投资者/>\\n[140]<a 服务协议/>\\n[141]<a 隐私政策/>\\n[142]<a 知识产权/>\\n[143]<img />\\n[144]<a 法律声明/>\\n[145]<a 阳光准则/>\\n[146]<a 网站地图/>\\n[147]<a 粤网文[2023]2882-203号/>\\n[148]<a 粤公网安备 44030502008569号/>\\n您的Cookies偏好\\n[150]<div />\\n欢迎来到Tencent.com！\\n我们希望使用分析型Cookies和类似技术 (“Cookies”) 来改善我们的网站。\\n          Cookies收集的信息不会识别您个人。有关我们使用的Cookies的类型以及您的偏好选项（包括如何更改您的偏好设置）的更多信息，请查看此处的\\n[151]<a Cookies政策/>\\n。\\n[152]<div 接受所有分析型Cookies/>\\n[153]<div 拒绝所有分析型Cookies/>\\n... 4752 pixels below - scroll or extract content to see more ...\\n[End of state]\",\"page_screenshot\":\"https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/sandbox/dev/1922198347043040704/1a465619-347f-4a61-a3d0-f7b74aba8616/screenshot_1b4ec137-bbe0-4fd4-b6d2-56eee6689660.webp?a=123\"}",
	}

	var input1 = &mcp.CallToolResult{}
	input1.Content = append(input1.Content, c1)
	input1.Content = append(input1.Content, c2)
	// 测试数据
	testCases := []struct {
		input *mcp.CallToolResult
		want  string
	}{
		{
			input: input1,
			want:  "https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/sandbox/dev/1922198347043040704/1a465619-347f-4a61-a3d0-f7b74aba8616/screenshot_1b4ec137-bbe0-4fd4-b6d2-56eee6689660.webp?a=123",
		},
	}

	for _, testCase := range testCases {
		result := extractDisplayURL(context.Background(), testCase.input)
		t.Logf("Result: %s", result)
		require.EqualValues(t, testCase.want, result)

	}
}
