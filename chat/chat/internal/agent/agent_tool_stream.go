package agent

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	pluginRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_exec_server"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	openapipb "git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"git.woa.com/ivy/qbot/qbot/chat/pkg/agentlogger"
	jsoniter "github.com/json-iterator/go"
)

// processStreamTools 调用返回的工具
func (a *ChatAgentImpl) processStreamTools(ctx context.Context, bs *botsession.BotSession,
	toolCall *openapi.ToolCall) (toolResponse string, status bool) {
	log.InfoContextf(ctx, "processStreamTools: %s", toolCall.GetFunction().GetName())
	step := agentlogger.New(ctx, agentlogger.StepKeyPlugin, "")
	defer func() {
		step.Output = toolResponse
		step.SetResult(nil)
		step.UpsertStep(true)
	}()
	var labels []*pluginRun.Label
	for k, v := range bs.CustomVariables {
		labels = append(labels, &pluginRun.Label{
			Name:   k,
			Values: []string{v},
		})
	}
	histories := a.getHistoryInfo(ctx, bs)

	toolName := toolCall.GetFunction().GetName()

	data := helper.Arguments2Map(toolCall.Function.Arguments)
	// Note(willzhen) 这里移到 rsp 装饰器里面统一处理
	// // 模型不可见且有默认值处理
	// for _, tool := range bs.ToolsInfo {
	// 	if tool.ToolName == toolName {
	// 		if len(tool.GetInputs()) > 0 {
	// 			for _, param := range tool.GetInputs() {
	// 				if param.GetAgentHidden() && param.GetDefaultValue() != "" { // 模型不可见且有默认值
	// 					data[param.GetName()] = HandleDeDefaultValue(param.GetType(), param.GetDefaultValue())
	// 				}
	// 			}
	// 		}
	// 		break
	// 	}
	// }
	log.InfoContextf(ctx, "processStreamTools|data: %s", helper.Object2String(data))
	arguments, _ := json.Marshal(data)
	toolCall.Function.Arguments = string(arguments)
	if tool, ok := bs.ToolsInfo[toolName]; ok {
		req := &pluginRun.RunToolReq{
			AppId:       strconv.FormatUint(bs.App.GetAppBizId(), 10),
			AppScene:    uint32(bs.Scene),
			PluginId:    tool.PluginId,
			ToolId:      tool.ToolId,
			HeaderValue: "",
			QueryValue:  "",
			BodyValue:   "",
			InputValue:  toolCall.GetFunction().Arguments,
			ExtraInfo: &pluginRun.ToolExtraInfo{
				// SystemRole: bs.SystemRole,
				SessionId: bs.SessionID,
				Labels:    labels,
				History:   histories,
			},
		}
		step.Input = req
		step.ExtraData = toolCall.GetFunction().GetName()
		streamReq := &pluginRun.StreamRunToolReq{
			Req:  req,
			Type: pluginRun.StreamRunToolReq_RUN,
		}
		log.InfoContextf(ctx, "processStreamTools｜req: %s", helper.Object2String(streamReq))

		cfg := config.App().Bot
		ch := make(chan *pluginRun.StreamRunToolRsp, cfg.ResponseChannelSize)
		g, gCtx := errgroupx.WithContext(ctx)
		signal := make(chan int, 10)
		g.Go(func() error {
			if err := a.dao.RunStreamTool(ctx, streamReq, ch, signal); !errors.Is(err, context.Canceled) {
				return err
			}
			return nil
		})
		g.Go(func() error {
			last, isTimeout := a.toolStreamReply(gCtx, bs, toolCall, ch, signal)
			toolResponse = last.GetRsp().GetRawResult()
			return helper.When(isTimeout, pkg.ErrLLMTimeout, nil)
		})
		if err := g.Wait(); err != nil {
			toolResponse = fmt.Sprintf("工具{%s}调用失败: %s", toolCall.GetFunction().GetName(), err.Error())
			return toolResponse, false
		}
		return toolResponse, true
	}
	toolResponse = fmt.Sprintf("工具{%s}不可用, 请尝试使用其他工具", toolCall.GetFunction().GetName())
	return toolResponse, false
}

// toolStreamReply 流式处理工具回复
func (a *ChatAgentImpl) toolStreamReply(ctx context.Context, bs *botsession.BotSession, toolCall *openapi.ToolCall,
	ch chan *pluginRun.StreamRunToolRsp, signal chan int) (last *pluginRun.StreamRunToolRsp, isTimeout bool) {
	cfg := config.App().Bot
	timeout := time.NewTimer(time.Duration(cfg.Timeout) * time.Second)
	ticker := time.NewTicker(time.Duration(cfg.StopGeneration.CheckInterval) * time.Millisecond)

	toolName := toolCall.GetFunction().GetName()
	p := event.NewToolProcessingTSProcedure(event.ProcedureAgentTool, toolName,
		helper.Object2StringEscapeHTML(toolCall.GetFunction()))
	bs.TokenStat.UpdateAgentProcedure(p)
	a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			signal <- 1
			log.ErrorContext(ctx, "toolStreamReply timeout")
			bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(event.ProcedureAgentTool))
			a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
			return last, true
		case <-ticker.C:
			if ok, _ := a.dao.IsGenerationStopped(ctx, bs.To.CorpStaffID, bs.RecordID); ok {
				log.InfoContextf(ctx, "toolStreamReply stopGeneration")
				a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
				signal <- 1
				return last, false
			}
		case rsp, ok := <-ch:
			timeout.Stop()
			if !ok {
				return last, false
			}
			last = rsp
			// 把result使用Json格式化一下
			procedure := a.thought.Procedures[len(a.thought.Procedures)-1]
			procedure = a.specialDisplayTools(ctx, procedure, toolCall, rsp.GetRsp().GetRawResult())
			if last.IsFinal {
				toolResponse := rsp.GetRsp().GetRawResult()
				bs.TokenStat.UpdateAgentProcedure(event.NewToolSuccessTSProcedure(toolName, toolResponse, p))
			}
			a.updateThoughtProcedure(procedure)
			a.ThoughtEventReply(ctx, bs)
		}
	}
}

func (a *ChatAgentImpl) getHistoryInfo(ctx context.Context, bs *botsession.BotSession) []*pluginRun.History {
	log.InfoContextf(ctx, "getHistoryInfo: %+v", bs.ChatHistoriesV2.ChatStack)
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessage)
	limit := int(m.GetHistoryLimit())
	num := helper.When(limit > len(bs.ChatHistoriesV2.ChatStack), len(bs.ChatHistoriesV2.ChatStack), limit)
	start := len(bs.ChatHistoriesV2.ChatStack) - num // 指定截取的起始位置
	histories := make([]*pluginRun.History, 0, num*2)
	for i := start; i < len(bs.ChatHistoriesV2.ChatStack); i++ {
		item := bs.ChatHistoriesV2.ChatStack[i]
		if helper.IsQueryContainsImage(item.OriginQuery) { // 问题中包含图片
			md := helper.New()
			_, p := md.ExtractLinkWithPlaceholder([]byte(item.OriginQuery))
			var placeholder, question string
			var totalIndex = 0 // 处理图片的总index
			for i := 0; i < len(p); i++ {
				placeholder = placeholder + fmt.Sprintf(config.App().MultiModal.Placeholder, i+totalIndex)
			}
			totalIndex = totalIndex + len(p)
			if helper.IsQueryOnlyImage(item.OriginQuery) { // 问题中仅包含图片
				question = placeholder + config.App().MultiModal.GetCaptionPrompt
			} else {
				question = helper.When(item.RewriteQuery != "", item.RewriteQuery, item.OriginQuery) // 使用改写后的query
			}
			histories = append(histories, &pluginRun.History{
				Role:    pluginRun.History_USER,
				Content: question,
				Images:  helper.GetImagesFromPlaceholders(p),
			})
		} else {
			histories = append(histories, &pluginRun.History{
				Role:    pluginRun.History_USER,
				Content: helper.When(item.RewriteQuery != "", item.RewriteQuery, item.OriginQuery), // 使用改写后的query
			})
		}
		histories = append(histories, &pluginRun.History{
			Role:    pluginRun.History_ASSISTANT,
			Content: strings.ReplaceAll(item.GetAssistantContent(), "\n", ""),
		})
	}
	return histories
}

// processStreamAgentTools 处理智能体的工具
func (a *ChatAgentImpl) processStreamAgentTools(
	ctx context.Context,
	bs *botsession.BotSession,
	rsp *llmm.Response,
	codeStreaming helper.Throttle,
) (isEvil, isFinish bool, err error) {
	tik := time.Now()
	call := rsp.GetMessage().GetToolCalls()[0]
	toolName := call.GetFunction().GetName()
	// 1. 初始化执行流程
	proc := event.AgentProcedure{}
	content := a.handleStreamCodeInterpreterDisplay(call.GetFunction().GetArguments())
	content = strings.ReplaceAll(content, "\\n", "\n")
	if codeStreaming.Hit(len([]rune(content)), false) {
		proc, _ = a.initStreamProcedure(ctx, bs, rsp, toolName, call)
	}

	// 2. 工具执行
	if rsp.GetFinished() {
		a.processAgentMemory(bs, rsp)
		proc, _ = a.initStreamProcedure(ctx, bs, rsp, toolName, call)
		toolResp, status := a.executeTool(ctx, bs, call, tik)
		if proc.Debugging.DisplayType == event.DisplayTypeSandbox {
			proc.Debugging.SandboxURL = a.SandboxURL
			proc.Debugging.DisplayURL = a.DisplayURL
		}
		proc.Status = event.ProcedureStatusSuccess
		if !status {
			proc.Status = event.ProcedureStatusFailed
		}
		// 3. 特殊展示处理
		proc = a.specialDisplayTools(ctx, proc, call, toolResp)
		log.DebugContextf(ctx, "last procedure is: %s", helper.Object2StringEscapeHTML(proc))
		a.updateThoughtProcedure(proc)
		a.ThoughtEventReply(ctx, bs)

		// 4. 输出过滤与记忆更新
		err = a.finalizeResponse(ctx, bs, toolName, call.GetId(), toolResp)
	}
	return false, false, err
}

// initStreamProcedure 构造并上报初始 Procedure
func (a *ChatAgentImpl) initStreamProcedure(
	ctx context.Context,
	bs *botsession.BotSession,
	rsp *llmm.Response,
	toolName string,
	call *openapipb.ToolCall,
) (event.AgentProcedure, model.AgentTool) {
	// 查找工具信息
	tool, ok := findTool(ctx, bs.ToolsInfo, toolName)
	if !ok {
		tool = model.AgentTool{AgentToolInfo: &agent_config_server.AgentToolInfo{}}
	}
	icon := config.AgentConfig.WorkflowIcon
	if ok && tool.IconUrl != "" {
		icon = tool.IconUrl
	}

	// 构造状态
	status := a.GenerateDisplayStatus(tool.PluginName, toolName, call.GetFunction().GetArguments())
	proc := event.AgentProcedure{
		Index:  0,
		Name:   toolName,
		Title:  tool.PluginName + "/" + toolName,
		Status: event.ProcedureStatusProcessing,
		Icon:   icon,
		Debugging: event.AgentProcedureDebugging{
			DisplayType:   a.GenerateDisplayType(tool.PluginName, toolName),
			DisplayStatus: status,
		},
		PluginType: int32(tool.PluginType),
	}
	content := a.handleStreamCodeInterpreterDisplay(call.GetFunction().GetArguments())
	content = strings.ReplaceAll(content, "\\n", "\n")
	proc.Debugging.Content = content

	a.addOrUpdateProcedure(proc)
	a.ThoughtEventReply(ctx, bs)
	return proc, tool
}

// handleStreamCodeInterpreterDisplay 处理代码解释器展示  {"Code": "def bubble
func (a *ChatAgentImpl) handleStreamCodeInterpreterDisplay(code string) string {
	codeObject := &CodeObject{}
	err := jsoniter.Unmarshal([]byte(code), codeObject)
	if err != nil {
		c := ExtractCodeValue(code)
		if len(c) > 0 {
			return "```python\n" + c + "\n```"
		}
		return code
	}
	result := "```python\n" + codeObject.Code + "\n```"
	return result
}

// ExtractCodeValue 用于从类似 {"Code": "...} 这种不完整 JSON 中
// 捕获 Code 字段后的所有内容（包括换行）。
func ExtractCodeValue(input string) string {
	// (?s) 启用 DOTALL 模式，使 . 能匹配换行
	re := regexp.MustCompile(`(?s)"Code"\s*:\s*"(.*)`)
	subs := re.FindStringSubmatch(input)
	if len(subs) < 2 {
		return ""
	}
	return subs[1]
}
