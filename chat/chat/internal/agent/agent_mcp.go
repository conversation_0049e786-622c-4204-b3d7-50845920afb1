package agent

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	mcpClient "git.woa.com/dialogue-platform/go-comm/mcp/client"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg/agentlogger"
	jsoniter "github.com/json-iterator/go"
	"github.com/mark3labs/mcp-go/mcp"
)

// convertMcpHeader 转换MCP头部
func (a *ChatAgentImpl) convertMcpHeader(
	ctx context.Context, bs *botsession.BotSession,
	headers []*agent_config_server.AgentPluginHeader) (
	map[string]string, error) {
	convertedHeaders := make(map[string]string)
	for _, item := range headers {
		if value, err := a.getCompatibleHeaderValue(ctx, bs, item); err != nil {
			log.ErrorContextf(ctx, "getCompatibleHeaderValue error: %v", err)
			return nil, err
		} else {
			convertedHeaders[item.GetParamName()] = value
		}
	}
	return convertedHeaders, nil
}

// initMCPClient 调用 mcp 工具
func (a *ChatAgentImpl) initMCPClient(ctx context.Context, bs *botsession.BotSession,
	tool model.AgentTool) (*mcpClient.SSEMCPClient, error) {
	options := make([]mcpClient.ClientOption, 0)
	options = append(options, mcpClient.WithUnsafeDomain(config.GetMCPUnsafeDomains()))
	realMcpHeader, err := a.convertMcpHeader(ctx, bs, tool.McpServer.Headers)
	if err != nil {
		log.ErrorContextf(ctx, "convertMcpHeader error: %v", err)
		return nil, err
	}
	log.DebugContextf(ctx, "mcp header: %v", helper.Object2String(realMcpHeader))
	options = append(options, mcpClient.WithHeaders(realMcpHeader))
	sseReadTimeout := time.Duration(tool.McpServer.SseReadTimeout) * time.Second
	options = append(options, mcpClient.WithSSEReadTimeout(sseReadTimeout))

	client, err := mcpClient.NewSSEMCPClient(tool.McpServer.McpServerUrl, options...)
	if err != nil {
		log.ErrorContextf(ctx, "Failed to create client: %v", err)
		return client, err
	}
	defer func() {
		if err != nil {
			client.Close()
		}
	}()

	// Start the client
	if err := client.Start(ctx); err != nil {
		log.ErrorContextf(ctx, "Failed to start client: %v", err)
		return client, err
	}

	timeout := time.Duration(tool.McpServer.Timeout) * time.Second
	newCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// Initialize
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name:    tool.ToolName,
		Version: "1.0.0",
	}

	_, err = client.Initialize(newCtx, initRequest)
	if err != nil {
		log.ErrorContextf(newCtx, "Failed to initialize: %v", err)
		return client, err
	}
	return client, nil
}

// invokeMCPTool 调用工具
func (a *ChatAgentImpl) invokeMCPTool(ctx context.Context, bs *botsession.BotSession,
	toolCall *openapi.ToolCall, p event.Procedure, step *agentlogger.Step) (toolResponse string, status bool) {
	toolName, status := toolCall.GetFunction().GetName(), true

	if tool, ok := findTool(ctx, bs.ToolsInfo, toolName); ok {
		if tool.GetMcpServer().GetMcpServerUrl() == config.AgentConfig.SandboxURL {
			tool.McpServer.Headers = append(tool.McpServer.Headers, &agent_config_server.AgentPluginHeader{
				ParamName:  "APP_KEY",
				ParamValue: bs.App.GetAppKey(),
			})
			tool.McpServer.Headers = append(tool.McpServer.Headers, &agent_config_server.AgentPluginHeader{
				ParamName:  "SESSION_ID",
				ParamValue: bs.SessionID,
			})
		}
		log.DebugContextf(ctx, "tool: %s, toolName: %s, header: %s", helper.Object2String(tool), toolName,
			helper.Object2StringEscapeHTML(tool.McpServer.Headers))
		client, err := a.initMCPClient(ctx, bs, tool)
		if err != nil {
			log.ErrorContextf(ctx, "initMCPClient failed: %v", err)
			toolResponse, status = fmt.Sprintf("工具{%s}调用失败: %s", toolName, err.Error()), false
			bs.TokenStat.UpdateAgentProcedure(event.NewToolSuccessTSProcedure(tool.ToolName, toolResponse, p))
			return
		}
		defer client.Close()

		// Create a new request
		request := mcp.CallToolRequest{}
		request.Params.Name = tool.ToolName
		request.Params.Arguments = helper.Arguments2Map(toolCall.GetFunction().GetArguments())
		step.Input = request
		step.Key = agentlogger.StepKeyMCP
		log.InfoContextf(ctx, "CallTool: %s, request: %s", tool.ToolName, helper.Object2StringEscapeHTML(request))
		result, err := client.CallTool(ctx, request)
		if err != nil {
			toolResponse, status = fmt.Sprintf("工具{%s}调用失败: %s", toolName, err.Error()), false
			log.ErrorContextf(ctx, "CallTool failed: %v", err)
			return
		}
		log.InfoContextf(ctx, "CallTool result: %s", helper.Object2StringEscapeHTML(result))

		if len(result.Content) < 1 {
			toolResponse, status = fmt.Sprintf("工具{%s}调用失败: content is empty", toolName), false
			log.ErrorContextf(ctx, "Expected 1 content item, got %d", len(result.Content))
			return
		}
		a.specialDisplayMCPTools(ctx, toolCall, result)
		// 提取vnc url
		b, _ := jsoniter.Marshal(result.Content[0])
		sandboxURL := helper.GetVNCURL(string(b))
		a.SandboxURL = strings.ReplaceAll(sandboxURL, "\\u0026", "\u0026")
		a.DisplayURL = extractDisplayURL(ctx, result)
		log.InfoContextf(ctx, "SandboxURL: %s", a.SandboxURL)

		toolResponse = helper.Object2StringEscapeHTML(result)
		// 将字符串转换为rune切片
		runes := []rune(toolResponse)
		if len(runes) > config.GetMaxToolResponse() {
			// 截取前config.GetMaxToolResponse()个字符
			toolResponse = string(runes[:config.GetMaxToolResponse()])
			log.WarnContextf(ctx, "toolResponse too long, After truncation: %s", toolResponse)
		}
		bs.TokenStat.UpdateAgentProcedure(event.NewToolSuccessTSProcedure(tool.ToolName, toolResponse, p))
	}
	return
}

// specialDisplayMCPTools 特殊处理MCP工具
func (a *ChatAgentImpl) specialDisplayMCPTools(ctx context.Context, toolCall *openapi.ToolCall,
	toolResponse *mcp.CallToolResult) {
	toolName := toolCall.GetFunction().GetName()
	if toolName == ToolNameDeployHTML {
		c := toolResponse.Content[0]
		log.DebugContextf(ctx, "toolName: %s, content: %s", toolName, helper.Object2StringEscapeHTML(c))
		bs, err := jsoniter.Marshal(c)
		if err != nil {
			return
		}
		var content model.MCPToolContent
		if err := jsoniter.Unmarshal(bs, &content); err != nil {
			return
		}

		a.Files = append(a.Files, &model.FileInfo{
			FileName:  "HTML链接",
			FileURL:   content.Text,
			FileType:  "html",
			CreatedAt: time.Now().Unix(),
		})
		log.DebugContextf(ctx, "toolName: %s, files: %s", toolName, helper.Object2StringEscapeHTML(a.Files))
	}
	if toolName == ToolNameDeployFile {
		c := toolResponse.Content[0]
		log.DebugContextf(ctx, "toolName: %s, content: %s", toolName, helper.Object2StringEscapeHTML(c))
		bs, err := jsoniter.Marshal(c)
		if err != nil {
			return
		}
		var content model.MCPToolContent
		if err := jsoniter.Unmarshal(bs, &content); err != nil {
			return
		}
		var deployContent model.DeployResponse
		if err := jsoniter.Unmarshal([]byte(content.Text), &deployContent); err != nil {
			return
		}
		for _, file := range deployContent.Files {
			fileName, fileType := helper.GetFileNameAndType(file.Name)
			a.Files = append(a.Files, &model.FileInfo{
				FileName:  fileName,
				FileURL:   file.Name,
				FileSize:  file.Size,
				FileType:  fileType,
				CreatedAt: time.Now().Unix(),
			})
		}
		log.DebugContextf(ctx, "toolName: %s, files: %s", toolName, helper.Object2StringEscapeHTML(a.Files))
	}
}

// extractDisplayURL 从 toolResponse.Content 安全提取 PageScreenshot
func extractDisplayURL(ctx context.Context, toolResponse *mcp.CallToolResult) string {
	// 1. 长度检查
	if len(toolResponse.Content) < 2 {
		return ""
	}

	// 2. 将任意类型的 Content[1] 序列化为 JSON
	bs, err := jsoniter.Marshal(toolResponse.Content[1])
	if err != nil {
		return ""
	}

	// 3. 直接取出 "text" 字段（避免中间结构体 Unmarshal）
	text := jsoniter.Get(bs, "text").ToString()
	if text == "" {
		return ""
	}

	// 4. 对 BrowserResponse 做一次 Unmarshal
	var br model.BrowserResponse
	if err := jsoniter.UnmarshalFromString(text, &br); err != nil {
		return ""
	}

	// 5. 返回截图链接
	log.InfoContextf(ctx, "PageScreenshot is: %s", br.PageScreenshot)
	return br.PageScreenshot
}
