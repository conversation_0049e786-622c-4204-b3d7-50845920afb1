package agent

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"git.woa.com/ivy/qbot/qbot/chat/pkg/agentlogger"
)

const template = "Thought: %s\nAction:"

// thinkingModelCompletion 智能体结果输出
func (a *ChatAgentImpl) thinkingModelCompletion(ctx context.Context, bs *botsession.BotSession,
	req *llmm.Request, thought string) (isFinish bool, err error) {
	// 发起请求
	cfg := config.App().Bot
	ch := make(chan *llmm.Response, cfg.ResponseChannelSize)
	g, gCtx := errgroupx.WithContext(ctx)
	agentCtx, cancel := context.WithCancel(ctx)
	isTimeout, last, signal := false, &llmm.Response{}, make(chan int, 10)
	llmStep := agentlogger.New(ctx, agentlogger.StepKeyLLM, req)

	req.PromptType = llmm.PromptType_TEXT_COMPLETION
	req.ModelName = DefaultThinkingModel
	message := &llmm.Message{
		Role:    llmm.Role_ASSISTANT,
		Content: fmt.Sprintf(template, thought),
	}
	req.Messages = append(req.Messages, message)
	req.ExtraParams.PromptTemplate = a.getMainAgentPromptNormal(ctx, bs)
	placeholders := make(map[string]string)

	g.Go(func() error {
		return chatReqShortLinkDecorator(func(ctx context.Context, req *llmm.Request, ch chan *llmm.Response,
			startTime time.Time, signal chan int) error {
			llmStep.Input = req
			return a.dao.Chat(ctx, req, ch, bs.StartTime, signal)
		}, placeholders)(agentCtx, req, ch, bs.StartTime, signal) // 调用LLM
	})
	g.Go(func() error {
		last, isFinish, isTimeout, err = a.thinkingModelResponse(gCtx, cancel, bs, ch, llmStep, signal, placeholders)
		llmStep.ExtraData = placeholders

		if last != nil && !last.GetFinished() {
			last.Finished = true
			extra := event.ReplyExtraInfo{EChartsInfo: a.EChartsInfo}
			re := bs.NewAgentReplyEvent(ctx, last, false, model.ReplyMethodAgent, bs.StartTime, extra)
			_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
		}

		return helper.When(isTimeout, pkg.ErrLLMTimeout, err)
	})
	err = g.Wait()
	llmStep.SetResult(err)
	llmStep.UpsertStep(true)
	return isFinish, err

}

// thinkingModelResponse TODO
// agentStreamDisplay 智能体结果输出
func (a *ChatAgentImpl) thinkingModelResponse(ctx context.Context, cancel context.CancelFunc,
	bs *botsession.BotSession, ch chan *llmm.Response, llmStep *agentlogger.Step,
	signal chan int, placeholders map[string]string) (last *llmm.Response, isFinish, isTimeout bool, err error) {
	cfg := config.App().Bot
	throttles := cfg.Throttles
	ticker := time.NewTicker(time.Duration(cfg.StopGeneration.CheckInterval) * time.Millisecond)
	timeout := time.NewTimer(time.Duration(cfg.Timeout) * time.Second)
	timeout2 := time.NewTimer(time.Duration(cfg.Timeout/10) * time.Second)
	replyThrottleCheck := helper.NewThrottle(throttles.Check)
	throttleStreaming := helper.NewThrottle(helper.When(bs.StreamingThrottle > 0, bs.StreamingThrottle,
		throttles.Streaming))
	codeStreaming := helper.NewThrottle(helper.When(bs.StreamingThrottle > 0, bs.StreamingThrottle,
		throttles.Streaming))
	index, isFinish, isEvil, last := 0, false, false, &llmm.Response{}

	defer func() {
		llmStep.Output = last
		ticker.Stop()
		timeout.Stop()
	}()
	for {
		select {
		case <-timeout.C:
			signal <- 1
			bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(event.ProcedureAgent))
			a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
			return last, isFinish, true, nil
		case <-timeout2.C:
			a.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
		case <-ticker.C:
			if ok, _ := a.dao.IsGenerationStopped(ctx, bs.To.CorpStaffID, bs.TokenStat.RecordID); ok {
				last.Finished = true
				extra := event.ReplyExtraInfo{EChartsInfo: a.EChartsInfo}
				re := bs.NewAgentReplyEvent(ctx, last, false, model.ReplyMethodAgent, bs.StartTime, extra)
				_ = a.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
				a.processAgentRecord(ctx, bs, last)
				signal <- 1
				return last, true, false, nil
			}
		case rsp, ok := <-ch:
			chatRspShortLinkDecorator(rsp, placeholders) // 短链接转长链接
			timeout.Stop()
			last = rsp
			if !ok {
				return last, isFinish, false, pkg.ErrAgentRunError
			}
			if rsp.Finished {
				a.processAgentThought(ctx, bs, rsp, throttleStreaming, true)

			}
			if isEvil, isFinish, err = a.processFunctionCalls(ctx, bs, rsp,
				throttleStreaming, replyThrottleCheck, codeStreaming, &index); isEvil || err != nil {
				log.InfoContextf(ctx, "thinkingModelResponse isEvil: %v, err: %v", isEvil, err)
				signal <- 1
				return last, isFinish, false, err
			}
			if isFinish || rsp.Finished {
				return last, isFinish, false, nil
			}
		}
	}
}
