package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

var defaultModel model.AppModel = model.AppModel{
	AppModelInfo: &bot_admin_config_server.AppModelInfo{
		ModelName:   "function-call-pro",
		Temperature: 0.5,
		TopP:        0.5,
	},
}

func getHandoffToolName(agentName string) string {
	return fmt.Sprintf("transfer_to_%s", agentName)
}

func toolName2Handoff(agentName string) string {
	return strings.TrimPrefix(agentName, "transfer_to_")
}

// NewContainer 从 agent 配置和 本地动态参数 构建出 agent container
func (a *ChatAgentImpl) NewContainer(ctx context.Context,
	bs *botsession.BotSession) (*Container, error) {

	envTag := agent_config_server.EnvType_TEST
	if bs.Scene == dao.AppReleaseScene {
		envTag = agent_config_server.EnvType_PROD
	}
	agentRsp, err := a.dao.DescribeAppAgentList(ctx, strconv.FormatUint(bs.App.AppBizId, 10), envTag)
	// agentRsp, err = mockAgent()
	if err != nil {
		log.ErrorContextf(ctx, "fetch agent config error: %v", err)
		return nil, pkg.ErrInternalServerError
	}

	agentContainer := &Container{
		AgentIDMap:            make(map[string]*agent_config_server.DescribeAppAgentListRsp_Agent),
		AgentNameMap:          make(map[string]*agent_config_server.DescribeAppAgentListRsp_Agent),
		AgentNameLocalToolMap: make(map[string]event.AgentTool),
	}
	hasMainAgent := false
	for _, agent := range agentRsp.Agents {
		if agent == nil {
			continue
		}
		agentContainer.AgentIDMap[agent.AgentId] = agent
		agentContainer.AgentNameMap[agent.Name] = agent
		if agent.AgentId == agentRsp.StaringAgentId {
			agentContainer.EntryAgent = agent.Name
			hasMainAgent = true
		}
	}
	if !hasMainAgent && len(agentRsp.Agents) > 0 {
		// 如果没有主 agent，用第一个agent 作为主 agent
		agentContainer.EntryAgent = agentRsp.Agents[0].Name
	}

	// 融合处理用户自定义的 agent 配置
	// 处理用户的自定义 agent
	for _, agent := range bs.AgentConfig.Agents {
		// 如果 agent 名字相同，前面的会覆盖后面的
		// 如果用户的 agent name 和云上的相同，会用用户本地的覆盖
		if agent == nil {
			continue
		}
		// 本地 agent 的 id 等于 name
		agent.AgentId = agent.GetName()
		// 如果云上已经存在，工具集，handoff 和云上的合并
		if existAgent, ok := agentContainer.AgentNameMap[agent.Name]; ok {
			agent.AgentId = existAgent.AgentId
			agent.Tools = existAgent.Tools
			agent.Handoffs = existAgent.Handoffs
		}
		agentContainer.AgentIDMap[agent.AgentId] = agent
		agentContainer.AgentNameMap[agent.Name] = agent
	}
	// 处理用户自定义的 handoff
	for _, handoff := range bs.AgentConfig.Handoffs {
		if sourceAgent, oks := agentContainer.GetSingleAgentByName(handoff.SourceAgentName); oks {
			if targetAgent, okt := agentContainer.GetSingleAgentByName(handoff.TargetAgentName); okt {
				sourceAgent.Handoffs = append(sourceAgent.Handoffs, targetAgent.AgentId)
			}
		}
	}
	// 导入本地工具
	agentContainer.ImportLocalTools(bs.AgentConfig.AgentTools)
	// 处理用户自定义 agent 指定入口的情况
	if bs.AgentConfig.StartAgentName != "" {
		if _, ok := agentContainer.GetSingleAgentByName(bs.AgentConfig.StartAgentName); ok {
			agentContainer.EntryAgent = bs.AgentConfig.StartAgentName
		}
	}
	log.InfoContextf(ctx, "agentContainer: %s", helper.Object2String(agentContainer))
	return agentContainer, nil
}

// Container 多 agent 的 continer
type Container struct {
	// 入口 agent 的 name
	EntryAgent string
	// agent_id -> SingleAgent
	AgentIDMap map[string]*agent_config_server.DescribeAppAgentListRsp_Agent
	// agent_name -> SingleAgent
	AgentNameMap map[string]*agent_config_server.DescribeAppAgentListRsp_Agent
	// agent_name -> []DynamicTool
	AgentNameLocalToolMap map[string]event.AgentTool
}

// GetEntryAgent 获取 agent 的入口
func (c Container) GetEntryAgent() string {
	return c.EntryAgent
}

// GetSingleAgentByName 用 agentName 获取单 Agent 的配置
func (c *Container) GetSingleAgentByName(agentName string) (*agent_config_server.DescribeAppAgentListRsp_Agent, bool) {
	v, ok := c.AgentNameMap[agentName]
	return v, ok
}

// GetSingleAgentByID 用 agentId 获取单 Agent 的配置
func (c *Container) GetSingleAgentByID(agentID string) (*agent_config_server.DescribeAppAgentListRsp_Agent, bool) {
	v, ok := c.AgentIDMap[agentID]
	return v, ok
}

// ImportLocalTools 导入用户本地工具
func (c *Container) ImportLocalTools(dynamicTools []event.AgentTool) {
	for _, dynamic := range dynamicTools {
		c.AgentNameLocalToolMap[dynamic.AgentName] = dynamic
	}
}

func recursionAdjust(def *openapi.Definition) {
	if def == nil {
		return
	}
	orders := []string{}
	for name := range def.GetProperties() {
		orders = append(orders, name)
	}
	requiredMap := map[string]struct{}{}
	for _, required := range def.GetRequired() {
		requiredMap[required] = struct{}{}
	}
	// 排序 orders
	sort.Slice(orders, func(i, j int) bool {
		namei := orders[i]
		namej := orders[j]
		_, reqi := requiredMap[namei]
		_, reqj := requiredMap[namej]
		if reqi == reqj {
			// 如果必要性相同，返回字典序列
			return strings.Compare(namei, namej) < 0
		} else {
			return reqi
		}
	})
	// required 顺序和 orders 保持一致
	sort.Slice(def.Required, func(i, j int) bool {
		namei := def.Required[i]
		namej := def.Required[j]
		_, reqi := requiredMap[namei]
		_, reqj := requiredMap[namej]
		if reqi == reqj {
			// 如果必要性相同，返回字典序列
			return strings.Compare(namei, namej) < 0
		} else {
			return reqi
		}
	})
	def.Order = orders
	for _, p := range def.GetProperties() {
		recursionAdjust(p)
	}
}

// GetFunctionCalls 获取 agent 的 function call
// 组装默认插件，用户本地插件，agent 配置的官方插件，handoff, 如果开启了系统优化，自动转交父 Agent
func (c *Container) GetFunctionCalls(
	ctx context.Context, agentName string, bs *botsession.BotSession) (
	calltools []*openapi.Tool, err error) {

	// 增加默认插件
	if !config.IsQBAppBizID(bs.App.GetAppBizId()) {
		calltools = model.GetDefaultTools()
		if c.GetEntryAgent() == bs.AgentStatus.CurrentAgent {
			// 如果是主 agent，增加默认的task completed 工具
			calltools = append(calltools, model.GetTaskCompletedTool())
		}
	}
	// 增加用户本地插件
	if dynamicTool, ok := c.AgentNameLocalToolMap[agentName]; ok {
		for _, tool := range dynamicTool.Tools {
			calltools = append(calltools, adaptLocalToolCustomVar(tool, bs))
		}
	}

	impl := NewChatAgentImpl()
	handoffToParent := false
	if single, ok := c.GetSingleAgentByName(agentName); ok {
		//  增加固定的插件
		for _, toolItem := range single.GetTools() {
			if toolItem == nil {
				continue
			}
			parameters := &openapi.Definition{
				Type:       model.DataTypeObject,
				Properties: make(map[string]*openapi.Definition),
				Required:   make([]string, 0),
			}
			p, err := impl.getProperties(ctx, bs, toolItem)
			if err != nil {
				log.ErrorContextf(ctx, "getProperties error: %v", err)
				return nil, err
			}
			if len(toolItem.GetInputs()) > 0 {
				parameters = &openapi.Definition{
					Type:       model.DataTypeObject,
					Properties: p,
					Required:   impl.getRequired(toolItem),
				}
			}
			calltools = append(calltools, &openapi.Tool{
				Type: "function",
				Function: &openapi.Function{
					Name:        toolItem.ToolName,
					Description: toolItem.ToolDesc,
					Parameters:  parameters,
				},
			})
		}

		// 增加 handoffs
		for _, handoff := range single.Handoffs {
			if handoffAgent, ok := c.GetSingleAgentByID(handoff); ok {
				if handoffAgent.Name == bs.AgentStatus.GetLastParentAgent() {
					handoffToParent = true
				}
				// handoff 转成 tool 不需要输入参数
				calltools = append(calltools, &openapi.Tool{
					Type: "function",
					Function: &openapi.Function{
						Name:        getHandoffToolName(handoffAgent.Name),
						Description: handoffAgent.HandoffDescription, // handoff description
						Parameters: &openapi.Definition{
							Type: "object", // 模型要求加上
						},
					},
				})
			}
		}
	}
	if !bs.AgentConfig.DisableSystemOpt && !handoffToParent && agentName != c.EntryAgent {
		// 没有关闭系统优化，且用户没有自动转交给父节点，且不是主节点 -> 添加默认转交给父节点工具
		ho, _ := c.GetAgentHandoffs(agentName)
		if len(ho) == 0 && !config.IsQBAppBizID(bs.App.GetAppBizId()) {
			// 没有转交关系，增加默认的转交父节点的插件，排除浏览器特殊场景
			calltools = append(calltools, model.GetTaskDoneTool())
		}
	}
	for _, toolItem := range calltools {
		recursionAdjust(toolItem.GetFunction().GetParameters())
	}
	return calltools, nil
}

// IsFunctoolHitHandoff function call 输出的工具是否是 handoff
func (c *Container) IsFunctoolHitHandoff(agentName, toolsName string) bool {
	singleAgent, ok := c.GetSingleAgentByName(agentName)
	if !ok {
		return false
	}
	for _, handoff := range singleAgent.Handoffs {
		hansoffAgent, ok := c.GetSingleAgentByID(handoff)
		if ok {
			if hansoffAgent.Name == toolsName {
				return true
			}
		}
	}
	return false
}

// GetAgentInstructions 获取 agent 对应的提示词
func (c *Container) GetAgentInstructions(agentName string) (instructions string, err error) {
	singleAgent, ok := c.GetSingleAgentByName(agentName)
	if !ok {
		return "", fmt.Errorf("can not found agent: %s", agentName)
	}
	return singleAgent.Instructions, nil
}

// GetAgentHandoffs 获取 agent 转交关系
func (c *Container) GetAgentHandoffs(agentName string) ([]string, error) {
	singleAgent, ok := c.GetSingleAgentByName(agentName)
	if !ok {
		return []string{}, fmt.Errorf("can not found agent: %s", agentName)
	}
	return singleAgent.GetHandoffs(), nil
}

// GetAgentModel 获取 agent 对应的 model
func (c *Container) GetAgentModel(agentName string) model.AppModel {
	singleAgent, ok := c.GetSingleAgentByName(agentName)
	if !ok {
		return defaultModel // 使用默认模型
	}
	if singleAgent.Model == nil {
		// 没有配置模型，用默认模型
		return defaultModel
	}
	bs, _ := json.Marshal(singleAgent.Model)
	appModelInfo := &bot_admin_config_server.AppModelInfo{}
	json.Unmarshal(bs, appModelInfo)
	return model.AppModel{
		AppModelInfo: appModelInfo,
	}
}

// GetAgentIcon 获取 agent 对应的 Icon
func (c *Container) GetAgentIcon(agentName string) string {
	singleAgent, ok := c.GetSingleAgentByName(agentName)
	if !ok || len(singleAgent.IconUrl) < 1 {
		return "" // 使用默认icon?
	}
	return singleAgent.IconUrl
}

// IsPdl 判断 agent 是否是 pdl
func (c *Container) IsPdl(agentName string) (
	singleAgent *agent_config_server.DescribeAppAgentListRsp_Agent, ok bool) {
	singleAgent, ok = c.GetSingleAgentByName(agentName)
	if !ok {
		return nil, false
	}
	if len(singleAgent.WorkflowId) == 0 {
		return nil, false
	}
	return singleAgent, true
}

// GetLocalTools 获取用户本地插件
func (c *Container) GetLocalTools(agentName string) event.AgentTool {
	dynamicTool, ok := c.AgentNameLocalToolMap[agentName]
	if !ok {
		return event.AgentTool{}
	}
	return dynamicTool
}

func mockAgent() (*agent_config_server.DescribeAppAgentListRsp, error) {

	model := &agent_config_server.DescribeAppAgentListRsp_AgentModelInfo{
		ModelName:    "function-call-pro",
		HistoryLimit: 5,
		Temperature:  0.5,
		TopP:         0.5,
	}
	mainagent := &agent_config_server.DescribeAppAgentListRsp_Agent{
		Name:               "新闻搜索",
		Instructions:       "根据用户的输入，搜索相关新闻，提供相应的web访问地址",
		HandoffDescription: "处理用户新闻搜索的需求",
		Model:              model,
		Tools: []*agent_config_server.AgentToolInfo{
			{
				PluginId:   "fbd411cd-dcd8-4351-9fe9-ca9491cc778f",
				PluginName: "混元搜索",
				ToolDesc:   "利用搜索引擎获取信息，并通过混元大模型总结回复",
				ToolId:     "16bfcfea-2ed8-4c00-b73d-3c1757d3d843",
				ToolName:   "HunyuanSearchSummary",
				Inputs: []*agent_config_server.AgentToolReqParam{
					{
						Desc:       "查询内容",
						IsRequired: true,
						Name:       "Query",
						Type:       0,
					},
				},
			},
		},
	}
	singleAgent := &agent_config_server.DescribeAppAgentListRsp_Agent{
		AgentId:            "agent_id1",
		Name:               "浏览器控制 agent",
		Instructions:       "根据用户需求进行浏览器的控制",
		HandoffDescription: "涉及到实际浏览器控制和操作的需求都可以交给我",
		Model:              model,
	}
	mainagent.Handoffs = append(mainagent.Handoffs, "agent_id1")
	// pdl mock
	pdlAgent := &agent_config_server.DescribeAppAgentListRsp_Agent{
		AgentId:            "agent_id2",
		WorkflowId:         "8c2cf597-c77f-46b3-b9be-16c62ef8f96a",
		Name:               "开发票",
		Instructions:       "自动开发票",
		HandoffDescription: "开发票的需求交给我",
		Model:              model,
	}
	mainagent.Handoffs = append(mainagent.Handoffs, "agent_id2")
	rsp := &agent_config_server.DescribeAppAgentListRsp{
		Agents:         []*agent_config_server.DescribeAppAgentListRsp_Agent{mainagent, singleAgent, pdlAgent},
		StaringAgentId: mainagent.AgentId,
	}
	return rsp, nil
}
