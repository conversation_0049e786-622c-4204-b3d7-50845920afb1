// Package agent TODO
package agent

import (
	"context"

	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/memory"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
)

// ChatAgentAPI Chat智能体API
type ChatAgentAPI interface {
	// AgentReply 智能体回复
	AgentReply(ctx context.Context, bs *botsession.BotSession) error
	ThoughtAPI
}

// ThoughtAPI 思考事件API
type ThoughtAPI interface {
	// ThoughtEventReply 思考事件回复
	ThoughtEventReply(ctx context.Context, bs *botsession.BotSession)
}

// ChatAgentImpl Chat智能体实现
type ChatAgentImpl struct {
	dao               dao.Dao
	thought           *event.AgentThoughtEvent
	agentMemory       []*model.AgentMemory
	history           [][2]model.HisMessage
	memory            memory.ChatMemoryAPI
	mustReply         bool
	ThinkingModelName string
	EChartsInfo       []string
	agentContainer    Container
	SandboxURL        string
	DisplayURL        string
	Files             []*model.FileInfo
}

// setContainer 配置 agent map
func (a *ChatAgentImpl) setContainer(agentContainer Container) {
	a.agentContainer = agentContainer
}

// New .
func New() ChatAgentAPI {
	return &ChatAgentImpl{
		dao:         dao.New(),
		thought:     &event.AgentThoughtEvent{},
		agentMemory: make([]*model.AgentMemory, 0),
		history:     make([][2]model.HisMessage, 0),
		memory:      memory.New(),
		EChartsInfo: make([]string, 0),
		Files:       make([]*model.FileInfo, 0),
	}
}

// NewChatAgentImpl .
func NewChatAgentImpl() *ChatAgentImpl {
	return &ChatAgentImpl{
		dao:         dao.New(),
		thought:     &event.AgentThoughtEvent{},
		agentMemory: make([]*model.AgentMemory, 0),
		history:     make([][2]model.HisMessage, 0),
		memory:      memory.New(),
		EChartsInfo: make([]string, 0),
		Files:       make([]*model.FileInfo, 0),
	}
}
