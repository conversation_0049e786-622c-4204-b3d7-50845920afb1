package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"reflect"
	"testing"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
)

// diffMaps 返回两个 map[string]interface{} 之间的差异描述字符串
func diffMaps(a, b map[string]interface{}, path string) error {
	// 检查 a 中的 key
	for k, v := range a {
		fullKey := k
		if path != "" {
			fullKey = path + "." + k
		}

		bv, ok := b[k]
		if !ok {
			return fmt.Errorf("Key %s missing in second map", fullKey)
		}

		// 如果是 map，递归比较
		vaMap, vaIsMap := v.(map[string]interface{})
		vbMap, vbIsMap := bv.(map[string]interface{})
		if vaIsMap && vbIsMap {
			err := diffMaps(vaMap, vbMap, fullKey)
			if err != nil {
				return err
			}
		}

		// 其他类型用 reflect.DeepEqual 比较
		s1, _ := json.Marshal(v)
		s2, _ := json.Marshal(bv)
		if string(s1) != string(s2) {
			tv1 := reflect.TypeOf(v)
			tv2 := reflect.TypeOf(bv)
			return fmt.Errorf("Value mismatch at key %s: %v != %v, type1: %s, type2: %s",
				fullKey, string(s1), string(s2), tv1.String(), tv2.String())
		}
	}

	// 检查 b 中有但 a 中没有的 key
	for k := range b {
		if _, ok := a[k]; !ok {
			fullKey := k
			if path != "" {
				fullKey = path + "." + k
			}
			return fmt.Errorf("Key %s missing in first map", fullKey)
		}
	}
	return nil
}

func newAgentToolReqParam(name, desc string, tp plugin_config_server.TypeEnum,
	hidden bool, defVal string) *agent_config_server.AgentToolReqParam {

	param := &agent_config_server.AgentToolReqParam{
		Name:        name,
		Desc:        desc,
		Type:        tp,
		AgentHidden: hidden,
		Input:       &agent_config_server.Input{},
	}
	if rand.Intn(2) == 0 {
		param.DefaultValue = defVal
	} else {
		param.Input.InputType = agent_config_server.InputSourceEnum_USER_INPUT
		param.Input.UserInputValue = &agent_config_server.UserInputValue{
			Values: []string{defVal},
		}
	}
	return param
}

func build1(originMap, expectedMap map[string]interface{}) []*agent_config_server.AgentToolReqParam {
	params := []*agent_config_server.AgentToolReqParam{}
	{
		// 加入一个 object 对象，模型可见
		name, defVal := "object1", ``
		param := newAgentToolReqParam(name, "", plugin_config_server.TypeEnum_OBJECT, false, defVal)
		subParam1 := newAgentToolReqParam("sub1", "", plugin_config_server.TypeEnum_INT, false, "5")
		subParam2 := newAgentToolReqParam("sub2", "", plugin_config_server.TypeEnum_STRING, false, "abcd")
		subParam3 := newAgentToolReqParam("sub3", "", plugin_config_server.TypeEnum_INT, true, "5")
		subParam4 := newAgentToolReqParam("sub4", "", plugin_config_server.TypeEnum_STRING, true, "abcd")
		osubMap := map[string]interface{}{
			"sub1": 7,
			"sub2": "sub2",
		}
		rsubMap := map[string]interface{}{
			"sub1": 7,
			"sub2": "sub2",
			"sub3": 5,
			"sub4": "abcd",
		}
		// 子对象，模型可见，里面的参数对模型都不可见
		subParam5 := newAgentToolReqParam("sub5", "", plugin_config_server.TypeEnum_OBJECT, false, "")
		subParam51 := newAgentToolReqParam("sub51", "", plugin_config_server.TypeEnum_STRING, true, "abcd")
		subParam52 := newAgentToolReqParam("sub52", "", plugin_config_server.TypeEnum_INT, true, "1")
		subParam5.SubParams = append(subParam5.SubParams, subParam51)
		subParam5.SubParams = append(subParam5.SubParams, subParam52)
		osubMap["sub5"] = map[string]interface{}{}
		rsubMap["sub5"] = map[string]interface{}{
			"sub51": "abcd",
			"sub52": 1,
		}

		// 子对象，模型可见，里面的参数对模型都不可见,且没有默认值
		subParam6 := newAgentToolReqParam("sub6", "", plugin_config_server.TypeEnum_OBJECT, false, "")
		subParam61 := newAgentToolReqParam("sub61", "", plugin_config_server.TypeEnum_STRING, true, "")
		subParam62 := newAgentToolReqParam("sub62", "", plugin_config_server.TypeEnum_INT, true, "")
		subParam6.SubParams = append(subParam6.SubParams, subParam61)
		subParam6.SubParams = append(subParam6.SubParams, subParam62)
		osubMap["sub6"] = map[string]interface{}{}
		rsubMap["sub6"] = map[string]interface{}{}

		// 子对象，模型不可见，里面的参数对模型都可见
		subParam7 := newAgentToolReqParam("sub7", "", plugin_config_server.TypeEnum_OBJECT, true, "")
		subParam71 := newAgentToolReqParam("sub71", "", plugin_config_server.TypeEnum_STRING, false, "abcd")
		subParam72 := newAgentToolReqParam("sub72", "", plugin_config_server.TypeEnum_INT, false, "1")
		subParam7.SubParams = append(subParam7.SubParams, subParam71)
		subParam7.SubParams = append(subParam7.SubParams, subParam72)
		rsubMap["sub7"] = map[string]interface{}{
			"sub71": "abcd",
			"sub72": 1,
		}

		// 子对象，模型不可见，里面的参数对模型都可见,且没有默认值
		subParam8 := newAgentToolReqParam("sub8", "", plugin_config_server.TypeEnum_OBJECT, true, "")
		subParam81 := newAgentToolReqParam("sub81", "", plugin_config_server.TypeEnum_STRING, false, "")
		subParam82 := newAgentToolReqParam("sub82", "", plugin_config_server.TypeEnum_INT, false, "")
		subParam8.SubParams = append(subParam8.SubParams, subParam81)
		subParam8.SubParams = append(subParam8.SubParams, subParam82)

		// 子对象，模型不可见，里面的参数对模型都可见,且部分有默认值
		subParam9 := newAgentToolReqParam("sub9", "", plugin_config_server.TypeEnum_OBJECT, true, "")
		subParam91 := newAgentToolReqParam("sub91", "", plugin_config_server.TypeEnum_STRING, false, "")
		subParam92 := newAgentToolReqParam("sub92", "", plugin_config_server.TypeEnum_INT, false, "556")
		subParam9.SubParams = append(subParam9.SubParams, subParam91)
		subParam9.SubParams = append(subParam9.SubParams, subParam92)
		rsubMap["sub9"] = map[string]interface{}{
			"sub92": 556,
		}

		// 子对象，模型可见，再嵌套一个不可见子对象
		subParam10 := newAgentToolReqParam("sub10", "", plugin_config_server.TypeEnum_OBJECT, false, "")
		subParam101 := newAgentToolReqParam("sub101", "", plugin_config_server.TypeEnum_STRING, false, "")
		subParam102 := newAgentToolReqParam("sub102", "", plugin_config_server.TypeEnum_INT, true, "556")
		subParam10.SubParams = append(subParam10.SubParams, subParam101)
		subParam10.SubParams = append(subParam10.SubParams, subParam102)
		subParam10.SubParams = append(subParam10.SubParams, subParam9)
		osubMap["sub10"] = map[string]interface{}{
			"sub101": "dsf",
		}
		rsubMap["sub10"] = map[string]interface{}{
			"sub101": "dsf",
			"sub102": 556,
			"sub9": map[string]interface{}{
				"sub92": 556,
			},
		}
		param.SubParams = append(param.SubParams, []*agent_config_server.AgentToolReqParam{subParam1, subParam2, subParam3, subParam4,
			subParam5, subParam6, subParam7, subParam8, subParam9, subParam10}...)
		params = append(params, param)

		originMap[name] = osubMap
		expectedMap[name] = rsubMap
	}

	{
		// 加入一个 object 对象，模型可见
		name, defVal := "arr_object1", ``
		param := newAgentToolReqParam(name, "", plugin_config_server.TypeEnum_ARRAY_OBJECT, false, defVal)
		// 子对象数组，模型可见，里面的参数部分对模型都不可见
		subParam1 := newAgentToolReqParam("arr_object1_sub1", "", plugin_config_server.TypeEnum_STRING, false, "")
		subParam2 := newAgentToolReqParam("arr_object1_sub2", "", plugin_config_server.TypeEnum_INT, true, "1")
		// 子 string 数组，模型不可见，有默认值
		subParam3 := newAgentToolReqParam("arr_object1_sub3", "", plugin_config_server.TypeEnum_ARRAY_STRING, true, `["1", "2", "3"]`)
		osubMap := []map[string]interface{}{
			{
				"arr_object1_sub1": "abc",
			},
			{
				"arr_object1_sub1": "abcd",
			},
		}

		rsubMap := []map[string]interface{}{
			{
				"arr_object1_sub1": "abc",
				"arr_object1_sub2": 1,
				"arr_object1_sub3": []string{"1", "2", "3"},
			},
			{
				"arr_object1_sub1": "abcd",
				"arr_object1_sub2": 1,
				"arr_object1_sub3": []string{"1", "2", "3"},
			},
		}

		param.SubParams = append(param.SubParams, []*agent_config_server.AgentToolReqParam{
			subParam1, subParam2, subParam3}...)
		params = append(params, param)

		originMap[name] = osubMap
		expectedMap[name] = rsubMap

	}

	{
		name, defVal := "arr_object2", ``
		param := newAgentToolReqParam(name, "", plugin_config_server.TypeEnum_ARRAY_OBJECT, false, defVal)
		// 子对象数组，模型不可见，里面的参数对模型都可见
		subParam1 := newAgentToolReqParam("arr_object2_sub1", "", plugin_config_server.TypeEnum_STRING, false, "abcd")
		subParam2 := newAgentToolReqParam("arr_object2_sub2", "", plugin_config_server.TypeEnum_INT, false, "1")
		param.SubParams = append(param.SubParams, []*agent_config_server.AgentToolReqParam{
			subParam1, subParam2}...)
		params = append(params, param)
		// 模型不可见，数组无法提取出来长度，默认值也无法赋值
	}
	return params
}

func TestDfsReplaceDefaultValue(t *testing.T) {
	// 构建 params 和原始的 map 已经预期的 map
	params := []*agent_config_server.AgentToolReqParam{}
	originMap := map[string]interface{}{}
	expectedMap := map[string]interface{}{}

	{
		// 加入一个 string 字段 stra，模型可见，无默认值
		name, defVal := "stra", ""
		param := newAgentToolReqParam(name, "模型可见，无默认值", plugin_config_server.TypeEnum_STRING, false, defVal)
		params = append(params, param)
		originMap[name] = "llm ouput"   // 模型提取出值
		expectedMap[name] = "llm ouput" // 最终用模型提取出值
	}

	{
		// 加入一个 string 字段 strb，模型可见，有默认值
		name, defVal := "strb", "default b"
		param := newAgentToolReqParam(name, "模型可见，有默认值", plugin_config_server.TypeEnum_STRING, false, defVal)
		params = append(params, param)
		originMap[name] = "llm ouput"   // 模型提取出值
		expectedMap[name] = "llm ouput" // 最终用模型提取出值
	}

	{
		// 加入一个 string 字段 strc，模型不可见，有默认值
		name, defVal := "strc", "default c"
		param := newAgentToolReqParam(name, "模型不可见，有默认值", plugin_config_server.TypeEnum_STRING, true, defVal)
		params = append(params, param)
		expectedMap[name] = "default c" // 最终用默认值
	}

	{
		// 加入一个 string 字段 strd，模型不可见，无默认值
		name, defVal := "strd", ""
		param := newAgentToolReqParam(name, "模型不可见，无默认值", plugin_config_server.TypeEnum_STRING, true, defVal)
		params = append(params, param)
	}

	{
		// 加入一个 int 字段 inte, 模型不可见，有默认值
		name, defVal := "inte", "13"
		param := newAgentToolReqParam(name, "模型不可见，有默认值", plugin_config_server.TypeEnum_INT, true, defVal)
		params = append(params, param)
		expectedMap[name] = int64(13) // 最终用默认值
	}

	{
		// 加入一个 int 字段 intf, 模型不可见，有默认值，默认值为浮点数
		name, defVal := "intf", "13.45"
		param := newAgentToolReqParam(name, "模型不可见，有默认值，默认值为浮点数", plugin_config_server.TypeEnum_INT, true, defVal)
		params = append(params, param)
		expectedMap[name] = int64(0) // 最终用默认值
	}

	{
		// 加入一个 int 字段 intg, 模型不可见，有默认值，默认值为非整数
		name, defVal := "intg", "abcdf"
		param := newAgentToolReqParam(name, "模型不可见，有默认值，默认值为非整数", plugin_config_server.TypeEnum_INT, true, defVal)
		params = append(params, param)
		expectedMap[name] = int64(0) // 最终为0
	}

	{
		// 加入一个 float 字段 float1, 模型不可见，有默认值
		name, defVal := "float1", "323.4"
		param := newAgentToolReqParam(name, "模型不可见，有默认值", plugin_config_server.TypeEnum_FLOAT, true, defVal)
		params = append(params, param)
		expectedMap[name] = float64(323.4) // 最终用默认值
	}

	{
		// 加入一个 float 字段 float2, 模型不可见，默认值非法
		name, defVal := "float2", "ewff"
		param := newAgentToolReqParam(name, "模型不可见，默认值非法", plugin_config_server.TypeEnum_FLOAT, true, defVal)
		params = append(params, param)
		expectedMap[name] = float64(0) // 最终为0
	}

	{
		// 加入一个 bool 字段 bool1, 模型不可见，有默认值
		name, defVal := "bool1", "true"
		param := newAgentToolReqParam(name, "模型不可见，有默认值", plugin_config_server.TypeEnum_BOOL, true, defVal)
		params = append(params, param)
		expectedMap[name] = true // 最终为0
	}

	{
		// 加入一个 bool 字段 bool2, 模型不可见，默认值非法
		name, defVal := "bool2", "cxfdsf"
		param := newAgentToolReqParam(name, "模型不可见，默认值非法", plugin_config_server.TypeEnum_BOOL, true, defVal)
		params = append(params, param)
		expectedMap[name] = false // 最终为false
	}

	{
		// 加入一个 array int 字段 arrint1, 模型不可见，有默认值
		name, defVal := "arrint1", `[1, 2, 5]`
		param := newAgentToolReqParam(name, "模型不可见，有默认值", plugin_config_server.TypeEnum_ARRAY_INT, true, defVal)
		params = append(params, param)
		expectedMap[name] = []int64{1, 2, 5}
	}

	{
		// 加入一个 array int 字段 arrint2, 模型不可见，默认值是 string
		name, defVal := "arrint2", `1 2 5`
		param := newAgentToolReqParam(name, "模型不可见，默认值是 string", plugin_config_server.TypeEnum_ARRAY_INT, true, defVal)
		params = append(params, param)
		expectedMap[name] = nil
	}
	params = append(params, build1(originMap, expectedMap)...)
	{
		// 加入一个 array int 字段 arrint3, 模型不可见，默认值非法
		name, defVal := "arrint3", `["a", "b", "c"]`
		param := newAgentToolReqParam(name, "模型不可见，默认值非法", plugin_config_server.TypeEnum_ARRAY_INT, true, defVal)
		params = append(params, param)
		expectedMap[name] = []int64{0, 0, 0}
	}

	a := ChatAgentImpl{}
	bs, _ := json.Marshal(originMap)
	aaa := map[string]interface{}{}
	_ = json.Unmarshal(bs, &aaa)
	originMap = aaa

	bs, _ = json.Marshal(originMap)
	t.Logf("originMap:\n %s\n", string(bs))
	a.dfsReplaceDefaultValue(context.Background(), nil, params, originMap, false)

	err := diffMaps(originMap, expectedMap, "")
	bs1, _ := json.Marshal(originMap)
	bs2, _ := json.Marshal(expectedMap)
	t.Logf("replaceMap:\n %s\n, expectedMap:\n %s\n", string(bs1), string(bs2))
	if err != nil {
		t.Fatal(err)
	}
}
