package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/utils"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
)

// CreateSession 创建会话 (访客端 & 评测端)
func (s *Service) CreateSession(ctx context.Context, req *pb.CreateSessionReq) (*pb.CreateSessionRsp, error) {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "CreateSession cost time:%v ms", time.Since(tik).Milliseconds())
	}()

	ctx = clues.NewTrackContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddT(ctx, "CreateSession", req)
	log.DebugContextf(ctx, "create session for explorer %s", helper.Object2String(req))
	typ := model.SessionType(req.GetType())
	u, err := s.dao.CheckSession(ctx)
	clues.AddTrackE(ctx, "dao.CheckSession", u, err)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
			return nil, err
		}
		return nil, pkg.ErrInternalServerError
	}
	if typ == model.SessionTypeNormal && req.GetSeatBizId() == 0 {
		return nil, pkg.ErrBadRequest
	}
	scene := dao.AppReleaseScene
	if typ == model.SessionTypeExperience || typ == model.SessionTypeWorkflow {
		scene = dao.AppTestScene
	}
	app, err := s.dao.GetAppByBizID(ctx, scene, req.GetBotBizId())
	clues.AddTrackE(ctx, "dao.GetAppByBizID", app, err)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}

	for _, blacklistedID := range config.App().App.BlackList {
		if app.GetAppBizId() == blacklistedID {
			return nil, pkg.ErrAppIDInBlacklist
		}
	}

	seatID := model.ID{}
	if req.GetSeatBizId() > 0 {
		seat, err := s.dao.GetCorpStaffByBizID(ctx, req.GetSeatBizId())
		clues.AddTrackE(ctx, "dao.GetCorpStaffByBizID", seat, err)
		if err != nil {
			return nil, pkg.ErrInternalServerError
		}
		if seat == nil {
			return nil, pkg.ErrSeatNotExist
		}
		seatID = model.ID{ID: seat.ID, BizID: seat.BusinessID}
	}
	botID := model.ID{ID: app.GetId(), BizID: app.GetAppBizId()}
	visitorID := model.ID{ID: u.ID, BizID: u.BusinessID}

	var isNew bool
	var session *model.Session
	log.InfoContextf(ctx, "create session: %s", helper.Object2String(typ))
	if typ == model.SessionTypeWorkflow {
		log.InfoContextf(ctx, "create workflow debug session: %s", helper.Object2String(req))
		return s.createWorkflowDebugSession(ctx, app, u, seatID)
	}
	session, isNew, err = s.dao.MustGetLastSession(ctx, typ, visitorID, botID, seatID)
	clues.AddTrackE(ctx, "dao.MustGetLastSession", clues.M{
		"typ": typ, "visitorID": visitorID, "botID": botID, "seatID": seatID, "session": session,
	}, err)
	if err != nil {
		if strings.Contains(err.Error(), "command denied to user") {
			return &pb.CreateSessionRsp{SessionId: uuid.NewString()}, nil // 切换过程中，降级。 todo 后续可以去掉
		}
		return nil, pkg.ErrInternalServerError
	}

	err = s.dao.RefreshSessionVisitTime(ctx, session.ID, session.SessionID)
	clues.AddTrackE(ctx, "dao.RefreshSessionVisitTime", session.ID, err)
	if err != nil {
		if strings.Contains(err.Error(), "command denied to user") {
			return &pb.CreateSessionRsp{SessionId: session.SessionID}, nil // 切换过程中，降级。 todo 后续可以去掉
		}
		return nil, pkg.ErrInternalServerError
	}
	err = s.sendGreeting(ctx, session, app, isNew, typ, u)
	if err != nil {
		return nil, err
	}
	return &pb.CreateSessionRsp{SessionId: session.SessionID}, nil
}

// createWorkflowDebugSession 创建会话 (访客端 & 评测端)
func (s *Service) createWorkflowDebugSession(ctx context.Context,
	app *model.App, u *model.CorpStaff, seatID model.ID) (*pb.CreateSessionRsp, error) {
	botID := model.ID{ID: app.GetId(), BizID: app.GetAppBizId()}
	visitorID := model.ID{ID: u.ID, BizID: u.BusinessID}

	session := model.NewSession(model.SessionTypeWorkflow, visitorID, botID, seatID)
	err := s.dao.CreateSession(ctx, session)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}

	err = s.sendGreeting(ctx, session, app, true, model.SessionTypeNormal, u)
	if err != nil {
		return nil, err
	}
	return &pb.CreateSessionRsp{SessionId: session.SessionID}, nil
}

func (s *Service) sendGreeting(ctx context.Context, session *model.Session, app *model.App,
	isNew bool, typ model.SessionType, u *model.CorpStaff) error {
	if app.GetGreeting() == "" {
		return nil
	}
	if !isNew {
		return nil
	}
	// needGreeting := session.NeedGreeting()
	// clues.AddT(ctx, "sendGreeting.needGreeting", needGreeting)
	// if isNew { // || needGreeting {
	// 【知识引擎敏捷】【体验中心】功能体验优化
	// https://tapd.woa.com/project_qrobot/prong/stories/view/1070080800117327567
	// 检查上一次消息, 是否为欢迎语, 如果是, 则不再发下发欢迎语
	// types := []model.RecordType{
	//	model.RecordTypeMessage,
	//	model.RecordTypeSearch,
	//	model.RecordTypeExperience,
	//	model.RecordTypeAbstract,
	//	model.RecordTypeSummary,
	//	model.RecordTypeGreeting,
	// }
	// records, err := s.dao.GetLastNBotRecord(ctx, session, 1, types)
	// clues.AddTrackE(ctx, "dao.GetLastNBotRecord", records, err)
	// if err != nil {
	//	return err
	// }
	// if len(records) == 1 && records[0].Type == model.RecordTypeGreeting {
	//	return nil
	// }
	msg := app.NewGreetingRecord(session.SessionID, u.ID, s.getVisitor(u))
	msg.BotBizID = app.AppBizId
	_, err := s.dao.CreateMsgRecord(ctx, msg, nil) // for query
	clues.AddTrackE(ctx, "dao.CreateMsgRecord", msg, err)
	return nil
}

func (s *Service) getVisitor(u *model.CorpStaff) model.SourceType {
	if u.UserType == model.LoginUserExpType {
		return model.SourceTypeExpVisitor
	}
	return model.SourceTypeVisitor
}

// ResetSession 重置会话
func (s *Service) ResetSession(ctx context.Context, req *pb.ResetSessionReq) (*pb.ResetSessionRsp, error) {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "ResetSession cost time:%v ms", time.Since(tik).Milliseconds())
	}()

	u, err := s.dao.CheckSession(ctx)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
			return nil, err
		}
		return nil, pkg.ErrInternalServerError
	}
	session, err := s.dao.GetSession(ctx, model.SessionTypeAny, req.GetSessionId())
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	if !session.IsVisitor(u.ID) {
		return nil, pkg.ErrSessionNotFound
	}

	ok, err := s.dao.ResetSession(ctx, req.GetSessionId(), req.GetIsOnlyEmptyTheDialog())
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	if ok {
		sr := event.SessionResetEvent{SessionID: req.GetSessionId()}
		env := model.SessionTypeToDmEnv[session.Type]
		connType := model.SessionTypeToConnType[session.Type]
		if req.GetIsOnlyEmptyTheDialog() { // 解除关联, 顺便下发一个当前的服务器时间, 注意这个时间可能和数据库里的不一样.
			sr.SessionDisassociatedTimestamp = uint64(time.Now().UnixMilli())
			_ = s.dao.ClearSession(ctx, &KEP_DM.ClearSessionRequest{SessionID: req.GetSessionId(), RunEnv: env,
				AppID: strconv.FormatUint(session.BotBizID, 10)})
			_ = s.dao.ClearWorkflowSession(ctx, &KEP_WF_DM.ClearSessionRequest{SessionID: req.GetSessionId(),
				RunEnv: model.SessionTypeToWfEnv[session.Type], AppID: strconv.FormatUint(session.BotBizID, 10)})
			_ = s.dao.SetMultiModalStatus(ctx, req.GetSessionId(), false) // 设置多模态状态为false
			_ = s.dao.SetFileKnowledgeStatus(ctx, req.GetSessionId(), false)
			_ = s.dao.SetMultiModalHistory(ctx, req.GetSessionId(), "")               // 清空多模态历史记录
			_ = s.dao.SetLastWorkflow(ctx, session.BotBizID, req.GetSessionId(), nil) // 清空上一次的工作流
		}
		// 不管是解除关联 还是清空会话 都要执行
		uin := pkg.Uin(ctx)
		s.dao.ReleasePod(ctx, uin, strconv.FormatUint(session.BotBizID, 10), req.GetSessionId())
		_ = s.dao.ClearAgentStatus(ctx, strconv.FormatUint(session.BotBizID, 10), req.GetSessionId())
		_ = s.dao.SetSessionUnInterrupt(ctx, strconv.FormatUint(session.BotBizID, 10), req.GetSessionId())
		s.dao.CodeRelease(ctx, req.GetSessionId())
		s.dao.SetWorkflowUnchanged(ctx, session.BotBizID, req.GetSessionId(), "")
		_ = s.dao.EmitWsUser(ctx, &model.Conn{Type: connType}, u.ID, sr)
	}
	return &pb.ResetSessionRsp{}, nil
}

func (s *Service) getFroms(
	ctx context.Context, sessions []model.Session, scene dao.AppScene,
) (map[uint64]*model.AppListInfo, map[uint64]model.CorpStaff, error) {
	var botBizIDs, staffBizIDs []uint64
	for _, v := range sessions {
		botBizIDs = append(botBizIDs, v.BotBizID)
		staffBizIDs = append(staffBizIDs, v.VisitorBizID, v.SeatBizID)
	}
	appScene := dao.AppReleaseScene
	if scene == dao.AppTestScene || scene == dao.AppWorkflowDebug {
		appScene = dao.AppTestScene
	}
	apps, err := s.dao.GetAppsByBizID(ctx, appScene, botBizIDs)
	clues.AddTrackDataWithError(ctx, "getFroms.dao.GetAppsByBizID", clues.M{
		"scene": appScene, "botBizIDs": botBizIDs, "apps": apps,
	}, err)
	if err != nil {
		return nil, nil, pkg.ErrInternalServerError
	}
	botMap := map[uint64]*model.AppListInfo{}
	for _, b := range apps {
		botMap[b.GetAppId()] = b
	}

	staffs, err := s.dao.GetCorpStaffsByBizID(ctx, staffBizIDs)
	clues.AddTrackDataWithError(ctx, "getFroms.dao.GetAppsByBizID", clues.M{
		"staffBizIDs": staffBizIDs, "staffs": staffs,
	}, err)
	if err != nil {
		return nil, nil, pkg.ErrInternalServerError
	}
	staffmap := map[uint64]model.CorpStaff{}
	for _, u := range staffs {
		staffmap[u.ID] = u
	}

	return botMap, staffmap, nil
}

// GetMsgRecord 获取消息记录
func (s *Service) GetMsgRecord(ctx context.Context, req *pb.GetMsgRecordReq) (*pb.GetMsgRecordRsp, error) {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "GetMsgRecord cost time:%v ms", time.Since(tik).Milliseconds())
	}()

	ctx = clues.NewTrackContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	log.InfoContextf(ctx, "REQ|GetMsgRecord %+v", req)
	typ := model.ConnType(req.GetType())

	if !helper.CheckSessionID(req.GetSessionId()) {
		log.WarnContextf(ctx, "[param invalid] GetMsgRecord, SessionID:%s is invalid, type:%d",
			req.GetSessionId(), typ)
	}
	if config.App().GetMsgCountLimit > 0 && req.GetCount() > uint32(config.App().GetMsgCountLimit) {
		log.InfoContextf(ctx, "GetMsgRecord, Count:%d over limit:%d, set count=limit",
			req.GetCount(), config.App().GetMsgCountLimit)
		req.Count = uint32(config.App().GetMsgCountLimit)
	}
	if config.App().GetMsgDefaultCount > 0 && req.GetCount() == 0 {
		log.InfoContextf(ctx, "GetMsgRecord, Count is zero, set count=default value(%d)",
			config.App().GetMsgDefaultCount)
		req.Count = uint32(config.App().GetMsgDefaultCount)
	}
	var midRecord *model.MsgRecord
	var err error
	if len(req.GetMidRecordId()) > 0 {
		if req.GetCount() == 0 {
			log.WarnContextf(ctx, "[param invalid] GetMsgRecord, Count is zero",
				req.GetCount(), config.App().GetMsgCountLimit)
			return nil, pkg.ErrBadRequest
		}
		if midRecord, err = s.getMsgRecordByMidRecordID(ctx, req); err != nil {
			return nil, err
		}
	}
	session, userID, userType, err := s.createGetMsgSession(ctx, req, midRecord)
	clues.AddTrackDataWithError(ctx, "GetMsgRecord.createGetMsgSession",
		map[string]any{"session": session, "userID": userID}, err)
	if err != nil {
		return nil, err
	}

	if session == nil && len(req.GetMidRecordId()) == 0 {
		return nil, pkg.ErrSessionNotFound
	}

	var lastIDCreateTime time.Time
	if len(req.GetLastRecordId()) > 0 {
		if lastIDCreateTime, err = s.getLastIDCreateTime(ctx, req, session); err != nil {
			return nil, err
		}
	}
	var records []model.MsgRecord
	if len(req.GetMidRecordId()) > 0 {
		records, err = s.getContextsWithMidRecord(ctx, req, midRecord)
	} else {
		mp := model.GetMsgRecordParam{
			LastIDCreateTime: lastIDCreateTime,
			Count:            req.GetCount(),
			Types: []model.RecordType{
				model.RecordTypeGreeting, model.RecordTypeMessage, model.RecordTypeExperience,
			},
			SessionID:      req.GetSessionId(),
			IncludeBotEvil: true,
			BotBizID:       session.BotBizID,
		}
		records, err = s.dao.GetMsgRecord(ctx, mp)
		clues.AddTrackDataWithError(ctx, "GetMsgRecord",
			map[string]any{"GetMsgRecordParam": mp, "MsgRecord": records}, err)
	}
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	var bots = make(map[uint64]*model.AppListInfo)
	var users = make(map[uint64]model.CorpStaff)
	if len(req.GetMidRecordId()) == 0 || (len(req.GetMidRecordId()) > 0 && session != nil) {
		bots, users, err = s.getFroms(ctx, []model.Session{*session}, dao.AppScene(req.GetScene()))
		if err != nil {
			return nil, pkg.ErrInternalServerError
		}
	}
	srcType := model.Conn2Source.GetSourceType(typ, userType)
	data := make([]*pb.MsgRecord, 0, len(records))
	for _, r := range records {
		data = append(data, s.getMsgRecordWithTokenStat(ctx, r, srcType, userID, bots, users))
	}
	var rt int64 = 0
	if len(req.GetMidRecordId()) == 0 {
		rt = session.ResetTime.UnixMilli()
		if rt < 0 {
			rt = 0
		}
	}
	resp := &pb.GetMsgRecordRsp{Records: data, SessionDisassociatedTimestamp: rt}
	log.DebugContextf(ctx, "RSP|GetMsgRecord %s", helper.Object2String(resp))
	clues.AddTrackData(ctx, "GetMsgRecord.GetMsgRecordRsp", resp)
	return resp, nil
}

func (s *Service) getLastIDCreateTime(ctx context.Context, req *pb.GetMsgRecordReq, session *model.Session) (
	time.Time, error) {
	if session == nil {
		return time.Time{}, pkg.ErrSessionNotFound
	}
	var lastIDCreateTime time.Time
	if !helper.CheckRecordID(req.GetLastRecordId()) {
		log.WarnContextf(ctx, "[param invalid] GetMsgRecord, LastRecordID:%s is invalid",
			req.GetLastRecordId())
	}
	lastRecord, err := s.dao.GetMsgRecordByRecordID(ctx, req.GetLastRecordId(), session.BotBizID)
	clues.AddTrackDataWithError(ctx, "GetMsgRecordByRecordID", map[string]any{"MsgRecord": lastRecord}, err)
	if err != nil {
		return lastIDCreateTime, pkg.ErrInternalServerError
	}
	if lastRecord != nil && lastRecord.SessionID != req.GetSessionId() {
		return lastIDCreateTime, pkg.ErrInvalidMsgRecord
	}
	if lastRecord != nil {
		lastIDCreateTime = lastRecord.CreateTime
	}
	return lastIDCreateTime, nil
}
func (s *Service) getMsgRecordByMidRecordID(ctx context.Context, req *pb.GetMsgRecordReq) (*model.MsgRecord, error) {
	var midRecord *model.MsgRecord
	if !helper.CheckRecordID(req.GetMidRecordId()) {
		log.WarnContextf(ctx, "[param invalid] GetMsgRecord, MidRecordId:%s is invalid",
			req.GetMidRecordId())
		return nil, pkg.ErrMsgRecordErr
	}
	scene := dao.AppReleaseScene
	if req.GetScene() == uint32(dao.AppTestScene) {
		scene = dao.AppTestScene
	}
	app, err := s.dao.GetAppByAppKey(ctx, scene, req.GetBotAppKey())
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}

	midRecord, err = s.dao.GetMsgRecordByRecordID(ctx, req.GetMidRecordId(), app.GetAppBizId())
	if err != nil {
		log.WarnContextf(ctx, "strconv.ParseUint failed, error: %v", err)
		return nil, pkg.ErrInvalidMsgRecord
	}
	return midRecord, nil
}

func (s *Service) getContextsWithMidRecord(ctx context.Context, req *pb.GetMsgRecordReq, r *model.MsgRecord) (
	[]model.MsgRecord, error) {
	var above []model.MsgRecord
	var below []model.MsgRecord
	var err error
	if r == nil {
		log.WarnContextf(ctx, "getContextsWithMidRecord, midRecord is nil")
		return nil, pkg.ErrInvalidMsgRecord
	}
	count := req.GetCount()
	half := count / 2
	remainder := count % 2
	if half > 0 {
		above, err = s.dao.GetMsgRecord(ctx, model.GetMsgRecordParam{
			LastIDCreateTime: r.CreateTime,
			Count:            half,
			Types:            []model.RecordType{r.Type, model.RecordTypeGreeting},
			SessionID:        r.SessionID,
			BotBizID:         r.BotBizID,
			IncludeBotEvil:   true,
			IncludeStart:     true,
		})
		clues.AddTrackE(ctx, "getContextsWithMidRecord.above", above, err)
		if err != nil {
			log.WarnContextf(ctx, "getContextsWithMidRecord, above, error: %v", err)
			return nil, err
		}
	}

	if half+remainder > 0 {
		below, err = s.dao.GetMsgRecord(ctx, model.GetMsgRecordParam{
			FirstIDCreateTime: r.CreateTime,
			Count:             half + remainder,
			Types:             []model.RecordType{r.Type, model.RecordTypeGreeting},
			SessionID:         r.SessionID,
			BotBizID:          r.BotBizID,
			IncludeBotEvil:    true,
		})
		clues.AddTrackE(ctx, "getContextsWithMidRecord.below", below, err)
		if err != nil {
			log.WarnContextf(ctx, "getContextsWithMidRecord, below, error: %v", err)
			return nil, err
		}
	}
	// above里面可能包含了r。最终返回的消息记录列表里面需要包含r，但是只能有一条，所以有下面的去重逻辑
	all := above
	var i int
	for i = 0; i < len(all); i++ {
		if all[i].ID == r.ID {
			break
		}
	}
	if i == len(all) {
		all = append(all, *r)
	}
	all = append(all, below...)
	return all, nil
}

func (s *Service) createGetMsgSession(ctx context.Context,
	req *pb.GetMsgRecordReq, midRecord *model.MsgRecord) (*model.Session, uint64, uint32, error) {
	session := &model.Session{SessionID: req.GetSessionId()}
	typ := model.ConnType(req.GetType())
	var userID uint64 = 0
	var userType uint32
	if typ == model.ConnTypeAPIVisitor {
		if req.GetBotAppKey() == "" {
			return nil, 0, userType, pkg.ErrRobotNotExist
		}
		if !helper.CheckBotAppKey(req.GetBotAppKey(), false) {
			log.WarnContextf(ctx, "[param invalid] GetMsgRecord, BotAppKey:%s is invalid, type:%d",
				req.GetBotAppKey(), typ)
		}
		scene := dao.AppReleaseScene
		if req.GetScene() == uint32(dao.AppTestScene) {
			scene = dao.AppTestScene
		}
		app, err := s.dao.GetAppByAppKey(ctx, scene, req.GetBotAppKey())
		clues.AddTrackDataWithError(ctx, "createGetMsgSession.GetAppByAppKey",
			map[string]any{"scene": scene, "app": app}, err)
		if err != nil {
			return nil, 0, userType, err
		}
		if app == nil {
			return nil, 0, userType, pkg.ErrRobotNotExist
		}
		session.BotBizID = app.GetAppBizId()
	} else {
		// 校验ctx里面的uin和token不能都为空。
		u, err := s.dao.CheckSession(ctx)
		clues.AddTrackDataWithError(ctx, "createGetMsgSession.CheckSession", map[string]any{"CorpStaff": u}, err)
		if err != nil {
			if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
				return nil, 0, userType, err
			}
			return nil, 0, userType, pkg.ErrInternalServerError
		}
		userID = u.ID
		userType = u.UserType
		sessionType := model.SessionTypeNormal
		if req.GetScene() == uint32(dao.AppTestScene) {
			sessionType = model.SessionTypeExperience
		} else if req.GetScene() == uint32(dao.AppWorkflowDebug) {
			sessionType = model.SessionTypeWorkflow
		}
		sessionID := req.GetSessionId()
		if len(req.GetMidRecordId()) > 0 {
			if midRecord == nil {
				log.WarnContextf(ctx, "createGetMsgSession, midRecord is nil")
				return nil, 0, userType, pkg.ErrInvalidMsgRecord
			}
			sessionID = midRecord.SessionID
		}
		session, err = s.dao.GetSession(ctx, sessionType, sessionID)
		clues.AddTrackDataWithError(ctx, "createGetMsgSession.GetSession",
			clues.M{"SessionType": sessionType, "SessionID": req.GetSessionId(), "session": session}, err)
		if err != nil {
			// https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800121714984
			// 不满意问回复查看对话记录，企点场景校验session会找不到会话，这里不报错
			if len(req.GetMidRecordId()) > 0 {
				return nil, userID, userType, nil
			}
			return nil, 0, userType, pkg.ErrInternalServerError
		}
		if typ == model.ConnTypeSeat && !session.IsSeat(u.ID) {
			return nil, 0, userType, pkg.ErrSessionNotFound
		}
		if (typ == model.ConnTypeVisitor) && !session.IsVisitor(u.ID) && len(req.GetMidRecordId()) == 0 {
			return nil, 0, userType, pkg.ErrSessionNotFound
		}
	}
	return session, userID, userType, nil
}

// GetPipelineDebugging pipeline debug 信息
func (s *Service) GetPipelineDebugging(ctx context.Context,
	req *pb.GetPipelineDebuggingReq) (*pb.GetPipelineDebuggingRsp, error) {
	ctx = clues.NewTrackContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddT(ctx, "GetPipelineDebugging", req)

	u, err := s.dao.CheckSession(ctx)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
			return nil, err
		}
		return nil, pkg.ErrInternalServerError
	}
	clues.AddTrackE(ctx, "dao.CheckSession", u, err)

	corpID1, err := s.dao.QueryCorpIDByStaffID(ctx, u.ID)
	clues.AddTrackE(ctx, "dao.QueryCorpIDByStaffID", corpID1, err)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}

	corpID2, err := s.dao.QueryCorpIDByAppID(ctx, req.GetAppBizId())
	clues.AddTrackE(ctx, "dao.QueryCorpIDByAppID", corpID2, err)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}

	if corpID1 != corpID2 {
		return nil, pkg.ErrInvalidApp
	}

	logs, err := s.dao.QueryPipelineDebugging(ctx, req.GetAppBizId(), req.GetRecordId())
	clues.AddTrackE(ctx, "dao.QueryPipelineDebugging", logs, err)

	if err != nil {
		return nil, pkg.ErrInternalServerError
	}

	resp := &pb.GetPipelineDebuggingRsp{}
	for i := range logs {
		d1, _ := json.Marshal(logs[i].Input)
		d2, _ := json.Marshal(logs[i].Output)
		d3, _ := json.Marshal(logs[i].ExtraData)
		d := &pb.GetPipelineDebuggingRsp_PipelineDebugging{
			Key:       logs[i].Key,
			Input:     string(d1),
			Output:    string(d2),
			Status:    uint32(logs[i].Status),
			FailMsg:   logs[i].FailMessage,
			ExtraData: string(d3),
			StartTime: logs[i].StartTime,
			EndTime:   logs[i].EndTime,
		}
		resp.Logs = append(resp.Logs, d)
	}
	clues.AddT(ctx, "GetPipelineDebuggingRsp", resp)
	return resp, nil
}

// getMsgRecordWithTokenStat 获取包含调试信息的消息记录
func (s *Service) getMsgRecordWithTokenStat(ctx context.Context, record model.MsgRecord, typ model.SourceType,
	userID uint64, bots map[uint64]*model.AppListInfo, staffs map[uint64]model.CorpStaff) *pb.MsgRecord {
	msgRecord := record.ToPbMsgRecord(ctx, typ, userID, bots, staffs)
	log.DebugContextf(ctx, "getMsgRecordWithTokenStat|msgRecord:%+v", helper.Object2String(msgRecord))
	if len(record.TokenStat) > 0 {
		var ts pb.TokenStat
		var thought pb.AgentThought
		err := json.Unmarshal([]byte(record.TokenStat), &ts)
		if err != nil {
			log.ErrorContextf(ctx, "getMsgRecordWithTokenStat|TokenStat.json.Unmarshal err:%+v", err)
			return msgRecord
		}
		stat, err := s.dao.GetMsgRecordTokenStatByStatID(ctx, ts.RecordId)
		if err != nil {
			log.ErrorContextf(ctx, "getMsgRecordWithTokenStat|GetMsgRecordTokenStatByStatID err:%+v", err)
			return msgRecord
		}
		if stat != nil && len(stat.Procedures) > 0 {
			var procedures []*pb.Procedure
			err = json.Unmarshal([]byte(stat.Procedures), &procedures)
			if err != nil {
				log.ErrorContextf(ctx, "getMsgRecordWithTokenStat|Procedure.json.Unmarshal err:%+v", err)
				return msgRecord
			}
			ts.Procedures = procedures
			ts.TraceId = stat.TraceID + " / " + ts.RecordId
		}
		if stat != nil && len(stat.AgentThought) > 0 {
			err = json.Unmarshal([]byte(stat.AgentThought), &thought)
			thought.TraceId = stat.TraceID + " / " + ts.RecordId
			if err != nil {
				log.ErrorContextf(ctx, "getMsgRecordWithTokenStat|Thought.json.Unmarshal err:%+v", err)
				return msgRecord
			}
			msgRecord.AgentThought = &thought
		}
		msgRecord.TokenStat = &ts
		msgRecord.ExtraInfo = s.wrapExtraInfo(ctx, stat)
		msgRecord.WorkFlow = s.wrapWorkflowInfo(ctx, stat)

	}
	return msgRecord
}

// GetAnswerFromDocs 从文档中找答案
func (s StreamService) GetAnswerFromDocs(request *pb.GetAnswerFromDocsRequest,
	server pb.StreamChat_GetAnswerFromDocsServer) error {
	ctx := server.Context()
	log.InfoContextf(ctx, "GetAnswerFromDocs, req: %s", utils.ToJsonString(request))
	chReply, err := eventbus.GetAnswerFromDocsImpl(trpc.CloneContext(ctx), request)
	if err != nil {
		return err
	}
	for llmRsp := range chReply {
		err := server.Send(llmRsp)
		if err != nil {
			log.ErrorContextf(ctx, "server.Send failed, error: %v", err)
			return err
		}
	}
	return nil
}

// GetAnswerFromKnowledge 从知识库中找答案
func (s StreamService) GetAnswerFromKnowledge(server pb.StreamChat_GetAnswerFromKnowledgeServer) error {
	ctx := server.Context()
	log.DebugContextf(ctx, "GetAnswerFromKnowledge|server:%+v", helper.Object2String(server))
	wg := sync.WaitGroup{}
	wg.Add(2)
	serverErrCh := make(chan error, 3)
	serverReqCh := make(chan *pb.GetAnswerFromKnowledgeRequest)
	// 处理流式请求
	go func() {
		defer errors.PanicHandler()
		defer wg.Done()
		defer close(serverReqCh)
		for {
			select {
			case <-ctx.Done():
				log.InfoContextf(ctx, "GetAnswerFromKnowledge|<-ctx.Done()")
				return
			default:
				req, err := server.Recv()
				if err == nil && req != nil {
					log.DebugContextf(ctx, "GetAnswerFromKnowledge|cli.Recv|req:%+v", req)
					serverReqCh <- req
				}
				if err == io.EOF {
					log.DebugContextf(ctx, "GetAnswerFromKnowledge|cli.Recv EOF return")
					return
				}
				if err != nil {
					log.ErrorContextf(ctx, "GetAnswerFromKnowledge|cli.Recv failed, err:%+v", err)
					serverErrCh <- err
					return
				}
			}
		}
	}()

	// 处理知识库中获取答案请求
	go func() {
		defer errors.PanicHandler()
		defer wg.Done()
		err := HandlerReq(ctx, server, serverReqCh)
		if err != nil {
			log.ErrorContextf(ctx, "GetAnswerFromKnowledge failed, err:%+v", err)
			serverErrCh <- err
		}
	}()

	wg.Wait()
	for {
		select {
		case <-ctx.Done():
			log.InfoContextf(ctx, "GetAnswerFromKnowledge|<-ctx.Done()")
			return nil
		case err := <-serverErrCh:
			if err != nil {
				log.ErrorContextf(ctx, "GetAnswerFromKnowledge|failed, err:%+v", err)
				return err
			}
		default:
			log.DebugContextf(ctx, "GetAnswerFromKnowledge|success")
			return nil
		}
	}
}

// HandlerReq 处理知识库中获取答案请求
func HandlerReq(ctx context.Context, server pb.StreamChat_GetAnswerFromKnowledgeServer,
	serverReqCh chan *pb.GetAnswerFromKnowledgeRequest) error {
	log.DebugContextf(ctx, "HandlerReq|called")
	for {
		select {
		case <-ctx.Done():
			log.InfoContextf(ctx, "HandlerReq|<-ctx.Done()")
			return nil
		case req, ok := <-serverReqCh:
			if !ok {
				log.InfoContextf(ctx, "HandlerReq|serverReqCh closed")
				return nil
			}
			err := HandlerGetAnswerFromKnowledgeReq(ctx, server, req)
			if err != nil {
				return err
			}
		}
	}
}

// HandlerGetAnswerFromKnowledgeReq 处理知识库中获取答案请求
func HandlerGetAnswerFromKnowledgeReq(ctx context.Context, server pb.StreamChat_GetAnswerFromKnowledgeServer,
	serverReq *pb.GetAnswerFromKnowledgeRequest) error {
	log.DebugContextf(ctx, "HandlerGetAnswerFromKnowledgeReq|called")
	stopSignal := make(chan struct{}, 1)
	switch serverReq.Type {
	case pb.GetAnswerFromKnowledgeRequest_GET_ANSWER:
		replyCh, err := eventbus.GetAnswerFromKnowledgeImpl(ctx, serverReq, stopSignal)
		if err != nil {
			return err
		}
		cfg := config.App().Bot
		timeout := time.NewTicker(time.Duration(cfg.AnswerFromKnowledgeTimeout) * time.Hour)
		defer timeout.Stop()
		for {
			select {
			case <-ctx.Done():
				log.InfoContextf(ctx, "HandlerGetAnswerFromKnowledgeReq|<-ctx.Done()")
				return nil
			case <-timeout.C:
				err := fmt.Errorf("timeout|timeoutConfig:%+v h", cfg.AnswerFromKnowledgeTimeout)
				log.InfoContextf(ctx, "HandlerGetAnswerFromKnowledgeReq|failed, err:%+v", err)
				return nil
			case rsp, ok := <-replyCh:
				if !ok {
					return nil
				}
				err := server.Send(rsp)
				if err != nil {
					log.ErrorContextf(ctx, "HandlerGetAnswerFromKnowledgeReq|server.Send() failed, err:%+v", err)
					return err
				}
			}
		}
	case pb.GetAnswerFromKnowledgeRequest_GET_CANCEL:
		stopSignal <- struct{}{}
		_, err := eventbus.GetAnswerFromKnowledgeImpl(ctx, serverReq, stopSignal)
		if err != nil {
			return err
		}
		log.InfoContextf(ctx, "HandlerGetAnswerFromKnowledgeReq|TASK_CANCEL")
		return nil
	default:
		err := fmt.Errorf("illegal reqType:%v", serverReq.GetType())
		log.ErrorContextf(ctx, "HandlerGetAnswerFromKnowledgeReq|failed, err:%+v", err)
		return err
	}
}

// GetUserDialogConfig 获取用户应用配置
func (s *Service) GetUserDialogConfig(ctx context.Context, req *pb.GetUserDialogConfigReq) (
	*pb.GetUserDialogConfigRsp, error) {
	rsp := &pb.GetUserDialogConfigRsp{}
	log.DebugContextf(ctx, "GetUserDialogConfig req: %+v", req)
	app, err := s.dao.GetAppByBizID(ctx, dao.AppReleaseScene, req.BotBizId)
	if err != nil {
		return rsp, err
	}
	if app == nil {
		return rsp, pkg.ErrRobotNotExist
	}
	dialogConfig, found, err := s.dao.GetUserDialogConfig(ctx, &model.UserDialogConfig{
		UserBizID: req.UserBizId,
		UserType:  model.DialogUserType(req.UserType),
		BotBizID:  req.BotBizId,
	})
	if err != nil {
		return rsp, err
	}
	rsp.SearchNetwork = string(getUserDialogConfigSearchNetwork(app, found, dialogConfig))
	rsp.AutoPlay = string(dialogConfig.AutoPlay)
	rsp.Stream = string(getUserDialogConfigStream(app, found, dialogConfig))
	rsp.WorkflowStatus = string(getUserDialogConfigWorkflowStatus(app, found, dialogConfig))
	return rsp, nil
}

// ModifyUserDialogConfig 更新用户应用配置
func (s *Service) ModifyUserDialogConfig(ctx context.Context, req *pb.ModifyUserDialogConfigReq) (
	*pb.ModifyUserDialogConfigRsp, error) {
	log.DebugContextf(ctx, "ModifyUserDialogConfig req: %+v", req)
	if req.UserType != string(model.DialogUserTypeExperience) && req.UserType != string(model.DialogUserTypeStaff) {
		return nil, pkg.ErrBadRequest
	}
	if req.SearchNetwork != string(model.SearchNetworkDefault) &&
		req.SearchNetwork != string(model.SearchNetworkEnabled) &&
		req.SearchNetwork != string(model.SearchNetworkDisabled) {
		return nil, pkg.ErrBadRequest
	}
	if req.AutoPlay != string(model.AutoPlayDefault) &&
		req.AutoPlay != string(model.AutoPlayEnabled) &&
		req.AutoPlay != string(model.AutoPlayDisabled) {
		return nil, pkg.ErrBadRequest
	}
	if req.Stream != string(model.ChatStreamDefault) &&
		req.Stream != string(model.ChatStreamEnabled) &&
		req.Stream != string(model.ChatStreamDisabled) {
		return nil, pkg.ErrBadRequest
	}
	if req.WorkflowStatus != string(model.WorkflowStatusDefault) &&
		req.WorkflowStatus != string(model.WorkflowStatusEnabled) &&
		req.WorkflowStatus != string(model.WorkflowStatusDisabled) {
		return nil, pkg.ErrBadRequest
	}

	err := s.dao.CreateOrUpdateUserDialogConfig(ctx, req)
	if err != nil {
		return nil, err
	}
	return &pb.ModifyUserDialogConfigRsp{}, nil
}

// wrapExtraInfo 包装额外信息
func (s *Service) wrapExtraInfo(ctx context.Context, stat *model.MsgRecordTokenStat) (extraInfo *pb.ExtraInfo) {
	extraInfo = &pb.ExtraInfo{
		EChartsInfo: make([]string, 0),
	}
	if stat != nil {

		var procedures []*pb.Procedure
		err := json.Unmarshal([]byte(stat.Procedures), &procedures)
		if err != nil {
			log.WarnContextf(ctx, "getMsgRecordWithTokenStat|Procedure.json.Unmarshal err:%+v", err)
			return extraInfo
		}

		for _, p := range procedures {
			title := p.Title
			input := p.GetDebugging().GetAgent().GetInput()
			if strings.HasSuffix(title, "GenerateCharts") && strings.Contains(input, "pyecharts") {
				log.InfoContextf(ctx, "GenerateCharts output:%s", p.GetDebugging().GetAgent().GetOutput())
				toolResponse := p.GetDebugging().GetAgent().GetOutput()
				response := &model.EChartsResponse{}
				err = jsoniter.Unmarshal([]byte(toolResponse), response)
				if err != nil {
					return
				}
				if FileIsHTML(*response) {
					echartsJSON := helper.FetchJSONFromURL(ctx, response.Data.Files[0].URL)
					if len(echartsJSON) > 0 {
						extraInfo.EChartsInfo = append(extraInfo.EChartsInfo, echartsJSON)
					}
					return
				}

			}
		}
	}
	return extraInfo
}

// FileIsHTML 文件是否是HTML
func FileIsHTML(response model.EChartsResponse) bool {
	return len(response.Data.Files) > 0 && len(response.Data.Files[0].URL) > 0 &&
		strings.HasSuffix(response.Data.Files[0].FileName, ".html")
}

// wrapWorkflowInfo 包装工作流信息
func (s *Service) wrapWorkflowInfo(ctx context.Context, stat *model.MsgRecordTokenStat) *pb.MsgRecord_WorkFlow {
	workflowInfo := &pb.MsgRecord_WorkFlow{}

	if stat != nil {
		var procedures []*pb.Procedure
		err := json.Unmarshal([]byte(stat.Procedures), &procedures)
		if err != nil {
			log.WarnContextf(ctx, "getMsgRecordWithTokenStat|Procedure.json.Unmarshal err:%+v", err)
		}
		for _, p := range procedures {
			workflowInfo.WorkflowId = p.GetDebugging().GetWorkFlow().GetWorkflowId()
			workflowInfo.WorkflowName = p.GetDebugging().GetWorkFlow().GetWorkflowName()
			workflowInfo.WorkflowRunId = p.GetDebugging().GetWorkFlow().GetWorkflowRunId()
			workflowInfo.OptionCards = p.GetDebugging().GetWorkFlow().GetOptionCards()
			workflowInfo.Outputs = p.GetDebugging().GetWorkFlow().GetOutputs()
			workflowInfo.WorkflowReleaseTime = p.GetDebugging().GetWorkFlow().GetWorkflowReleaseTime()
		}
	}
	return workflowInfo
}

func getUserDialogConfigStream(app *model.App, found bool, dialogConfig model.UserDialogConfig) model.ChatStream {
	if !found || dialogConfig.Stream == model.ChatStreamDefault {
		if app.GetKnowledgeQa().GetPattern() == model.AppModeAgent { // agent模式
			return model.ChatStreamEnabled // agent模式只有流式
		}
		if app.GetKnowledgeQa().GetPattern() == model.AppModeSingleWorkflow { // 单工作流模式
			return model.ChatStreamDefault
		}
		if app.GetKnowledgeQa().GetOutput().GetMethod() == model.ChatOutputMethodStream {
			return model.ChatStreamEnabled
		} else {
			return model.ChatStreamDefault
		}
	}
	return dialogConfig.Stream
}

func getUserDialogConfigWorkflowStatus(app *model.App, found bool,
	dialogConfig model.UserDialogConfig) model.WorkflowStatus {
	if !found || dialogConfig.WorkflowStatus == model.WorkflowStatusDefault {
		if app.GetKnowledgeQa().GetPattern() == model.AppModeAgent { // agent模式
			return model.WorkflowStatusDefault // agent模式无工作流
		}
		if app.GetKnowledgeQa().GetPattern() == model.AppModeSingleWorkflow { // 单工作流模式
			return model.WorkflowStatusEnabled //  单工作流模式有工作流
		}
		if app.GetKnowledgeQa().GetWorkflow().GetIsEnabled() {
			return model.WorkflowStatusEnabled
		} else {
			return model.WorkflowStatusDefault
		}
	}
	return dialogConfig.WorkflowStatus
}

func getUserDialogConfigSearchNetwork(app *model.App, found bool,
	dialogConfig model.UserDialogConfig) model.SearchNetwork {
	if !found || dialogConfig.SearchNetwork == model.SearchNetworkDefault {
		if app.GetKnowledgeQa().GetPattern() == model.AppModeAgent { // agent模式
			return model.SearchNetworkDefault
		}
		if app.GetKnowledgeQa().GetPattern() == model.AppModeSingleWorkflow { // 单工作流模式
			return model.SearchNetworkDefault
		}
		if app.GetKnowledgeQa().GetUseSearchEngine() {
			return model.SearchNetworkEnabled
		} else {
			return model.SearchNetworkDefault
		}
	}
	return dialogConfig.SearchNetwork
}
