package service

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/message"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
)

// RequestNodeSSEEventHandler SSE 事件处理方法【已废弃】
func (s *Service) RequestNodeSSEEventHandler(ctx context.Context, ev *message.Event) (rev *message.Event, err error) {
	defer func() {
		success := helper.When(err == nil, "1", "0")
		metrics.ReportSseClientConnect(success)
		log.InfoContextf(ctx, "RequestNodeSSEEventHandler ReportSseClientConnect end |Process")
	}()
	return &message.Event{}, nil
}
