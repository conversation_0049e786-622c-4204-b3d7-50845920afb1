// chat
//
// @(#)send_sse.go  Wednesday, May 15, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package service

import (
	"context"
	"encoding/base64"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/message"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

// SseEventHandler SSE 事件处理方法
func (s *Service) SseEventHandler(ctx context.Context, ev *message.Event) (rev *message.Event, err error) {
	ctx = pf.NewPipelineFlowContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	pf.StartElapsed(ctx, "SSE."+event.EventSend)
	statistics := &pkg.PPLStatistics{
		PPLStartTime: time.Now(),
	}
	pkg.WithInterfaceType(ctx, "sse")
	pkg.WithStatistics(ctx, statistics)
	clues.AddT(ctx, "SseEventHandler", ev)
	defer func() {
		success := helper.When(err == nil, "1", "0")
		metrics.ReportSseClientConnect(success)
	}()
	typ := ev.GetType()
	if typ == message.EventTypeDisconnect {
		_ = s.dao.DelSseClient(ctx, ev.GetClientId())
		return &message.Event{}, nil
	}
	if typ != message.EventTypeConnect {
		return &message.Event{}, nil
	}
	req, err := getRequest(ctx, ev)
	if err != nil {
		return nil, err
	}
	app, err := s.dao.GetAppByAppKey(ctx, dao.AppReleaseScene, req.BotAppKey)
	clues.AddTrackE(ctx, "dao.GetAppByAppKey",
		clues.M{"scene": dao.AppReleaseScene, "appKey": req.BotAppKey, "app": app}, err)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}
	for _, blacklistedID := range config.App().App.BlackList {
		if app.GetAppBizId() == blacklistedID {
			return nil, pkg.ErrAppIDInBlacklist
		}
	}
	if reached, err := s.limitByApp(ctx, app, req.Content); err != nil || reached {
		log.InfoContextf(ctx, "botAppKey:%s SSE rate limit reached", req.BotAppKey)
		return nil, pkg.ErrAppRateLimiter
	}
	visitor, err := s.dao.MustGetVisitor(ctx, app.GetId(), app.GetAppBizId(), req.VisitorBizID)
	clues.AddTrackE(ctx, "dao.MustGetVisitor", clues.M{"botID": app.GetId(),
		"botBizID": app.GetAppBizId(), "visitorBizID": req.VisitorBizID, "visitor": visitor}, err)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	if len(req.VisitorLabels) > config.GetLabelCount() {
		return nil, pkg.ErrInvalidVisitorLabel
	}
	labels, err := getSseLabels(&req)
	if err != nil {
		return nil, err
	}
	conn := &model.Conn{
		IsSSE:          true,
		Type:           model.ConnTypeAPIVisitor,
		ClientID:       ev.GetClientId(),
		APIBotBizID:    app.GetAppBizId(),
		CorpStaffID:    visitor.ID,
		CorpStaffBizID: visitor.ID,
		VisitorLabels:  labels,
	}
	if err := s.dao.SetSseClient(ctx, conn.ClientID, true); err != nil {
		return nil, pkg.ErrInternalServerError
	}
	if req.Incremental {
		dao.InitOutputCache(ctx, ev.GetClientId())
	}
	reqByte, _ := jsoniter.Marshal(req)
	if err := eventbus.Push(ctx, conn, event.EventDialog, reqByte); err != nil {
		return nil, pkg.ErrInternalServerError
	}
	return &message.Event{}, nil
}

func getRequest(ctx context.Context, ev *message.Event) (event.DialogEvent, error) {
	req := event.DialogEvent{}
	payload := ev.GetPayload().GetStringValue()
	bs, err := base64.StdEncoding.DecodeString(payload)
	if err != nil {
		log.WarnContextf(ctx, "Decode sse payload error: %+v, payload: %+v", err, payload)
		return req, pkg.ErrBadRequest
	}

	err = jsoniter.Unmarshal(bs, &req)
	clues.AddTrackE(ctx, "json.Unmarshal", &req, err)
	if err != nil {
		log.WarnContextf(ctx, "Unmarshal sse payload error: %+v, payload: %+v", err, string(bs))
		return req, pkg.ErrBadRequest
	}
	if !req.IsValid(ctx) || len(req.BotAppKey) < 5 || len(req.BotAppKey) > 128 {
		return req, pkg.ErrBadRequest
	}
	req.OriginContent = req.Content
	req.Environment = event.EventSend
	req.StartTime = time.Now() // todo 提前
	return req, nil
}

func getSseLabels(req *event.DialogEvent) ([]model.Label, error) {
	var labels []model.Label
	for _, l := range req.VisitorLabels {
		label := model.Label{
			Name:   strings.TrimSpace(l.Name),
			Values: helper.Map(l.Values, strings.TrimSpace),
		}
		if !label.IsValid() {
			return nil, pkg.ErrInvalidVisitorLabel
		}
		labels = append(labels, label)
	}
	return labels, nil
}
