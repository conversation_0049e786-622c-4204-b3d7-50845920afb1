package service

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/message"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
)

// AnswerNodeDocSSEEventHandler SSE 事件处理方法【已废弃】
func (s *Service) AnswerNodeDocSSEEventHandler(ctx context.Context, ev *message.Event) (rev *message.Event, err error) {
	defer func() {
		success := helper.When(err == nil, "1", "0")
		metrics.ReportSseClientConnect(success)
		log.InfoContextf(ctx, "AnswerNodeDocSSEEventHandler ReportSseClientConnect end |Process")
	}()
	log.InfoContextf(ctx, "AnswerNodeDocSSEEventHandler start ev|%v", ev)
	return &message.Event{}, nil
}
