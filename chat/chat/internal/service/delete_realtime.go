package service

import (
	"context"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	parse "git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
)

// DeleteRealtimeDoc 定时任务，获取实时文档，并调用删除接口
func (s *Service) DeleteRealtimeDoc(ctx context.Context) error {
	key := "DeleteRealtimeDoc"
	if !s.tryLock(key) {
		log.InfoContextf(ctx, "task [%s] in processing", key)
		return nil
	}
	defer s.unlock(key)
	t0 := time.Now()
	log.InfoContextf(ctx, "DeleteRealtimeDoc start: %s", t0.String())

	wg := &sync.WaitGroup{}
	jobs := make(chan Task, 10)
	for w := 0; w < 10; w++ {
		go s.worker(ctx, jobs, wg)
	}

	defer func() {
		log.InfoContextf(ctx, "DeleteRealtimeDoc finished: %s", time.Since(t0).String())
		close(jobs)
	}()
	// 获取所有待删除的BotBIzID
	for {
		botBizIDs, err := s.dao.GetDistinctBotBizID(trpc.CloneContext(ctx))
		if err != nil {
			return err
		}
		if len(botBizIDs) == 0 {
			log.DebugContextf(ctx, "GetDistinctBotBizID empty")
			return nil
		}
		// 获取所有待删除的实时文档
		for _, botBizID := range botBizIDs {
			s.DeleteRealtimeDocByBotBizID(trpc.CloneContext(ctx), botBizID, jobs, wg)
		}
		wg.Wait()
	}
}

// DeleteRealtimeDocByBotBizID TODO
func (s *Service) DeleteRealtimeDocByBotBizID(ctx context.Context, botBizID uint64, jobs chan Task,
	wg *sync.WaitGroup) {
	for {
		// 获取实时文档ID
		sessionIDs, err := s.dao.GetSessionID(ctx, botBizID)
		if err != nil {
			return
		}
		if len(sessionIDs) == 0 {
			log.InfoContextf(ctx, "GetRealtimeDocIDByBotBizID empty")
			return
		}
		// 删除实时文档
		for _, sessionID := range sessionIDs {
			// 获取docIDs
			docIDs, err := s.dao.GetDocIDBySessionID(ctx, botBizID, sessionID)
			if err != nil || len(docIDs) == 0 {
				continue
			}
			// 调用下游接口删除
			task := Task{
				BotBizID:  botBizID,
				SessionID: sessionID,
				DocIDs:    docIDs,
			}
			jobs <- task // 最多10并发
			wg.Add(1)
		}
		wg.Wait() // 防止任务没完成，下次GetSession又查出来
	}
}

// Task 任务
type Task struct {
	BotBizID  uint64
	SessionID string
	DocIDs    []uint64
}

func (s *Service) worker(ctx context.Context, tasks <-chan Task, wg *sync.WaitGroup) {
	for task := range tasks {
		_, err := s.dao.DeleteRealtimeDoc(ctx, &parse.DeleteRealtimeDocReq{
			BotBizId:  task.BotBizID,
			SessionId: task.SessionID,
			DocIds:    task.DocIDs,
		})
		// 删除成功后更新状态
		if err == nil {
			_ = s.dao.UpdateDeleteStatus(ctx, task.BotBizID, task.SessionID)
		}
		wg.Done()
	}
	log.InfoContextf(ctx, "worker done")
}
