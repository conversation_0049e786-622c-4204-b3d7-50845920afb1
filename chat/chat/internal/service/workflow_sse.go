package service

import (
	"context"
	"encoding/base64"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/message"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

// NodeDialogEventHandler 工作流对话节点 SSE 事件处理方法  todo： 需要修改
func (s *Service) NodeDialogEventHandler(ctx context.Context, ev *message.Event) (rev *message.Event, err error) {
	defer func() {
		success := helper.When(err == nil, "1", "0")
		metrics.ReportSseClientConnect(success)
	}()
	typ := ev.GetType()
	if typ == message.EventTypeDisconnect {
		_ = s.dao.DelSseClient(ctx, ev.GetClientId())
		return &message.Event{}, nil
	}
	if typ != message.EventTypeConnect {
		return &message.Event{}, nil
	}
	req, err := getNodeDialogRequest(ctx, ev)
	if err != nil {
		return nil, err
	}
	app, err := s.dao.GetAppByAppKey(ctx, dao.AppReleaseScene, req.BotAppKey)
	clues.AddTrackE(ctx, "dao.GetAppByAppKey",
		clues.M{"scene": dao.AppReleaseScene, "appKey": req.BotAppKey, "app": app}, err)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}
	for _, blacklistedID := range config.App().App.BlackList {
		if app.GetAppBizId() == blacklistedID {
			return nil, pkg.ErrAppIDInBlacklist
		}
	}
	if reached, err := s.limitByApp(ctx, app, req.Content); err != nil || reached {
		log.InfoContextf(ctx, "botAppKey:%s SSE rate limit reached", req.BotAppKey)
		return nil, pkg.ErrAppRateLimiter
	}
	visitor, err := s.dao.MustGetVisitor(ctx, app.GetId(), app.GetAppBizId(), req.VisitorBizID)
	clues.AddTrackE(ctx, "dao.MustGetVisitor", clues.M{"botID": app.GetId(),
		"botBizID": app.GetAppBizId(), "visitorBizID": req.VisitorBizID, "visitor": visitor}, err)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	if err != nil {
		return nil, err
	}
	conn := &model.Conn{
		IsSSE:          true,
		Type:           model.ConnTypeAPIVisitor,
		ClientID:       ev.GetClientId(),
		APIBotBizID:    app.GetAppBizId(),
		CorpStaffID:    visitor.ID,
		CorpStaffBizID: visitor.ID,
	}
	if err := s.dao.SetSseClient(ctx, conn.ClientID, true); err != nil {
		return nil, pkg.ErrInternalServerError
	}
	reqByte, _ := jsoniter.Marshal(req)
	if err := eventbus.Push(ctx, conn, event.EventNodeDialog, reqByte); err != nil {
		return nil, pkg.ErrInternalServerError
	}
	return &message.Event{}, nil
}

// NodeDebugEventHandler 工作流非对话节点 SSE 事件处理方法
func (s *Service) NodeDebugEventHandler(ctx context.Context, ev *message.Event) (rev *message.Event, err error) {
	defer func() {
		success := helper.When(err == nil, "1", "0")
		metrics.ReportSseClientConnect(success)
	}()
	typ := ev.GetType()
	if typ == message.EventTypeDisconnect {
		_ = s.dao.DelSseClient(ctx, ev.GetClientId())
		return &message.Event{}, nil
	}
	if typ != message.EventTypeConnect {
		return &message.Event{}, nil
	}
	req, err := getNodeDebugRequest(ctx, ev)
	if err != nil {
		return nil, err
	}
	app, err := s.dao.GetAppByAppKey(ctx, dao.AppReleaseScene, req.BotAppKey)
	clues.AddTrackE(ctx, "dao.GetAppByAppKey",
		clues.M{"scene": dao.AppReleaseScene, "appKey": req.BotAppKey, "app": app}, err)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}
	for _, blacklistedID := range config.App().App.BlackList {
		if app.GetAppBizId() == blacklistedID {
			return nil, pkg.ErrAppIDInBlacklist
		}
	}
	visitor, err := s.dao.MustGetVisitor(ctx, app.GetId(), app.GetAppBizId(), req.VisitorBizID)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	if err != nil {
		return nil, err
	}
	conn := &model.Conn{
		IsSSE:          true,
		Type:           model.ConnTypeAPIVisitor,
		ClientID:       ev.GetClientId(),
		APIBotBizID:    app.GetAppBizId(),
		CorpStaffID:    visitor.ID,
		CorpStaffBizID: visitor.ID,
	}
	if err := s.dao.SetSseClient(ctx, conn.ClientID, true); err != nil {
		return nil, pkg.ErrInternalServerError
	}
	reqByte, _ := jsoniter.Marshal(req)
	if err := eventbus.Push(ctx, conn, event.EventNodeDebug, reqByte); err != nil {
		return nil, pkg.ErrInternalServerError
	}
	return &message.Event{}, nil
}

func getNodeDialogRequest(ctx context.Context, ev *message.Event) (*event.NodeDialogDebugEvent, error) {
	req := event.NodeDialogDebugEvent{}
	payload := ev.GetPayload().GetStringValue()
	bs, err := base64.StdEncoding.DecodeString(payload)
	if err != nil {
		log.WarnContextf(ctx, "Decode sse payload error: %+v, payload: %+v", err, payload)
		return &req, pkg.ErrBadRequest
	}

	err = jsoniter.Unmarshal(bs, &req)
	clues.AddTrackE(ctx, "json.Unmarshal", &req, err)
	if err != nil {
		log.WarnContextf(ctx, "Unmarshal sse payload error: %+v, payload: %+v", err, string(bs))
		return &req, pkg.ErrBadRequest
	}
	if err = req.IsValid(); err != nil {
		return nil, err
	}
	return &req, nil
}

func getNodeDebugRequest(ctx context.Context, ev *message.Event) (*event.NodeDebugEvent, error) {
	req := event.NodeDebugEvent{}
	payload := ev.GetPayload().GetStringValue()
	bs, err := base64.StdEncoding.DecodeString(payload)
	if err != nil {
		log.WarnContextf(ctx, "Decode sse payload error: %+v, payload: %+v", err, payload)
		return &req, pkg.ErrBadRequest
	}

	err = jsoniter.Unmarshal(bs, &req)
	if err != nil {
		log.WarnContextf(ctx, "Unmarshal sse payload error: %+v, payload: %+v", err, string(bs))
		return &req, pkg.ErrBadRequest
	}
	if err = req.IsValid(); err != nil {
		return nil, err
	}
	return &req, nil
}
