package service

import (
	"context"
	"encoding/base64"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/message"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"git.woa.com/ivy/qbot/qbot/chat/pkg/rate"
	jsoniter "github.com/json-iterator/go"
)

// DocParseEventHandler 文档解析 SSE 事件处理方法
func (s *Service) DocParseEventHandler(ctx context.Context, ev *message.Event) (rev *message.Event, err error) {
	defer func() {
		success := helper.When(err == nil, "1", "0")
		metrics.ReportSseClientConnect(success)
	}()
	typ := ev.GetType()
	pkg.WithInterfaceType(ctx, "sse")
	if typ == message.EventTypeDisconnect {
		_ = s.dao.DelSseClient(ctx, ev.GetClientId())
		return &message.Event{}, nil
	}
	if typ != message.EventTypeConnect {
		return &message.Event{}, nil
	}
	payload := ev.GetPayload().GetStringValue()
	bs, err := base64.StdEncoding.DecodeString(payload)
	if err != nil {
		log.WarnContextf(ctx, "Decode sse payload error: %+v, payload: %+v", err, payload)
		return nil, pkg.ErrBadRequest
	}
	req := event.SseDocParseEvent{}
	if err := jsoniter.Unmarshal(bs, &req); err != nil {
		log.WarnContextf(ctx, "Unmarshal sse payload error: %+v, payload: %+v", err, string(bs))
		return nil, pkg.ErrBadRequest
	}
	if !req.IsValid() {
		return nil, pkg.ErrBadRequest
	} // 参数校验
	app := &model.App{}
	if strings.TrimSpace(req.BotAppKey) != "" {
		reached, err1 := rate.Increment(ctx, req.BotAppKey, 1) // 限频限流
		if err1 != nil || reached {
			log.WarnContextf(ctx, "botAppKey:%s SSE rate limit reached", req.BotAppKey)
			return nil, pkg.ErrAppRateLimiter
		}
		app, err = s.dao.GetAppByAppKey(ctx, dao.AppTestScene, req.BotAppKey)
	} else if strings.TrimSpace(req.ShareCode) != "" {
		reached, err2 := rate.Increment(ctx, req.ShareCode, 1) // 限频限流
		if err2 != nil || reached {
			log.WarnContextf(ctx, "botAppKey:%s SSE rate limit reached", req.BotAppKey)
			return nil, pkg.ErrAppRateLimiter
		}
		robotID, err3 := s.dao.GetRobotIDByShareCode(ctx, req.ShareCode)
		clues.AddTrackE(ctx, "dao.GetRobotIDByShareCode", robotID, err)
		log.DebugContextf(ctx, "DocParseEventHandler,robotId:%v", robotID)
		if err3 != nil {
			return nil, err
		}
		if robotID == 0 {
			return nil, pkg.ErrRobotNotExist
		}
		app, err = s.dao.GetAppByBizID(ctx, dao.AppReleaseScene, robotID)
	} else {
		return nil, pkg.ErrBadRequest
	}
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}
	for _, blacklistedID := range config.App().App.BlackList {
		if app.GetAppBizId() == blacklistedID {
			return nil, pkg.ErrAppIDInBlacklist
		}
	}
	conn := &model.Conn{
		IsSSE:       true,
		Type:        model.ConnTypeAPIVisitor,
		ClientID:    ev.GetClientId(),
		SessionID:   req.SessionID,
		APIBotBizID: app.GetAppBizId(),
	}
	if err := s.dao.SetSseClient(ctx, ev.GetClientId(), true); err != nil {
		return nil, pkg.ErrInternalServerError
	}
	if err := eventbus.Push(ctx, conn, event.EventSseDocParse, req); err != nil {
		return nil, pkg.ErrInternalServerError
	}
	return &message.Event{}, nil
}
