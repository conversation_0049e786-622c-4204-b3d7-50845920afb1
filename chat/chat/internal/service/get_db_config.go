package service

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// 获取DB中的静态配置表

// GetRejectAnswer 获取拒绝问答
func (s *Service) GetRejectAnswer(ctx context.Context) error {
	t0 := time.Now()
	log.InfoContextf(ctx, "GetRejectAnswer start: %s", t0.String())
	defer func() {
		log.InfoContextf(ctx, "GetRejectAnswer finished: %s", time.Since(t0).String())
	}()
	var err error
	model.Patterns, err = s.dao.GetRejectAnswer(trpc.CloneContext(ctx))
	if err != nil {
		return err
	}
	if len(model.Patterns) == 0 {
		log.DebugContextf(ctx, "GetRejectAnswer empty")
		return nil
	}
	return nil
}

// LoadPromptVersionFromDB 定时任务，获取prompt版本
func (s *Service) LoadPromptVersionFromDB(ctx context.Context) error {
	t0 := time.Now()
	log.InfoContextf(ctx, "LoadPromptVersionFromDB start: %s", t0.String())
	defer func() {
		log.InfoContextf(ctx, "LoadPromptVersionFromDB finished: %s", time.Since(t0).String())
	}()
	var err error
	model.PromptVersionMap, err = s.dao.GetPromptVersion(trpc.CloneContext(ctx))
	if err != nil {
		return err
	}
	if len(model.PromptVersionMap) == 0 {
		log.DebugContextf(ctx, "LoadPromptVersionFromDB empty")
		return nil
	}
	return nil
}

const (
	robotKeywordsSize     = 200
	robotKeywordsInterval = 60
)

// UpdateRobotKeywords 更新应用关键词
func (s *Service) UpdateRobotKeywords(ctx context.Context) error {
	t := time.Now()
	log.InfoContextf(ctx, "UpdateRobotKeywords.start")
	defer func() {
		log.InfoContextf(ctx, "UpdateRobotKeywords.end,cosTime:%dms", time.Since(t).Milliseconds())
	}()
	var robotID uint64
	for {
		keywords, err := s.dao.GetRobotKeywords(ctx, robotID, robotKeywordsInterval, robotKeywordsSize)
		if err != nil {
			log.WarnContextf(ctx, "UpdateRobotKeywords.GetRobotKeywords|err:%+v", err)
			return err
		}
		if len(keywords) == 0 {
			return nil
		}
		if err := s.dao.UpdateRobotKeywordsCache(ctx, keywords); err != nil {
			log.WarnContextf(ctx, "UpdateRobotKeywords.UpdateRobotKeywordsCache|err:%+v", err)
			return err
		}
		if len(keywords) < robotKeywordsSize {
			return nil
		}
		robotID = keywords[len(keywords)-1].RobotID
	}
}

// ClearMsgRecord 清理消息记录
func (s *Service) ClearMsgRecord(ctx context.Context) error {
	t := time.Now()
	clearNums := 0
	retentionDay := config.App().CleanDataConf.RetentionDay
	batchSize := config.App().CleanDataConf.SelectBatchSize
	retentionTime := time.Now().AddDate(0, 0, -retentionDay)
	fmt.Println(retentionTime)
	log.InfoContextf(ctx, "ClearMsgRecord.start retentionTime：%v", retentionTime)
	defer func() {
		log.InfoContextf(ctx, "ClearMsgRecord.end data:%s, cleanNum:%d, cosTime:%d ms", retentionTime,
			clearNums, time.Since(t).Milliseconds())
	}()
	for {
		// 分批获取保留天数以前的数据
		records, err := s.dao.GetMsgRecordToClean(ctx, retentionTime, batchSize)
		if err != nil {
			log.WarnContextf(ctx, "ClearMsgRecord.GetMsgRecordToClean|err:%+v", err)
			return err
		}
		// 没有数据，直接退出
		if len(records) == 0 {
			log.InfoContextf(ctx, "ClearMsgRecord.GetMsgRecordToClean|no data")
			return nil
		}
		clearNums += len(records)
		// 删除数据消息记录表
		if err := s.dao.DeleteMsgRecord(ctx, records); err != nil {
			log.WarnContextf(ctx, "ClearMsgRecord.DeleteMsgRecord|err:%+v", err)
			return err
		}
		// 删除数据消息记录表token_stat表
		if err := s.dao.DeleteMsgRecordTokenStat(ctx, records); err != nil {
			log.WarnContextf(ctx, "ClearMsgRecord.DeleteMsgRecordTokenStat|err:%+v", err)
			return err
		}
		// 清理间隔ms
		time.Sleep(time.Duration(config.App().CleanDataConf.CleanInterval) * time.Millisecond)
	}
}

// GetAgentToolConfig 获取所有 Agent 工具配置
func (s *Service) GetAgentToolConfig(ctx context.Context) error {
	t0 := time.Now()
	log.InfoContextf(ctx, "GetAgentToolConfig start: %s", t0.String())
	defer func() {
		log.InfoContextf(ctx, "GetAgentToolConfig finished: %s", time.Since(t0).String())
	}()

	var err error
	// 从 DAO 拉取配置列表
	model.AgentToolConfigs, err = s.dao.GetAgentToolConfig(trpc.CloneContext(ctx))
	if err != nil {
		return err
	}

	if len(model.AgentToolConfigs) == 0 {
		log.DebugContextf(ctx, "GetAgentToolConfig empty")
	}
	return nil
}
