package service

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// GetSharePageTempCredential 获取云API临时凭证
func (s *Service) GetSharePageTempCredential(ctx context.Context, req *pb.GetSharePageTempCredentialReq) (
	*pb.GetSharePageTempCredentialRsp, error) {
	t0 := time.Now()
	log.InfoContextf(ctx, "REQ|GetSharePageTmpCredential %+v", req)
	// 校验分享码
	if err := checkShareCode(ctx, req.GetShareCode()); err != nil {
		return nil, err
	}
	// 通过shareCode获取robotId
	robotID, err := s.getBotBizIDByShareCode(ctx, req.GetShareCode())
	if err != nil {
		return nil, err
	}
	// 获取token
	reqToken := pb.GetTempCredentialReq{BotBizId: robotID, Product: req.GetProduct(), Type: uint32(model.ConnTypeVisitor)}
	resp, err := s.GetTempCredential(ctx, &reqToken)
	if err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "RESP|GetSharePageTmpCredential %s", time.Since(t0))
	rsp := &pb.GetSharePageTempCredentialRsp{
		Credentials: resp.GetCredentials(),
		Expired:     resp.Expired,
	}
	return rsp, nil
}

// GetTempCredential 获取云API临时凭证
func (s *Service) GetTempCredential(ctx context.Context, req *pb.GetTempCredentialReq) (
	*pb.GetTempCredentialRsp, error) {
	t0 := time.Now()
	log.InfoContextf(ctx, "REQ|GetTempCredential %+v", req)
	// 检查参数
	product := model.Product(req.GetProduct())
	if product != model.ProductASR {
		log.WarnContextf(ctx, "[param invalid] GetTempCredential, Product:%s is invalid", product)
		return nil, pkg.ErrBadRequest
	}
	// 获取用户信息
	_, err := s.dao.CheckSession(ctx)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
			return nil, err
		}
		return nil, pkg.ErrInternalServerError
	}
	// 校验机器人
	_, err = s.getAppByBotBizID(ctx, req.GetBotBizId(), req.GetType())
	if err != nil {
		return nil, err
	}
	// 检查配置
	//if err = checkAppAudioConfig(ctx, req.GetBotBizId(), app); err != nil {
	//	return nil, err
	//}
	// 获取签名
	credential, err := s.dao.GetTencentCloudCredential(ctx, product)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	log.InfoContextf(ctx, "RESP|GetTempCredential %s", time.Since(t0))
	rsp := &pb.GetTempCredentialRsp{
		Credentials: &pb.Credentials{
			TmpSecretId:  credential.TmpSecretID,
			TmpSecretKey: credential.TmpSecretKey,
			Token:        credential.Token,
			AppId:        credential.AppID,
		},
		Expired: credential.Expired,
	}
	return rsp, nil
}
