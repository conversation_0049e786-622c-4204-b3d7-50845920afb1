// chat
//
// @(#)tag_sse.go  Tuesday, March 19, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package service

import (
	"context"
	"encoding/base64"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/message"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

// TagExperienceSSEEventHandler SSE 事件处理方法
func (s *Service) TagExperienceSSEEventHandler(ctx context.Context, ev *message.Event) (rev *message.Event, err error) {
	defer func() {
		success := helper.When(err == nil, "1", "0")
		metrics.ReportSseClientConnect(success)
	}()

	typ := ev.GetType()
	if typ == message.EventTypeDisconnect {
		_ = s.dao.DelSseClient(ctx, ev.GetClientId())
		return &message.Event{}, nil
	}
	if typ != message.EventTypeConnect {
		return &message.Event{}, nil
	}

	payload := ev.GetPayload().GetStringValue()
	bs, err := base64.StdEncoding.DecodeString(payload)
	if err != nil {
		log.WarnContextf(ctx, "Decode sse payload error: %+v, payload: %+v", err, payload)
		return nil, pkg.ErrBadRequest
	}

	req := &event.ExperienceSSETagExtractionEvent{}
	if err := jsoniter.Unmarshal(bs, req); err != nil {
		log.WarnContextf(ctx, "Unmarshal sse payload error: %+v, payload: %+v", err, string(bs))
		return nil, pkg.ErrBadRequest
	}
	if !req.IsValid() {
		return nil, pkg.ErrBadRequest
	}

	app, err := s.dao.GetAppByAppKey(ctx, dao.AppTestScene, req.BotAppKey)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}
	if reached, err := s.limitByApp(ctx, app, req.Content); err != nil || reached {
		log.InfoContextf(ctx, "botAppKey:%s SSE rate limit reached", req.BotAppKey)
		return nil, pkg.ErrAppRateLimiter
	}

	visitor, err := s.dao.MustGetVisitor(ctx, app.GetId(), app.GetAppBizId(), req.VisitorBizID)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}

	for _, l := range req.VisitorLabels {
		if !l.IsValid() {
			return nil, pkg.ErrBadRequest
		}
	}

	conn := &model.Conn{
		IsSSE:          true,
		Type:           model.ConnTypeAPIVisitor,
		ClientID:       ev.GetClientId(),
		APIBotBizID:    app.GetAppBizId(),
		CorpStaffID:    visitor.ID,
		CorpStaffBizID: visitor.ID,
		VisitorLabels:  req.VisitorLabels,
	}
	if err := s.dao.SetSseClient(ctx, conn.ClientID, true); err != nil {
		return nil, pkg.ErrInternalServerError
	}

	e0 := event.TagExtractionExperienceEvent{
		SessionID:         req.SessionID,
		RequestID:         req.RequestID,
		Content:           req.Content,
		StreamingThrottle: req.StreamingThrottle,
		ModelName:         req.ModelName,
		Tags:              req.Tags,
	}
	if err := eventbus.Push(ctx, conn, event.EventTagExtractionExperience, e0); err != nil {
		return nil, pkg.ErrInternalServerError
	}
	return &message.Event{}, nil
}

// TagSSEEventHandler SSE 事件处理方法
func (s *Service) TagSSEEventHandler(ctx context.Context, ev *message.Event) (rev *message.Event, err error) {
	defer func() {
		success := helper.When(err == nil, "1", "0")
		metrics.ReportSseClientConnect(success)
	}()

	typ := ev.GetType()
	if typ == message.EventTypeDisconnect {
		_ = s.dao.DelSseClient(ctx, ev.GetClientId())
		return &message.Event{}, nil
	}
	if typ != message.EventTypeConnect {
		return &message.Event{}, nil
	}

	payload := ev.GetPayload().GetStringValue()
	bs, err := base64.StdEncoding.DecodeString(payload)
	if err != nil {
		log.WarnContextf(ctx, "Decode sse payload error: %+v, payload: %+v", err, payload)
		return nil, pkg.ErrBadRequest
	}

	req := &event.SSETagExtractionEvent{}
	if err := jsoniter.Unmarshal(bs, req); err != nil {
		log.WarnContextf(ctx, "Unmarshal sse payload error: %+v, payload: %+v", err, string(bs))
		return nil, pkg.ErrBadRequest
	}
	if !req.IsValid() {
		return nil, pkg.ErrBadRequest
	}
	app, err := s.dao.GetAppByAppKey(ctx, dao.AppReleaseScene, req.BotAppKey)
	if err != nil {
		return nil, err
	}
	if app == nil {
		return nil, pkg.ErrRobotNotExist
	}
	if reached, err := s.limitByApp(ctx, app, req.Content); err != nil || reached {
		log.InfoContextf(ctx, "botAppKey:%s SSE rate limit reached", req.BotAppKey)
		return nil, pkg.ErrAppRateLimiter
	}

	visitor, err := s.dao.MustGetVisitor(ctx, app.GetId(), app.GetAppBizId(), req.VisitorBizID)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}

	if len(req.VisitorLabels) > config.GetLabelCount() {
		return nil, pkg.ErrInvalidVisitorLabel
	}
	labels, err := getTagSseLabels(req)
	if err != nil {
		return nil, err
	}
	conn := &model.Conn{
		IsSSE:          true,
		Type:           model.ConnTypeAPIVisitor,
		ClientID:       ev.GetClientId(),
		APIBotBizID:    app.GetAppBizId(),
		CorpStaffID:    visitor.ID,
		CorpStaffBizID: visitor.ID,
		VisitorLabels:  labels,
	}
	if err := s.dao.SetSseClient(ctx, conn.ClientID, true); err != nil {
		return nil, pkg.ErrInternalServerError
	}

	e0 := event.TagExtractionEvent{
		SessionID:         req.SessionID,
		RequestID:         req.RequestID,
		Content:           req.Content,
		StreamingThrottle: req.StreamingThrottle,
		ModelName:         req.ModelName,
		Tags:              req.Tags,
	}
	if err := eventbus.Push(ctx, conn, event.EventTagExtraction, e0); err != nil {
		return nil, pkg.ErrInternalServerError
	}
	return &message.Event{}, nil
}

func getTagSseLabels(req *event.SSETagExtractionEvent) ([]model.Label, error) {
	labels := make([]model.Label, 0, len(req.VisitorLabels))
	for _, l := range req.VisitorLabels {
		label := model.Label{
			Name:   strings.TrimSpace(l.Name),
			Values: helper.Map(l.Values, strings.TrimSpace),
		}
		if !label.IsValid() {
			return nil, pkg.ErrInvalidVisitorLabel
		}
		labels = append(labels, label)
	}
	return labels, nil
}
