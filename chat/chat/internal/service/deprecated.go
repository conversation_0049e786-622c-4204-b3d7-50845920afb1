package service

import (
	"context"
	"math/rand"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// Chat 聊天接口
func (s *Service) Chat(ctx context.Context, req *pb.ChatReq) (*pb.ChatRsp, error) {
	return &pb.ChatRsp{}, nil
}

// Summary 摘要和小结
func (s *Service) Summary(ctx context.Context, req *pb.SummaryReq) (*pb.SummaryRsp, error) {
	return &pb.SummaryRsp{}, nil
}

// EmitWsClient 推送事件给客户端
func (s *Service) EmitWsClient(ctx context.Context, req *pb.EmitWsClientReq) (*pb.EmitWsClientRsp, error) {
	// TODO implement me
	panic("implement me")
}

// EmitWsUser 推送事件给用户
func (s *Service) EmitWsUser(ctx context.Context, req *pb.EmitWsUserReq) (*pb.EmitWsUserRsp, error) {
	// TODO implement me
	panic("implement me")
}

// GetRecentContact 获取最近联系人
func (s *Service) GetRecentContact(ctx context.Context, req *pb.GetRecentContactReq) (*pb.GetRecentContactRsp, error) {
	u, err := s.dao.CheckSession(ctx)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
			return nil, err
		}
		return nil, pkg.ErrInternalServerError
	}

	sessions, err := s.dao.GetSessionsBySeat(ctx, u.ID)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}

	recordIDs := make([]string, 0, len(sessions))
	for _, v := range sessions {
		if v.IsTransferedToSeat() {
			recordIDs = append(recordIDs, v.LastRecordID)
		}
	}

	records, err := s.dao.GetMsgRecordsByRecordID(ctx, recordIDs, 0)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}

	bots, staffs, err := s.getFroms(ctx, sessions, dao.AppReleaseScene)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}

	data := make([]*pb.GetRecentContactRsp_Contact, 0, len(records))
	for _, r := range records {
		visitorID := r.GetVisitorID()
		data = append(data, &pb.GetRecentContactRsp_Contact{
			Avatar:     staffs[visitorID].Avatar,
			NickName:   staffs[visitorID].NickName,
			SessionId:  r.SessionID,
			LastRecord: s.getMsgRecordWithTokenStat(ctx, r, model.SourceTypeSeat, u.ID, bots, staffs),
		})
	}

	return &pb.GetRecentContactRsp{Contacts: data}, nil
}

// GetRecommendQuestion 获取推荐问题
func (s *Service) GetRecommendQuestion(
	ctx context.Context, req *pb.GetRecommendQuestionReq,
) (*pb.GetRecommendQuestionRsp, error) {
	_, err := s.dao.CheckSession(ctx)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
			return nil, err
		}
		return nil, pkg.ErrInternalServerError
	}

	all, err := s.dao.GetRecommendQuestion(ctx)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}

	c := len(all)
	if int(req.GetCount()) < c {
		c = int(req.GetCount())
	}

	seq := rand.Perm(len(all))
	selected := make([]string, 0, c)
	for i := 0; i < c; i++ {
		selected = append(selected, all[seq[i]].Question)
	}

	return &pb.GetRecommendQuestionRsp{Questions: selected}, nil
}

// GetTransferStatus 获取机器人转人工状态
func (s *Service) GetTransferStatus(
	ctx context.Context, req *pb.GetTransferStatusReq,
) (*pb.GetTransferStatusRsp, error) {
	u, err := s.dao.CheckSession(ctx)

	log.DebugContextf(ctx, "sid:%s|GetTransferStatus,u:%+v", pkg.SessionID(ctx), u)

	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
			return nil, err
		}
		return nil, pkg.ErrInternalServerError
	}

	session, err := s.dao.GetSession(ctx, model.SessionTypeAny, req.GetSessionId())
	log.DebugContextf(ctx, "sid:%s|GetTransferStatus,botsession:%+v", pkg.SessionID(ctx), session)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}

	log.DebugContextf(ctx, "sid:%s|GetTransferStatus,u:%+v", pkg.SessionID(ctx), u)
	if !session.IsVisitor(u.ID) {
		return nil, pkg.ErrSessionNotFound
	}

	var avatar string
	if session.IsTransfered {
		seat, err := s.dao.GetCorpStaffByBizID(ctx, session.SeatBizID)
		if err != nil {
			return nil, pkg.ErrInternalServerError
		}
		if seat == nil {
			return nil, pkg.ErrSeatNotExist
		}
		avatar = seat.Avatar
	}

	return &pb.GetTransferStatusRsp{Transfered: session.IsTransfered, Avatar: avatar}, nil
}

// GetSingleMsgRecord 获取单条聊天记录
func (s *Service) GetSingleMsgRecord(
	ctx context.Context, req *pb.GetSingleMsgRecordReq,
) (*pb.GetSingleMsgRecordRsp, error) {
	u, err := s.dao.CheckSession(ctx)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
			return nil, err
		}
		return nil, pkg.ErrInternalServerError
	}

	r, err := s.dao.GetMsgRecordByRecordID(ctx, req.GetRecordId(), 0)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}

	if r == nil || !(r.IsFrom(model.SourceTypeVisitor, u.ID) || r.IsFrom(model.SourceTypeExpVisitor, u.ID)) {
		return nil, pkg.ErrInvalidMsgRecord
	}

	return &pb.GetSingleMsgRecordRsp{
		Record: s.getMsgRecordWithTokenStat(ctx, *r, s.getVisitor(u), u.ID, nil, nil),
	}, nil
}
