package service

import (
	"context"
	"encoding/base64"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// GetSharePageTTSSignature 获取tts签名
func (s *Service) GetSharePageTTSSignature(ctx context.Context, req *pb.GetSharePageTTSSignatureReq) (
	*pb.GetSharePageTTSSignatureRsp, error) {
	t0 := time.Now()
	log.InfoContextf(ctx, "REQ|GetSharePageTTSSignature %+v", req)
	// 校验参数
	_, err := getTTSSignatureParam(ctx, req.GetRawQuery())
	if err != nil {
		return nil, err
	}
	// 校验分享码
	if err := checkShareCode(ctx, req.GetShareCode()); err != nil {
		return nil, err
	}
	// 通过shareCode获取robotId
	robotID, err := s.getBotBizIDByShareCode(ctx, req.GetShareCode())
	if err != nil {
		return nil, err
	}
	// 获取token
	reqToken := pb.GetTTSSignatureReq{BotBizId: robotID, RawQuery: req.GetRawQuery(),
		Type: uint32(model.ConnTypeVisitor)}
	resp, err := s.GetTTSSignature(ctx, &reqToken)
	if err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "RESP|GetSharePageTTSSignature %s %s", resp.GetSignature(), time.Since(t0))
	rsp := &pb.GetSharePageTTSSignatureRsp{
		Signature: resp.GetSignature(),
		AppId:     resp.AppId,
		SecretId:  resp.SecretId,
		Timestamp: resp.Timestamp,
		Expired:   resp.Expired,
	}
	return rsp, nil
}

// GetTTSSignature 获取tts签名
func (s *Service) GetTTSSignature(ctx context.Context, req *pb.GetTTSSignatureReq) (*pb.GetTTSSignatureRsp, error) {
	t0 := time.Now()
	log.InfoContextf(ctx, "REQ|GetTTSSignature %+v", req)
	// 校验参数
	params, err := getTTSSignatureParam(ctx, req.GetRawQuery())
	if err != nil {
		return nil, err
	}
	// 获取用户信息
	_, err = s.dao.CheckSession(ctx)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
			return nil, err
		}
		return nil, pkg.ErrInternalServerError
	}
	// 获取应用配置
	app, err := s.getAppByBotBizID(ctx, req.GetBotBizId(), req.GetType())
	if err != nil {
		return nil, err
	}
	// 检查TTS配置
	if err = checkAppAudioConfig(ctx, req.GetBotBizId(), app); err != nil {
		return nil, err
	}
	sign, err := s.dao.GenTTSSignature(ctx, params)
	if err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "RESP|GenTTSSignature %s %s", sign.Signature, time.Since(t0))
	rsp := &pb.GetTTSSignatureRsp{
		Signature: sign.Signature,
		AppId:     sign.AppID,
		SecretId:  sign.SecretID,
		Timestamp: sign.Timestamp,
		Expired:   sign.Expired,
	}
	return rsp, nil
}

func getTTSSignatureParam(ctx context.Context, rawQuery string) (map[string]string, error) {
	if len(rawQuery) <= 0 {
		log.WarnContextf(ctx, "[param invalid]rawQuery empty.")
		return nil, pkg.ErrBadRequest
	}
	bs, err := base64.StdEncoding.DecodeString(rawQuery)
	if err != nil {
		log.WarnContextf(ctx, "[param invalid]decode rawQuery failed. err:%v", err)
		return nil, pkg.ErrBadRequest
	}
	params, err := helper.ParseRawQuery(string(bs))
	if err != nil {
		log.WarnContextf(ctx, "[param invalid]parse rawQuery failed. err:%v", err)
		return nil, pkg.ErrBadRequest
	}
	if len(params) == 0 {
		log.WarnContextf(ctx, "[param invalid]parse rawQuery empty.")
		return nil, pkg.ErrBadRequest
	}
	if voiceType, ok := params["VoiceType"]; !ok {
		log.WarnContextf(ctx, "[param invalid]VoiceType empty.")
		return nil, pkg.ErrBadRequest
	} else if helper.GetUint64FromString(voiceType) == 0 {
		log.WarnContextf(ctx, "[param invalid]VoiceType illegal.")
		return nil, pkg.ErrBadRequest
	}
	if len(params["SessionId"]) == 0 {
		log.WarnContextf(ctx, "[param invalid]SessionId empty.")
		return params, pkg.ErrBadRequest
	}
	return params, nil
}

func checkAppAudioConfig(ctx context.Context, botBizID uint64, app *model.App) error {
	// 判断是否开启
	aiCall := app.GetKnowledgeQa().GetAiCall()
	if aiCall == nil || !aiCall.EnableVoiceInteract {
		log.WarnContextf(ctx, "checkAppAudioConfig| botBizID:%d not enable voice interact", botBizID)
		return pkg.ErrAudioInteractNotEnabled
	}
	// 检查声音参数
	if aiCall.Voice.VoiceType == 0 {
		log.WarnContextf(ctx, "checkAppAudioConfig| botBizID:%d not config voice", botBizID)
		return pkg.ErrRTCVoiceNotConfig
	}
	return nil
}
