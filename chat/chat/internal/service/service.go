// Package service 业务逻辑层
package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/goredis"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"github.com/LK4D4/trylock"
	"github.com/go-redis/redis/v8"
)

var (
	httpSvcOnce sync.Once
	httpSvc     *HTTPService
)

// Service is service logic object
type Service struct {
	dao dao.Dao
	mu  sync.Map
}

// StreamService is service logic object
type StreamService struct {
	dao dao.Dao
}

// HTTPService is service logic object
type HTTPService struct {
	dao dao.Dao
}

// New creates service instance
func New() *Service {
	return &Service{
		dao: dao.New(),
		mu:  sync.Map{},
	}
}

// NewStream creates service instance
func NewStream() *StreamService {
	return &StreamService{
		dao: dao.New(),
	}
}

// NewHTTP creates service instance
func NewHTTP() *HTTPService {
	httpSvcOnce.Do(func() {
		httpSvc = &HTTPService{
			dao: dao.New(),
		}
	})
	return httpSvc
}

func (s *Service) tryLock(key string) bool {
	lock := &trylock.Mutex{}
	value, ok := s.mu.LoadOrStore(key, lock)
	if ok {
		lock = value.(*trylock.Mutex)
	}
	if lock.TryLock() {
		log.Infof("lock key: %s", key)
		return true
	}
	log.Infof("key: %s locked", key)
	return false
}

func (s *Service) unlock(key string) {
	value, ok := s.mu.Load(key)
	if ok {
		log.Infof("unlock key: %s", key)
		s.mu.Delete(key)
		lock := value.(*trylock.Mutex)
		lock.Unlock()
	}
}

// Scheduler 调度策略
type Scheduler struct {
	redisClient redis.UniversalClient
}

// NewScheduler new
func NewScheduler() *Scheduler {
	r, err := goredis.New("redis.qbot.qbot", nil)
	ctx := trpc.BackgroundContext()
	if err != nil {
		log.ErrorContextf(ctx, "init Scheduler redis client failed.  err:%v", err)
	} else {
		log.InfoContextf(ctx, "init Scheduler goredis client sucess: %s", r.Ping(context.Background()).String())
	}
	return &Scheduler{
		redisClient: r,
	}
}

const (
	// RedisKey 前缀标识
	RedisKey = "lke:chat:scheduler"
)

// Schedule 互斥任务定时器； holeTime 默认1s
func (s *Scheduler) Schedule(serviceName string, newNode string, holdTime time.Duration) (nowNode string, err error) {
	ctx := context.TODO()
	key := RedisKey + ":" + serviceName

	res := s.redisClient.SetNX(ctx, key, newNode, holdTime)
	if res.Val() {
		return newNode, nil
	}
	return s.redisClient.Get(ctx, key).Val(), fmt.Errorf("locak failed")
}

func (s *Service) limitByApp(ctx context.Context, app *model.App, content string) (bool, error) {
	if !config.App().Limiter.Enable {
		return false, nil
	}
	// 默认读配置，主要依靠后续用户购买的tpm和qpm拦截
	limit := config.GetCorpReqLimit(app.GetCorpId())
	// 调redis获取当前正在处理的请求数
	timePeriod := config.App().Limiter.TimePeriod
	if timePeriod <= 0 {
		timePeriod = 1
	}
	key := fmt.Sprintf("limiter:chat:time:%d:%s", app.GetCorpId(), app.GetMainModelName())
	ok, current, err := s.dao.Limiter().LimitByTimePeriod(ctx, key, pkg.TraceID(ctx)+encode.GenerateUUID(),
		uint(limit), time.Duration(timePeriod)*time.Second)
	if err != nil { // 降级
		log.WarnContextf(ctx, "LimitByTimePeriod error: %+v", err)
		return false, nil // 不拦截
	}
	log.DebugContextf(ctx, "limitByApp| key: %s, current limit: %d, limit: %d", key, current, limit)
	// 判断是否超过限制，第一层限制加个缓冲
	if !ok {
		// 上报超并发数
		dosage := dao.OverConcurrencyDosage{
			AppID:     app.GetAppBizId(),
			ModelName: app.GetMainModelName(),
			UsageTime: time.Now(),
			Content:   content,
		}
		_ = s.dao.ReportOverConcurrencyDosage(ctx, app, dosage)
		log.WarnContextf(ctx, "limitByApp| limit: %d, %d", current, limit)
		return true, nil
	}
	return false, nil
}

// getSceneByEvent 获取事件场景
func (s *Service) getSceneByEvent(typ string) (dao.AppScene, bool, error) {
	switch typ {
	// 体验场景
	case event.EventExperience, event.EventKnowledgeSummaryExperience, event.EventTagExtractionExperience,
		event.EventSSeRoleSend:
		return dao.AppTestScene, true, nil
	// 发布场景
	case event.EventSseDocParse, event.EventRating,
		event.EventSend, event.EventKnowledgeSummary, event.EventTagExtraction, event.EventReconnect:
		return dao.AppReleaseScene, true, nil
	// 不需要限制
	case event.EventRecommendedTo, event.EventReferTo, event.EventStopGeneration:
		return 0, false, nil
	}
	// 未枚举的事件报错
	return 0, false, pkg.ErrEventHandlerNotExist
}
