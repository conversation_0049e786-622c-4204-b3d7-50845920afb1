package service

import (
	"context"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/message"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
)

// GetWsToken 获取 WS Token
func (s *Service) GetWsToken(ctx context.Context, req *pb.GetWsTokenReq) (*pb.GetWsTokenRsp, error) {
	ctx = clues.NewTrackContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddTrackData(ctx, "GetWsToken", req)
	t0 := time.Now()
	log.InfoContextf(ctx, "REQ|GetWsToken %+v", req)
	var conn *model.Conn
	var app *model.App
	var err error
	typ := model.ConnType(req.GetType())
	log.DebugContextf(ctx, "REQ|GetWsToken:typ:%v, GetBotAppKey:%v", typ, req.GetBotAppKey())
	var b int = 1
	var inputLenLimit = int32(dao.AppChatInputLimitDefault)
	if typ == model.ConnTypeAPIVisitor {
		conn, app, err = s.token4APIVisitor(ctx, req)
		if err != nil {
			return nil, err
		}
		b = s.getModelBalance(ctx, app) // 余量查询, 这个位置只下发余量, 不拦截
		reqLimit := &bot_admin_config_server.GetAppChatInputNumReq{
			AppBizId:  app.GetAppBizId(),
			ModelName: app.GetModelName(),
		}
		inputLenLimit = s.dao.GetAppChatInputLimit(ctx, reqLimit)
	} else {
		appID, _ := strconv.ParseUint(req.GetBotAppKey(), 10, 64)
		if appID == 0 { // 这里有个很奇葩的设计，如果非API模式，这里传的是AppID，而不是BotAppKey，先校验一下
			return nil, pkg.ErrRobotNotExist
		}
		u, err := s.dao.CheckSession(ctx) // 还需要校验用户和bot的关系
		clues.AddTrackE(ctx, "GetWsToken.dao.CheckSession", u, err)
		if err != nil {
			if errs.Code(err) == errs.Code(pkg.ErrUserNotLogin) {
				return nil, err
			}
			return nil, pkg.ErrInternalServerError
		}
		if len(req.GetBotAppKey()) > 0 { // 这里传的是 AppID
			app, err = s.dao.GetAppByBizID(ctx, dao.AppTestScene, appID)
			clues.AddTrackE(ctx, "dao.GetAppByBizID", clues.M{"appID": req.GetBotAppKey(), "app": app}, err)
			if err != nil || app == nil {
				return nil, pkg.ErrRobotNotExist
			}
			b = s.getModelBalance(ctx, app) // 余量查询, 这个位置只下发余量, 不拦截
		}
		conn = &model.Conn{
			Type: typ, CorpStaffID: u.ID, CorpStaffBizID: u.BusinessID, LoginUserType: u.UserType,
			APIBotBizID: appID,
		}
		pkg.WithStaffID(ctx, u.BusinessID)
	}
	token := uuid.NewString()
	singleWorkflowID := app.GetKnowledgeQa().GetWorkflowId()
	singleWorkflowInfo := &pb.KnowledgeQaSingleWorkflow{
		WorkflowId:   singleWorkflowID,
		WorkflowName: "",
		WorkflowDesc: "",
		Status:       "",
		IsEnable:     false,
	}
	if singleWorkflowID != "" {
		singleWorkflowInfo, _ = s.getWorkflowInfoByBizID(ctx, app.GetAppBizId(), singleWorkflowID)
	}
	err = s.dao.SaveWsToken(ctx, token, conn)
	clues.AddTrackE(ctx, "dao.SaveWsToken", clues.M{"token": token, "conn": conn}, err)
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	log.InfoContextf(ctx, "RESP|GetWsToken %s %s", token, time.Since(t0))
	tokenResp := &pb.GetWsTokenRsp{
		Token:          token,
		Balance:        float64(b),
		InputLenLimit:  inputLenLimit,
		SingleWorkflow: singleWorkflowInfo,
		Pattern:        app.GetKnowledgeQa().GetPattern(),
	}
	clues.AddT(ctx, "GetWsTokenRsp", tokenResp)
	return tokenResp, nil
}

func (s *Service) token4APIVisitor(ctx context.Context, req *pb.GetWsTokenReq) (*model.Conn, *model.App, error) {
	log.DebugContextf(ctx, "req.GetBotAppKey():%v", req.GetBotAppKey())
	if req.GetBotAppKey() == "" {
		log.WarnContextf(ctx, "[param invalid] BotAppKey is empty string")
		return nil, nil, pkg.ErrRobotNotExist
	}
	if !helper.CheckBotAppKey(req.GetBotAppKey(), false) {
		log.WarnContextf(ctx, "[param invalid] GetWsToken, BotAppKey:%s is invalid, type:%d",
			req.GetBotAppKey(), req.GetType())
	}
	app, err := s.dao.GetAppByAppKey(ctx, dao.AppReleaseScene, req.GetBotAppKey())
	if err != nil {
		return nil, nil, err
	}
	log.DebugContextf(ctx, "req.GetBotAppKey(),app:%v", app)
	if app == nil {
		return nil, nil, pkg.ErrRobotNotExist
	}
	visitor, err := s.dao.MustGetVisitor(ctx, app.GetId(), app.GetAppBizId(), req.GetVisitorBizId())
	if err != nil {
		return nil, nil, pkg.ErrInternalServerError
	}

	if len(req.GetVisitorLabels()) > config.GetLabelCount() {
		return nil, nil, pkg.ErrInvalidVisitorLabel
	}
	labels := make([]model.Label, 0, len(req.GetVisitorLabels()))
	for _, l := range req.GetVisitorLabels() {
		label := model.Label{
			Name:   strings.TrimSpace(l.GetName()),
			Values: helper.Map(l.GetValues(), strings.TrimSpace),
		}
		if !label.IsValid() {
			return nil, nil, pkg.ErrInvalidVisitorLabel
		}
		labels = append(labels, label)
	}
	conn := &model.Conn{
		Type:           model.ConnTypeAPIVisitor,
		APIBotBizID:    app.GetAppBizId(),
		CorpStaffID:    visitor.ID,
		CorpStaffBizID: visitor.ID,
		VisitorLabels:  labels,
	}
	return conn, app, nil
}

// WsEventHandler WS 事件处理方法 todo 函数太长了，需要拆分
func (s *Service) WsEventHandler(ctx context.Context, ev *message.Event) (*message.Event, error) {
	ctx = pf.NewPipelineFlowContext(ctx)
	pkg.WithMessageID(ctx, ev.GetMessageId())
	pkg.WithInterfaceType(ctx, "websocket")
	typ := ev.GetType()
	pf.StartElapsed(ctx, typ)
	// 新上报方式
	statistics := &pkg.PPLStatistics{
		PPLStartTime: time.Now(),
	}
	pkg.WithStatistics(ctx, statistics)
	if typ != message.EventTypeHeartbeat {
		log.InfoContextf(ctx, "REQ|WsEventHandler %+v", ev)
	}
	if typ == message.EventTypeConnect {
		return s.wsConnectEventHandler(ctx, ev)
	}
	if typ == message.EventTypeDisconnect {
		return s.wsDisconnectEventHandler(ctx, ev)
	}
	cli, err := s.dao.GetWsClient(ctx, ev.GetClientId())
	if typ != message.EventTypeHeartbeat {
		clues.AddTrackE(ctx, "dao.GetWsClient", cli, err)
	}
	if err != nil {
		return nil, pkg.ErrInternalServerError
	}
	// 除 connect, disconnect 外的其他事件只在鉴权后处理
	if cli == nil { // 这里是不是应该报错给Apex
		log.WarnContextf(ctx, "Client not exist, clientID: %s", ev.GetClientId())
		// _, err = s.wsCli.Close(ctx, &service.CloseReq{ClientId: ev.GetClientId()})
		return &message.Event{}, nil
	}
	if typ == message.EventTypeHeartbeat {
		_ = s.dao.RenewWsClient(ctx, cli.ClientID)
		return &message.Event{}, nil
	}
	scene, needLimit, err := s.getSceneByEvent(typ)
	if err != nil {
		return nil, err
	}
	bs, err := ev.GetPayload().GetStructValue().MarshalJSON()
	clues.AddTrackE(ctx, ev.GetType()+".Payload", ev.GetPayload().GetStructValue(), err)
	if err != nil {
		log.ErrorContextf(ctx, "Marshal ws payload error: %+v, payload: %+v", err, ev.GetPayload())
		return nil, pkg.ErrInternalServerError
	}
	log.DebugContextf(ctx, "REQ|WsEventHandler:cli:%v, typ:%v, payload is:%v", cli, typ, string(bs))
	if needLimit {
		app, err := s.dao.GetAppByBizID(ctx, scene, cli.APIBotBizID)
		if err != nil {
			return nil, err
		}
		if app == nil {
			return nil, pkg.ErrRobotNotExist
		}
		if reached, err := s.limitByApp(ctx, app, getWsEventContent(bs)); err != nil || reached {
			log.WarnContextf(ctx, "appID:%d SSE rate limit reached", cli.APIBotBizID)
			return nil, pkg.ErrAppRateLimiter
		}
	}
	if typ == event.EventExperience || typ == event.EventSend {
		req := event.DialogEvent{}
		_ = jsoniter.Unmarshal(bs, &req)
		if !req.IsValid(ctx) { // todo 事件总线里面的NewRequest没有被调用，待查。
			return nil, pkg.ErrBadRequest
		}
		if req.Incremental {
			dao.InitOutputCache(ctx, ev.GetClientId())
		}
		req.Environment = typ
		req.StartTime = time.Now()
		req.OriginContent = req.Content
		reqByte, _ := jsoniter.Marshal(req)
		if err := eventbus.Push(ctx, cli, event.EventDialog, reqByte); err != nil {
			return nil, err
		}
	} else { // 其他事件，比如rating、stop_generation、session_reset等
		if err := eventbus.Push(ctx, cli, typ, bs); err != nil {
			return nil, err
		}
	}
	return &message.Event{}, nil
}

// wsConnectEventHandler WS 连接事件处理方法
func (s *Service) wsConnectEventHandler(ctx context.Context, ev *message.Event) (rev *message.Event, err error) {
	defer func() {
		success := helper.When(err == nil, "1", "0")
		metrics.ReportClientConnect(success)
	}()
	auth := ev.GetHandshake().GetAuth().GetStructValue().AsMap()
	log.DebugContextf(ctx, ": %s", auth)
	token, ok := auth["token"].(string)
	if !ok || token == "" {
		log.WarnContextf(ctx, "Token not exist or empty")
		return nil, pkg.ErrAuthTokenFailed
	}
	if err := s.dao.AuthWsClient(ctx, token, ev.GetClientId()); err != nil {
		return nil, pkg.ErrAuthTokenFailed
	}
	return &message.Event{}, nil
}

// wsDisconnectEventHandler WS 断开连接事件处理方法
func (s *Service) wsDisconnectEventHandler(ctx context.Context, ev *message.Event) (rev *message.Event, err error) {
	defer func() {
		success := helper.When(err == nil, "1", "0")
		metrics.ReportClientDisconnect(success)
	}()
	if err := s.dao.DeleteWsClient(ctx, ev.GetClientId()); err != nil {
		return nil, pkg.ErrInternalServerError
	}
	return &message.Event{}, nil
}

// getWsEventContent TODO
func getWsEventContent(bs []byte) string {
	// EventContent 事件内容
	type EventContent struct {
		Content string `json:"content"`
	}
	var eventContent EventContent
	_ = jsoniter.Unmarshal(bs, &eventContent)
	return eventContent.Content
}
