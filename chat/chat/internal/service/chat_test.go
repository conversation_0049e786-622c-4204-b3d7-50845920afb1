// Package service ...
// @Author: halelv
// @Date: 2024/7/17 21:26
package service

import (
	"encoding/json"
	"fmt"
	"testing"

	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
)

func TestService_getMsgRecordWithTokenStat(t *testing.T) {
	var procedures []*pb.Procedure
	err := json.Unmarshal([]byte("[{\"name\":\"task_flow\",\"title\":\"调用任务流程\",\"status\":\"success\",\"input_count\":1613,\"output_count\":55,\"count\":1668,\"debugging\":{\"content\":\"氨基酸注射18AA-I的使用情况\",\"task_flow\":{\"intent_name\":\"病人信息查询\",\"updated_slot_values\":[{\"ID\":\"0b4ca79c-ba0b-4506-87b5-ca3dac6bdbaf\",\"Name\":\"药品名称\",\"ValueType\":1,\"ValueStr\":\"氨基酸注射18AA-I\"}],\"run_nodes\":[{\"NodeType\":2,\"NodeID\":\"d9cb5640-58da-1164-fcbc-e62130e3a012\",\"NodeName\":\"智能接口1\",\"InvokeAPI\":{\"Method\":\"GET\",\"URL\":\"http://www.baidu.com/\",\"QueryValues\":[{\"Name\":\"cls\"},{\"Name\":\"sign\"},{\"Name\":\"check\"},{\"Name\":\"drug\",\"Value\":\"氨基酸注射18AA-I\"}],\"ResponseBody\":\"\\u003c!DOCTYPE html\\u003e\\r\\n\\u003c!--STATUS OK--\\u003e\\u003chtml\\u003e \\u003chead\\u003e\\u003cmeta http-equiv=content-type content=text/html;charset=utf-8\\u003e\\u003cmeta http-equiv=X-UA-Compatible content=IE=Edge\\u003e\\u003cmeta content=always name=referrer\\u003e\\u003clink rel=stylesheet type=text/css href=http://s1.bdstatic.com/r/www/cache/bdorz/baidu.min.css\\u003e\\u003ctitle\\u003e百度一下，你就知道\\u003c/title\\u003e\\u003c/head\\u003e \\u003cbody link=#0000cc\\u003e \\u003cdiv id=wrapper\\u003e \\u003cdiv id=head\\u003e \\u003cdiv class=head_wrapper\\u003e \\u003cdiv class=s_form\\u003e \\u003cdiv class=s_form_wrapper\\u003e \\u003cdiv id=lg\\u003e \\u003cimg hidefocus=true src=//www.baidu.com/img/bd_logo1.png width=270 height=129\\u003e \\u003c/div\\u003e \\u003cform id=form name=f action=//www.baidu.com/s class=fm\\u003e \\u003cinput type=hidden name=bdorz_come value=1\\u003e \\u003cinput type=hidden name=ie value=utf-8\\u003e \\u003cinput type=hidden name=f value=8\\u003e \\u003cinput type=hidden name=rsv_bp value=1\\u003e \\u003cinput type=hidden name=rsv_idx value=1\\u003e \\u003cinput type=hidden name=tn value=baidu\\u003e\\u003cspan class=\\\"bg s_ipt_wr\\\"\\u003e\\u003cinput id=kw name=wd class=s_ipt value maxlength=255 autocomplete=off autofocus\\u003e\\u003c/span\\u003e\\u003cspan class=\\\"bg s_btn_wr\\\"\\u003e\\u003cinput type=submit id=su value=百度一下 class=\\\"bg s_btn\\\"\\u003e\\u003c/span\\u003e \\u003c/form\\u003e \\u003c/div\\u003e \\u003c/div\\u003e \\u003cdiv id=u1\\u003e \\u003ca href=http://news.baidu.com name=tj_trnews class=mnav\\u003e新闻\\u003c/a\\u003e \\u003ca href=http://www.hao123.com name=tj_trhao123 class=mnav\\u003ehao123\\u003c/a\\u003e \\u003ca href=http://map.baidu.com name=tj_trmap class=mnav\\u003e地图\\u003c/a\\u003e \\u003ca href=http://v.baidu.com name=tj_trvideo class=mnav\\u003e视频\\u003c/a\\u003e \\u003ca href=http://tieba.baidu.com name=tj_trtieba class=mnav\\u003e贴吧\\u003c/a\\u003e \\u003cnoscript\\u003e \\u003ca href=http://www.baidu.com/bdorz/login.gif?login\\u0026amp;tpl=mn\\u0026amp;u=http%3A%2F%2Fwww.baidu.com%2f%3fbdorz_come%3d1 name=tj_login class=lb\\u003e登录\\u003c/a\\u003e \\u003c/noscript\\u003e \\u003cscript\\u003edocument.write('\\u003ca href=\\\"http://www.baidu.com/bdorz/login.gif?login\\u0026tpl=mn\\u0026u='+ encodeURIComponent(window.location.href+ (window.location.search === \\\"\\\" ? \\\"?\\\" : \\\"\\u0026\\\")+ \\\"bdorz_come=1\\\")+ '\\\" name=\\\"tj_login\\\" class=\\\"lb\\\"\\u003e登录\\u003c/a\\u003e');\\u003c/script\\u003e \\u003ca href=//www.baidu.com/more/ name=tj_briicon class=bri style=\\\"display: block;\\\"\\u003e更多产品\\u003c/a\\u003e \\u003c/div\\u003e \\u003c/div\\u003e \\u003c/div\\u003e \\u003cdiv id=ftCon\\u003e \\u003cdiv id=ftConw\\u003e \\u003cp id=lh\\u003e \\u003ca href=http://home.baidu.com\\u003e关于百度\\u003c/a\\u003e \\u003ca href=http://ir.baidu.com\\u003eAbout Baidu\\u003c/a\\u003e \\u003c/p\\u003e \\u003cp id=cp\\u003e\\u0026copy;2017\\u0026nbsp;Baidu\\u0026nbsp;\\u003ca href=http://www.baidu.com/duty/\\u003e使用百度前必读\\u003c/a\\u003e\\u0026nbsp; \\u003ca href=http://jianyi.baidu.com/ class=cp-feedback\\u003e意见反馈\\u003c/a\\u003e\\u0026nbsp;京ICP证030173号\\u0026nbsp; \\u003cimg src=//www.baidu.com/img/gs.gif\\u003e \\u003c/p\\u003e \\u003c/div\\u003e \\u003c/div\\u003e \\u003c/div\\u003e \\u003c/body\\u003e \\u003c/html\\u003e\\r\\n\",\"ResponseValues\":[{\"ID\":\"e3c2e75f-1068-14f6-7f30-b608548706ff\",\"Name\":\"test\",\"ValueType\":1,\"ValueStr\":\"null\"}]},\"SlotValues\":[{\"ID\":\"0b4ca79c-ba0b-4506-87b5-ca3dac6bdbaf\",\"Name\":\"药品名称\",\"ValueType\":1,\"ValueStr\":\"氨基酸注射18AA-I\"},{\"ID\":\"96d1efb5-ea47-4e69-b86f-3facd11406c3\",\"Name\":\"药品类别或治疗方式\"},{\"ID\":\"a96b042b-021b-4934-b3db-bd1a5b7fe8f2\",\"Name\":\"指标\"},{\"ID\":\"f4949e3d-fad3-4338-b5dd-5a70e89670ec\",\"Name\":\"医学检查\"}]},{\"NodeType\":4,\"NodeID\":\"b75c58db-815b-fd53-6e22-ca704c7ed15b\",\"NodeName\":\"结束回复1\"}]}},\"resource_status\":0}]"), &procedures)
	if err != nil {
		fmt.Printf("getMsgRecordWithTokenStat|Procedure.json.Unmarshal err:%+v\n", err)
	}
	fmt.Printf("procedures:%+v", procedures)
}
