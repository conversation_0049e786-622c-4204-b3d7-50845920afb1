package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	commonErrors "git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/go-comm/utils"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

// RTCEventHandle 处理 ai 对话 回调通知
func RTCEventHandle(w http.ResponseWriter, r *http.Request) (err error) {
	ctx := trpc.BackgroundContext()
	t0 := time.Now()
	defer func() {
		commonErrors.PanicHandler()
		writeRTCResponse(ctx, w, err)
		log.InfoContextf(ctx, "RTCEventHandle| end handle, cost:%s", time.Since(t0))
	}()
	// 检查回调是否开启
	if !config.App().RTC.Callback.Enable {
		log.WarnContextf(ctx, "RTCEventHandle| event not enable")
		return nil
	}
	// 获取请求报文头中的数据
	sign := r.Header.Get("Sign")
	sdkAppID := r.Header.Get("SdkAppId")
	if sign == "" || sdkAppID == "" {
		log.WarnContextf(ctx, "RTCEventHandle| reader header error, sign:%s, sdkAppId:%s", sign, sdkAppID)
		return pkg.ErrBadRequest
	}
	// 获取请求报文中的数据
	body, err := io.ReadAll(r.Body)
	if err != nil {
		log.WarnContextf(ctx, "RTCEventHandle| read body error:%s", err)
		return pkg.ErrBadRequest
	}
	// 检查回调签名
	if err = checkRTCEventSign(ctx, body, sign); err != nil {
		return err
	}
	// 打印请求报文
	if config.App().RTC.Callback.EnableDebug {
		log.InfoContextf(ctx, "RTCEventHandle| read body success, sign:%s, sdkAppId:%s, body:%s", sign, sdkAppID,
			string(body))
	}
	// 解析请求报文
	event := &model.RTCEventRequest{}
	if err = jsoniter.Unmarshal(body, &event); err != nil {
		log.WarnContextf(ctx, "RTCEventHandle| unmarshal body error:%s, body:%s", err, string(body))
		return pkg.ErrBadRequest
	}
	// 处理事件
	handleRTCEvent(ctx, event)
	return nil
}

// checkRTCEventSign 检查回调签名
func checkRTCEventSign(ctx context.Context, data []byte, sign string) error {
	key := config.App().TRTC.CallbackKey
	calSign := genRTCEventSign(data, key)
	if calSign != sign {
		log.WarnContextf(ctx, "RTCEventHandle| signature invalid, body:%s sign:%s, genSign:%s",
			string(data), sign, calSign)
		return pkg.ErrBadRequest
	}
	return nil
}

// genRTCEventSign 计算回调签名
func genRTCEventSign(data []byte, key string) string {
	h := hmac.New(sha256.New, []byte(key))
	h.Write(data)
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

// writeRTCResponse 返回 ai 对话 回调通知
func writeRTCResponse(ctx context.Context, w http.ResponseWriter, bizErr error) {
	// 注意：使用 ResponseWriter 回包时，Set/WriteHeader/Write 这三个方法必须严格按照以下顺序调用
	w.Header().Set("Content-type", "application/json")
	// 为响应报文设置 HTTP 状态码
	if errors.Is(bizErr, pkg.ErrBadRequest) {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	w.WriteHeader(http.StatusOK)
	// 为响应报文设置 BodyF
	resp := &model.RTCEventResponse{Code: 0, Message: "success"}
	if bizErr != nil {
		resp.Code = 500
		resp.Message = "Internal Server Error"
	}
	// 返回响应报文
	rsp, _ := jsoniter.Marshal(resp)
	_, err := w.Write(rsp)
	log.InfoContextf(ctx, "RTCEventHandle| return response, rsp:%s, err:%v", string(rsp), err)
}

// handleRTCEvent 事件处理方法
func handleRTCEvent(ctx context.Context, event *model.RTCEventRequest) {
	eventType := model.RTCEventType(event.EventType)
	switch eventType {
	case model.EventTypeAiServiceStart:
		_ = rtcStartEventHandler(ctx, eventType, event.EventInfo)
		break
	case model.EventTypeAiServiceStop:
		_ = rtcStopEventHandler(ctx, eventType, event.EventInfo)
		break
	case model.EventTypeAiStartOfSpeech:
		_ = rtcSpeechStartEventHandler(ctx, eventType, event.EventInfo)
	case model.EventTypeAiErrorMetricCallback:
		_ = rtcErrorEventHandler(ctx, eventType, event.EventInfo)
	case model.EventTypeAiMetricMessage:
		_ = rtcMetricEventHandler(ctx, eventType, event.EventInfo)
	default:
		log.WarnContextf(ctx, "RTCEventHandle| event type ignore, eventType:%d", event.EventType)
	}
}

// rtcStartEventHandler 开始事件处理方法
func rtcStartEventHandler(ctx context.Context, eventType model.RTCEventType, ev model.RTCEventInfo) error {
	log.InfoContextf(ctx, "RTCEventHandle[%d]| start handle, taskID:%s, payload:%+v", eventType, ev.TaskID,
		utils.ToJsonString(ev.Payload))
	cli, success := &model.RTCClient{}, "1"
	defer func() {
		labelMap := buildRTCMetricLabelMap(ev.TaskID, cli)
		labelMap["success"] = success
		metrics.ReportRTCStart(labelMap)
	}()
	// 解析负载信息
	payload := &model.RTCStartEventPayload{}
	err := parsePayload(ctx, eventType, ev.Payload, payload)
	if err != nil {
		return err
	}
	// 任务是否开启成功
	startSuccess := payload.Status == 0
	if startSuccess {
		success = "1"
		log.InfoContextf(ctx, "RTCEventHandle[%d]| task start success, taskID:%s", eventType, ev.TaskID)
	} else {
		success = "0"
		log.WarnContextf(ctx, "RTCEventHandle[%d]| task start fail, taskID:%s", eventType, ev.TaskID)
	}
	// 获取任务信息
	task, err := httpSvc.dao.GetRTCTask(ctx, ev.TaskID)
	if err != nil {
		log.WarnContextf(ctx, "RTCEventHandle[%d]| get task error:%s, taskID:%s", eventType, err, ev.TaskID)
		return err
	} else if task == nil {
		log.WarnContextf(ctx, "RTCEventHandle[%d]| get task empty, taskID:%s", eventType, ev.TaskID)
		return nil
	}
	log.InfoContextf(ctx, "RTCEventHandle[%d]| get task success, taskID:%s, task:%v", eventType, ev.TaskID, task)
	// 生成客户端信息
	cli = &model.RTCClient{
		Token:     task.Token,
		SdkAppID:  task.SdkAppID,
		RoomID:    task.RoomID,
		RTCType:   task.RTCType,
		ConnType:  task.ConnType,
		CorpID:    task.CorpID,
		BotBizID:  task.BotBizID,
		Timestamp: uint64(time.Now().Unix()),
	}
	// 任务成功则进行续期，
	if startSuccess {
		return httpSvc.dao.SaveRTCClient(ctx, ev.TaskID, cli)
	}
	// 开启失败则删除任务
	return httpSvc.dao.ClearRTCTask(ctx, ev.TaskID)
}

// rtcStopEventHandler 关闭事件处理方法
func rtcStopEventHandler(ctx context.Context, eventType model.RTCEventType, ev model.RTCEventInfo) error {
	log.InfoContextf(ctx, "RTCEventHandle[%d]| start handle, taskID:%s, payload:%+v", eventType, ev.TaskID,
		utils.ToJsonString(ev.Payload))
	cli, leaveCode := &model.RTCClient{}, 0
	defer func() {
		labelMap := buildRTCMetricLabelMap(ev.TaskID, cli)
		labelMap["leave_code"] = fmt.Sprintf("%d", leaveCode)
		metrics.ReportRTCStop(labelMap)
	}()
	// 解析负载信息
	payload := &model.RTCStopEventPayload{}
	err := parsePayload(ctx, eventType, ev.Payload, payload)
	if err != nil {
		log.WarnContextf(ctx, "RTCEventHandle[%d]| parse payload error:%s, taskID:%s", eventType, err, ev.TaskID)
		return err
	}
	// 任务结束原因
	leaveCode = payload.LeaveCode
	if leaveCode == 0 {
		log.InfoContextf(ctx, "RTCEventHandle[%d]| task stop normal, taskID:%s", eventType, ev.TaskID)
	} else {
		log.WarnContextf(ctx, "RTCEventHandle[%d]| task stop abnormal, leaveCode:%d, taskID:%s", eventType,
			leaveCode, ev.TaskID)
	}
	// 清理任务
	err = httpSvc.dao.ClearRTCTask(ctx, ev.TaskID)
	// 获取客户端用于上报
	cli, _ = httpSvc.dao.GetRTCClient(ctx, ev.TaskID)
	// 清理客户端
	_ = httpSvc.dao.ClearRTCClient(ctx, ev.TaskID)
	return err
}

// rtcSpeechStartEventHandler ASR识别开始事件处理方法
func rtcSpeechStartEventHandler(ctx context.Context, eventType model.RTCEventType, ev model.RTCEventInfo) error {
	log.InfoContextf(ctx, "RTCEventHandle[%d]| start handle, taskID:%s, payload:%+v", eventType, ev.TaskID,
		utils.ToJsonString(ev.Payload))
	// 续期任务
	_ = httpSvc.dao.RenewRTCTask(ctx, ev.TaskID)
	// 续期房间
	_ = httpSvc.dao.RenewRoomID(ctx, ev.RoomID)
	// 续期客户端
	_ = httpSvc.dao.RenewRTCClient(ctx, ev.TaskID)
	return nil
}

// rtcErrorEventHandler 错误事件处理方法
func rtcErrorEventHandler(ctx context.Context, eventType model.RTCEventType, ev model.RTCEventInfo) error {
	log.InfoContextf(ctx, "RTCEventHandle[%d]| start handle, taskID:%s, payload:%+v", eventType, ev.TaskID,
		utils.ToJsonString(ev.Payload))
	// 解析负载信息
	payload := &model.RTCErrorEventPayload{}
	err := parsePayload(ctx, eventType, ev.Payload, payload)
	if err != nil {
		log.WarnContextf(ctx, "RTCEventHandle[%d]| parse payload error:%s, taskID:%s", eventType, err, ev.TaskID)
		return err
	}
	// 清理任务
	errorCode := fmt.Sprintf("%s_%d", payload.Metric, payload.Tag.Code)
	isNeedClear := config.App().RTC.Callback.PanicErrorMap[errorCode]
	if isNeedClear {
		_ = httpSvc.dao.ClearRTCTask(ctx, ev.TaskID)
	} else {
		log.DebugContextf(ctx, "RTCEventHandle[%d]| not need handle, errorCode:%s, taskID:%s", eventType,
			errorCode, ev.TaskID)
	}
	// 获取客户端用于上报
	cli, _ := httpSvc.dao.GetRTCClient(ctx, ev.TaskID)
	labelMap := buildRTCMetricLabelMap(ev.TaskID, cli)
	labelMap["err_type"] = payload.Metric
	labelMap["err_code"] = fmt.Sprintf("%d", payload.Tag.Code)
	metrics.ReportRTCError(labelMap)
	return nil
}

// rtcMetricEventHandler 指标事件处理方法
func rtcMetricEventHandler(ctx context.Context, eventType model.RTCEventType, ev model.RTCEventInfo) error {
	log.InfoContextf(ctx, "RTCEventHandle[%d]| start handle, taskID:%s, payload:%+v", eventType, ev.TaskID,
		utils.ToJsonString(ev.Payload))
	// 解析负载信息
	payload := &model.RTCMetricEventPayload{}
	err := parsePayload(ctx, eventType, ev.Payload, payload)
	if err != nil {
		log.WarnContextf(ctx, "RTCEventHandle[%d]| parse payload error:%s, taskID:%s", eventType, err, ev.TaskID)
		return err
	}
	// 不支持的指标
	metricType := config.App().RTC.Callback.ReportMetricMap[payload.Metric]
	if metricType == "" {
		if config.App().RTC.Callback.EnableDebug {
			log.InfoContextf(ctx, "RTCEventHandle[%d]| not need report, metric:%s, value:%d, taskID:%s",
				eventType, payload.Metric, payload.Value, ev.TaskID)
		} else {
			log.DebugContextf(ctx, "RTCEventHandle[%d]| not need report, metric:%s, value:%d, taskID:%s",
				eventType, payload.Metric, payload.Value, ev.TaskID)
		}
		return nil
	}
	// 是否带全维度上报指标
	cli := &model.RTCClient{}
	if config.App().RTC.Callback.EnableMetricLabel {
		cli, _ = httpSvc.dao.GetRTCClient(ctx, ev.TaskID)
	}
	labelMap := buildRTCMetricLabelMap(ev.TaskID, cli)
	// 上报指标
	if metricType == "histogram" && payload.Value > 0 {
		metrics.ReportRTCHistogramMetric(payload.Metric, payload.Value, labelMap)
	} else if metricType == "sum" {
		metrics.ReportRTCSumMetric(payload.Metric, labelMap)
	}
	return err
}

// parsePayload 解析负载信息
func parsePayload(ctx context.Context, eventType model.RTCEventType, payload interface{}, result interface{}) error {
	jsonStr, ok := payload.(string)
	if !ok {
		var err error
		jsonStr, err = jsoniter.MarshalToString(payload)
		if err != nil {
			log.WarnContextf(ctx, "RTCEventHandle[%d]| marshal payload error: %v", eventType, err)
			return fmt.Errorf("marshal payload error: %v", err)
		}
	}
	if err := jsoniter.UnmarshalFromString(jsonStr, result); err != nil {
		log.WarnContextf(ctx, "RTCEventHandle[%d]| unmarshal payload error: %v", eventType, err)
		return fmt.Errorf("unmarshal payload error: %v", err)
	}
	return nil
}

func buildRTCMetricLabelMap(taskID string, cli *model.RTCClient) map[string]string {
	labelMap := make(map[string]string)
	labelMap["task_id"] = taskID
	if cli == nil {
		cli = &model.RTCClient{}
	}
	labelMap["type"] = cli.RTCType
	labelMap["app_id"] = fmt.Sprintf("%d", cli.BotBizID)
	labelMap["corp_id"] = fmt.Sprintf("%d", cli.CorpID)
	labelMap["conn_type"] = fmt.Sprintf("%d", cli.ConnType)
	return labelMap
}
