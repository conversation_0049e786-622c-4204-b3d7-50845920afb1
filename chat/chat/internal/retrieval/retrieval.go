// Package retrieval 检索
package retrieval

import (
	"context"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// ChatRetrievalAPI 检索API
type ChatRetrievalAPI interface {
	BasicSearch(ctx context.Context, bs *botsession.BotSession) bool
	KnowledgeSearch(context.Context, *botsession.BotSession) error
	SearchQa(ctx context.Context, bs *botsession.BotSession) []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc
	SearchRealtime(ctx context.Context, bs *botsession.BotSession, fileInfos []*model.FileInfo,
		modelLength int64) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error)
	SearchMultiDoc(ctx context.Context, bs *botsession.BotSession,
		fileInfos []*model.FileInfo) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error)
	GetDocFullText(ctx context.Context, bs *botsession.BotSession, fileInfo model.FileInfo,
		modelLength int64) (*model.FileInfo, error)
	WorkflowSearch(context.Context, *botsession.BotSession) error
}

// Retrieval 检索
type Retrieval struct {
	dao dao.Dao
}

var (
	search ChatRetrievalAPI
)

func init() {
	search = &Retrieval{
		dao: dao.New(),
	}
}

// New .
func New() ChatRetrievalAPI {
	return search
}
