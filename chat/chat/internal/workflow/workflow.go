// Package workflow TODO
package workflow

import (
	"context"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/memory"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
)

// ChatWorkflowAPI 工作流API
type ChatWorkflowAPI interface {
	// RetrieveWorkflows 检索工作流topN
	RetrieveWorkflows(context.Context, KEP_WF_DM.RunEnvType, uint64, string,
		string) (*KEP_WF_DM.RetrieveWorkflowsReply, error)
	// WorkflowReply 工作流回复
	WorkflowReply(ctx context.Context, bs *botsession.BotSession,
		hitIntent model.CandidateIntent, isDebug bool) (err error)
	// ThoughtEventStopReply 思考事件停止回复
	ThoughtEventStopReply(ctx context.Context, bs *botsession.BotSession, cancel context.CancelFunc)
	// CreateQueryMsgRecord 创建查询消息记录
	CreateQueryMsgRecord(ctx context.Context, bs *botsession.BotSession) error
	// ProcessImageAndFileCount 处理图片和文件数量
	ProcessImageAndFileCount(ctx context.Context, bs *botsession.BotSession)
	// GetSession 获取会话
	GetSession(ctx context.Context, bs *botsession.BotSession) (err error)
	// SendTokenStat 发送token统计
	SendTokenStat(ctx context.Context, clientID string, tokenStat *event.TokenStatEvent)
	// ThoughtEventReply 思考事件回复
	ThoughtEventReply(ctx context.Context, bs *botsession.BotSession, throttleStreaming []helper.Throttle,
		replyList []*KEP_WF_DM.ThoughtInfo, cancel context.CancelFunc)
	// ReplyFromSelf 首包回复自身信息
	ReplyFromSelf(ctx context.Context, bs *botsession.BotSession)
}

// WorkFlow 工作流
type WorkFlow struct {
	dao    dao.Dao
	memory memory.ChatMemoryAPI
}

var (
	workflow ChatWorkflowAPI
)

func init() {
	workflow = &WorkFlow{
		dao:    dao.New(),
		memory: memory.New(),
	}
}

// New .
func New() ChatWorkflowAPI {
	return workflow
}
