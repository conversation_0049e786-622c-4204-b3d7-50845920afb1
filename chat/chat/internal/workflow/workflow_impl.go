package workflow

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	errors2 "git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	jsoniter "github.com/json-iterator/go"
)

// RetrieveWorkflows 检索topN工作流
func (w *WorkFlow) RetrieveWorkflows(ctx context.Context, envType KEP_WF_DM.RunEnvType,
	appID uint64, rewriteQuery, recordID string) (*KEP_WF_DM.RetrieveWorkflowsReply, error) {
	workflowResult, err := w.dao.RetrieveWorkflows(ctx, envType, appID, rewriteQuery)
	if err != nil {
		return workflowResult, err
	}
	go w.dao.UpdateWorkflowResult(ctx, recordID, helper.Object2StringEscapeHTML(workflowResult))
	return workflowResult, nil
}

// WorkflowReply 工作流回复
func (w *WorkFlow) WorkflowReply(ctx context.Context, bs *botsession.BotSession,
	hitIntent model.CandidateIntent, isDebug bool) (err error) {
	if !bs.App.GetKnowledgeQa().GetWorkflow().GetIsEnabled() {
		return pkg.ErrWorkflowDisable
	}
	// 更新气泡信息 这里是不是要改为工作流的气泡信息？？todo
	_ = w.dao.SetWorkflowStatus(ctx, bs.App.GetAppBizId(), bs.SessionID, true)
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedureWorkflow))
	w.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
	// 封装请求
	req := w.NewWorkflowRequest(ctx, bs, hitIntent.WorkflowID, isDebug)
	clues.AddTrackData(ctx, "WorkflowReply.NewWorkflowRequest", req)
	// 发起请求
	cfg := config.App().Bot
	ch := make(chan *KEP_WF_DM.RunWorkflowReply, cfg.ResponseChannelSize)
	g, gCtx := errgroupx.WithContext(ctx)
	dmCtx, cancel := context.WithCancel(ctx)
	signal := make(chan int, 10)
	pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.Workflow)
	g.Go(func() error {
		return w.dao.RunWorkflow(dmCtx, req, ch, signal) // 调用DM
	})
	g.Go(func() error {
		last, isTimeout, err := w.workflowStreamDisplay(gCtx, cancel, bs, ch)
		if last != nil && !last.GetIsFinal() {
			last.IsFinal = true

			if NeedReplyThoughtEvent(ctx, bs) { // 下发停止思考事件
				w.ThoughtEventStopReply(ctx, bs, cancel)
			}

			re := bs.NewWorkflowReplyEvent(last, false, model.ReplyMethodWorkflowAnswer, nil)
			_ = w.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
		}
		return helper.When(isTimeout, pkg.ErrLLMTimeout, err)
	})
	if err = g.Wait(); err != nil {
		return err
	}
	return nil
}

// ThoughtEventStopReply 思维链事件终止回复
func (w *WorkFlow) ThoughtEventStopReply(ctx context.Context, bs *botsession.BotSession, cancel context.CancelFunc) {
	bs.Thought.Elapsed = 0
	if len(bs.Thought.Procedures) == 0 {
		log.WarnContextf(ctx, "thoughtEventReply: procedures empty")
		return
	}
	for i := 0; i < len(bs.Thought.Procedures); i++ {
		if bs.Thought.Procedures[i].Status != event.ProcedureStatusProcessing {
			continue
		}
		cost := uint32(time.Since(bs.Thought.Procedures[i].StartTime).Milliseconds())
		bs.Thought.Procedures[i].Elapsed = cost
		bs.Thought.Procedures[i].Status = event.ProcedureStatusStop
		bs.Thought.Procedures[i].Title = config.App().ThoughtConf.StatusTitle[string(event.ProcedureStatusStop)]
		_ = w.dao.DoEmitWsClient(ctx, bs.To.ClientID, bs.Thought, cancel)
		bs.Thought.Elapsed += bs.Thought.Procedures[i].Elapsed
	}
}

// NewWorkflowRequest 构造请求，请求工作流
func (w *WorkFlow) NewWorkflowRequest(ctx context.Context,
	bs *botsession.BotSession, workflowID string, isDebug bool) *KEP_WF_DM.RunWorkflowRequest {
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, bs.ModelType)
	clues.AddTrackData(ctx, "workflow.App.GetModel", map[string]any{
		"appType": model.AppTypeKnowledgeQA, "ModelType": bs.ModelType, "m": m,
	})
	histories := w.makeWorkflowHistories(ctx, bs, int(config.App().Bot.HistoryLimit))
	log.DebugContextf(ctx, "histories :%v", histories)
	// 组装历史消息
	var messages []*KEP_WF_DM.Message
	for _, pair := range model.TruncateHistories(histories, int(m.GetPromptWordsLimit())) { // todo 限制交给DM去做？
		messages = append(messages,
			&KEP_WF_DM.Message{Role: KEP_WF_DM.Role_USER, Content: pair[0].Content, RecordID: pair[0].RecordID},
			&KEP_WF_DM.Message{Role: KEP_WF_DM.Role_ASSISTANT, Content: pair[1].Content, RecordID: pair[1].RecordID},
		)
	}
	// 生成请求体
	log.DebugContextf(ctx, "DM request history: %+v", messages)
	systemVariables := make(map[string]*KEP_WF_DM.Variable, 0)
	for k, v := range bs.SystemInfo {
		systemVariables[k] = &KEP_WF_DM.Variable{Value: v}
	}
	cv := make(map[string]*KEP_WF_DM.Variable, len(bs.CustomVariables)*2)
	for k, v := range bs.CustomVariables {
		cv[k] = &KEP_WF_DM.Variable{Value: v}
	}

	runEnv := KEP_WF_DM.RunEnvType_SANDBOX
	if bs.EventSource == event.EventSend {
		runEnv = KEP_WF_DM.RunEnvType_PRODUCT
	}
	return &KEP_WF_DM.RunWorkflowRequest{
		SessionID:         bs.SessionID,
		RunEnv:            runEnv,
		RequestType:       0,
		AppID:             strconv.FormatUint(bs.App.GetAppBizId(), 10),
		WorkflowID:        workflowID,
		Query:             bs.OriginContent,
		RewriteQuery:      bs.PromptCtx.Question,
		QueryHistory:      messages,
		CustomVariables:   cv,
		RelatedRecordID:   bs.RelatedRecordID,
		RecordID:          bs.RecordID,
		IsDebug:           isDebug,
		MainModelName:     bs.App.GetMainModelName(),
		Inputs:            bs.WorkflowInput,
		VerboseMode:       KEP_WF_DM.VerboseModeType(bs.Verbose),
		FinanceSubBizType: bs.GetFinanceSubBizType(),
	}
}

// workflowStreamDisplay 工作流结果向前端流式输出
func (w *WorkFlow) workflowStreamDisplay(ctx context.Context, cancel context.CancelFunc, bs *botsession.BotSession,
	ch chan *KEP_WF_DM.RunWorkflowReply,
) (last *KEP_WF_DM.RunWorkflowReply, isTimeout bool, err error) {
	cfg := config.App().Bot
	throttles := cfg.Throttles
	ticker := time.NewTicker(time.Duration(cfg.StopGeneration.CheckInterval) * time.Millisecond)
	timeout := time.NewTimer(time.Duration(cfg.Timeout) * time.Second)
	timeout2 := time.NewTimer(time.Duration(cfg.Timeout/10) * time.Second)
	throttleCheck, thoughtStreaming := helper.NewThrottle(throttles.Check), helper.NewThrottleList()
	streamingStep := helper.When(bs.StreamingThrottle > 0, bs.StreamingThrottle, throttles.Streaming)
	throttleStreaming := helper.NewThrottle(streamingStep)
	clues.AddTrackData(ctx, "streamDisplay().vars", map[string]any{
		"throttles": throttles, "ts.StreamingThrottle": bs.StreamingThrottle,
		"StopGeneration": cfg.StopGeneration, "Timeout": cfg.Timeout,
	})
	defer ticker.Stop()
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			cancel()
			bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(event.ProcedureWorkflow))
			w.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
			return last, true, nil
		case <-timeout2.C:
			w.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
		case <-ticker.C:
			ok, err := w.dao.IsGenerationStopped(ctx, bs.To.CorpStaffID, bs.TokenStat.RecordID)
			if ok {
				log.DebugContextf(ctx, "workflowStreamDisplay: generation stopped")
				w.sendWorkflowToken(ctx, bs, last, true) // todo 后面优化一下这里。
				_ = w.dao.SetLastWorkflow(ctx, bs.App.GetAppBizId(), bs.SessionID, nil)
				lastEvil := &infosec.CheckRsp{
					ResultCode: bs.Msg.ResultCode,
					ResultType: bs.Msg.ResultType,
				}
				refs := GetReferences(last.GetRespond().GetReferences())
				record := bs.NewWorkflowRecord(ctx, last.GetRespond().GetContent(), model.ReplyMethodWorkflow,
					lastEvil, bs.TokenStat, last.GetRespond().GetOptionCards(), refs)
				log.DebugContextf(ctx, "Token Stat: %v", helper.Object2String(bs.TokenStat))
				log.DebugContextf(ctx, "record.tokenStat: %v", record.TokenStat)
				newRecord, newStat := event.GetMsgRecordAndTokenStat(ctx, record)
				newStat.AgentThought, _ = jsoniter.MarshalToString(bs.Thought)
				_, _ = w.dao.CreateMsgRecord(ctx, newRecord, newStat) // for answer
				cancel()
				return last, false, nil
			}
			if err != nil && (errors.Is(err, context.Canceled) ||
				strings.Contains(errs.Msg(err), "context canceled")) {
				return last, false, err
			}
		case rsp, ok := <-ch:
			timeout.Stop()
			if !ok {
				return last, false, pkg.ErrWorkflowRunError
			}
			last = rsp
			if len(rsp.GetRespond().GetThoughtList()) > len(bs.Thought.Procedures) {
				AddThoughtProcedure(ctx, bs, rsp.GetRespond().GetThoughtList(), &thoughtStreaming, streamingStep)
			}
			if NeedReplyThoughtEvent(ctx, bs) {
				w.ThoughtEventReply(ctx, bs, thoughtStreaming, rsp.GetRespond().GetThoughtList(), cancel)
			}
			if discardWorkflowMiddleResult(ctx, *bs, rsp) {
				continue
			}
			if isEvil := w.processWorkflowResponse(ctx, bs, last, throttleCheck, throttleStreaming); isEvil {
				cancel()
				return last, false, nil
			}
			if rsp.IsFinal {
				return last, false, nil
			}
		}
	}
}

// processWorkflowResponse 处理工作流的返回
func (w *WorkFlow) processWorkflowResponse(ctx context.Context, bs *botsession.BotSession,
	rsp *KEP_WF_DM.RunWorkflowReply, throttleCheck, throttleStreaming helper.Throttle) (isEvil bool) {

	// 1. 构造调试信息
	debugInfo := w.createWorkflowDebugInfo(ctx, bs, rsp)
	replyMethod := model.ReplyMethodWorkflow

	l := len([]rune(rsp.GetRespond().GetContent()))
	isFirstReply := throttleCheck.IsFirstReply()
	if bs.NeedCheck && throttleCheck.Hit(l, rsp.IsFinal) {
		checkCode, checkType := w.dao.CheckTextEvil(ctx, bs.App.GetAppBizId(),
			bs.App.GetCorpId(), bs.RecordID, rsp.GetRespond().GetContent(), bs.App.GetInfosecBizType())
		bs.Msg.ResultCode = checkCode
		bs.Msg.ResultType = checkType
		isEvil = checkCode == ispkg.ResultEvil
		if isEvil {
			ctx, cancel := context.WithCancel(ctx)
			rsp.IsFinal = true
			re := bs.NewWorkflowReplyEvent(rsp, isEvil, model.ReplyMethodEvil, debugInfo)
			_ = w.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
		}
	}
	if !isEvil && throttleStreaming.Hit(l, rsp.IsFinal) {
		ctx, cancel := context.WithCancel(ctx)
		if rsp.GetRespond().GetWorkflowStatus() == KEP_WF_DM.WorkflowStatus_SUCCESS {
			replyMethod = model.ReplyMethodWorkflowAnswer
		}
		if rsp.GetRespond().GetWorkflowStatus() == KEP_WF_DM.WorkflowStatus_FAILED {
			w.dao.SetLastWorkflow(ctx, bs.App.GetAppBizId(), bs.SessionID, nil)
		}
		if rsp.GetRespond().GetWorkflowStatus() != KEP_WF_DM.WorkflowStatus_RUNNING &&
			config.IsWorkflowUnchanged(bs.App.GetAppBizId()) {
			w.dao.SetWorkflowUnchanged(ctx, bs.App.GetAppBizId(), bs.SessionID, "")
		}
		re := bs.NewWorkflowReplyEvent(rsp, isEvil, replyMethod, debugInfo)
		// 记录首包、尾包耗时
		uin := pkg.Uin(ctx)
		mainModelName := bs.IntentCate
		if isFirstReply {
			metrics.WorkflowContentFirstToken(ctx, time.Since(bs.StartTime).Milliseconds())
			metrics.WorkflowFirstToken(ctx, time.Since(bs.StartTime).Milliseconds())
			pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.Workflow, mainModelName, "", uin, -1)
			// 端到端首包耗时
			pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.DialogPPL, mainModelName, "", uin, -1)
		} else if rsp.GetIsFinal() {
			pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.Workflow, mainModelName, "", uin, -1)
		}
		_ = w.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
	}
	if err := w.sendWorkflowToken(ctx, bs, rsp, false); err != nil { // token 使用量统计
		return isEvil
	}

	if rsp.IsFinal {
		if !isEvil {
			w.workflowReference(ctx, bs, rsp)
		}
		// 结果写DB
		lastEvil := &infosec.CheckRsp{
			ResultCode: bs.Msg.ResultCode,
			ResultType: bs.Msg.ResultType,
		}
		refs := GetReferences(rsp.GetRespond().GetReferences())
		record := bs.NewWorkflowRecord(ctx, rsp.GetRespond().Content, replyMethod,
			lastEvil, bs.TokenStat, rsp.GetRespond().GetOptionCards(), refs)
		log.DebugContextf(ctx, "Token Stat: %v", helper.Object2String(bs.TokenStat))
		log.DebugContextf(ctx, "record.tokenStat: %v", record.TokenStat)
		newRecord, newStat := event.GetMsgRecordAndTokenStat(ctx, record)
		newStat.AgentThought, _ = jsoniter.MarshalToString(bs.Thought)
		_, _ = w.dao.CreateMsgRecord(ctx, newRecord, newStat) // for answer
	}
	return isEvil
}

// createWorkflowDebugInfo 提取调试信息的构造逻辑
func (w *WorkFlow) createWorkflowDebugInfo(ctx context.Context, bs *botsession.BotSession,
	rsp *KEP_WF_DM.RunWorkflowReply) *event.WorkflowDebugInfo {
	respond := rsp.GetRespond()
	debugInfo := &event.WorkflowDebugInfo{
		WorkflowName:  respond.GetWorkflowName(),
		WorkflowID:    respond.GetWorkflowID(),
		WorkflowRunID: respond.GetWorkflowRunID(),
		OptionCards:   respond.GetOptionCards(),
		Outputs:       respond.GetContentList(),
	}
	if len(respond.GetRunNodes()) != 0 {
		debugInfo.CurrentNode = respond.GetRunNodes()[len(respond.GetRunNodes())-1]
	}
	return debugInfo
}

func (w *WorkFlow) sendWorkflowToken(ctx context.Context,
	bs *botsession.BotSession, reply *KEP_WF_DM.RunWorkflowReply, stop bool) error {
	if reply == nil {
		p := event.NewSuccessTSProcedure(event.ProcedureWorkflow, nil,
			event.ProcedureDebugging{Content: bs.OriginContent, CustomVariables: bs.CustomVariablesForDisplay}, nil)
		bs.TokenStat.UpdateSuccessProcedure(p)
		w.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
		return nil
	}
	// @boyucao @halelv 调试信息
	d := event.ProcedureDebugging{Content: bs.OriginContent, CustomVariables: bs.CustomVariablesForDisplay}
	llmmToken := &llmm.StatisticInfo{}
	tokenUsage := make([]*event.TokenUsage, 0)
	// LLM统计信息。
	for i := 0; i < len(reply.GetStatisticInfos()); i++ {
		llmmToken.InputTokens += reply.GetStatisticInfos()[i].InputTokens
		llmmToken.OutputTokens += reply.GetStatisticInfos()[i].OutputTokens
		llmmToken.TotalTokens += reply.GetStatisticInfos()[i].TotalTokens

		tokenUsage = append(tokenUsage, &event.TokenUsage{
			TotalTokens:  reply.GetStatisticInfos()[i].TotalTokens,
			InputTokens:  reply.GetStatisticInfos()[i].InputTokens,
			OutputTokens: reply.GetStatisticInfos()[i].OutputTokens,
			ModelName:    reply.GetStatisticInfos()[i].ModelName,
		})
	}
	if len(reply.GetStatisticInfos()) == 1 { // 如果工作流只上报了一个，需要更新外层的模型名
		bs.TokenStat.MainModelName = reply.GetStatisticInfos()[0].ModelName
	}
	pre, err := w.dao.GetWorkflowRunNodes(ctx, bs.SessionID, reply.GetRespond().GetWorkflowRunID())
	if errors.Is(err, context.Canceled) || strings.Contains(errs.Msg(err), "context canceled") {
		return err
	}
	runNodes, needOutput := MergeRunNodeInfos(ctx, pre, reply.GetRespond().GetRunNodes())
	if !reply.GetIsFinal() && !stop && !needOutput {
		return nil
	}
	d.Workflow = w.createWorkflowSummary(ctx, bs, runNodes, reply)

	_ = w.dao.SetWorkflowRunNodes(ctx, bs.SessionID, reply.GetRespond().GetWorkflowRunID(), d.Workflow.RunNodes)
	if reply.IsFinal || stop {
		p := event.NewSuccessTSProcedure(event.ProcedureWorkflow, llmmToken, d, tokenUsage)
		bs.TokenStat.UpdateSuccessProcedure(p)
	} else {
		p := event.NewProcessingTSProcedureWithDebug(event.ProcedureWorkflow, llmmToken, d, tokenUsage)
		bs.TokenStat.UpdateProcedure(p)
	}
	w.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
	log.DebugContextf(ctx, "sendWorkflowFinishToken: %v", helper.Object2String(bs.TokenStat))
	return nil
}

// createWorkflowSummary 构建工作流的摘要信息
func (w *WorkFlow) createWorkflowSummary(ctx context.Context, bs *botsession.BotSession,
	runNodes []*KEP_WF_DM.RunNodeInfo,
	reply *KEP_WF_DM.RunWorkflowReply) event.WorkflowSummary {
	ws := event.WorkflowSummary{
		WorkflowID:          reply.GetRespond().GetWorkflowID(),
		WorkflowName:        reply.GetRespond().GetWorkflowName(),
		RunNodes:            runNodes,
		WorkflowRunID:       reply.GetRespond().GetWorkflowRunID(),
		OptionCards:         reply.GetRespond().GetOptionCards(),
		Outputs:             reply.GetRespond().GetContentList(),
		WorkflowReleaseTime: helper.ParseRFC3339ToUnix(reply.GetRespond().GetWorkflowReleaseTime()),
	}
	hitOption := reply.GetRespond().HitOptionCardIndex
	if hitOption <= 0 {
		return ws
	}
	chatStack := bs.ChatHistoriesV2.ChatStack
	for i := len(chatStack) - 1; i >= 0; i-- {
		if chatStack[i].HasOptionCards {
			ws.OptionCardIndex = event.OptionCardIndex{
				RecordID: chatStack[i].RecordID,
				Index:    hitOption,
			}
			break
		}
	}
	return ws
}

// SendTokenStat 发送 token 统计事件到前端
func (w *WorkFlow) SendTokenStat(ctx context.Context, clientID string, tokenStat *event.TokenStatEvent) {
	if tokenStat == nil {
		return
	}
	ctx, cancel := context.WithCancel(ctx)
	err := w.dao.DoEmitWsClient(ctx, clientID, tokenStat, cancel)
	tse0 := *tokenStat
	tse0.Procedures = append([]event.Procedure{}, tokenStat.Procedures...)
	clues.AddTrackDataWithError(ctx, tokenStat.EventSource+".TokenStatEvent", tse0, err)
}

func (w *WorkFlow) workflowReference(ctx context.Context, bs *botsession.BotSession, rsp *KEP_WF_DM.RunWorkflowReply) {
	if len(rsp.GetRespond().GetReferences()) > 0 {
		refs := GetReferences(rsp.GetRespond().GetReferences())
		reEvent := &event.ReferenceEvent{
			RecordID:   bs.TokenStat.RecordID,
			References: refs,
		}
		ctx, cancel := context.WithCancel(ctx)
		_ = w.dao.DoEmitWsClient(ctx, bs.To.ClientID, reEvent, cancel)
		clues.AddTrackData(ctx, "processDisplay.EmitWsUser", map[string]any{
			"To.CorpStaffID": bs.To.CorpStaffID, "To.Type": bs.To.Type, "ReferenceEvent": reEvent,
		})
	}
}

// GetReferences 获取引用
func GetReferences(ref []*KEP_WF_DM.Reference) []model.Reference {
	res := make([]model.Reference, 0)
	for _, v := range ref {
		res = append(res, model.Reference{
			ID:    v.GetID(),
			Type:  v.GetType(),
			URL:   v.GetUrl(),
			DocID: v.GetDocID(),
			Name:  v.GetName(),
		})
	}
	return res
}

// MergeRunNodeInfos 合并 RunNodeInfo 列表，满足回退或前进场景
func MergeRunNodeInfos(ctx context.Context, pre []*KEP_WF_DM.RunNodeInfo, next []*KEP_WF_DM.RunNodeInfo) (
	[]*KEP_WF_DM.RunNodeInfo, bool) {
	needOutput := false
	// 建立 pre 中 NodeID -> index 的映射，便于查找
	preIndexMap := make(map[string]int, len(pre))
	for i, node := range pre {
		if node.GetStatus() == KEP_WF_DM.RunNodeInfo_RUNNING {
			node.IsCurrent = true
		} else {
			node.IsCurrent = false
		}
		preIndexMap[node.NodeID] = i
	}

	// 1. 寻找 next 中第一个与 pre 重叠的节点
	overlapIndex := -1
	for _, nextNode := range next {
		if idx, ok := preIndexMap[nextNode.NodeID]; ok {
			overlapIndex = idx
			break
		}
	}

	var merged []*KEP_WF_DM.RunNodeInfo
	if overlapIndex == -1 {
		// 2(a). 无重叠： merged = pre + next
		merged = append(merged, pre...)
		if len(next) > 0 {
			needOutput = true // 有新增节点
		}
	} else {
		// 2(b). 有重叠：merged = pre[:overlapIndex] + next
		//       若 overlapIndex < len(pre)，说明丢弃了部分节点，必然需要输出
		if overlapIndex < len(pre) {
			needOutput = true
		}
		merged = append(merged, pre[:overlapIndex]...)
	}

	// 3. 将 next 整体拼接到 merged 末尾，并做状态检查
	for _, nextNode := range next {
		// 如果在 pre 中已存在，看是否状态变化
		if idx, ok := preIndexMap[nextNode.NodeID]; ok {
			// 状态不同 ⇒ 需要输出
			if pre[idx].Status != nextNode.Status {
				needOutput = true
			}
		} else {
			// pre 中不存在 ⇒ 新增节点 ⇒ 需要输出
			needOutput = true
		}
		// 设置标记位
		nextNode.IsCurrent = true
		merged = append(merged, nextNode)
	}

	return merged, needOutput
}

// makeWorkflowHistories 构建工作流的历史记录
func (w *WorkFlow) makeWorkflowHistories(ctx context.Context, bs *botsession.BotSession,
	limit int) [][2]model.HisMessage {
	num := helper.When(limit > len(bs.ChatHistoriesV2.ChatStack), len(bs.ChatHistoriesV2.ChatStack), limit)
	start := len(bs.ChatHistoriesV2.ChatStack) - num // 指定截取的起始位置
	pairs := make([][2]model.HisMessage, 0, num)
	for i := start; i < len(bs.ChatHistoriesV2.ChatStack); i++ {
		item := bs.ChatHistoriesV2.ChatStack[i]
		q := model.HisMessage{RecordID: item.RelatedRecordID, Content: item.OriginQuery} // 使用原始query
		a := model.HisMessage{RecordID: item.RecordID, Content: item.GetAssistantContent(),
			Intent: item.Intent, IntentCategory: item.IntentCategory}
		pairs = append(pairs, [2]model.HisMessage{q, a})
	}
	return pairs
}

// discardWorkflowMiddleResult 是否仅回尾包
func discardWorkflowMiddleResult(ctx context.Context, bs botsession.BotSession, resp *KEP_WF_DM.RunWorkflowReply) bool {
	var discard bool
	if resp.GetIsFinal() {
		return discard // 尾包不丢弃,直接返回
	}
	if !bs.IsWorkflowOutputStream() && !resp.GetIsFinal() { // 非流式 且 不是最后一个输出，丢弃
		discard = true
	}
	// 流式,非尾包为空，丢弃（大模型首包可能出现内容为空）
	if bs.IsWorkflowOutputStream() && !resp.GetIsFinal() &&
		resp.GetRespond().GetContent() == "" && len(resp.GetRespond().GetRunNodes()) == 0 {
		discard = true
	}
	if !discard {
		// 占位符输出不完整的时候，丢弃
		reply := resp.GetRespond().GetContent()
		discard = helper.IsPlaceholderEnd(reply) || helper.IsEndWithCalc(reply) || helper.IsEndWithPicture(reply) ||
			helper.EndsWithPlaceholderPrefix(reply, "[Calculator") ||
			helper.EndsWithPlaceholderPrefix(reply, "[Picture")
	}
	log.DebugContextf(ctx, "R|discardMiddleResult|%s %t", bs.EventSource, discard)
	return discard
}

// AddThoughtProcedure 添加一个思考过程
func AddThoughtProcedure(ctx context.Context, bs *botsession.BotSession, thoughtList []*KEP_WF_DM.ThoughtInfo,
	thoughtStreaming *[]helper.Throttle, step int) {
	bs.Thought.RecordID = bs.RecordID

	// 以当前已有的程序数作为起始索引
	startIndex := len(bs.Thought.Procedures)
	for i := startIndex; i < len(thoughtList); i++ {
		sec := thoughtList[i].GetStartTime() / 1000
		nsec := (thoughtList[i].GetStartTime() % 1000) * 1000000

		procedure := event.AgentProcedure{
			Index:      uint32(len(bs.Thought.Procedures)),
			Name:       "thought",
			Title:      "思考",
			NodeName:   thoughtList[i].NodeName,
			ReplyIndex: thoughtList[i].ReplyIndex,
			Icon:       config.AgentConfig.ThoughtIcon,
			Status:     event.ProcedureStatusProcessing,
			Elapsed:    0,
			StartTime:  time.Unix(sec, nsec),
		}
		bs.Thought.Procedures = append(bs.Thought.Procedures, procedure)
		*thoughtStreaming = append(*thoughtStreaming, helper.NewThrottle(step)) // 每次添加思考，重新初始化节流器
		log.DebugContextf(ctx, "addThoughtProcedure: %s", helper.Object2String(procedure))
	}
}

// NeedReplyThoughtEvent 判断是否需要回复思考过程
func NeedReplyThoughtEvent(ctx context.Context, bs *botsession.BotSession) bool {
	for _, p := range bs.Thought.Procedures {
		if p.Status == event.ProcedureStatusProcessing {
			return true
		}
	}
	return false
}

// ThoughtEventReply 思维链事件回复
func (w *WorkFlow) ThoughtEventReply(ctx context.Context,
	bs *botsession.BotSession, throttleStreaming []helper.Throttle, replyList []*KEP_WF_DM.ThoughtInfo,
	cancel context.CancelFunc) {
	bs.Thought.Elapsed = 0
	if len(bs.Thought.Procedures) == 0 || len(replyList) == 0 {
		log.WarnContextf(ctx, "thoughtEventReply: procedures empty")
		return
	}
	if throttleStreaming[0].IsFirstReply() {
		metrics.WorkflowFirstToken(ctx, time.Since(bs.StartTime).Milliseconds())
		metrics.WorkflowThoughtFirstToken(ctx, time.Since(bs.StartTime).Milliseconds())
	}
	for i := 0; i < len(replyList); i++ {
		if throttleStreaming[i].Hit(len([]rune(replyList[i].Content)), replyList[i].EndTime != 0) {
			end := time.Now()
			if replyList[i].EndTime != 0 {
				sec := replyList[i].GetEndTime() / 1000
				nsec := (replyList[i].GetEndTime() % 1000) * 1000000
				end = time.Unix(sec, nsec)
				bs.Thought.Procedures[i].Status = event.ProcedureStatusSuccess
			}
			cost := uint32(end.Sub(bs.Thought.Procedures[i].StartTime).Milliseconds())
			bs.Thought.Procedures[i].Elapsed = cost
			bs.Thought.Procedures[i].Debugging.Content = replyList[i].Content
			bs.Thought.Elapsed += bs.Thought.Procedures[i].Elapsed
			log.DebugContextf(ctx, "thoughtEventReply: %+v", bs.Thought)
			_ = w.dao.DoEmitWsClient(ctx, bs.To.ClientID, bs.Thought, cancel)
		}
	}

}

// ReplyFromSelf 首包回复自身信息
func (w *WorkFlow) ReplyFromSelf(ctx context.Context, bs *botsession.BotSession) {
	ctx, cancel := context.WithCancel(ctx)
	// 如果IsEvil为true，是否有必要设置ReplyMethod。这里异步执行即可。
	go w.dao.DoEmitWsClient(ctx, bs.To.ClientID, &event.ReplyEvent{
		IsFinal:    true,
		IsFromSelf: true,
		IsEvil:     bs.Msg.ResultCode == ispkg.ResultEvil,
		RequestID:  bs.RequestID,
		FromName:   bs.App.BaseConfig.GetName(),
		FromAvatar: bs.App.BaseConfig.GetAvatar(),
		Content:    bs.OriginContent,
		RecordID:   bs.RelatedRecordID,
		SessionID:  bs.SessionID,
		Timestamp:  bs.StartTime.Unix(),
		FileInfos:  bs.FileInfos,
	}, cancel)
	return
}

// CreateQueryMsgRecord 创建查询消息记录
func (w *WorkFlow) CreateQueryMsgRecord(ctx context.Context, bs *botsession.BotSession) error {
	pf.StartElapsed(ctx, bs.EventSource+".createQueryMsgRecord")
	fileInfos, _ := jsoniter.MarshalToString(bs.FileInfos)
	msg := model.MsgRecord{
		BotBizID:     bs.App.GetAppBizId(),
		SessionID:    bs.SessionID,
		RecordID:     bs.RecordID,
		Type:         bs.Type,
		ToID:         bs.App.GetId(),
		ToType:       model.SourceTypeRobot,
		FromID:       bs.To.CorpStaffID,
		FromType:     bs.To.GetSourceType(),
		Content:      bs.OriginContent,
		FileInfos:    fileInfos,
		CreateTime:   time.Now(),
		CfgVersionID: bs.App.GetConfigVersionId(),
		TraceID:      model.TraceID(ctx),
	}
	msgID, err := w.dao.CreateMsgRecord(ctx, msg, nil)                  // for query
	go w.dao.CreateChatPrompt(trpc.CloneContext(ctx), model.ChatPrompt{ // 初始化一下ChatPrompt
		BotBizID:   msg.BotBizID,
		SessionID:  msg.SessionID,
		RecordID:   msg.RecordID,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	})
	pf.AppendSpanElapsed(ctx, bs.EventSource+".createQueryMsgRecord")
	pkg.WithRecordID(ctx, bs.RecordID) // query record_id
	clues.AddTrackData(ctx, "query:record:id", msg.RecordID)
	bs.RelatedRecordID = bs.RecordID // 写消息记录后，设置RelatedRecordID
	bs.Msg = &msg
	if err != nil {
		return err
	}
	msg.ID = uint64(msgID)
	return err
}

// ProcessImageAndFileCount 处理图片和文件数量
func (w *WorkFlow) ProcessImageAndFileCount(ctx context.Context, bs *botsession.BotSession) {
	if w.dao.IsInFileKnowledge(ctx, bs.SessionID, bs.App.GetHistoryLimit()) && len(bs.FileInfos) == 0 {
		_ = w.dao.SetFileKnowledgeCount(ctx, bs.SessionID) // 对话轮数+1
	}
	if w.dao.IsInMultiModal(ctx, bs.SessionID, bs.App.GetHistoryLimit()) &&
		!helper.IsQueryContainsImage(bs.OriginContent) {
		go func() {
			defer errors2.PanicHandler()
			err := w.dao.SetMultiModalCount(ctx, bs.SessionID)
			if err != nil {
				log.ErrorContextf(ctx, "processImageAndFileCount SetMultiModalCount error: %v", err)
			}
		}() // 多模态次数+1
	}
}

// GetSession 获取会话信息
func (w *WorkFlow) GetSession(ctx context.Context, bs *botsession.BotSession) (err error) {
	session := &model.Session{
		SessionID:    bs.SessionID,
		BotBizID:     bs.To.APIBotBizID,
		VisitorID:    bs.To.CorpStaffID,
		VisitorBizID: bs.To.CorpStaffBizID,
	}
	if bs.To.Type != model.ConnTypeAPIVisitor {
		if session, err = w.dao.GetSession(ctx, bs.SessionType, bs.SessionID); err != nil {
			return err
		}
		if session == nil {
			return pkg.ErrSessionNotFound
		}
	}
	clues.AddTrackData(ctx, "Process.botsession", session)
	bs.Session = session
	return nil
}
