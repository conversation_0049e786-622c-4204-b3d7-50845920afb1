// Package multimodal TODO
package multimodal

import (
	"context"

	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
)

// ChatMultiModalAPI 多模态API
type ChatMultiModalAPI interface {
	GetCaption(ctx context.Context, imageURLs []string, req *llmm.Request, query string) (*llmm.Response, error)
	QueryWithImage(ctx context.Context, bs *botsession.BotSession) (err error)
	// MultiModalProcess(ctx context.Context, bs *botsession.BotSession) error
}

// MultiModal TODO
type MultiModal struct {
	dao dao.Dao
}

var (
	multiModal ChatMultiModalAPI
)

func init() {
	multiModal = &MultiModal{
		dao: dao.New(),
	}
}

// New .
func New() ChatMultiModalAPI {
	return multiModal
}
