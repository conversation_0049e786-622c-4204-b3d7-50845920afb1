package dao

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/limiter"
	"git.woa.com/dialogue-platform/common/v3/utils"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
)

// Lock 锁
func (d *dao) Lock(ctx context.Context, req limiter.MultiLockReq) (*limiter.MultiLockRsp, error) {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "Lock cost: %s, req:%s", time.Since(tik), helper.Object2String(req))
	}()
	lock, err := d.limiterV2.MultiLock(ctx, &req)
	if err != nil {
		log.ErrorContextf(ctx, "Lock error: %+v, req: %s", err, helper.Object2String(req))
		return lock, err
	}
	return lock, nil
}

// Unlock 解锁
func (d *dao) Unlock(ctx context.Context, req limiter.MultiUnlockReq) {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "Unlock cost: %s, req:%s", time.Since(tik), helper.Object2String(req))
	}()
	_, err := d.limiterV2.MultiUnLock(ctx, &req)
	if err != nil {
		log.ErrorContextf(ctx, "Unlock error: %+v, req: %s", err, helper.Object2String(req))
		return
	}
	return
}

// GetDefaultQpmTpmLimit 获取默认QPM/TPM限制
func (d *dao) GetDefaultQpmTpmLimit(ctx context.Context) (cfg limiter.DefaultConfig) {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "GetDefaultQpmTpmLimit cost: %s, cfg:%s", time.Since(tik), utils.Any2String(cfg))
	}()
	cfg = limiter.DefaultConfig{
		DefaultQPM:   config.App().Finance.DefaultQPM,
		DefaultTPM:   config.App().Finance.DefaultTPM,
		DsDefaultQPM: config.App().Finance.DsDefaultQPM,
		DsDefaultTPM: config.App().Finance.DsDefaultTPM,
	}
	req := &admin.GetDefaultQpmTpmLimitReq{}
	rsp, err := d.apiCli.GetDefaultQpmTpmLimit(ctx, req)
	if err != nil {
		log.WarnContextf(ctx, "GetDefaultQpmTpmLimit error: %+v, req: %s", err, helper.Object2String(req))
		return cfg
	}
	if rsp.GetQpm() > 0 {
		cfg.DefaultQPM = rsp.GetQpm()
	}
	if rsp.GetTpm() > 0 {
		cfg.DefaultTPM = rsp.GetTpm()
	}
	if rsp.GetDsQpm() > 0 {
		cfg.DsDefaultQPM = rsp.GetDsQpm()
	}
	if rsp.GetDsTpm() > 0 {
		cfg.DsDefaultTPM = rsp.GetDsTpm()
	}
	return cfg
}

// ReportToken 上报token,用于tpm限频
func (d *dao) ReportToken(ctx context.Context, req limiter.ReportTokenReq) error {
	tik := time.Now()
	defer func() {
		log.DebugContextf(ctx, "ReportToken cost: %s, req:%s", time.Since(tik), helper.Object2String(req))
	}()
	rsp, err := d.limiterV2.ReportToken(ctx, &req)
	if err != nil {
		log.WarnContextf(ctx, "ReportToken error: %+v, req: %s", err, helper.Object2String(req))
		return err
	}
	if !rsp.OK || !rsp.ModelStatus {
		log.WarnContextf(ctx, "ReportToken error: %+v, rsp: %s", err, helper.Object2String(rsp))
	}
	return nil
}
