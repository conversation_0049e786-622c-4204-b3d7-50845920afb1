package dao

import (
	"context"
	"errors"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	sts "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sts/v20180813"
)

var (
	stsClient *sts.Client
	stsOnce   sync.Once
)

// GetTencentCloudCredential 获取访问腾讯云的临时秘钥
func (d *dao) GetTencentCloudCredential(ctx context.Context, product model.Product) (
	*model.TmpCredential, error) {
	client, err := getSTSClient(ctx)
	if err != nil {
		return nil, err
	}
	// 获取临时秘钥的策略
	credentialConfig, ok := config.App().STS.CredentialMap[string(product)]
	if !ok {
		return nil, errors.New("credential config empty")
	} else if credentialConfig.Name == "" || credentialConfig.Policy == "" {
		return nil, errors.New("credential config invalid")
	}
	// 临时秘钥的有效期，单位：秒，默认1小时
	ttl := credentialConfig.TTL
	if ttl == 0 {
		ttl = 3600
	}
	request := getFederationTokenRequest(credentialConfig.Name, credentialConfig.Policy, ttl)
	response, err := client.GetFederationTokenWithContext(ctx, request)
	if err != nil {
		log.ErrorContextf(ctx, "get credential from tencet cloud error: %+v", err)
		return nil, err
	}
	if response == nil {
		return nil, errors.New("credential response is nil")
	}
	log.InfoContextf(ctx, "get credential from tencet cloud：%s", response.ToJsonString())
	if response.Response == nil {
		return nil, errors.New("credential response is nil")
	}
	return &model.TmpCredential{
		Token:        *response.Response.Credentials.Token,
		TmpSecretID:  *response.Response.Credentials.TmpSecretId,
		TmpSecretKey: *response.Response.Credentials.TmpSecretKey,
		Expired:      *response.Response.ExpiredTime,
		AppID:        config.App().STS.AppID,
	}, nil
}

func getFederationTokenRequest(name, policy string, duration uint64) *sts.GetFederationTokenRequest {
	request := sts.NewGetFederationTokenRequest()
	request.Name = common.StringPtr(name)
	request.Policy = common.StringPtr(policy)
	request.DurationSeconds = common.Uint64Ptr(duration)
	return request
}

func getSTSClient(ctx context.Context) (*sts.Client, error) {
	if stsClient != nil {
		return stsClient, nil
	}
	var err error
	stsOnce.Do(func() {
		credential := common.NewCredential(config.App().STS.SecretID, config.App().STS.SecretKey)
		cpf := profile.NewClientProfile()
		cpf.HttpProfile.Endpoint = config.App().STS.Endpoint
		stsClient, err = sts.NewClient(credential, config.App().STS.Region, cpf)
	})
	if err != nil {
		log.ErrorContextf(ctx, "Init sts client error: %+v", err)
		return nil, err
	}
	return stsClient, nil
}
