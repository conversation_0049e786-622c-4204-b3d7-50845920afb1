package dao

import (
	"context"
	"errors"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/encoding/protojson"
)

// PrefixWorkflow workflowDM Redis前缀
const PrefixWorkflow = "qbot:chat:workflow:%d:%s"

// PrefixWorkflowLast 上一轮workflow意图 Redis前缀
const PrefixWorkflowLast = "qbot:chat:workflow:last:%d:%s"

// PrefixWorkflowRunNodes 上一轮workflow运行节点 Redis前缀
const PrefixWorkflowRunNodes = "qbot:chat:workflow:nodes"

// keyWorkflowStatus workflow 状态 key
func keyWorkflowStatus(appID uint64, sessionID string) string {
	return fmt.Sprintf(PrefixWorkflow, appID, sessionID)
}

// keyWorkflowLast workflow 上一轮 key
func keyWorkflowLast(appID uint64, sessionID string) string {
	return fmt.Sprintf(PrefixWorkflowLast, appID, sessionID)
}

// keyWorkflowRunNodes workflow 运行节点 key
func keyWorkflowRunNodes(sessionID, runID string) string {
	return PrefixWorkflowRunNodes + ":" + sessionID + ":" + runID
}

// RetrieveWorkflows 获取topN工作流
func (d *dao) RetrieveWorkflows(ctx context.Context,
	runEnv KEP_WF_DM.RunEnvType, appID uint64, rewriteQuery string) (*KEP_WF_DM.RetrieveWorkflowsReply, error) {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "RetrieveWorkflows cost: %s", time.Since(tik))
	}()
	if config.App().Mock.EnableWorkflow {
		rsp := &KEP_WF_DM.RetrieveWorkflowsReply{
			Workflows: []*KEP_WF_DM.RetrieveWorkflow{},
		}
		log.InfoContextf(ctx, "Mock RetrieveWorkflows %s", helper.Object2String(rsp))
		return rsp, nil
	}
	// 构造请求
	req := &KEP_WF_DM.RetrieveWorkflowsRequest{
		RunEnv:       runEnv,
		AppID:        strconv.FormatUint(appID, 10),
		RewriteQuery: rewriteQuery,
	}
	log.InfoContextf(ctx, "RetrieveWorkflows req: %s", helper.Object2String(req))

	rsp, err := d.workflowCli.RetrieveWorkflows(ctx, req)
	if err != nil {
		log.WarnContextf(ctx, "RetrieveWorkflows error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "RetrieveWorkflows rsp: %s", helper.Object2String(rsp))
	return rsp, nil
}

// RunWorkflow 执行工作流：负责客户端创建、发送请求和资源释放
func (d *dao) RunWorkflow(ctx context.Context, req *KEP_WF_DM.RunWorkflowRequest,
	ch chan *KEP_WF_DM.RunWorkflowReply, signal chan int) (err error) {
	var last *KEP_WF_DM.RunWorkflowReply
	defer close(ch)
	// 若最后一条消息未标识为最终状态，则补充推送一条最终响应
	defer func() {
		if last != nil && !last.IsFinal {
			last.IsFinal = true
			ch <- last
		}
	}()

	start := time.Now()
	reqBytes, _ := protojson.Marshal(req)
	log.InfoContextf(ctx, "workflow request is: %s", string(reqBytes))

	opts := []client.Option{WithTrpcSelector()}
	cli, err := d.workflowCli.RunWorkflow(ctx, opts...) // 定义流式客户端
	if err != nil {
		log.ErrorContextf(ctx, "New workflow stream client error: %+v", err)
		return err
	}
	// 关闭发送流
	defer func() {
		if err := cli.CloseSend(); err != nil {
			log.ErrorContextf(ctx, "CloseSend error: %+v", err)
		}
		log.InfoContextf(ctx, "CloseSend done.")
	}()

	if err = cli.Send(req); err != nil {
		log.ErrorContextf(ctx, "Send workflow stream request error: %+v, req: %+v", err, req)
		return err
	}

	// 调用独立的循环处理函数
	last, err = d.runWorkflowLoop(ctx, req, cli, ch, signal, start)
	return err
}

// runWorkflowLoop 独立处理工作流流式接收及业务逻辑
func (d *dao) runWorkflowLoop(ctx context.Context, req *KEP_WF_DM.RunWorkflowRequest,
	cli KEP_WF_DM.WorkflowDm_RunWorkflowClient, ch chan *KEP_WF_DM.RunWorkflowReply,
	signal chan int, start time.Time) (last *KEP_WF_DM.RunWorkflowReply, err error) {
	idx, logSpeed := 0, 0
	for {
		select {
		case <-ctx.Done():
			req.RequestType = KEP_WF_DM.RequestType_STOP
			_ = cli.Send(req)
			return last, nil
		case <-signal:
			close(signal)
			req.RequestType = KEP_WF_DM.RequestType_STOP
			err = cli.Send(req)
			log.WarnContextf(ctx, "RunWorkflow send cancel: %+v, req: %s", err, helper.Object2StringEscapeHTML(req))
			return last, nil
		default:
			last, err = cli.Recv()
			// 定期打印日志
			if idx%(10*logOutput[logSpeed]) == 0 || (last != nil && last.IsFinal) {
				rspBytes, _ := protojson.Marshal(last)
				log.InfoContextf(ctx, "RunWorkflow[%d] rsp: %s, cost: %s", idx, string(rspBytes), time.Since(start))
				if logSpeed < len(logOutput)-1 {
					logSpeed++
				}
			}
			if err == nil && last != nil && last.Code == 0 {
				ch <- last
			}
			pf.AppendFullSpanElapsed(ctx, "workflow_ms", "", "", pkg.Uin(ctx), -1)
			// 收到最终响应或遇到 EOF 则退出
			if (last != nil && last.GetIsFinal()) || err == io.EOF {
				appID, _ := strconv.ParseUint(req.AppID, 10, 64)
				if last.GetCode() == 8000003 {
					_ = d.SetLastWorkflow(ctx, appID, req.SessionID, nil)
					return last, pkg.ErrWorkflowReference
				}
				if last.GetCode() != 0 {
					_ = d.SetLastWorkflow(ctx, appID, req.SessionID, nil)
					return last, pkg.ErrWorkflowRunError
				}
				return last, nil
			}
			// 错误处理：若因上下文取消则发送停止请求后退出
			if err != nil {
				if errors.Is(err, context.Canceled) || strings.Contains(errs.Msg(err), "context canceled") {
					req.RequestType = KEP_WF_DM.RequestType_STOP
					err = cli.Send(req)
					log.WarnContextf(ctx, "RunWorkflow send cancel: %+v, req: %s", err, helper.Object2String(req))
					return last, nil
				}
				log.ErrorContextf(ctx, "RunWorkflow error: %+v, req: %+v", err, req)
				return last, err
			}
			idx++
		}
	}
}

// ClearWorkflowSession 清除会话
func (d *dao) ClearWorkflowSession(ctx context.Context, req *KEP_WF_DM.ClearSessionRequest) error {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "ClearWorkflowSession cost: %s", time.Since(tik))
	}()
	log.InfoContextf(ctx, "ClearWorkflowSession req: %s", helper.Object2String(req))
	opts := []client.Option{WithTrpcSelector()}
	_, err := d.workflowCli.ClearSession(ctx, req, opts...)
	if err != nil {
		log.WarnContextf(ctx, "Invoke ClearWorkflowSession error: %+v, req: %+v", err, req)
		return err
	}
	log.InfoContextf(ctx, "Invoke ClearWorkflowSession OK.")
	return nil
}

// SetWorkflowStatus 设置在工作流中状态标记 一天过期，与DM一致。
func (d *dao) SetWorkflowStatus(ctx context.Context, appID uint64, sessionID string, flag bool) error {
	_, err := d.redis.Do(ctx, "SET", keyWorkflowStatus(appID, sessionID), flag, "EX", 24*60*60)
	if err != nil {
		log.ErrorContextf(ctx, "SetWorkflowStatus error: %+v, sessionID: %s", err, sessionID)
		return err
	}
	log.InfoContextf(ctx, "SetWorkflowStatus success, sessionID: %s, flag: %t", sessionID, flag)
	return nil
}

// IsInWorkflow 是否在工作流中
func (d *dao) IsInWorkflow(ctx context.Context, appID uint64, sessionID string) bool {
	rsp, err := redis.Bool(d.redis.Do(ctx, "GET", keyWorkflowStatus(appID, sessionID)))
	if err != nil {
		return false
	}
	log.InfoContextf(ctx, "IsInWorkflow success, sessionID: %s, flag: %t", sessionID, rsp)
	return rsp
}

// SetLastWorkflow 设置上一轮工作流
func (d *dao) SetLastWorkflow(ctx context.Context, appID uint64, sessionID string,
	result *KEP_WF_DM.RetrieveWorkflow) error {
	b, _ := protojson.Marshal(result)
	key := keyWorkflowLast(appID, sessionID)
	_, err := d.redis.Do(ctx, "SET", key, b, "EX", 5*60*60)
	if err != nil {
		log.ErrorContextf(ctx, "SetLastWorkflow error: %+v, key: %s", err, key)
		return err
	}
	log.InfoContextf(ctx, "SetLastWorkflow success, key: %s, req: %s", key, string(b))
	return nil
}

// GetLastWorkflow 获取上一轮工作流
func (d *dao) GetLastWorkflow(ctx context.Context, appID uint64, sessionID string) (*KEP_WF_DM.RetrieveWorkflow,
	error) {
	key := keyWorkflowLast(appID, sessionID)
	b, err := redis.Bytes(d.redis.Do(ctx, "GET", key))
	if err != nil {
		log.WarnContextf(ctx, "GetLastWorkflow warn: %+v, key: %s", err, key)
		return nil, err
	}
	result := &KEP_WF_DM.RetrieveWorkflow{}
	if err = protojson.Unmarshal(b, result); err != nil {
		log.WarnContextf(ctx, "Unmarshal GetLastWorkflow error: %+v, key: %s", err, key)
		return nil, err
	}
	log.InfoContextf(ctx, "GetLastWorkflow success, key: %s, req: %s", key, string(b))
	return result, nil
}

// SetWorkflowRunNodes 设置工作流运行节点
func (d *dao) SetWorkflowRunNodes(ctx context.Context, sessionID, runID string, nodes []*KEP_WF_DM.RunNodeInfo) error {
	b, _ := jsoniter.Marshal(nodes)
	key := keyWorkflowRunNodes(sessionID, runID)
	_, err := d.redis.Do(ctx, "SET", key, b, "EX", 10*60*60)
	if err != nil {
		log.ErrorContextf(ctx, "SetWorkflowRunNodes error: %+v, key: %s", err, key)
		return err
	}
	log.DebugContextf(ctx, "SetWorkflowRunNodes success, key: %s, req: %s", key, string(b)) // 日志过多，容易OOM
	return nil
}

// GetWorkflowRunNodes 获取工作流运行节点
func (d *dao) GetWorkflowRunNodes(ctx context.Context, sessionID, runID string) ([]*KEP_WF_DM.RunNodeInfo, error) {
	key := keyWorkflowRunNodes(sessionID, runID)
	b, err := redis.Bytes(d.redis.Do(ctx, "GET", key))
	if err != nil {
		log.WarnContextf(ctx, "GetWorkflowRunNodes warn: %+v, key: %s", err, key)
		return nil, err
	}
	nodes := make([]*KEP_WF_DM.RunNodeInfo, 0)
	if err = jsoniter.Unmarshal(b, &nodes); err != nil {
		log.WarnContextf(ctx, "Unmarshal GetWorkflowRunNodes error: %+v, key: %s", err, key)
		return nil, err
	}
	log.DebugContextf(ctx, "GetWorkflowRunNodes success, key: %s, req: %s", key, string(b)) // 日志过多，容易OOM
	return nodes, nil
}

// GetWorkflowStatus 获取工作流状态
func (d *dao) GetWorkflowStatus(ctx context.Context, appID uint64, workflowID string,
	scene AppScene) (*KEP_WF.GetWorkflowReleaseStatusResp, error) {
	var envTag uint32
	if scene == AppTestScene {
		envTag = 0
	} else if scene == AppReleaseScene {
		envTag = 1
	}
	req := &KEP_WF.GetWorkflowReleaseStatusReq{
		AppBizId:   fmt.Sprintf("%d", appID),
		WorkflowId: workflowID,
		EnvTag:     envTag,
	}
	rsp, err := d.configCli.GetWorkflowReleaseStatus(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowStatus error: %+v, req: %+v", err, req)
		return rsp, err
	}
	log.InfoContextf(ctx, "GetWorkflowStatus success, req: %+v, rsp: %+v", req, rsp)
	return rsp, nil
}

// GetWorkflowByIDs 获取指定工作流信息
func (d *dao) GetWorkflowByIDs(ctx context.Context, appBizID uint64, workflowIDs []string) (
	map[string]*KEP_WF.ListWorkflowInnerRsp_Workflow, error) {
	if len(workflowIDs) == 0 {
		return nil, nil
	}
	req := &KEP_WF.ListWorkflowInnerReq{
		Page:        1,
		PageSize:    uint32(len(workflowIDs)),
		AppBizId:    fmt.Sprintf("%d", appBizID),
		WorkflowIds: workflowIDs,
	}
	rsp, err := d.configCli.ListWorkflowInner(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "ListWorkflowInner req:%+v err:%+v", req, err)
		return nil, err
	}
	mapWorkflow := make(map[string]*KEP_WF.ListWorkflowInnerRsp_Workflow)
	for _, v := range rsp.GetList() {
		mapWorkflow[v.GetWorkflowId()] = v
	}
	return mapWorkflow, nil
}

// SetWorkflowUnchanged 设置在工作流保持信息
func (d *dao) SetWorkflowUnchanged(ctx context.Context, appID uint64, sessionID string, workflowID string) {
	key := fmt.Sprintf("qbot:chat:workflow:unchanged:%d:%s", appID, sessionID)
	// 如果workflowID为空，则删除
	if workflowID == "" {
		_, err := d.redis.Do(ctx, "DEL", key)
		if err != nil {
			log.ErrorContextf(ctx, "SetWorkflowUnchanged error: %+v, key: %s", err, key)
			return
		}
		log.InfoContextf(ctx, "SetWorkflowUnchanged success, key: %s", key)
		return
	}
	_, err := d.redis.Do(ctx, "SET", key, workflowID, "EX", 24*60*60)
	if err != nil {
		log.ErrorContextf(ctx, "SetWorkflowUnchanged error: %+v, key: %s", err, key)
		return
	}
	log.InfoContextf(ctx, "SetWorkflowUnchanged success, key: %s, workflowID: %s", key, workflowID)
}

// GetWorkflowUnchanged 获取在工作流保持信息
func (d *dao) GetWorkflowUnchanged(ctx context.Context, appID uint64, sessionID string) string {
	key := fmt.Sprintf("qbot:chat:workflow:unchanged:%d:%s", appID, sessionID)
	workflowID, err := redis.String(d.redis.Do(ctx, "GET", key))
	if err != nil {
		log.DebugContextf(ctx, "GetWorkflowUnchanged warn: %+v, key: %s", err, key)
		return ""
	}
	log.InfoContextf(ctx, "GetWorkflowUnchanged success, key: %s, workflowID: %s", key, workflowID)
	return workflowID
}

// NodeDebugWorkflow 非对话节点调试
func (d *dao) NodeDebugWorkflow(ctx context.Context,
	req *KEP_WF_DM.DebugWorkflowNodeRequest) (reply *KEP_WF_DM.DebugWorkflowNodeReply, err error) {
	reply, err = d.workflowCli.DebugWorkflowNode(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "NodeDebugWorkflow req:%+v err:%+v", req, err)
		return nil, err
	}
	log.InfoContextf(ctx, "NodeDebugWorkflow req:%+v reply:%+v", req, reply)
	return reply, err
}
