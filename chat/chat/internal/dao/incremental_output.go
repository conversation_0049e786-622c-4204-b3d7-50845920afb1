package dao

import (
	"context"
	"fmt"
	"sync"

	"git.code.oa.com/trpc-go/trpc-database/localcache"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	jsoniter "github.com/json-iterator/go"
)

const (
	expiration = 7200
	capacity   = 1000
)

var (
	globalOutPutCache localcache.Cache
	cacheLockMap      sync.Map
)

// OutputCache 增量输出的缓存
type OutputCache struct {
	Reply string
	// index -> thought
	ThoughtMap map[uint32]string
}

func newOutputCache() *OutputCache {
	return &OutputCache{
		ThoughtMap: make(map[uint32]string),
	}
}

// InitGlobalOutputCache 初始化全局DialogCache
func InitGlobalOutputCache() {
	globalOutPutCache = localcache.New(
		localcache.WithExpiration(expiration),
		localcache.WithCapacity(capacity),
		localcache.WithOnDel(func(item *localcache.Item) {
			// 过期删除的时候将锁也删除，防止残留
			cacheLockMap.Delete(item.Key)
		}))
}

// InitOutputCache 为client初始化cache
func InitOutputCache(ctx context.Context, clientID string) {
	globalOutPutCache.Set(getCacheKey(ctx, clientID), newOutputCache())
	if globalOutPutCache.Len() >= 0.9*capacity {
		log.ErrorContext(ctx, "output cache is going to be full, len: %v", globalOutPutCache.Len())
	}
}

func getCacheKey(ctx context.Context, clientID string) string {
	key := clientID + "#" + model.TraceID(ctx)
	return key
}

func getClientCache(ctx context.Context, clientID string) (*OutputCache, bool) {
	cache, exist := globalOutPutCache.Get(getCacheKey(ctx, clientID))
	if !exist {
		return nil, false
	}

	outputCache, ok := cache.(*OutputCache)
	if !ok {
		log.ErrorContext(ctx, "cache type error")
		return nil, false
	}
	return outputCache, true
}

func deleteOutputCache(ctx context.Context, clientID string) {
	globalOutPutCache.Del(getCacheKey(ctx, clientID))
}

func convertToIncrementalEvents(ctx context.Context, clientID string, e model.WsEvent) (model.WsEvent, error) {
	cache, ok := getClientCache(ctx, clientID)
	if !ok {
		// cache不存在，代表不是增量输出
		return e, nil
	}
	// 判断类型, 只处理 reply 和 though类型
	eventType := ""
	switch e.(type) {
	case *event.ReplyEvent, event.ReplyEvent:
		// 如果传入给interface e的是结构体地址
		eventType = event.EventReply
	case *event.AgentThoughtEvent, event.AgentThoughtEvent:
		eventType = event.EventThought
	default:
		return e, nil
	}
	// 加锁，获取缓存
	mu, _ := cacheLockMap.LoadOrStore(getCacheKey(ctx, clientID), &sync.Mutex{})
	clientLock, ok := mu.(*sync.Mutex)
	if !ok {
		log.ErrorContext(ctx, "cache lock type error")
		return e, fmt.Errorf("cache lock type error")
	}
	clientLock.Lock()
	defer clientLock.Unlock()
	// 防止缓存有更新，获取到锁之后再获取一次缓存
	cache, ok = getClientCache(ctx, clientID)
	if !ok {
		return e, fmt.Errorf("get cache error")
	}
	// 通过序列化深拷贝，防止影响外层逻辑
	buf, err := jsoniter.Marshal(e)
	if err != nil {
		log.ErrorContext(ctx, "json marshal reply event error: %v", err)
		return e, err
	}
	// 对输出做差分，并更新缓存
	if eventType == event.EventReply {
		replyEvent := event.ReplyEvent{}
		err = jsoniter.Unmarshal(buf, &replyEvent)
		if err != nil {
			log.ErrorContext(ctx, "json unmarshal reply event error: %v", err)
			return e, err
		}
		if replyEvent.IsFromSelf {
			return e, nil
		}
		last := cache.Reply
		cache.Reply = replyEvent.Content
		replyEvent.Content = truncateWithLength(ctx, last, replyEvent.Content)
		e = replyEvent
		if !replyEvent.IsFromSelf && replyEvent.IsFinal {
			cacheLockMap.Delete(getCacheKey(ctx, clientID))
			deleteOutputCache(ctx, clientID)
			log.InfoContextf(ctx, "reply finish, delete cache for client %s, remain %v", clientID, globalOutPutCache.Len())
		}
	} else if eventType == event.EventThought {
		thoughtEvent := event.AgentThoughtEvent{}
		err = jsoniter.Unmarshal(buf, &thoughtEvent)
		if err != nil {
			log.ErrorContext(ctx, "json unmarshal reply event error: %v", err)
			return e, err
		}
		for i, procedure := range thoughtEvent.Procedures {
			last := cache.ThoughtMap[procedure.Index]
			if procedure.Debugging.DisplayType != 0 {
				cache.ThoughtMap[procedure.Index] = procedure.Debugging.DisplayContent
				thoughtEvent.Procedures[i].Debugging.Content = truncateWithLength(ctx,
					last, procedure.Debugging.DisplayContent)
			} else {
				cache.ThoughtMap[procedure.Index] = procedure.Debugging.Content
				thoughtEvent.Procedures[i].Debugging.Content = truncateWithLength(ctx,
					last, procedure.Debugging.Content)
			}
		}
		e = thoughtEvent
	}
	return e, nil
}

func truncateWithLength(ctx context.Context, old, newStr string) string {
	// 将字符串转换为 rune 切片
	runesOld := []rune(old)
	runesNew := []rune(newStr)

	// 获取字符串的长度
	lenOld := len(runesOld)
	lenNew := len(runesNew)

	// 检查 b 的长度是否大于 a 的长度
	if lenNew > lenOld {
		// 截取 b 的后半部分，前面丢弃的长度等于 a 的长度
		return string(runesNew[lenOld:])
	} else {
		if lenNew < lenOld {
			log.WarnContextf(ctx, "[content invalid] old: %s, new: %s", old, newStr)
		}
		return ""
	}
}
