package dao

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-database/mysql"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

const (
	fieldVisitor = `id, bot_id, bot_biz_id, visitor_biz_id, create_time, update_time`
)

const (
	getVisitor = `SELECT ` + fieldVisitor + ` FROM t_visitor WHERE bot_biz_id = ? AND visitor_biz_id = ? LIMIT 1`

	getVisitorByID = `SELECT ` + fieldVisitor + ` FROM t_visitor WHERE bot_biz_id = ? AND id = ? LIMIT 1`

	createVisitor = `
	INSERT INTO t_visitor (` + fieldVisitor + `) VALUES (null, :bot_id, :bot_biz_id, :visitor_biz_id, NOW(), NOW())`
)

const (
	lockCreateVisitor = "qbot:lock:create:visitor"
)

// CreateVisitor 创建访客
func (d *dao) CreateVisitor(ctx context.Context, visitor model.Visitor) error {
	sql := createVisitor
	r, err := d.db.NamedExec(ctx, sql, visitor)
	if err != nil {
		log.ErrorContextf(ctx, "Create visitor error: %+v, sql: %s, params: %+v", err, sql, visitor)
		return err
	}
	id, _ := r.LastInsertId()
	log.DebugContextf(ctx, "Created visitor, id: %d, v: %+v", id, visitor)
	return nil
}

// GetVisitor 获取访客
func (d *dao) GetVisitor(ctx context.Context, botBizID uint64, visitorBizID string) (*model.Visitor, error) {
	sql := getVisitor
	visitor := model.Visitor{}
	params := []any{botBizID, visitorBizID}
	if err := d.db.Get(ctx, &visitor, sql, params...); err != nil {
		if mysql.IsNoRowsError(err) {
			return nil, nil
		}
		log.ErrorContextf(ctx, "Get visitor error: %+v, sql: %s, params: %+v", err, sql, params)
		return nil, err
	}
	return &visitor, nil
}

// MustGetVisitor 获取访客（如果没有则创建）
func (d *dao) MustGetVisitor(ctx context.Context, botID, botBizID uint64, visitorBizID string) (*model.Visitor, error) {
	lock := fmt.Sprintf("%s:%d:%s", lockCreateVisitor, botID, visitorBizID)
	visitor, _, err := d.limiter.TryDo(
		ctx, lock,
		func() error {
			return d.CreateVisitor(ctx, model.Visitor{
				BotID:        botID,
				BotBizID:     botBizID,
				VisitorBizID: visitorBizID,
			})
		},
		func() (bool, any, error) {
			visitor, err := d.GetVisitor(ctx, botBizID, visitorBizID)
			if err != nil {
				return false, nil, err
			}
			return visitor != nil, visitor, nil
		},
	)
	if err != nil {
		return nil, err
	}

	v := visitor.(*model.Visitor)
	if v == nil {
		return nil, pkg.ErrCreateVisitorFailed
	}
	return v, nil
}

// GetVisitorByID 获取访客
func (d *dao) GetVisitorByID(ctx context.Context, botBizID uint64, id uint64) (*model.Visitor, error) {
	sql := getVisitorByID
	visitor := model.Visitor{}
	params := []any{botBizID, id}
	if err := d.db.Get(ctx, &visitor, sql, params...); err != nil {
		if mysql.IsNoRowsError(err) {
			return nil, nil
		}
		log.ErrorContextf(ctx, "Get visitorByID error: %+v, sql: %s, params: %+v", err, sql, params)
		return nil, err
	}
	return &visitor, nil
}
