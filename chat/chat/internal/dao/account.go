package dao

import (
	"context"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// DescribeAccountBalance 获取账户余额
func (d *dao) DescribeAccountBalance(ctx context.Context, corpID uint64, accountTypeList []model.AccountType) (
	*admin.DescribeAccountBalanceRsp, error) {
	accountTypes := make([]*admin.AccountType, 0, len(accountTypeList))
	for _, v := range accountTypeList {
		accountTypes = append(accountTypes, &admin.AccountType{Type: v.Type, ModelName: v.ModelName})
	}
	t0 := time.Now()
	req := &admin.DescribeAccountBalanceReq{
		BizType:      model.BizType,
		CorpType:     model.CorpType,
		CorpId:       strconv.FormatUint(corpID, 10),
		AccountTypes: accountTypes,
	}
	rsp, err := d.apiCli.DescribeAccountBalance(ctx, req)
	clues.AddTrack4RPC(ctx, "DescribeAccountBalance", req, rsp, err, t0)
	if err != nil {
		log.ErrorContextf(ctx, "Describe account balance req:%+v error: %+v", req, err)
		return nil, err
	}
	return rsp, nil
}
