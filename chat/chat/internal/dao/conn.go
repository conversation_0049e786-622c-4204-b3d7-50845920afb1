package dao

import (
	"context"
	"strconv"
	"time"
	"unicode"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/proto/service"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/common/v3/utils"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
	"go.opentelemetry.io/otel/trace"
)

const (
	prefixToken           = "qbot:ws:token:"
	prefixClient          = "qbot:ws:client:"
	prefixBindStaffID     = "qbot:ws:bind:uin:"
	prefixReconnectDialog = "qbot:ws:reconnect:dialog:" // 对话重连断点续传
	prefixReconnectTimes  = "qbot:ws:reconnect:times:"  // 对话重连断点续传次数
)

// keyToken Token Key
func keyToken(token string) string {
	return prefixToken + token
}

// keyClient 客户端信息 Key
func keyClient(clientID string) string {
	return prefixClient + clientID
}

// keyReconnectDialog 对话重连断点续传 Key
func keyReconnectDialog(recordID string) string {
	return prefixReconnectDialog + recordID
}

// keyBindUserID UserID 和 ClientID 绑定关系
func keyBindUserID(staffID uint64, typ model.ConnType) string {
	return prefixBindStaffID + strconv.FormatUint(staffID, 10) + ":" + strconv.Itoa(int(typ))
}

// keyReconnectTimes 对话重连断点续传次数
func keyReconnectTimes(recordID string) string {
	return prefixReconnectTimes + recordID
}

// DeleteWsClient 删除 WS 连接
func (d *dao) DeleteWsClient(ctx context.Context, clientID string) error {
	cli, err := d.GetWsClient(ctx, clientID)
	if err != nil {
		return err
	}
	if cli == nil {
		return nil
	}
	conn, err := d.redis.Pipeline(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "Delete client start redis pipeline error: %+v", err)
		return err
	}
	defer conn.Close()
	if err := conn.Send("DEL", keyClient(clientID)); err != nil {
		log.ErrorContextf(ctx, "Delete client error: %+v, clientID: %s", err, clientID)
		return err
	}
	if err := conn.Send("SREM", keyBindUserID(cli.CorpStaffID, cli.Type), clientID); err != nil {
		log.ErrorContextf(ctx, "Unbind clientID error: %+v, userID: %d, clientID: %s", err, cli.CorpStaffID, clientID)
		return err
	}
	if err = conn.Flush(); err != nil {
		log.ErrorContextf(ctx, "Flush redis pipeline error: %+v", err)
		return err
	}
	return nil
}

// UnbindWsClient 解绑 WS 客户端
func (d *dao) UnbindWsClient(ctx context.Context, clientID string, userID uint64, typ model.ConnType) error {
	key := keyBindUserID(userID, typ)
	if _, err := d.redis.Do(ctx, "SREM", key, clientID); err != nil {
		log.ErrorContextf(ctx, "Unbind client error: %+v, key: %s, clientID: %s", err, key, clientID)
		return err
	}
	return nil
}

// EmitWsUser 推送事件给用户
func (d *dao) EmitWsUser(ctx context.Context, cli *model.Conn, userID uint64, e model.WsEvent) error {
	clientIDs, err := d.GetWsClientIDs(ctx, []uint64{userID}, cli.Type)
	if err != nil {
		return err
	}
	if len(clientIDs) == 0 {
		return nil
	}
	g, ctx := errgroupx.WithContext(ctx)
	g.SetLimit(config.App().Websocket.EmitMaxGoroutine)
	for _, clientID := range clientIDs {
		clientID := clientID
		g.Go(func() error {
			gctx, cancel := context.WithCancel(ctx)
			if err := d.DoEmitWsClient(gctx, clientID, e, cancel); err != nil {
				if model.IsClientNotFoundError(err) {
					_ = d.UnbindWsClient(ctx, clientID, userID, cli.Type)
					return nil
				}
				return err
			}
			return nil
		})
	}
	if err = g.Wait(); err != nil {
		return err
	}
	return nil
}

// EmitWsClient 推送事件
func (d *dao) EmitWsClient(ctx context.Context, cli *model.Conn, clientIDs []string, e model.WsEvent) error {
	if len(clientIDs) == 0 {
		return nil
	}
	g, ctx := errgroupx.WithContext(ctx)
	g.SetLimit(config.App().Websocket.EmitMaxGoroutine)
	for _, clientID := range clientIDs {
		clientID := clientID
		ctx, cancel := context.WithCancel(ctx)
		g.Go(func() error {
			_ = d.DoEmitWsClient(ctx, clientID, e, cancel)
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return err
	}
	return nil
}

// DoEmitWsClient 推送事件到单个客户端
func (d *dao) DoEmitWsClient(ctx context.Context, clientID string, e model.WsEvent,
	cancel context.CancelFunc) (err error) {
	defer func() {
		if model.IsClientNotFoundError(err) {
			metrics.ReportClientNotFound()
		}
		success := helper.When(err == nil, "1", "0")
		metrics.ReportSendEvent(e.Name(), success)
	}()

	e, err = convertToIncrementalEvents(ctx, clientID, e)
	if err != nil {
		return err
	}

	bs, err := jsoniter.Marshal(e)
	if err != nil {
		log.ErrorContextf(ctx, "Marshal ws message error: %+v, msg: %+v", err, e)
		return err
	}
	m := map[string]any{}
	if err := jsoniter.Unmarshal(bs, &m); err != nil {
		log.ErrorContextf(ctx, "Unmarshal ws message error: %+v, msg: %+v", err, bs)
		return err
	}
	// 2.4.0 适配调试信息协议
	if e.Name() == "token_stat" {
		m = convertProceduresMapFieldFormat(m)
	}
	recordID, ok := m["record_id"]
	if ok {
		m["trace_id"] = trace.SpanContextFromContext(ctx).TraceID().String() + " / " + recordID.(string)
	} else {
		m["trace_id"] = trace.SpanContextFromContext(ctx).TraceID().String()
	}
	log.DebugContextf(ctx, "Emit ws message: %s", utils.Any2String(m))
	if err = d.wsCli.Emit(ctx, clientID, e.Name(), m); err == nil {
		return nil
	}
	if model.IsClientNotFoundError(err) { // 客户端不存在，不中断流式，将后续消息存redis
		log.WarnContextf(ctx, "client not found: %s, clientID: %s, event: %s, interface: %s", err, clientID,
			e.Name(), pkg.InterfaceType(ctx))
		// 将后续消息写入redis + 加上过期淘汰
		if isSupportSaveReconnectData(e.Name()) && pkg.InterfaceType(ctx) == "websocket" {
			d.saveReconnectData(ctx, recordID.(string), model.WsMessage{Event: e.Name(), Payload: m})
		} else {
			log.WarnContextf(ctx, "ctx cancel client not found: %s, clientID: %s, event: %s, data: %s", err,
				clientID, e.Name(), utils.Any2String(m))
			if cancel != nil { // 兼容调用者可能不传 cancel
				cancel()
			}
		}
		go func() {
			defer errors.PanicHandler()
			_ = d.DeleteWsClient(trpc.BackgroundContext(), clientID)
		}()
		return err
	}
	if model.IsContextCanceled(err) {
		log.WarnContextf(ctx, "Emit event canceled: %+v, clientID: %s, event: %+v", err, clientID, e)
		return err
	}
	log.ErrorContextf(ctx, "Emit event error: %+v, clientID: %s, event: %+v", err, clientID, e)
	return err
}

// convertProceduresMapFieldFormat 统一回包字段格式
func convertProceduresMapFieldFormat(m map[string]any) map[string]any {
	// 2.4.0 逻辑：只处理任务流程调试信息的字段
	if p, ok := m["procedures"]; ok {
		m["procedures"] = convertKeysToUnderscore(p)
		return m
	}
	return m
}

// convertKeysToUnderscore 转换key为下划线命名
func convertKeysToUnderscore(input any) any {
	switch i := input.(type) {
	case map[string]any:
		newM := make(map[string]any)
		for k, v := range i {
			key := camelToSnake(k)
			newM[key] = convertKeysToUnderscore(v)
		}
		return newM
	case []any:
		for index, value := range i {
			i[index] = convertKeysToUnderscore(value)
		}
		return i
	case struct{}:
		value, err := jsoniter.Marshal(i)
		if err != nil {
			return i
		}
		m := map[string]any{}
		if err := jsoniter.Unmarshal(value, &m); err != nil {
			return i
		}
		newM := make(map[string]any)
		for k, v := range m {
			key := camelToSnake(k)
			newM[key] = convertKeysToUnderscore(v)
		}
		return newM
	default:
		return i
	}
}

// camelToSnake 将驼峰命名转换为下划线命名
func camelToSnake(s string) string {
	var snake string
	runes := []rune(s)
	l := len(runes)

	for i := 0; i < l; i++ {
		if unicode.IsUpper(runes[i]) {
			if i > 0 && ((i+1 < l && unicode.IsLower(runes[i+1])) || unicode.IsLower(runes[i-1])) {
				snake += "_"
			}
			snake += string(unicode.ToLower(runes[i]))
		} else {
			snake += string(runes[i])
		}
	}
	return snake
}

// SaveWsToken 保存 WS Token
func (d *dao) SaveWsToken(ctx context.Context, token string, cli *model.Conn) error {
	bs, err := jsoniter.Marshal(cli)
	if err != nil {
		log.ErrorContextf(ctx, "Marshal client error: %+v, cli: %+v", err, cli)
		return err
	}
	ttl := config.App().Websocket.TokenTTL
	log.InfoContextf(ctx, "I|SaveWsToken %s,ttl: %v ", token, ttl)
	if _, err := redis.String(d.redis.Do(ctx, "SETEX", keyToken(token), ttl, bs)); err != nil {
		log.ErrorContextf(ctx, "Save ws token error: %+v, token: %s, cli: %+v, ttl: %d", err, token, cli, ttl)
		return err
	}
	return nil
}

// AuthWsClient 认证 WS 连接
func (d *dao) AuthWsClient(ctx context.Context, token, clientID string) error {
	s, err := redis.String(d.redis.Do(ctx, "GET", keyToken(token)))
	if err != nil && err != redis.ErrNil {
		log.ErrorContextf(ctx, "Get client by token error: %+v, token: %s", err, token)
		return err
	}
	if err == redis.ErrNil {
		log.WarnContextf(ctx, "Auth client error: token invalid, token: %s", token)
		return pkg.ErrAuthTokenFailed
	}
	cli := &model.Conn{}
	if err := jsoniter.UnmarshalFromString(s, &cli); err != nil {
		log.ErrorContextf(ctx, "Unmarshal ws client error: %+v, token: %s, data: %s", err, token, s)
		return err
	}

	conn, err := d.redis.Pipeline(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "Auth client start redis pipeline error: %+v", err)
		return err
	}
	defer conn.Close()
	if err := conn.Send("RENAME", keyToken(token), keyClient(clientID)); err != nil {
		log.ErrorContextf(ctx, "Auth client error: %+v, token: %s, clientID: %s", err, token, clientID)
		return err
	}
	ttl := config.App().Websocket.ClientTTL
	if err := conn.Send("EXPIRE", keyClient(clientID), ttl); err != nil {
		log.ErrorContextf(ctx, "Renew client error: %+v, clientID: %s, ttl: %d", err, clientID, ttl)
		return err
	}
	if err := conn.Send("SADD", keyBindUserID(cli.CorpStaffID, cli.Type), clientID); err != nil {
		log.ErrorContextf(ctx, "Bind client error: %+v, uin: %d, clientID: %s", err, cli.CorpStaffID, clientID)
		return err
	}
	if err = conn.Flush(); err != nil {
		log.ErrorContextf(ctx, "Flush redis pipeline error: %+v", err)
		return err
	}
	return nil
}

// RenewWsClient WS 客户端续期
func (d *dao) RenewWsClient(ctx context.Context, clientID string) error {
	ttl := config.App().Websocket.ClientTTL
	if _, err := redis.Int64(d.redis.Do(ctx, "EXPIRE", keyClient(clientID), ttl)); err != nil {
		log.ErrorContextf(ctx, "Renew client error: %+v, clientID: %s, ttl: %d", err, clientID, ttl)
		return err
	}
	return nil
}

// GetWsClient 获取已认证的 WS 客户端信息
func (d *dao) GetWsClient(ctx context.Context, clientID string) (*model.Conn, error) {
	t0 := time.Now()
	// log.InfoContextf(ctx, "REQ|GetWsClient %s", clientID)
	s, err := redis.String(d.redis.Do(ctx, "GET", keyClient(clientID)))
	if err != nil && err != redis.ErrNil {
		log.ErrorContextf(ctx, "Get ws client error: %+v, clientID: %s", err, clientID)
		return nil, err
	}

	if err == redis.ErrNil {
		return nil, nil
	}

	cli := &model.Conn{}
	if err := jsoniter.UnmarshalFromString(s, &cli); err != nil {
		log.ErrorContextf(ctx, "Unmarshal ws client error: %+v, clientID: %s, data: %s", err, clientID, s)
		return nil, err
	}

	cli.ClientID = clientID
	log.InfoContextf(ctx, "RESP|GetWsClient %+v %s", cli, time.Since(t0))
	return cli, nil
}

// GetWsClientIDs 获取 UserID 对应的客户端 ID
func (d *dao) GetWsClientIDs(ctx context.Context, userIDs []uint64, typ model.ConnType) ([]string, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}
	t0 := time.Now()
	m := map[uint64][]string{}
	for _, userID := range userIDs {
		clientIDs, err := redis.Strings(d.redis.Do(ctx, "SMEMBERS", keyBindUserID(userID, typ)))
		if err != nil && err != redis.ErrNil {
			log.ErrorContextf(ctx, "Get clientIDs error: %+v, userID: %d", err, userID)
			return nil, err
		}
		m[userID] = clientIDs
	}

	var clientIDs []string
	for _, v := range m {
		clientIDs = append(clientIDs, v...)
	}
	log.InfoContextf(ctx, "R|GetWsClientIDs userIDs: %v, type: %d, clientIDs: %v, %s",
		userIDs, typ, clientIDs, time.Since(t0))
	return clientIDs, nil
}

// RegisterSSEClient 注册 SSE 客户端
func (d *dao) RegisterSSEClient(ctx context.Context, cli *model.Conn) error {
	bs, err := jsoniter.Marshal(cli)
	if err != nil {
		log.ErrorContextf(ctx, "Marshal client error: %+v, cli: %+v", err, cli)
		return err
	}
	conn, err := d.redis.Pipeline(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "Register client start redis pipeline error: %+v", err)
		return err
	}
	defer conn.Close()
	ttl := config.App().Websocket.ClientTTL
	if err := conn.Send("SETEX", cli.ClientID, ttl, bs); err != nil {
		log.ErrorContextf(ctx, "Save client error: %+v, clientID: %s, cli: %+v, ttl: %d", err, cli.ClientID, cli, ttl)
		return err
	}
	if err := conn.Send("SADD", keyBindUserID(cli.CorpStaffID, cli.Type), cli.ClientID); err != nil {
		log.ErrorContextf(ctx, "Bind client error: %+v, uin: %d, clientID: %s", err, cli.CorpStaffID, cli.ClientID)
		return err
	}
	if err = conn.Flush(); err != nil {
		log.ErrorContextf(ctx, "Flush redis pipeline error: %+v", err)
		return err
	}
	return nil
}

// DeleteSSEClient 关闭 SSE 连接
func (d *dao) DeleteSSEClient(ctx context.Context, clientID string) (err error) {
	if config.App().SSE.EnableDeferClientClose {
		log.DebugContextf(ctx, "clientID: %s SSE.DeferClientCloseTime: %+v",
			clientID, config.App().SSE.DeferClientCloseTime)
		time.Sleep(config.App().SSE.DeferClientCloseTime)
	}
	for i := 0; i < 3; i++ { // 关闭最多重试 3 次
		_, err = d.wsCli.Close(ctx, &service.CloseReq{ClientId: clientID})
		if err == nil || model.IsClientNotFoundError(err) {
			go func() {
				defer errors.PanicHandler()
				_ = d.DeleteWsClient(trpc.BackgroundContext(), clientID)
			}()
			log.InfoContextf(ctx, "Delete SSE client ok: %s", clientID)
			return nil
		}
		if model.IsContextCanceled(err) {
			log.WarnContextf(ctx, "Delete SSE client canceled: %v, clientID: %s", err, clientID)
			return nil
		}
		log.ErrorContextf(ctx, "Delete SSE client error: %+v, clientID: %s", err, clientID)
	}
	log.InfoContextf(ctx, "Delete SSE client ok: %s", clientID)
	return err
}

// GetReconnectData 获取重连恢复的数据
func (d *dao) GetReconnectData(ctx context.Context, recordID string) (*model.WsMessage, error) {
	data := &model.WsMessage{}
	key := keyReconnectDialog(recordID)
	b, err := redis.Bytes(d.redis.Do(ctx, "GET", key))
	if err != nil {
		log.WarnContextf(ctx, "GetReconnectData error: %+v, recordID: %s", err, recordID)
		return nil, err
	}
	err = jsoniter.Unmarshal(b, data)
	if err != nil {
		log.ErrorContextf(ctx, "GetReconnectData Unmarshal error: %+v, recordID: %s", err, recordID)
		return data, err
	}
	return data, nil
}

// SetReconnectTimes 保存重连次数
func (d *dao) SetReconnectTimes(ctx context.Context, recordID string, times int) error {
	ttl := config.App().Reconnect.ReconnectDataTTL
	key := keyReconnectTimes(recordID)
	log.InfoContextf(ctx, "reconnect times save key:%s,ttl: %v", key, ttl)
	if _, err := redis.String(d.redis.Do(ctx, "SETEX", key, ttl, times)); err != nil {
		log.ErrorContextf(ctx, "Save reconnect times error: %+v, key: %s", err, key)
		return err
	}
	return nil
}

// GetReconnectTimes 获取重连次数
func (d *dao) GetReconnectTimes(ctx context.Context, recordID string) int {
	key := keyReconnectTimes(recordID)
	v, err := redis.Int(d.redis.Do(ctx, "GET", key))
	if err != nil {
		log.WarnContextf(ctx, "Get reconnect times error: %+v, key: %s", err, key)
		return 0
	}
	log.InfoContextf(ctx, "Get reconnect times ok: %s, v: %d", key, v)
	return v
}

// saveReconnectData 保存需要重连恢复的数据
func (d *dao) saveReconnectData(ctx context.Context, recordID string, data model.WsMessage) error {
	ttl := config.App().Reconnect.ReconnectDataTTL
	key := keyReconnectDialog(recordID)
	b, _ := jsoniter.Marshal(data)
	log.InfoContextf(ctx, "reconnect data save key:%s,ttl: %v", key, ttl)
	if _, err := redis.String(d.redis.Do(ctx, "SETEX", key, ttl, b)); err != nil {
		log.ErrorContextf(ctx, "Save reconnect data error: %+v, key: %s", err, key)
		return err
	}
	return nil
}

// isSupportSaveReconnectData 是否支持保存重连恢复的数据
func isSupportSaveReconnectData(event string) bool {
	for _, val := range config.App().Reconnect.SupportEvents {
		if val == event {
			return true
		}
	}
	return false
}
