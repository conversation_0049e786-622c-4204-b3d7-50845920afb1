package dao

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// GetIntent 获取意图
func (d *dao) GetIntent(ctx context.Context, policyID uint32, content string) (*admin.GetIntentRsp, error) {
	if content == "" {
		return nil, errs.Newf(errs.Code(pkg.ErrIntentNotFound), "content is empty")
	}
	req := &admin.GetIntentReq{PolicyId: policyID, Name: content}
	t0 := time.Now()
	rsp, err := d.apiCli.GetIntent(ctx, req)
	clues.AddTrack4RPC(ctx, "apiCli.GetIntent", req, rsp, err, t0)
	if err != nil {
		log.ErrorContextf(ctx, "Get intent error: %+v, req: %+v", err, req)
		return nil, err
	}
	return rsp, nil
}
