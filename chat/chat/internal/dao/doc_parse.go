package dao

import (
	"context"
	"errors"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	parse "git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"github.com/jmoiron/sqlx"
	"google.golang.org/protobuf/encoding/protojson"
)

const (
	fieldMsgDocRecord = `
		id, bot_biz_id, session_id,record_id, file_url, doc_id, cos_id, 
		doc_name,model_name,summary,is_deleted, create_time, update_time
	`
)

const (
	insertMsgDocRecord = `
	INSERT INTO t_msg_doc_record (` + fieldMsgDocRecord + `) VALUES (
		null, :bot_biz_id, :session_id,:record_id, :file_url, :doc_id, :cos_id, 
		:doc_name,:model_name,:summary,:is_deleted, NOW(), NOW()
	)`

	updateMsgDocInfo = `UPDATE t_msg_doc_record SET doc_id = ?,summary=? WHERE bot_biz_id = ? and cos_id = ?`

	// 筛选BotBIzID
	selectDistinctBotBizID = `SELECT
  									DISTINCT bot_biz_id
							FROM
							  t_msg_doc_record
							WHERE
							  is_deleted = 0
							  and create_time < ?
							limit
							  100`

	// 筛选sessionID
	selectSessionID = `SELECT DISTINCT session_id FROM t_msg_doc_record 
                           WHERE bot_biz_id = ? and is_deleted = 0 and create_time < ? limit 100`

	// 更新删除状态
	updateDeleteStatus = `UPDATE t_msg_doc_record SET is_deleted = 1 
                        WHERE bot_biz_id = ? and session_id = ? and create_time < ?`

	// 获取文档ID
	selectDocID = `SELECT doc_id FROM t_msg_doc_record 
              WHERE bot_biz_id = ? and session_id = ? and is_deleted = 0 and create_time < ? limit 100`
	getMsgDocRecord = `
    	SELECT 
    		%s
    	FROM
    		t_msg_doc_record
    	WHERE 
    		is_deleted = 0 AND doc_id IN (%s) AND model_name = ?
    `
	getMsgDocSummary = `
    	SELECT 
    		%s
    	FROM
    		t_msg_doc_record
    	WHERE 
    		is_deleted = 0 AND doc_id IN (%s) 
    `
)

// CreateMsgDocRecord 创建消息文档记录
func (d *dao) CreateMsgDocRecord(ctx context.Context, record model.MsgDocRecord) (int64, error) {
	var id int64
	if err := d.db.Transactionx(ctx, func(tx *sqlx.Tx) error {
		sql := insertMsgDocRecord
		r, err := tx.NamedExec(sql, record)
		if err != nil {
			log.ErrorContextf(ctx, "Insert msg doc record error: %+v, sql: %s, params: %+v", err, sql, record)
			return err
		}

		id, _ = r.LastInsertId()
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "Create msg doc record transaction error: %+v", err)
		return 0, err
	}

	return id, nil
}

// UpdateDocInfo 更新Doc信息
func (d *dao) UpdateDocInfo(ctx context.Context, docID, botBizID, cosID uint64, summary string) error {
	sql := updateMsgDocInfo
	params := []any{docID, summary, botBizID, cosID}
	if _, err := d.db.Exec(ctx, sql, params...); err != nil {
		log.ErrorContextf(ctx, "Update doc info error: %+v, sql: %s, params: %+v", err, sql, params)
		return err
	}
	return nil
}

// GetDistinctBotBizID 获取BotBizID
func (d *dao) GetDistinctBotBizID(ctx context.Context) ([]uint64, error) {
	var botBizIDs []uint64

	startTime := time.Now().Add(-24 * time.Hour)
	log.DebugContextf(ctx, "Get distinct bot biz id start: %s", startTime)
	params := []any{startTime}
	if err := d.db.Select(ctx, &botBizIDs, selectDistinctBotBizID, params...); err != nil {
		log.ErrorContextf(ctx, "Get distinct bot biz id error: %+v, sql: %s, params: %+v", err, selectDistinctBotBizID,
			params)
		return nil, err
	}
	log.InfoContextf(ctx, "Get distinct bot biz id success: %v", botBizIDs)
	return botBizIDs, nil

}

// GetSessionID 获取SessionID
func (d *dao) GetSessionID(ctx context.Context, botBizID uint64) ([]string, error) {
	var sessionIDs []string
	startTime := time.Now().Add(-24 * time.Hour)
	params := []any{botBizID, startTime}
	if err := d.db.Select(ctx, &sessionIDs, selectSessionID, params...); err != nil {
		log.ErrorContextf(ctx, "Get botsession id error: %+v, sql: %s, params: %+v", err, selectSessionID, params)
		return nil, err
	}
	log.InfoContextf(ctx, "Get botsession id success: %v", sessionIDs)
	return sessionIDs, nil
}

// GetDocIDBySessionID 获取DocID
func (d *dao) GetDocIDBySessionID(ctx context.Context, botBizID uint64, sessionID string) ([]uint64, error) {
	var docIDs []string
	startTime := time.Now().Add(-24 * time.Hour)
	params := []any{botBizID, sessionID, startTime}
	if err := d.db.Select(ctx, &docIDs, selectDocID, params...); err != nil {
		log.ErrorContextf(ctx, "Get doc id error: %+v, sql: %s, params: %+v", err, selectDocID, params)
		return nil, err
	}
	log.InfoContextf(ctx, "Get doc id success: %v", docIDs)
	res := make([]uint64, 0, len(docIDs))
	for _, v := range docIDs {
		if len(v) < 2 {
			continue // 有些是0
		}
		id, _ := strconv.ParseUint(v, 10, 64)
		res = append(res, id)
	}
	if len(docIDs) > 0 && len(res) == 0 {
		_ = d.UpdateDeleteStatus(ctx, botBizID, sessionID)
	}

	return res, nil
}

// UpdateDeleteStatus 更新删除状态
func (d *dao) UpdateDeleteStatus(ctx context.Context, botBizID uint64, sessionID string) error {
	startTime := time.Now().Add(-24 * time.Hour)
	params := []any{botBizID, sessionID, startTime}
	if _, err := d.db.Exec(ctx, updateDeleteStatus, params...); err != nil {
		log.ErrorContextf(ctx, "Update delete status error: %+v, sql: %s, params: %+v",
			err, updateDeleteStatus, params)
		return err
	}
	log.InfoContextf(ctx, "Update delete status success, botBizID: %d, sessionID: %s", botBizID, sessionID)
	return nil
}

// GetMsgDocRecord 获取文档解析记录
func (d *dao) GetMsgDocRecord(ctx context.Context, docIDs []uint64, modelName string) (
	[]*model.MsgDocRecord, error) {
	var records []*model.MsgDocRecord
	querySQL := fmt.Sprintf(getMsgDocRecord, fieldMsgDocRecord, placeholder(len(docIDs)))
	args := make([]any, 0)
	for _, docID := range docIDs {
		args = append(args, docID)
	}
	args = append(args, modelName)
	if err := d.db.QueryToStructs(ctx, &records, querySQL, args...); err != nil {
		log.ErrorContextf(ctx, "查询文档解析记录 sql:%s args:%+v err:%+v", querySQL, args, err)
		return nil, err
	}
	return records, nil
}

// GetMsgDocSummary 获取文档解析后的摘要, 不传model_name，避免查不到的情况（这里只是Query改写用）
func (d *dao) GetMsgDocSummary(ctx context.Context, docIDs []uint64) (map[string]string, error) {
	var records []*model.MsgDocRecord
	querySQL := fmt.Sprintf(getMsgDocSummary, fieldMsgDocRecord, placeholder(len(docIDs)))
	args := make([]any, 0)
	for _, docID := range docIDs {
		args = append(args, docID)
	}
	if err := d.db.QueryToStructs(ctx, &records, querySQL, args...); err != nil {
		log.ErrorContextf(ctx, "GetMsgDocSummary sql:%s args:%+v err:%+v", querySQL, args, err)
		return nil, err
	}
	res := make(map[string]string)
	for _, v := range records {
		res[v.DocID] = v.Summary
	}
	return res, nil
}

// StreamSaveDoc 流式文档解析
func (d *dao) StreamSaveDoc(ctx context.Context,
	req *parse.StreamSaveDocReq, ch chan *parse.StreamSaveDocRsp, signal chan int) (err error) {
	log.InfoContextf(ctx, "StreamSaveDoc request is: %s", helper.Object2String(req)) // 入口打请求参数日志
	last := &parse.StreamSaveDocRsp{}
	// 结束的处理
	defer close(ch)
	defer func() {
		if last != nil && !last.GetIsFinal() {
			last.IsFinal = true
			ch <- last
		}
	}()
	opts := []client.Option{WithTrpcSelector()}
	cli, err := d.knowledgeAPICli.StreamSaveDoc(ctx, opts...) // 定义流式客户端
	if err != nil {
		log.ErrorContextf(ctx, "New knowledge stream client error: %+v", err)
		return err
	}
	defer func() {
		if err := cli.CloseSend(); err != nil {
			log.ErrorContextf(ctx, "StreamSaveDoc CloseSend error: %+v", err)
		} // 关闭发送
		log.InfoContextf(ctx, "StreamSaveDoc CloseSend success.")
	}()
	idx, start := 0, time.Now()
	if err := cli.Send(req); err != nil {
		log.ErrorContextf(ctx, "Send knowledge stream request error: %+v, req: %+v", err, req)
		return err
	}
	for {
		select {
		case <-ctx.Done():
			req.ReqType = parse.StreamSaveDocReq_TASK_CANCEL
			_ = cli.Send(req)
			return nil
		case <-signal:
			close(signal)
			req.ReqType = parse.StreamSaveDocReq_TASK_CANCEL
			err = cli.Send(req)
			log.WarnContextf(ctx, "StreamSaveDoc send cancel: %+v, req: %s", err, helper.Object2String(req))
			return nil
		default:
			rsp, err := cli.Recv()
			if idx%10 == 0 { // 每10条 打一条日志
				log.InfoContextf(ctx, "RESP|StreamSaveDoc[%d] rsp: %s, cost: %s",
					idx, helper.Object2String(rsp), time.Since(start))
			}
			if err != nil {
				if errors.Is(err, context.Canceled) || strings.Contains(errs.Msg(err), "context canceled") {
					req.ReqType = parse.StreamSaveDocReq_TASK_CANCEL // 取消时的处理
					err = cli.Send(req)
					log.WarnContextf(ctx, "StreamSaveDoc send cancel: %+v, req: %s", err, helper.Object2String(req))
					return nil
				}
				log.ErrorContextf(ctx, "StreamSaveDoc error: %+v, req: %+v", err, req)
				return err
			}
			if rsp.GetTaskRsp().Status == parse.TaskRsp_FAILED { // 错误码处理
				ch <- rsp
				last = rsp
				log.WarnContextf(ctx, "doc parse error: %+v, req: %s, rsp: %s",
					err, helper.Object2String(req), helper.Object2String(rsp))
				return err
			}
			lidx := strconv.Itoa(idx / 10) // 上报指标
			isFirst, isFinal := helper.When(idx == 0, "1", "0"), helper.When(rsp.GetIsFinal(), "1", "0")
			metrics.ReportLLMLatency(time.Since(start).Seconds(), lidx, isFirst, isFinal)
			ms := time.Since(start).Milliseconds()
			metrics.ReportTime("StreamSaveDoc", idx, ms)
			idx++
			ch <- rsp
			last = rsp
			if rsp.GetIsFinal() {
				return nil
			}
		}
	}
}

// GetDocFullText 获取文档全文
func (d *dao) GetDocFullText(ctx context.Context, req *parse.GetDocFullTextReq) (rsp *parse.GetDocFullTextRsp,
	err error) {
	t0 := time.Now()
	log.InfoContextf(ctx, "get doc fulltext req: %s", helper.Object2String(req))
	rsp, err = d.knowledgeAPICli.GetDocFullText(ctx, req)
	clues.AddTrack4RPC(ctx, "knowledge.GetDocFullText", req, rsp, err, t0)
	if err != nil {
		log.ErrorContextf(ctx, "get doc fulltext error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "get doc fulltext rsp: %s", helper.Object2String(rsp))
	return rsp, nil
}

// SearchRealtime 搜索实时文档
func (d *dao) SearchRealtime(ctx context.Context, req *parse.SearchRealtimeReq) (docs []*parse.SearchRealtimeRsp_Doc,
	err error) {
	t0 := time.Now()
	log.InfoContextf(ctx, "search realtime doc req: %s", helper.Object2String(req))
	rsp, err := d.knowledgeAPICli.SearchRealtime(ctx, req)
	clues.AddTrack4RPC(ctx, "knowledge.SearchRealtime", req, rsp, err, t0)
	if err != nil {
		log.ErrorContextf(ctx, "search realtime doc error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "search realtime doc rsp: %s", helper.Object2String(rsp))
	if rsp.GetDocs() == nil {
		return make([]*parse.SearchRealtimeRsp_Doc, 0), nil
	}
	return rsp.GetDocs(), nil
}

// DeleteRealtimeDoc 删除实时文档
func (d *dao) DeleteRealtimeDoc(ctx context.Context, req *parse.DeleteRealtimeDocReq) (rsp *parse.DeleteRealtimeDocRsp,
	err error) {
	t0 := time.Now()
	log.InfoContextf(ctx, "delete realtime doc req: %s", helper.Object2String(req))
	rsp, err = d.knowledgeAPICli.DeleteRealtimeDoc(ctx, req)
	clues.AddTrack4RPC(ctx, "knowledge.DeleteRealtimeDoc", req, rsp, err, t0)
	if err != nil {
		log.ErrorContextf(ctx, "delete realtime doc error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "delete realtime doc rsp: %s", helper.Object2String(rsp))
	return rsp, nil
}

// mockDocSummaryStream TODO
func mockDocSummaryStream(reply string) []*parse.GetDocSummaryRsp {
	rsps := make([]*parse.GetDocSummaryRsp, 0)
	step := 5
	if config.App().StreamOutputBatchSize > 0 {
		step = config.App().StreamOutputBatchSize
	}
	replyRune := []rune(reply)
	length := len(replyRune)
	var replyChunk []string
	start := 0
	for i := 0; i < length; i += step {
		end := i + step
		if end > length {
			end = length
		}
		replyChunk = append(replyChunk, string(replyRune[start:end]))
	}
	// 假流式输出
	chunkLength := len(replyChunk)
	for i, chunk := range replyChunk {
		finished := false
		if i == chunkLength-1 {
			finished = true
		}
		rsps = append(rsps, &parse.GetDocSummaryRsp{DocSummary: chunk, IsFinal: finished})
	}
	return rsps
}

func (d *dao) fillDocSummary(ctx context.Context, req *parse.GetDocSummaryReq) ([]*parse.GetDocSummaryRsp, error) {
	if len(req.GetFileInfos()) == 0 {
		return nil, nil
	}
	docIDs := make([]uint64, 0)
	for _, v := range req.GetFileInfos() {
		docIDs = append(docIDs, v.GetDocId())
	}
	msgDocRecord, err := d.GetMsgDocRecord(ctx, docIDs, req.GetModelName())
	if err != nil {
		return nil, nil
	}
	if len(msgDocRecord) == 0 {
		return nil, nil
	}
	var summary string
	for _, v := range msgDocRecord {
		if v.Summary == "" { // 如果没有摘要，则走实时的文档摘要总结
			return nil, errors.New("no summary")
		}
		summary += v.Summary + "\n\n"
	}
	return mockDocSummaryStream(summary), nil
}

func (d *dao) GetDocSummary(ctx context.Context, req *parse.GetDocSummaryReq,
	chReply chan *parse.GetDocSummaryRsp) (err error) {
	last := &parse.GetDocSummaryRsp{}
	defer close(chReply)
	defer func() {
		if last != nil && !last.IsFinal {
			last.IsFinal = true
			chReply <- last
		}
		if err != nil {
			if errors.Is(err, context.Canceled) {
				log.WarnContextf(ctx, "GetDocSummary canceled: %v", err)
				return
			}
			log.ErrorContext(ctx, "GetDocSummary error: %v", err)
			return
		}
	}()
	if len(req.GetQuery()) == 0 || helper.IsQueryOnlyImage(req.GetQuery()) {
		docSummaryRsps, err := d.fillDocSummary(ctx, req)
		if err == nil && len(docSummaryRsps) > 0 {
			for _, v := range docSummaryRsps {
				last = v
				chReply <- last
			}
			return nil
		}
	}
	b, _ := protojson.Marshal(req)
	log.InfoContextf(ctx, "GetDocSummary request is: %s", string(b))
	idx, start := 0, time.Now()
	rsp, err := d.knowledgeAPICli.GetDocSummary(ctx, req)
	clues.AddTrack4RPC(ctx, "knowledge.GetDocSummary", req, rsp, err, start)
	if err != nil {
		log.ErrorContextf(ctx, "get doc summary error: %+v, req: %+v", err, req)
		return err
	}
	defer func() {
		if err := rsp.CloseSend(); err != nil {
			log.WarnContextf(ctx, "GetDocSummary CloseSend error: %+v", err)
		} // 关闭发送
		log.InfoContextf(ctx, "GetDocSummary CloseSend success.")
	}()
	for {
		select {
		case <-ctx.Done():
			log.WarnContextf(ctx, "ctx.Done")
			return nil
		default:
			last, err = rsp.Recv()
			if err == io.EOF {
				log.InfoContextf(ctx, "GetDocSummary recv io.EOF")
				return nil
			}
			if err != nil {
				log.ErrorContextf(ctx, "GetDocSummary recv error: %+v", err)
				return err
			}
			if last == nil {
				continue
			}
			chReply <- last

			b, _ = protojson.Marshal(last)
			if idx%10 == 0 || last.IsFinal { // 每10条 打一条日志
				log.InfoContextf(ctx, "get doc summary reply [index:%d] is: %s, time cost: %s",
					idx, string(b), time.Since(start))
			}
			ms := time.Since(start).Milliseconds()
			metrics.ReportTime("get_doc_summary_ms", idx, ms)
			idx++
			if (last != nil && last.IsFinal) || err == io.EOF {
				return nil
			}
			if err != nil {
				if strings.Contains(err.Error(), "context canceled") {
					log.WarnContextf(ctx, "GetDocSummary canceled: %v", err)
					err = nil
					return nil // todo 业务怎么处理这种情况 需要考虑下
				}
				return err
			}
		}
	}
}

// PrefixFileKnowledge FileKnowledge Redis前缀
const PrefixFileKnowledge = "qbot:chat:file_knowledge"

// PrefixFileKnowledgeCount FileKnowledgeCount Redis前缀
const PrefixFileKnowledgeCount = "qbot:chat:count_file_knowledge"

// PrefixRealTimeFileHistory 实时文档历史记录 Redis前缀
const PrefixRealTimeFileHistory = "qbot:chat:history_realtime_file"

// keyFileKnowledgeStatus FileKnowledge 状态 key
func keyFileKnowledgeStatus(sessionID string) string {
	return PrefixFileKnowledge + ":" + sessionID
}

// keyFileKnowledgeCount FileKnowledgeCount key
func keyFileKnowledgeCount(sessionID string) string {
	return PrefixFileKnowledgeCount + ":" + sessionID
}

// KeyRealTimeFileHistory 实时文档历史记录 key
func KeyRealTimeFileHistory(sessionID string) string {
	return PrefixRealTimeFileHistory + ":" + sessionID

}

// SetFileKnowledgeStatus 设置FileKnowledge状态
func (d *dao) SetFileKnowledgeStatus(ctx context.Context, sessionID string, flag bool) error {
	_, err := d.redis.Do(ctx, "SET", keyFileKnowledgeStatus(sessionID), flag, "EX", 24*60*60)
	if err != nil {
		log.ErrorContextf(ctx, "SetFileKnowledgeStatus error: %+v, sessionID: %s", err, sessionID)
		return err
	}
	_, err = d.redis.Do(ctx, "SET", keyFileKnowledgeCount(sessionID), 1, "EX", 24*60*60)
	if err != nil {
		log.ErrorContextf(ctx, "SetFileKnowledgeCount error: %+v, sessionID: %s", err, sessionID)
		return err
	}
	log.InfoContextf(ctx, "SetFileKnowledgeStatusAndCount success, sessionID: %s, flag: %t", sessionID, flag)
	return nil
}

// SetFileKnowledgeCount 设置FileKnowledge状态
func (d *dao) SetFileKnowledgeCount(ctx context.Context, sessionID string) error {
	_, err := d.redis.Do(ctx, "INCR", keyFileKnowledgeCount(sessionID))
	if err != nil {
		log.ErrorContextf(ctx, "SetFileKnowledgeCount error: %+v, sessionID: %s", err, sessionID)
		return err
	}
	log.InfoContextf(ctx, "SetFileKnowledgeCount success, sessionID: %s", sessionID)
	return nil
}

// GetFileKnowledgeCount 获取FileKnowledgeCount
func (d *dao) GetFileKnowledgeCount(ctx context.Context, sessionID string) (int, error) {
	count, err := redis.Int(d.redis.Do(ctx, "GET", keyFileKnowledgeCount(sessionID)))
	if err != nil {
		log.ErrorContextf(ctx, "GetFileKnowledgeCount error: %+v, sessionID: %s", err, sessionID)
		return 0, err
	}
	log.InfoContextf(ctx, "GetFileKnowledgeCount success, sessionID: %s, count: %d", sessionID, count)
	return count, nil
}

// IsInFileKnowledge 是否在FileKnowledge中
func (d *dao) IsInFileKnowledge(ctx context.Context, sessionID string, historyLimit uint32) bool {
	rsp, err := redis.Bool(d.redis.Do(ctx, "GET", keyFileKnowledgeStatus(sessionID)))
	if err != nil || !rsp {
		return false
	}
	log.InfoContextf(ctx, "IsInFileKnowledge success, sessionID: %s, flag: %t", sessionID, rsp)
	count, err := redis.Int(d.redis.Do(ctx, "GET", keyFileKnowledgeCount(sessionID)))
	if err != nil {
		return false
	}
	log.InfoContextf(ctx, "GetKeyFileKnowledgeCount success, sessionID: %s, count: %d", sessionID, count)
	if count >= int(historyLimit) {
		_ = d.SetFileKnowledgeStatus(ctx, sessionID, false)
		// 删除文档
		return false
	}
	return rsp
}

// SetRealTimeFileHistory 设置实时文档历史记录
func (d *dao) SetRealTimeFileHistory(ctx context.Context, sessionID string, history string) error {
	// 使用d.redis
	_, err := d.redis.Do(ctx, "SET", KeyRealTimeFileHistory(sessionID), history, "EX", 24*60*60)
	if err != nil {
		log.WarnContextf(ctx, "SetRealTimeFileHistory error: %+v, sessionID: %s", err, sessionID)
		return err
	}
	log.InfoContextf(ctx, "SetRealTimeFileHistory success, sessionID: %s, history: %s", sessionID, history)

	// cmd := d.goRedis.Set(ctx, KeyRealTimeFileHistory(sessionID), history, 24*60*60*time.Second)
	// if cmd.Err() != nil {
	//	log.WarnContextf(ctx, "SetRealTimeFileHistory error: %+v, sessionID: %s", cmd.Err(), sessionID)
	//	return cmd.Err()
	// }
	// log.InfoContextf(ctx, "SetRealTimeFileHistory success, sessionID: %s, history: %s", sessionID, history)
	return nil
}

// GetRealTimeFileHistory 获取实时文档历史记录
func (d *dao) GetRealTimeFileHistory(ctx context.Context, sessionID string) (string, error) {
	// 使用d.redis
	history, err := redis.String(d.redis.Do(ctx, "GET", KeyRealTimeFileHistory(sessionID)))
	if err != nil {
		log.WarnContextf(ctx, "GetRealTimeFileHistory error: %+v, sessionID: %s", err, sessionID)
		return "", err
	}
	log.InfoContextf(ctx, "GetRealTimeFileHistory success, sessionID: %s, history: %s", sessionID, history)
	// 使用goRedis
	// history, err := d.goRedis.Get(ctx, KeyRealTimeFileHistory(sessionID)).Result()
	// if err != nil {
	//	log.WarnContextf(ctx, "GetRealTimeFileHistory error: %+v, sessionID: %s", err, sessionID)
	//	return "", err
	// }
	// log.InfoContextf(ctx, "GetRealTimeFileHistory success, sessionID: %s, history: %s", sessionID, history)
	return history, nil
}
