// Package dao TODO
// @Author: halelv
// @Date: 2024/7/11 15:21
package dao

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"github.com/jmoiron/sqlx"
)

const (
	fieldMsgRecordTokenStat = `
		id, record_id, trace_id, used_count, free_count, order_count, status_summary, 
		status_summary_title, elapsed, token_count, procedures, agent_thought, is_deleted, create_time, update_time
	`

	createMsgRecordTokenStat = `
		INSERT INTO 
		    t_msg_record_token_stat (%s)
		VALUES 
		    (null, :record_id, :trace_id, :used_count, :free_count, :order_count, :status_summary, 
		     :status_summary_title, :elapsed, :token_count, :procedures, :agent_thought, :is_deleted, 
		     :create_time, :update_time)
	`

	getMsgRecordTokenStat = `
		SELECT
			%s
		FROM
		    t_msg_record_token_stat
		WHERE
		    record_id = ? AND is_deleted = 0
	`

	deleteMsgRecordTokenStat = `
	DELETE FROM t_msg_record_token_stat WHERE record_id IN (%s)`
)

// createMsgRecordTokenStatWithTx 创建消息记录统计信息
func (d *dao) createMsgRecordTokenStatWithTx(ctx context.Context, tx *sqlx.Tx, stat *model.MsgRecordTokenStat) (
	int64, error) {
	log.InfoContextf(ctx, "CreateMsgRecordTokenStat|stat:%+v", stat)
	if stat == nil {
		return 0, nil
	}
	var id int64
	sql := fmt.Sprintf(createMsgRecordTokenStat, fieldMsgRecordTokenStat)
	r, err := tx.NamedExec(sql, stat)
	if err != nil {
		log.ErrorContextf(ctx, "CreateMsgRecordTokenStat|error: %+v, sql: %s, params: %+v", err, sql, stat)
		return 0, err
	}
	id, _ = r.LastInsertId()
	return id, nil
}

// GetMsgRecordTokenStatByStatID 通过ID获取消息记录统计信息
func (d *dao) GetMsgRecordTokenStatByStatID(ctx context.Context, recordID string) (
	*model.MsgRecordTokenStat, error) {
	log.InfoContextf(ctx, "GetMsgRecordTokenStatByStatID|recordID:%s", recordID)
	sql := fmt.Sprintf(getMsgRecordTokenStat, fieldMsgRecordTokenStat)
	tokenStat := make([]*model.MsgRecordTokenStat, 0)
	args := []any{recordID}
	if err := d.tdsqlDB.QueryToStructs(ctx, &tokenStat, sql, args...); err != nil {
		log.ErrorContextf(ctx, "GetMsgRecordTokenStatByStatID|sql:%s args:%+v err:%+v", sql, args, err)
		return nil, err
	}
	if len(tokenStat) == 0 {
		return nil, nil
	}
	return tokenStat[0], nil
}

// DeleteMsgRecordTokenStat 删除消息记录统计信息【只允许定时任务调用】
func (d *dao) DeleteMsgRecordTokenStat(ctx context.Context, recordIDs []string) error {
	sql := fmt.Sprintf(deleteMsgRecordTokenStat, placeholder(len(recordIDs)))
	params := make([]any, 0, len(recordIDs))
	for _, id := range recordIDs {
		params = append(params, id)
	}
	if _, err := d.tdsqlDB.Exec(ctx, sql, params...); err != nil {
		log.ErrorContextf(ctx, "DeleteMsgRecordTokenStat error: %+v, sql: %s, params: %+v", err, sql, params)
		return err
	}
	return nil
}
