package dao

import (
	"context"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// IWorkflow 工作流接口
type IWorkflow interface {
	// RetrieveWorkflows 获取topN工作流
	RetrieveWorkflows(context.Context, KEP_WF_DM.RunEnvType, uint64, string) (*KEP_WF_DM.RetrieveWorkflowsReply, error)
	// RunWorkflow 执行工作流
	RunWorkflow(context.Context, *KEP_WF_DM.RunWorkflowRequest, chan *KEP_WF_DM.RunWorkflowReply, chan int) error
	// ClearWorkflowSession 清除会话
	ClearWorkflowSession(context.Context, *KEP_WF_DM.ClearSessionRequest) error
	// SetWorkflowStatus 设置在工作流中状态标记 一天过期，与DM一致。看起来暂时没用到。
	SetWorkflowStatus(ctx context.Context, appID uint64, sessionID string, flag bool) error
	// IsInWorkflow 是否在工作流中, 目前没有用到。
	IsInWorkflow(ctx context.Context, appID uint64, sessionID string) bool
	// SetLastWorkflow 设置上一轮工作流
	SetLastWorkflow(ctx context.Context, appID uint64, sessionID string, result *KEP_WF_DM.RetrieveWorkflow) error
	// GetLastWorkflow 获取上一轮工作流
	GetLastWorkflow(ctx context.Context, appID uint64, sessionID string) (*KEP_WF_DM.RetrieveWorkflow, error)
	// SetWorkflowRunNodes 设置工作流运行节点
	SetWorkflowRunNodes(ctx context.Context, sessionID, runID string, nodes []*KEP_WF_DM.RunNodeInfo) error
	// GetWorkflowRunNodes 获取工作流运行节点
	GetWorkflowRunNodes(ctx context.Context, sessionID, runID string) ([]*KEP_WF_DM.RunNodeInfo, error)
	// GetWorkflowStatus 获取工作流状态
	GetWorkflowStatus(ctx context.Context, appID uint64, workflowID string,
		envTag AppScene) (*KEP_WF.GetWorkflowReleaseStatusResp, error)
	// GetWorkflowByIDs 获取工作流信息
	GetWorkflowByIDs(ctx context.Context, appBizID uint64, workflowIDs []string) (
		map[string]*KEP_WF.ListWorkflowInnerRsp_Workflow, error)
	// SetWorkflowUnchanged 设置在工作流保持信息
	SetWorkflowUnchanged(ctx context.Context, appID uint64, sessionID string, workflowID string)
	// GetWorkflowUnchanged 获取在工作流保持信息
	GetWorkflowUnchanged(ctx context.Context, appID uint64, sessionID string) string
	// NodeDebugWorkflow 非对话节点调试
	NodeDebugWorkflow(context.Context, *KEP_WF_DM.DebugWorkflowNodeRequest) (*KEP_WF_DM.DebugWorkflowNodeReply, error)
	// NodeDialogWorkflow 对话节点调试
	NodeDialogWorkflow(context.Context, *KEP_WF_DM.DebugWorkflowNodeDialogRequest,
		chan *KEP_WF_DM.DebugWorkflowNodeDialogReply, chan int) error
}
