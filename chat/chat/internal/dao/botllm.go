package dao

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	botllm "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_llm_server"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// TextTruncateCommon 文本截断
func (d *dao) TextTruncateCommon(ctx context.Context, promptTmpl string, req any, limit int) (string, error) {
	prompt, err := model.Render(ctx, promptTmpl, req)
	if err != nil {
		log.ErrorContextf(ctx, "TextTruncateCommon,err:%+v", err)
		return "", err
	}
	return d.truncatePrompt(ctx, prompt, limit), nil
}

// TextTruncate 文本截断
func (d *dao) TextTruncate(ctx context.Context, appModel *model.AppModel, req any) (string, error) {
	prompt, limit, err := appModel.RenderPromptWithoutTruncate(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "TextTruncateCommon,err:%+v", err)
		return "", err
	}
	return d.truncatePrompt(ctx, prompt, limit), nil
}

// GetTextTokenLen 获取文本token长度
func (d *dao) GetTextTokenLen(ctx context.Context, text string) int {
	if len(text) == 0 {
		return 0
	}
	rsp, err := d.botllmCli.GetToken(ctx, &botllm.GetTokenReq{
		RequestId: model.TraceID(ctx),
		Text:      text,
	})
	if err != nil {
		log.WarnContextf(ctx, "GetTextTokenLen,err:%+v", err)
		return len([]rune(text))
	}
	return int(rsp.GetLength())
}

func (d *dao) textTruncate(ctx context.Context, req *botllm.TextTruncateReq) (string, error) {
	log.InfoContextf(ctx, "TextTruncateCommon req:%+v", req)
	rsp, err := d.botllmCli.TextTruncate(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "TextTruncateCommon,err:%+v", err)
		return "", err
	}
	log.InfoContextf(ctx, "TextTruncateCommon rsp:%+v", rsp)
	return rsp.GetText(), nil
}

func (d *dao) truncatePrompt(ctx context.Context, prompt string, limit int) string {
	runes := []rune(prompt)
	totalLen := len(runes)
	start := strings.Index(prompt, model.TruncStart)
	finish := strings.Index(prompt, model.TruncFinish)
	if start != -1 && finish != -1 && finish > start {
		body := []rune(prompt[start+model.TruncStartLen : finish])
		realTotalLen := totalLen - model.TruncLen
		if limit == 0 || realTotalLen <= limit {
			return prompt[:start] + string(body) + prompt[finish+model.TruncFinishLen:] // 确保标记去除
		}
		text := string(body)                             // 使用body进行token截断
		tokenLimit := limit - (realTotalLen - len(body)) // prompt中出去固定模板内容外还能支持的长度（已知内容）
		// tpl := prompt[:start] + "%s" + prompt[finish+model.TruncFinishLen:]
		prefix := prompt[:start]
		suffix := prompt[finish+model.TruncFinishLen:]
		if tokenLimit < 0 { // prompt中固定内容长度已超过限制，理论上不该走到该分支
			log.WarnContextf(ctx, "No len for body, remain: %d, limit: %d, prompt: %s", tokenLimit, limit, prompt)
			tokenLimit = limit // 使用整个prompt进行token截断
			text = string(runes)
			// tpl = "%s"
			prefix = ""
			suffix = ""
		}
		truncateBody, err := d.textTruncate(ctx, &botllm.TextTruncateReq{
			RequestId:  model.TraceID(ctx),
			Text:       text,
			TokenLimit: int64(tokenLimit),
		})
		if err != nil {
			log.WarnContextf(ctx, "textTruncate error: %+v, use rune truncate", err)
			truncateBody = string([]rune(text)[:tokenLimit])
		}
		if len(truncateBody) == 0 {
			log.WarnContextf(ctx, "textTruncate rsp empty")
			truncateBody = string([]rune(text)[:tokenLimit])
		}
		// 需要判断尾部是否有</passage [n]>，没有的话需要补上
		lastPassageIdx := getLastPassageIndex(truncateBody) // 先获取最后一个passage的index
		if lastPassageIdx > 0 {
			truncateBody = addClosingPassage(truncateBody, lastPassageIdx) // 如果缺失，则补上
		}
		return prefix + truncateBody + suffix
	}

	if limit <= 0 || totalLen <= limit {
		return prompt
	}
	res, err := d.textTruncate(ctx, &botllm.TextTruncateReq{
		RequestId:  model.TraceID(ctx),
		Text:       prompt,
		TokenLimit: int64(limit),
	})
	if err != nil {
		log.ErrorContextf(ctx, "TextTruncateCommon error: %+v, use rune truncate", err)
		return string(runes[:limit]) // 剩余长度不足，返回直接截断内容
	}
	if len(res) == 0 {
		return string(runes[:limit]) // 剩余长度不足，返回直接截断内容
	}
	return res
}

// TextTruncateTemp 文本截断 for V2.6.1 临时解决
func (d *dao) TextTruncateTemp(ctx context.Context, appModel *model.AppModel, req any) (string, error) {
	prompt, limit, err := appModel.RenderPromptWithoutTruncateTemp(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "TextTruncateCommon,err:%+v", err)
		return "", err
	}
	return d.truncatePrompt(ctx, prompt, limit), nil
}

// getLastPassageIndex 获取最后一个passage的index
func getLastPassageIndex(str string) int {
	re := regexp.MustCompile(`<passage \[(\d+)\]>`)
	matches := re.FindAllStringSubmatch(str, -1)
	if len(matches) > 0 {
		lastMatch := matches[len(matches)-1]
		lastIndex, _ := strconv.Atoi(lastMatch[1])
		return lastIndex
	}
	return 0
}

// addClosingPassage 添加最后一个passage的结束标记
func addClosingPassage(str string, lastIndex int) string {
	trimmedStr := strings.TrimRight(str, " \n") // 去除尾部空格和换行
	end := fmt.Sprintf("</passage [%d]>", lastIndex)
	if !strings.HasSuffix(trimmedStr, end) { // 如果尾部没有</passage [n]>，则补上
		str += "\n" + end + "\n"
	}
	return str
}
