package dao

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	json "github.com/json-iterator/go"
)

// AppScene 使用场景 测试还是正式
type AppScene uint32 // 使用场景

const (
	// AppTestScene 测试场景
	AppTestScene AppScene = 1 // 测试场景
	// AppReleaseScene 正式场景
	AppReleaseScene AppScene = 2 // 正式场景
	// AppWorkflowDebug 工作流调试场景
	AppWorkflowDebug AppScene = 10 // 工作流调试场景
	// AppChatInputLimitDefault TODO
	AppChatInputLimitDefault = 3000
)

// GetAppChatInputLimit 通过App信息获取对话输入字符限制
func (d *dao) GetAppChatInputLimit(ctx context.Context, req *admin.GetAppChatInputNumReq) int32 {
	log.InfoContextf(ctx, "GetAppChatInputLimit req:%+v", req)
	rsp, err := d.apiCli.GetAppChatInputNum(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "GetAppChatInputLimit,err:%+v", err)
		return AppChatInputLimitDefault
	}
	log.InfoContextf(ctx, "GetAppChatInputLimit rsp:%+v", rsp)
	return rsp.GetInputLenLimit()
}

// GetModelFinanceInfo 获取模型计费信息
func (d *dao) GetModelFinanceInfo(ctx context.Context, mainModel string) *admin.GetModelFinanceInfoRsp_ModelFinance {
	req := &admin.GetModelFinanceInfoReq{ModelNames: []string{mainModel}}
	rsp, err := d.apiCli.GetModelFinanceInfo(ctx, req)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrAdminAppKeyInvalid) {
			log.WarnContextf(ctx, "Get app by appKey error: %+v, req: %+v", err, req)
		} else {
			log.ErrorContextf(ctx, "Get app by appKey error: %+v, req: %+v", err, req)
		}
		return &admin.GetModelFinanceInfoRsp_ModelFinance{}
	}
	if info, ok := rsp.GetModelFinanceInfo()[mainModel]; ok {
		return info
	}
	return &admin.GetModelFinanceInfoRsp_ModelFinance{}
}

// GetRobotIDByShareCode 通过分享码获取机器人ID
func (d *dao) GetRobotIDByShareCode(ctx context.Context, shareCode string) (uint64, error) {
	req := &KEP.GetRobotIdByShareCodeReq{
		ShareCode: shareCode,
	}
	log.DebugContextf(ctx, "GetRobotIdByShareCode req: %s", helper.Object2String(req))
	robotInfo, err := d.configCli.GetRobotIdByShareCode(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "GetRobotIdByShareCode error: %+v", err)
		return 0, err
	}
	botID := robotInfo.GetBotBizId()
	if botID == "" {
		botID = "0"
	}
	log.DebugContextf(ctx, "GetRobotIdByShareCode robotInfo: %s", helper.Object2String(robotInfo))
	botBizID, err := strconv.ParseUint(botID, 10, 64)
	if err != nil {
		log.ErrorContextf(ctx, "GetRobotIdByShareCode ParseUint error: %+v", err)
		return 0, err
	}

	log.DebugContextf(ctx, "GetRobotIdByShareCode rsp: %v", robotInfo.GetBotBizId())
	return botBizID, nil
}

// GetAppByAppKey 根据 AppKey 获取APP
func (d *dao) GetAppByAppKey(ctx context.Context, scene AppScene, appKey string) (*model.App, error) {
	tik := time.Now()
	defer func() {
		log.InfoContextf(ctx, "GetAppByAppKey cost time: %v", time.Since(tik).Milliseconds())
	}()
	req := &admin.GetAppInfoReq{AppKey: appKey, Scenes: uint32(scene)}
	rsp, err := d.apiCli.GetAppInfo(ctx, req)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrAdminRobotNotExist) {
			return nil, nil
		}
		if errs.Code(err) == errs.Code(pkg.ErrAdminAppKeyInvalid) {
			log.WarnContextf(ctx, "Get app by appKey error: %+v, req: %+v", err, req)
		} else {
			log.ErrorContextf(ctx, "Get app by appKey error: %+v, req: %+v", err, req)
		}
		return nil, err
	}
	log.InfoContextf(ctx, "GetAppByAppKey rsp: %s", helper.Object2StringEscapeHTML(rsp))
	return &model.App{GetAppInfoRsp: rsp}, nil
}

// GetAppByBizID 根据业务 ID 获取App
func (d *dao) GetAppByBizID(ctx context.Context, scene AppScene, id uint64) (*model.App, error) {
	tik := time.Now()
	req := &admin.GetAppInfoReq{AppBizId: id, Scenes: uint32(scene)}
	rsp, err := d.apiCli.GetAppInfo(ctx, req)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrAdminRobotNotExist) {
			return nil, nil
		}
		log.ErrorContextf(ctx, "Get app by biz id error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "GetAppByBizID cost:%d, rsp: %s", time.Since(tik).Milliseconds(),
		helper.Object2StringEscapeHTML(rsp))
	return &model.App{GetAppInfoRsp: rsp}, nil
}

// GetAppsByBizID 根据业务 ID 批量获取App
// AppTestScene = 1 测试场景
// AppReleaseScene = 2 正式场景
func (d *dao) GetAppsByBizID(ctx context.Context, scene AppScene, ids []uint64) ([]*model.AppListInfo, error) {
	t0 := time.Now()
	req := &admin.GetAppsByBizIDsReq{AppBizIds: ids, Scenes: uint32(scene)}
	log.InfoContextf(ctx, "REQ|GetAppsByBizID request: %s", helper.Object2String(req))
	rsp, err := d.apiCli.GetAppsByBizIDs(ctx, req)
	if err != nil {
		if errs.Code(err) == errs.Code(pkg.ErrAdminRobotNotExist) {
			return nil, nil
		}
		log.ErrorContextf(ctx, "Get apps by biz id error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.DebugContextf(ctx, "GetAppsByBizID rsp: %s, cost: %s", helper.Object2String(rsp), time.Since(t0))
	apps := make([]*model.AppListInfo, 0, len(rsp.GetApps()))
	for _, v := range rsp.GetApps() {
		apps = append(apps, &model.AppListInfo{GetAppsByBizIDsRsp_AppInfo: v})
	}
	log.InfoContextf(ctx, "R|GetAppsByBizID apps: %s", helper.Object2String(apps))
	return apps, nil
}

// PrefixAppCorp GetCorp 获取企业信息前缀
const PrefixAppCorp = "qbot:chat:app_corp"

// keyAppCorp app corp  key
func keyAppCorp(requestID, corpID string) string {
	return PrefixAppCorp + ":" + requestID + ":" + corpID
}

// GetCorp 获取企业信息 todo by gaussguan 加redis缓存
func (d *dao) GetCorp(ctx context.Context, requestID string, corpID uint64) (rsp *admin.GetCorpRsp, err error) {
	t0 := time.Now()
	// 先从缓存取
	str, err := redis.String(d.redis.Do(ctx, "GET", keyAppCorp(requestID, fmt.Sprintf("%d", corpID))))
	if err == nil && len(str) > 0 {
		if err = json.Unmarshal([]byte(str), &rsp); err == nil {
			log.DebugContextf(ctx, "GetCorp info ok, requestID: %s, rsp: %s,  cost: %s",
				requestID, helper.Object2String(rsp), time.Since(t0))
			return rsp, nil
		}
	}

	req := &admin.GetCorpReq{
		Id: corpID,
	}
	log.InfoContextf(ctx, "REQ|GetCorp request: %s", helper.Object2String(req))
	rsp, err = d.apiCli.GetCorp(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Get corp by biz id error: %+v, cost: %s", err, time.Since(t0))
		return nil, err
	}
	log.InfoContextf(ctx, "RESP|GetCorp %s, cost: %s", helper.Object2String(rsp), time.Since(t0))

	// 写缓存
	_, err = d.redis.Do(ctx, "SET", keyAppCorp(requestID, fmt.Sprintf("%d", corpID)),
		helper.Object2String(rsp), "EX", 2*60)
	if err != nil {
		log.WarnContextf(ctx, "SetCorp info  error: %+v, requestID: %s", err, requestID)
	}
	log.DebugContextf(ctx, "SetCorp info success, requestID: %s", requestID)

	return rsp, nil
}
