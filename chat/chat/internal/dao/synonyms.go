package dao

import (
	"context"
	"strconv"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/naming/registry"
	"git.woa.com/dialogue-platform/go-comm/clues"
	kpb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
)

// GetSynonymsNER 获取同义词NER
func (d *dao) GetSynonymsNER(ctx context.Context, scene AppScene, robotID uint64, question string) (string, error) {
	t0 := time.Now()
	node := &registry.Node{}
	opts := []client.Option{client.WithSelectorNode(node)}
	var calleeNamespace string
	if utf8.RuneCountInString(question) > config.App().RetrievalConfig.NERThreshold {
		return question, nil
	}
	if speicalApp, ok := config.App().ExperimentConfig.ReplaceAppMap[robotID]; ok {
		robotID = speicalApp.ReplaceAppID
		calleeNamespace = speicalApp.CalleeNamespace
		log.InfoContextf(ctx, "GetSynonymsNER replace app: %s", helper.Object2String(speicalApp))
	}

	req := &kpb.SynonymsNERReq{
		BotBizId: strconv.FormatUint(robotID, 10),
		Query:    question,
		Scenes:   uint32(scene),
	}
	cli := d.knowledgeAPICli
	if d.GetExperimentKnowledgeCli() != nil && calleeNamespace == config.App().ExperimentConfig.Namespace {
		cli = d.GetExperimentKnowledgeCli()
	}
	rsp, err := cli.SynonymsNER(ctx, req, opts...)
	tm := codec.Message(ctx)
	clues.AddT(ctx, "knowledgeAPICli.GlobalKnowledge.codec.Message", clues.M{
		"CallerApp": tm.CallerApp(), "CallerServer": tm.CallerServer(), "CallerService": tm.CallerService(),
		"CalleeApp": tm.CalleeApp(), "CalleeServer": tm.CalleeServer(), "CalleeService": tm.CalleeService(),
		"Namespace": tm.Namespace(), "EnvName": tm.EnvName(), "CalleeSetName": tm.CalleeSetName(),
		"RemoteAddr":    tm.RemoteAddr().String(),
		"ServerRPCName": tm.ServerRPCName(),
		"node":          clues.ConvertNode(node),
	})
	clues.AddTrack4RPC(ctx, "knowledge.GlobalKnowledge", req, rsp, err, t0)
	if err != nil {
		log.ErrorContextf(ctx, "GetSynonymsNER失败，err: %+v", err)
		return "", err
	}
	log.InfoContextf(ctx, "GetSynonymsNER Rsp: %s", helper.Object2String(rsp))
	return rsp.ReplacedQuery, nil
}
