package dao

import (
	"context"

	"git.woa.com/dialogue-platform/common/v3/limiter"
)

// IModelLimit 模型限制
type IModelLimit interface {
	// Lock 锁
	Lock(ctx context.Context, req limiter.MultiLockReq) (*limiter.MultiLockRsp, error)
	// Unlock 解锁
	Unlock(ctx context.Context, req limiter.MultiUnlockReq)
	// GetDefaultQpmTpmLimit 获取默认的QPM和TPM限制
	GetDefaultQpmTpmLimit(ctx context.Context) limiter.DefaultConfig
	// ReportToken 上报token
	ReportToken(ctx context.Context, req limiter.ReportTokenReq) error
}
