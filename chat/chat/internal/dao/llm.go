package dao

import (
	"context"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/localcache"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"go.opentelemetry.io/otel/trace"
)

const (
	defaultCacheTTL = 60 // 默认缓存过期时间，单位：秒
)

var (
	modelInfoCache localcache.Cache
	once           sync.Once
)

func localcacheInst() localcache.Cache {
	once.Do(func() {
		ttl := int64(defaultCacheTTL)
		if config.App().Model.LocalCacheTTL > ttl {
			ttl = config.App().Model.LocalCacheTTL
		}
		modelInfoCache = localcache.New(localcache.WithExpiration(ttl))
	})
	return modelInfoCache
}

// GetModelPromptLimit 获取模型Prompt长度限制
func (d *dao) GetModelPromptLimit(ctx context.Context, modelName string) int {
	t0 := time.Now()
	// 1. 先从本地缓存中获取
	rsp, found := queryFromLocalCache(modelName, llmm.RequestType_ONLINE, false)
	if found {
		log.InfoContextf(ctx, "Get model info from local cache, rsp: %s, modelName: %s, time cost: %s",
			helper.Object2String(rsp), modelName, time.Since(t0))
		return int(rsp.Length)
	}
	// 2. 本地缓存中没有，则从远端获取
	req := &llmm.GetModelRequest{
		RequestId:   trace.SpanContextFromContext(ctx).TraceID().String(),
		ModelName:   modelName,
		RequestType: llmm.RequestType_ONLINE,
	}
	rsp, err := d.llmmCli.GetModelInfo(ctx, req)
	clues.AddTrack4RPC(ctx, "llm.GetModelInfo", req, rsp, err, t0)
	if err != nil || rsp == nil || rsp.Code != 0 {
		log.ErrorContextf(ctx, "Get model info error: %+v, req: %+v, time cost: %s", err, req, time.Since(t0))
		// 3. 远端获取失败，则尝试从本地缓存中获取
		rsp, found := queryFromLocalCache(modelName, llmm.RequestType_ONLINE, true)
		if found {
			log.InfoContextf(ctx, "Get model info from local cache, rsp: %s, modelName: %s, time cost: %s",
				helper.Object2String(rsp), modelName, time.Since(t0))
			return int(rsp.Length)
		} else {
			// 4. 本地缓存中也没有，则返回默认值
			return config.GetDefaultTokenLimit() // 该接口必须成功返回一个值
		}
	}
	log.InfoContextf(ctx, "Get model info rsp: %s, req: %s, time cost: %s",
		helper.Object2String(rsp), helper.Object2String(req), time.Since(t0))
	if rsp.Length == 0 {
		return config.GetDefaultTokenLimit()
	}
	saveToLocalCache(modelName, llmm.RequestType_ONLINE, rsp)
	return int(rsp.Length)
}

func queryFromLocalCache(modelName string, reqType llmm.RequestType, ignoreExpire bool) (*llmm.GetModelResponse, bool) {
	key := modelName + "_" + reqType.String()
	inst := localcacheInst()
	val, status := inst.GetWithStatus(key)
	if status == localcache.CacheNotExist {
		return nil, false
	}
	if status == localcache.CacheExpire && !ignoreExpire {
		return nil, false
	}
	v, ok := val.(*llmm.GetModelResponse)
	if !ok {
		return nil, false
	}
	return v, true
}

func saveToLocalCache(modelName string, reqType llmm.RequestType, rsp *llmm.GetModelResponse) {
	inst := localcacheInst()
	key := modelName + "_" + reqType.String()
	if !inst.Set(key, rsp) {
		log.WarnContext(context.Background(), "save model info to local cache failed, modelName: %s, reqType: %s")
	}
}
