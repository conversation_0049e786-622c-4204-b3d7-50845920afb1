package dao

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// PrefixMultiModal MultiModal Redis前缀
const PrefixMultiModal = "qbot:chat:multimodal"

// PrefixMultiModalCount MultiModalCount Redis前缀
const PrefixMultiModalCount = "qbot:chat:count_multimodal"

// PrefixMultiModalHistory MultiModalHistory Redis前缀
const PrefixMultiModalHistory = "qbot:chat:history_multimodal"

// keyMultiModalStatus MultiModal 状态 key
func keyMultiModalStatus(sessionID string) string {
	return PrefixMultiModal + ":" + sessionID
}

// keyMultiModalCount MultiModalCount key
func keyMultiModalCount(sessionID string) string {
	return PrefixMultiModalCount + ":" + sessionID
}

// keyMultiModalHistory MultiModalHistory key
func keyMultiModalHistory(sessionID string) string {
	return PrefixMultiModalHistory + ":" + sessionID
}

// SetMultiModalStatus 设置在多模态中状态标记 一天过期
func (d *dao) SetMultiModalStatus(ctx context.Context, sessionID string, flag bool) error {
	_, err := d.redis.Do(ctx, "SET", keyMultiModalStatus(sessionID), flag, "EX", 24*60*60)
	if err != nil {
		log.ErrorContextf(ctx, "SetMultiModalStatus error: %+v, sessionID: %s", err, sessionID)
		return err
	}
	log.InfoContextf(ctx, "SetMultiModalStatus success, sessionID: %s, flag: %t", sessionID, flag)
	_, err = d.redis.Do(ctx, "SET", keyMultiModalCount(sessionID), 1, "EX", 24*60*60)
	if err != nil {
		log.ErrorContextf(ctx, "SetKeyMultiModalCount error: %+v, sessionID: %s", err, sessionID)
		return err
	}
	log.InfoContextf(ctx, "SetMultiModalStatusAndCount success, sessionID: %s, flag: %t", sessionID, flag)
	return nil
}

// SetMultiModalCount 设置多模态次数
func (d *dao) SetMultiModalCount(ctx context.Context, sessionID string) error {
	_, err := d.redis.Do(ctx, "INCR", keyMultiModalCount(sessionID))
	if err != nil {
		log.ErrorContextf(ctx, "SetMultiModalCount error: %+v, sessionID: %s", err, sessionID)
		return err
	}
	log.InfoContextf(ctx, "SetMultiModalCount success, sessionID: %s", sessionID)
	return nil
}

// GetMultiModalCount 获取多模态次数
func (d *dao) GetMultiModalCount(ctx context.Context, sessionID string) (int, error) {
	count, err := redis.Int(d.redis.Do(ctx, "GET", keyMultiModalCount(sessionID)))
	if err != nil {
		log.ErrorContextf(ctx, "GetMultiModalCount error: %+v, sessionID: %s", err, sessionID)
		return 0, err
	}
	log.InfoContextf(ctx, "GetMultiModalCount success, sessionID: %s, count: %d", sessionID, count)
	return count, nil
}

// IsInMultiModal 是否在多模态中
func (d *dao) IsInMultiModal(ctx context.Context, sessionID string, historyLimit uint32) bool {
	rsp, err := redis.Bool(d.redis.Do(ctx, "GET", keyMultiModalStatus(sessionID)))
	if err != nil || !rsp {
		log.DebugContextf(ctx, "Not In MultiModal error: %+v, sessionID: %s", err, sessionID)
		return false
	}
	log.InfoContextf(ctx, "IsInMultiModal success, sessionID: %s, flag: %t", sessionID, rsp)
	count, err := redis.Int(d.redis.Do(ctx, "GET", keyMultiModalCount(sessionID)))
	if err != nil {
		log.WarnContextf(ctx, "GetMultiModalCount error: %+v, sessionID: %s", err, sessionID)
		return false
	}
	log.InfoContextf(ctx, "GetMultiModalCount success, sessionID: %s, count: %d", sessionID, count)
	if count >= int(historyLimit) {
		_ = d.SetMultiModalStatus(ctx, sessionID, false)
		return false
	}
	return rsp
}

// SetMultiModalHistory 设置多模态历史记录
func (d *dao) SetMultiModalHistory(ctx context.Context, sessionID string, history string) error {
	// 使用d.redis
	_, err := d.redis.Do(ctx, "SET", keyMultiModalHistory(sessionID), history, "EX", 24*60*60)
	if err != nil {
		log.WarnContextf(ctx, "SetMultiModalHistory error: %+v, sessionID: %s", err, sessionID)
		return err
	}
	log.InfoContextf(ctx, "SetMultiModalHistory success, sessionID: %s, history: %s", sessionID, history)

	// cmd := d.goRedis.Set(ctx, keyMultiModalHistory(sessionID), history, 24*60*60*time.Second)
	// if cmd.Err() != nil {
	//	log.WarnContextf(ctx, "SetMultiModalHistory error: %+v, sessionID: %s", cmd.Err(), sessionID)
	//	return cmd.Err()
	// }
	// log.InfoContextf(ctx, "SetMultiModalHistory success, sessionID: %s, history: %s", sessionID, history)
	return nil
}

// GetMultiModalHistory 获取多模态历史记录
func (d *dao) GetMultiModalHistory(ctx context.Context, sessionID string) (string, error) {
	// 使用d.redis
	history, err := redis.String(d.redis.Do(ctx, "GET", keyMultiModalHistory(sessionID)))
	if err != nil {
		log.WarnContextf(ctx, "GetMultiModalHistory error: %+v, sessionID: %s", err, sessionID)
		return "", err
	}
	log.InfoContextf(ctx, "GetMultiModalHistory success, sessionID: %s, history: %s", sessionID, history)
	return history, nil
	// 使用goRedis
	// history, err := d.goRedis.Get(ctx, keyMultiModalHistory(sessionID)).Result()
	// if err != nil {
	//	log.WarnContextf(ctx, "GetMultiModalHistory error: %+v, sessionID: %s", err, sessionID)
	//	return "", err
	// }
	// log.InfoContextf(ctx, "GetMultiModalHistory success, sessionID: %s, history: %s", sessionID, history)
	// return history, nil
}
