package dao

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	trpcerrs "git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/naming/registry"
	"git.code.oa.com/trpc-go/trpc-go/naming/selector"
	"git.woa.com/dialogue-platform/go-comm/clues"
	kpb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
)

// WithTrpcSelector 还原为 trpc 默认 Selector
func WithTrpcSelector() client.Option {
	return func(o *client.Options) {
		o.Selector = &selector.TrpcSelector{}
	}
}

// SimpleChat 非流式调用 LLM
func (d *dao) SimpleChat(ctx context.Context, req *llmm.Request) (*llmm.Response, error) {
	t0 := time.Now()
	log.InfoContextf(ctx, "Simple LLM request: %s", helper.Object2StringEscapeHTML(req))
	if config.App().Mock.EnableLLM {
		rsp := &llmm.Response{
			Code:     0,
			Message:  &llmm.Message{Content: "mock response"},
			Finished: true,
		}
		log.InfoContextf(ctx, "Mock SimpleChat %s", helper.Object2String(rsp))
		return rsp, nil
	}
	node := &registry.Node{}
	opts := []client.Option{WithTrpcSelector(), client.WithSelectorNode(node)}
	if req.GetModelName() == "" || req.GetModelName() == "finance-13b" { // 金融大模型使用 cs-normal
		req.ModelName = "cs-normal"
	}
	rsp, err := d.llmmCli.SimpleChat(ctx, req, opts...)
	tm := codec.Message(ctx)
	clues.AddT(ctx, "llmmCli.SimpleChat.codec.Message", clues.M{
		"CallerApp": tm.CallerApp(), "CallerServer": tm.CallerServer(), "CallerService": tm.CallerService(),
		"CalleeApp": tm.CalleeApp(), "CalleeServer": tm.CalleeServer(), "CalleeService": tm.CalleeService(),
		"Namespace": tm.Namespace(), "EnvName": tm.EnvName(), "CalleeSetName": tm.CalleeSetName(),
		"RemoteAddr":    tm.RemoteAddr().String(),
		"ServerRPCName": tm.ServerRPCName(),
		"node":          clues.ConvertNode(node),
	})
	clues.AddTrack4RPC(ctx, "llmmCli.SimpleChat", req, rsp, err, t0)
	if err != nil {
		log.ErrorContextf(ctx, "Invoke SimpleChat error: %+v, req: %+v", err, req)
		return nil, err
	}

	if rsp.GetCode() != 0 {
		err := trpcerrs.New(int(rsp.GetCode()), rsp.GetErrMsg())
		log.ErrorContextf(ctx, "Invoke SimpleChat error: %+v, req: %+v", err, req)
		return nil, err
	}
	log.InfoContextf(ctx, "RESP|SimpleChat %v, ERR: %v, %s", helper.Object2String(rsp), err, time.Since(t0).String())
	return rsp, nil
}

var logOutput = []int{1, 1, 5, 10, 20, 50, 100, 200, 500, 1000, 2000, 5000}

// Chat 流式调用 LLM
func (d *dao) Chat(ctx context.Context, req *llmm.Request, ch chan *llmm.Response,
	startTime time.Time, signal chan int) error {
	var last *llmm.Response
	defer close(ch)
	if config.App().Mock.EnableLLM {
		rsp := &llmm.Response{
			Code:     0,
			Message:  &llmm.Message{Content: "mock response"},
			Finished: true,
		}
		log.InfoContextf(ctx, "Mock Chat %s", helper.Object2String(rsp))
		ch <- rsp
		return nil
	}
	defer func() {
		if last != nil && !last.GetFinished() { // 确保流式输出终止 final
			last.Finished = true
			ch <- last
		}
	}()
	node := &registry.Node{}
	opts := []client.Option{client.WithSelectorNode(node)}
	cli, err := d.llmmCli.Chat(ctx, opts...)
	clues.AddT(ctx, "llmmCli.Chat.node", clues.ConvertNode(node))
	if err != nil {
		log.ErrorContextf(ctx, "New chat stream client error: %+v", err)
		return err
	}
	defer func() {
		if err := cli.CloseSend(); err != nil {
			log.WarnContextf(ctx, "Chat CloseSend error: %+v", err)
		}
	}()
	// idx, logSpeed, start := 0, 0, time.Now()
	if req.GetModelName() == "" || req.GetModelName() == "finance-13b" { // 金融大模型使用 cs-normal
		req.ModelName = "cs-normal"
	}
	log.InfoContextf(ctx, "Stream LLM request: %s", helper.Object2StringEscapeHTML(req))
	clues.AddT(ctx, "Chat.req", req)
	if err := cli.Send(req); err != nil {
		log.ErrorContextf(ctx, "Send chat stream request error: %+v, req: %+v", err, req)
		return err
	}
	return d.processChatResponse(ctx, req, ch, startTime, last, cli, signal)
}

func (d *dao) processChatResponse(ctx context.Context, req *llmm.Request, ch chan *llmm.Response,
	startTime time.Time, last *llmm.Response, cli llmm.Chat_ChatClient, signal chan int) error {
	idx, logSpeed, start := 0, 0, time.Now()
	for {
		select {
		case <-signal:
			req.PromptType = llmm.PromptType_CMD_STOP
			_ = cli.Send(req)
			return nil
		case <-ctx.Done():
			req.PromptType = llmm.PromptType_CMD_STOP
			_ = cli.Send(req)
			return nil
		default:
			rsp, err := cli.Recv()
			if err != nil {
				if errors.Is(err, context.Canceled) || strings.Contains(err.Error(), "context canceled") {
					req.PromptType = llmm.PromptType_CMD_STOP
					_ = cli.Send(req)
					log.WarnContextf(ctx, "Chat stream canceled: %+v, req: %s", err, helper.Object2String(req))
					return nil
				}
				log.ErrorContextf(ctx, "Read model response error: %+v, req: %+v", err, req)
				return err
			}
			if idx%(10*logOutput[logSpeed]) == 0 || rsp.GetFinished() {
				log.InfoContextf(ctx, "Stream Chat[%d] rsp:%s,LLM time cost:%s, total time cost:%v ERR: %v", idx,
					helper.Object2StringEscapeHTML(rsp), time.Since(start), time.Since(startTime).Milliseconds(), err)
				if logSpeed < len(logOutput)-1 {
					logSpeed++
				}
			}
			if rsp.GetCode() != 0 {
				err := trpcerrs.Newf(int(rsp.GetCode()), rsp.GetErrMsg())
				log.ErrorContextf(ctx, "Read model response biz error: %+v, req: %+v", err, req)
				return err
			}
			reply := rsp.GetMessage().GetContent()
			lidx := strconv.Itoa(idx / 10)
			isFirst, isFinal := helper.When(idx == 0, "1", "0"), helper.When(rsp.GetFinished(), "1", "0")
			metrics.ReportLLMLength(float64(len(reply)), lidx, isFirst, isFinal)
			metrics.ReportLLMLatency(time.Since(start).Seconds(), lidx, isFirst, isFinal)
			ms := time.Since(start).Milliseconds()
			metrics.ReportTime("llmm_chat_ms", idx, ms)
			idx++
			ch <- rsp
			last = rsp
			if rsp.GetFinished() {
				return nil
			}
		}
	}
}

// AddUnsatisfiedReply 增加不满意回复
func (d *dao) AddUnsatisfiedReply(ctx context.Context, req *kpb.AddUnsatisfiedReplyReq) error {
	t0 := time.Now()
	node := &registry.Node{}
	opts := []client.Option{client.WithSelectorNode(node)}
	rsp, err := d.knowledgeAPICli.AddUnsatisfiedReply(ctx, req, opts...)
	tm := codec.Message(ctx)
	clues.AddT(ctx, "knowledgeAPICli.AddUnsatisfiedReply.codec.Message", clues.M{
		"CallerApp": tm.CallerApp(), "CallerServer": tm.CallerServer(), "CallerService": tm.CallerService(),
		"CalleeApp": tm.CalleeApp(), "CalleeServer": tm.CalleeServer(), "CalleeService": tm.CalleeService(),
		"Namespace": tm.Namespace(), "EnvName": tm.EnvName(), "CalleeSetName": tm.CalleeSetName(),
		"RemoteAddr":    tm.RemoteAddr().String(),
		"ServerRPCName": tm.ServerRPCName(),
		"node":          clues.ConvertNode(node),
	})
	clues.AddTrack4RPC(ctx, "knowledge.AddUnsatisfiedReply", req, rsp, err, t0)
	if err != nil {
		log.ErrorContextf(ctx, "Add unsatisfied reply error: %+v, req: %+v", err, req)
		return err
	}
	return nil
}
