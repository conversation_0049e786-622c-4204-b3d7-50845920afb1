package dao

import (
	"context"
	"fmt"
	"sort"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/utils"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// GenTTSSignature 生成tts语音合成签名
func (d *dao) GenTTSSignature(ctx context.Context, params map[string]string) (*model.TTSSign, error) {
	tts := config.App().TTS
	if tts.AppID == 0 || tts.SecretID == "" || tts.SecretKey == "" {
		log.ErrorContext(ctx, "TTS appId or ak sk is empty")
		return nil, pkg.ErrInternalServerError
	}
	timestamp := uint64(time.Now().Unix())
	expired := timestamp + config.GetTTSSignatureTTL()
	sign := &model.TTSSign{
		SecretID:  tts.SecretID,
		AppID:     tts.AppID,
		Timestamp: timestamp,
		Expired:   expired,
	}
	signingContent := getTTSSingContent(params, sign)
	signingContent = fmt.Sprintf("%s%s?%s", "GET", config.GetTTSSignatureURL(), signingContent)
	sign.Signature = utils.HmacSha1Base64Encode(tts.SecretKey, signingContent)
	return sign, nil
}

func getTTSSingContent(params map[string]string, sign *model.TTSSign) string {
	params["Action"] = config.GetTTSSignatureAction()
	params["AppId"] = fmt.Sprintf("%v", sign.AppID)
	params["SecretId"] = sign.SecretID
	params["Timestamp"] = fmt.Sprintf("%d", sign.Timestamp)
	params["Expired"] = fmt.Sprintf("%d", sign.Expired)
	signingContent := ""
	pathKey := make([]string, 0, len(params))
	for k := range params {
		pathKey = append(pathKey, k)
	}
	sort.Strings(pathKey)
	for _, k := range pathKey {
		if signingContent != "" {
			signingContent += "&"
		}
		signingContent += k + "=" + params[k]
	}
	return signingContent
}
