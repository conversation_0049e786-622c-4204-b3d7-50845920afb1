package dao

import (
	"context"

	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	trtc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/trtc/v20190722"
)

// IRTC 实时通话接口
type IRTC interface {
	// GenRTCUserSig 生成用户签名
	GenRTCUserSig(ctx context.Context, sdkAppID uint64, key, userID string, expire uint64) (string, error)
	// SaveTRCToken 获取rtc token
	SaveTRCToken(ctx context.Context, token string, cli *model.RTCToken) error
	// AuthRTCToken 验证rtc token
	AuthRTCToken(ctx context.Context, token string) (*model.RTCToken, error)
	// StartAIConversation 开启AI对话
	StartAIConversation(ctx context.Context, request *trtc.StartAIConversationRequest) (string, error)
	// StopAIConversation 关闭AI对话
	StopAIConversation(ctx context.Context, taskID string) error
	// SaveRTCTask 保存rtc任务
	SaveRTCTask(ctx context.Context, taskID string, cli *model.RTCToken) error
	// GetRTCTask 获取rtc任务
	GetRTCTask(ctx context.Context, taskID string) (*model.RTCToken, error)
	// RenewRTCTask 续期rtc任务
	RenewRTCTask(ctx context.Context, taskID string) error
	// ClearRTCTask 清理rtc任务
	ClearRTCTask(ctx context.Context, taskID string) error
	// LimitRTCConcurrency 限制rtc并发
	LimitRTCConcurrency(ctx context.Context, rtcToken *model.RTCToken) (bool, error)
	// ReleaseRTCConcurrency 释放rtc并发
	ReleaseRTCConcurrency(ctx context.Context, rtcToken *model.RTCToken) error
	// AssignRoomID 生成房间号
	AssignRoomID(ctx context.Context) (uint32, error)
	// RenewRoomID 续期房间号
	RenewRoomID(ctx context.Context, roomID string) error
	// SaveRTCClient 保存rtc客户端
	SaveRTCClient(ctx context.Context, taskID string, cli *model.RTCClient) error
	// GetRTCClient 获取rtc客户端
	GetRTCClient(ctx context.Context, taskID string) (*model.RTCClient, error)
	// RenewRTCClient 续期rtc任务
	RenewRTCClient(ctx context.Context, taskID string) error
	// ClearRTCClient 清理rtc任务
	ClearRTCClient(ctx context.Context, taskID string) error
}
