package dao

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-database/mysql"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

const (
	fieldSession = `
		id, type, session_id, last_record_id, bot_id, bot_biz_id, seat_id, seat_biz_id,
		visitor_id, visitor_biz_id, is_closed, is_transfered, is_deleted,
		last_visit_time, untransfer_time, reset_time, create_time, update_time
	`
)

const (
	createSession = `
	INSERT INTO t_session (` + fieldSession + `) VALUES (
		null, :type, :session_id, :last_record_id, :bot_id, :bot_biz_id, :seat_id, :seat_biz_id,
		:visitor_id, :visitor_biz_id, :is_closed, :is_transfered, :is_deleted,
		NOW(), NOW(), :reset_time, NOW(), NOW()
	)`

	getLastSession = `
	SELECT ` + fieldSession + ` FROM t_session 
	WHERE is_deleted = 0 AND type = ? AND visitor_id = ? AND bot_id = ? AND seat_id = ? AND seat_biz_id = ? ` +
		` AND visitor_biz_id = ? AND is_closed = 0 ORDER BY create_time DESC LIMIT 1`

	getSession = `SELECT ` + fieldSession + ` FROM t_session WHERE is_deleted = 0 AND session_id = ?`

	resetSession           = `UPDATE t_session SET is_closed = 1 WHERE is_deleted = 0 AND is_closed = 0 AND session_id = ?`
	updateSessionResetTime = `UPDATE t_session SET reset_time = NOW() WHERE is_deleted = 0 AND session_id = ?`

	getSessionsBySeat = `
	SELECT ` + fieldSession + ` FROM t_session WHERE is_deleted = 0 AND seat_id = ? AND is_closed = 0`

	// transferSession = `UPDATE t_session SET is_transfered = 1 WHERE is_deleted = 0 AND session_id = ?`

	// untransferSession = `
	// UPDATE t_session SET is_transfered = 0, untransfer_time = NOW() WHERE is_deleted = 0 AND session_id = ?`

	refreshVisitTime = `UPDATE t_session SET last_visit_time = NOW() WHERE is_deleted = 0 AND id = ? AND session_id = ?`

	getUserCellphoneByCorpStaffBizID = `SELECT cellphone FROM t_exp_user WHERE business_id = ? AND status = 1`
)

const (
	lockCreateSession = "qbot:lock:create:botsession"
)

// TransferSession 会话转人工
// func (d *dao) TransferSession(ctx context.Context, sessionID string) error {
//	sql := transferSession
//	params := []any{sessionID}
//	if _, err := d.db.Exec(ctx, sql, params...); err != nil {
//		log.ErrorContextf(ctx, "Transfer botsession error: %+v, sql: %s, params: %+v", err, sql, params)
//		return err
//	}
//	return nil
// }

// UntransferSession 重置会话转人工
// func (d *dao) UntransferSession(ctx context.Context, sessionID string) error {
//	sql := untransferSession
//	params := []any{sessionID}
//	if _, err := d.db.Exec(ctx, sql, params...); err != nil {
//		log.ErrorContextf(ctx, "Untransfer botsession error: %+v, sql: %s, params: %+v", err, sql, params)
//		return err
//	}
//	return nil
// }

// CreateSession 创建会话
func (d *dao) CreateSession(ctx context.Context, session *model.Session) error {
	s := createSession
	if _, err := d.tdsqlDB.NamedExec(ctx, createSession, session); err != nil {
		log.ErrorContextf(ctx, "Create botsession error: %+v, sql: %s, params: %+v", err, s, session)
		return err
	}
	log.DebugContextf(ctx, "Created botsession: %+v", session)
	return nil
}

// GetSession 获取会话
func (d *dao) GetSession(ctx context.Context, typ model.SessionType, sessionID string) (*model.Session, error) {
	s := getSession
	params := []any{sessionID}
	if typ != model.SessionTypeAny {
		s += " AND type = ?"
		params = append(params, typ)
	}
	session := model.Session{}
	err := d.tdsqlDB.Get(ctx, &session, s, params...)
	clues.AddTrackDataWithError(ctx, "GetSession.Get", clues.M{"sql": s, "params": params, "botsession": session}, err)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			return nil, nil
		}
		log.ErrorContextf(ctx, "Get botsession error: %+v, sql: %s, params: %+v", err, s, params)
		return nil, err
	}
	return &session, nil
}

// ResetSession 重置会话
func (d *dao) ResetSession(ctx context.Context, sessionID string, isOnlyEmptyTheDialog bool) (bool, error) {
	sql := resetSession
	if isOnlyEmptyTheDialog {
		sql = updateSessionResetTime
	}
	params := []any{sessionID}
	r, err := d.tdsqlDB.Exec(ctx, sql, params...)
	if err != nil {
		log.ErrorContextf(ctx, "Reset botsession error: %+v, sql: %s, params: %+v", err, sql, params)
		return false, err
	}
	affected, _ := r.RowsAffected()
	return affected == 1, nil
}

// GetSessionsBySeat 坐席侧获取会话列表
func (d *dao) GetSessionsBySeat(ctx context.Context, seatID uint64) ([]model.Session, error) {
	s := getSessionsBySeat
	params := []any{seatID}
	var sessions []model.Session
	if err := d.tdsqlDB.Select(ctx, &sessions, s, params...); err != nil {
		log.ErrorContextf(ctx, "Get sessions by seat error: %+v, sql: %s, params: %+v", err, s, params)
		return nil, err
	}
	return sessions, nil
}

// GetLastSession 获取最新会话
func (d *dao) GetLastSession(
	ctx context.Context,
	typ model.SessionType, visitorID, botID, seatID, seatBizID, visitorBizID uint64,
) (*model.Session, error) {
	s := getLastSession
	session := model.Session{}
	params := []any{typ, visitorID, botID, seatID, seatBizID, visitorBizID}
	err := d.tdsqlDB.Get(ctx, &session, s, params...)
	clues.AddTrackDataWithError(ctx, "GetLastSession.Get", clues.M{"sql": s, "params": params, "session": session}, err)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			return nil, nil
		}
		log.ErrorContextf(ctx, "Get last botsession error: %+v, sql: %s, params: %+v", err, s, params)
		return nil, err
	}
	return &session, nil
}

// RefreshSessionVisitTime 刷新会话访问时间
func (d *dao) RefreshSessionVisitTime(ctx context.Context, id uint64, sessionID string) error {
	s := refreshVisitTime
	params := []any{id, sessionID}
	if _, err := d.tdsqlDB.Exec(ctx, refreshVisitTime, params...); err != nil {
		log.ErrorContextf(ctx, "Refresh botsession visit time error: %+v, sql: %s, params: %+v", err, s, params)
		return err
	}
	return nil
}

// MustGetLastSession 获取最后一次会话（如果没有则创建）
func (d *dao) MustGetLastSession(
	ctx context.Context, typ model.SessionType, visitor, bot, seat model.ID,
) (*model.Session, bool, error) {
	lock := fmt.Sprintf("%s:%d:%d:%d:%d", lockCreateSession, typ, visitor.ID, bot.ID, seat.ID)
	session, isNew, err := d.limiter.TryDo(
		ctx, lock,
		func() error {
			return d.CreateSession(ctx, model.NewSession(typ, visitor, bot, seat))
		},
		func() (bool, any, error) {
			session, err := d.GetLastSession(ctx, typ, visitor.ID, bot.ID, seat.ID, seat.BizID, visitor.BizID)
			if err != nil {
				return false, nil, err
			}
			return session != nil, session, err
		},
	)
	if err != nil {
		return nil, false, err
	}

	v := session.(*model.Session)
	if v == nil {
		return nil, false, pkg.ErrCreateSessionFailed
	}
	return v, isNew, nil
}

// GetUserCellphoneByCorpStaffBizID ..
func (d *dao) GetUserCellphoneByCorpStaffBizID(ctx context.Context, corpStaffBizID uint64) string {
	cellphone := ""
	params := []any{corpStaffBizID}
	if err := d.db.Get(ctx, &cellphone, getUserCellphoneByCorpStaffBizID, params...); err != nil {
		if mysql.IsNoRowsError(err) {
			return ""
		} else {
			log.ErrorContextf(ctx, "GetUserCellphoneByCorpStaffBizID error: %+v, sql: %s, params: %+v", err,
				getUserCellphoneByCorpStaffBizID, params)
		}
		return ""
	}
	return cellphone
}
