package dao

import (
	"context"
	"fmt"
	"math/rand"
	"strconv"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	trtc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/trtc/v20190722"
	"github.com/tencentyun/tls-sig-api-v2-golang/tencentyun"
)

const (
	prefixRTCToken   = "qbot:rtc:token:"
	prefixRTCClient  = "qbot:rtc:client:"
	prefixRTCTask    = "qbot:rtc:task:"
	prefixRTCLimit   = "limiter:rtc:time:"
	prefixRTCRoom    = "qbot:rtc:room:"
	prefixRTCRoomSeq = "qbot:rtc:room_seq:"
	roomSeqLimit     = 42940
	roomIDLimit      = 4294967294
)

var (
	trtcClient *trtc.Client
	trtcOnce   sync.Once
)

// keyRTCToken Token Key
func keyRTCToken(token string) string {
	return prefixRTCToken + token
}

// keyRTCClient 客户端信息 Key
func keyRTCClient(clientID string) string {
	return prefixRTCClient + clientID
}

// keyRTCTask Token Key
func keyRTCTask(token string) string {
	return prefixRTCTask + token
}

// keyRTCLimiter 获取限流key
func keyRTCLimiter(corpID uint64) string {
	return fmt.Sprintf("%s%d", prefixRTCLimit, corpID)
}

// keyRTCRoomSeq 获取房间序号key
func keyRTCRoomSeq(timestamp int64) string {
	return fmt.Sprintf("%s%d", prefixRTCRoomSeq, timestamp)
}

// keyRTCRoom 获取房间key
func keyRTCRoom(roomID uint32) string {
	return fmt.Sprintf("%s%d", prefixRTCRoom, roomID)
}

// keyRTCStrRoom 获取房间key
func keyRTCStrRoom(roomID string) string {
	return fmt.Sprintf("%s%s", prefixRTCRoom, roomID)
}

// GenRTCUserSig 生成用户签名
func (d *dao) GenRTCUserSig(ctx context.Context, sdkAppID uint64, key, userID string, expire uint64) (
	string, error) {
	sig, err := tencentyun.GenUserSig(int(sdkAppID), key, userID, int(expire))
	if err != nil {
		log.ErrorContextf(ctx, "Gen userSig error: %+v, userID: %+v", err, userID)
		return "", err
	}
	return sig, nil
}

// SaveTRCToken 保存 TRC Token
func (d *dao) SaveTRCToken(ctx context.Context, token string, cli *model.RTCToken) error {
	bs, err := jsoniter.Marshal(cli)
	if err != nil {
		log.ErrorContextf(ctx, "SaveTRCToken| marshal token error: %+v, cli: %+v", err, cli)
		return err
	}
	ttl := config.GetRTCTokenTTL()
	if _, err := redis.String(d.redis.Do(ctx, "SETEX", keyRTCToken(token), ttl, bs)); err != nil {
		log.ErrorContextf(ctx, "SaveTRCToken| save error: %+v, token: %s, cli: %+v, ttl: %d", err, token,
			cli, ttl)
		return err
	}
	log.InfoContextf(ctx, "SaveTRCToken| save success  %s,ttl: %v ", token, ttl)
	return nil
}

// AuthRTCToken 认证 RTC Token
func (d *dao) AuthRTCToken(ctx context.Context, token string) (*model.RTCToken, error) {
	key := keyRTCToken(token)
	// 获取token
	s, err := redis.String(d.redis.Do(ctx, "GET", key))
	if err != nil && err != redis.ErrNil {
		log.ErrorContextf(ctx, "AuthRTCToken| get rtc token error: %+v, token: %s", err, token)
		return nil, err
	}
	if err == redis.ErrNil {
		log.WarnContextf(ctx, "AuthRTCToken| auth rtc client error: token invalid, token: %s", token)
		return nil, pkg.ErrAuthTokenFailed
	}
	cli := &model.RTCToken{}
	if err := jsoniter.UnmarshalFromString(s, &cli); err != nil {
		log.ErrorContextf(ctx, "AuthRTCToken| unmarshal rtc token error:%+v, token:%s, data:%s", err, token, s)
		return nil, err
	}
	// 删除token
	_, err = d.redis.Do(ctx, "DEL", key)
	if err != nil {
		log.ErrorContextf(ctx, "AuthRTCToken| del rtc token error:%+v, token:%s", err, token)
	} else {
		log.InfoContextf(ctx, "AuthRTCToken| del rtc token success, token:%s", token)
	}
	return cli, nil
}

// StartAIConversation 开启AI对
func (d *dao) StartAIConversation(ctx context.Context, request *trtc.StartAIConversationRequest) (string, error) {
	t0 := time.Now()
	client, err := getTRTCClient(ctx)
	if err != nil {
		return "", err
	}
	log.InfoContextf(ctx, "StartAIConversation req:%v", request.ToJsonString())
	rsp, err := client.StartAIConversation(request)
	if rsp != nil {
		log.InfoContextf(ctx, "StartAIConversation rsp:%v cost:%s", rsp.ToJsonString(), time.Since(t0))
	}
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok &&
			sdkError.GetCode() == trtc.RESOURCEINSUFFICIENT_REQUESTREJECTION {
			log.WarnContextf(ctx, "StartAIConversation resource insufficient: %+v", err)
			return "", pkg.ErrRTCResourceInsufficient
		}
		log.ErrorContextf(ctx, "StartAIConversation error: %+v", err)
		return "", pkg.ErrInternalServerError
	}
	if rsp == nil || rsp.Response == nil || rsp.Response.TaskId == nil {
		log.ErrorContextf(ctx, "StartAIConversation rsp is empty")
		return "", pkg.ErrInternalServerError
	}
	return *rsp.Response.TaskId, nil
}

// StopAIConversation 关闭AI对话
func (d *dao) StopAIConversation(ctx context.Context, taskID string) error {
	t0 := time.Now()
	client, err := getTRTCClient(ctx)
	if err != nil {
		return err
	}
	request := trtc.NewStopAIConversationRequest()
	request.TaskId = common.StringPtr(taskID)
	log.InfoContextf(ctx, "StopAIConversation req:%v", request.ToJsonString())
	rsp, err := client.StopAIConversation(request)
	if rsp != nil {
		log.InfoContextf(ctx, "StopAIConversation rsp:%v cost:%s", rsp.ToJsonString(), time.Since(t0))
	}
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok &&
			sdkError.GetCode() == trtc.FAILEDOPERATION_TASKNOTEXIST {
			log.WarnContextf(ctx, "StopAIConversation task not exist or stop: %+v, taskID: %s", err, taskID)
			return nil
		}
		log.ErrorContextf(ctx, "StopAIConversation error: %+v", err)
		return pkg.ErrInternalServerError
	}
	return nil
}

func getTRTCClient(ctx context.Context) (*trtc.Client, error) {
	if trtcClient != nil {
		return trtcClient, nil
	}
	var err error
	trtcOnce.Do(func() {
		credential := common.NewCredential(
			config.App().TRTC.SecretID,
			config.App().TRTC.SecretKey,
		)
		cpf := profile.NewClientProfile()
		cpf.HttpProfile.Endpoint = config.App().TRTC.Endpoint
		trtcClient, err = trtc.NewClient(credential, config.App().TRTC.Region, cpf)
	})
	if err != nil {
		log.ErrorContextf(ctx, "Init trtc client error: %+v", err)
		return nil, err
	}
	return trtcClient, nil
}

// SaveRTCTask 保存 TRC Task
func (d *dao) SaveRTCTask(ctx context.Context, taskID string, cli *model.RTCToken) error {
	bs, err := jsoniter.Marshal(cli)
	if err != nil {
		log.ErrorContextf(ctx, "SaveTRCTask| marshal task error: %+v, cli: %+v", err, cli)
		return err
	}
	ttl := config.GetRTCTaskTTL()
	if _, err := redis.String(d.redis.Do(ctx, "SETEX", keyRTCTask(taskID), ttl, bs)); err != nil {
		log.ErrorContextf(ctx, "SaveTRCTask| save error: %+v, taskID: %s, cli: %+v, ttl: %d", err, taskID,
			cli, ttl)
		return err
	}
	log.InfoContextf(ctx, "SaveTRCTask| save success, taskID:%s, ttl:%v ", taskID, ttl)
	return nil
}

// RenewRTCTask 续期RTC 任务
func (d *dao) RenewRTCTask(ctx context.Context, taskID string) error {
	ttl := config.GetRTCTaskTTL()
	if _, err := d.redis.Do(ctx, "EXPIRE", keyRTCTask(taskID), ttl); err != nil {
		log.ErrorContextf(ctx, "RenewRTCTask| renew error: %+v, taskID: %s, ttl: %d", err, taskID, ttl)
		return err
	}
	log.InfoContextf(ctx, "RenewRTCTask| renew success, taskID:%+v", taskID)
	return nil
}

// GetRTCTask 获取RTC 任务
func (d *dao) GetRTCTask(ctx context.Context, taskID string) (*model.RTCToken, error) {
	t0 := time.Now()
	s, err := redis.String(d.redis.Do(ctx, "GET", keyRTCTask(taskID)))
	if err != nil && err != redis.ErrNil {
		log.ErrorContextf(ctx, "GetRTCTask| get task error: %+v, taskID: %s", err, taskID)
		return nil, err
	}
	if err == redis.ErrNil {
		return nil, nil
	}
	cli := &model.RTCToken{}
	if err := jsoniter.UnmarshalFromString(s, &cli); err != nil {
		log.ErrorContextf(ctx, "GetRTCTask| unmarshal rtc task error: %+v, taskID: %s, data: %s", err, taskID, s)
		return nil, err
	}
	log.InfoContextf(ctx, "GetRTCTask| get taskID:%+v cost:%s", taskID, time.Since(t0))
	return cli, nil
}

// ClearRTCTask 清除RTC 任务
func (d *dao) ClearRTCTask(ctx context.Context, taskID string) error {
	// 获取rtcToken
	rtcToken, err := d.GetRTCTask(ctx, taskID)
	if err != nil {
		log.WarnContextf(ctx, "ClearRTCTask| get rtc task failed, taskID:%s", taskID)
		return err
	}
	// 检查token是否存在(可能已过期)
	if rtcToken == nil {
		log.WarnContextf(ctx, "ClearRTCTask| rtc task not exist, taskID:%s", taskID)
		return nil
	}
	log.InfoContextf(ctx, "ClearRTCTask| get rtc task success, taskID:%s token:%s", taskID, rtcToken.Token)
	// 释放并发占用
	_ = d.ReleaseRTCConcurrency(ctx, rtcToken)

	// 回收房间号
	_ = d.RecycleRoomID(ctx, rtcToken.RoomID)

	// 删除任务信息
	taskKey := keyRTCTask(taskID)
	_, err = d.redis.Do(ctx, "DEL", keyRTCTask(taskID))
	if err != nil {
		log.WarnContextf(ctx, "ClearRTCTask| del rtc task failed, key:%s", taskKey)
	} else {
		log.InfoContextf(ctx, "ClearRTCTask| del rtc task success, key:%s", taskKey)
	}
	return nil
}

// LimitRTCConcurrency 限制rtc并发
func (d *dao) LimitRTCConcurrency(ctx context.Context, rtcToken *model.RTCToken) (bool, error) {
	// 判断是否开启限流
	rtcConf := config.App().RTC
	if !rtcConf.Limiter.Enable {
		return false, nil
	}
	// 获取并发限制
	concurrence := int(getRTCMaxConcurrence(rtcToken.CorpID))
	if concurrence <= 0 {
		concurrence = 5
	}
	// 时间窗口默认10分钟
	timePeriod := rtcConf.Limiter.TimePeriod
	if timePeriod <= 0 {
		timePeriod = 600
	}
	key := keyRTCLimiter(rtcToken.CorpID)
	limitBuffer := rtcConf.Limiter.LimitBuffer
	limit := uint(concurrence + limitBuffer)
	ok, current, err := d.Limiter().LimitByTimePeriod(ctx, key, rtcToken.Token, limit,
		time.Duration(timePeriod)*time.Second)
	if err != nil {
		log.ErrorContextf(ctx, "limitRTCConcurrency｜ LimitByTimePeriod error: %+v key: %s", err, key)
		return true, err
	}
	log.DebugContextf(ctx, "limitRTCConcurrency| key: %s, current limit: %d, limit: %d", key, current, limit)
	// 判断是否超过限制，第一层限制加个缓冲
	if !ok {
		log.WarnContextf(ctx, "limitRTCConcurrency| corpID:%d limit reached: %d, %d, %d", rtcToken.CorpID, current,
			concurrence, limitBuffer)
		return true, nil
	}
	return false, nil
}

// ReleaseRTCConcurrency 释放rtc并发
func (d *dao) ReleaseRTCConcurrency(ctx context.Context, rtcToken *model.RTCToken) error {
	key := keyRTCLimiter(rtcToken.CorpID)
	_, err := d.redis.Do(ctx, "ZREM", key, rtcToken.Token)
	if err != nil {
		log.WarnContextf(ctx, "releaseRTCConcurrency| release error, key:%s token:%s error: %+v", key,
			rtcToken.Token, err)
		return err
	}
	log.InfoContextf(ctx, "releaseRTCConcurrency| release success, key:%s token:%s", key, rtcToken.Token)
	return nil
}

// AssignRoomID 分配房间号
func (d *dao) AssignRoomID(ctx context.Context) (uint32, error) {
	// 重试次数
	roomRetry := config.App().RTC.RoomRetry
	if roomRetry <= 0 {
		roomRetry = 3
	}
	// 设置房间号，过期时间为24小时
	roomTTL := config.GetRTCRoomTTL()
	for i := 0; i < int(roomRetry); i++ {
		// 随机生成房间号
		roomID := d.genRoomID(ctx)
		// 判断是否重复
		key := keyRTCRoom(roomID)
		if _, err := redis.String(d.redis.Do(ctx, "SET", key, 1, "EX", roomTTL, "NX")); err != nil {
			if err == redis.ErrNil {
				log.WarnContextf(ctx, "AssignRoomID| roomID is exist, key: %s", key)
			} else {
				log.ErrorContextf(ctx, "AssignRoomID| roomID check error: %+v, key: %s", err, key)
			}
			continue
		}
		return roomID, nil
	}
	log.ErrorContextf(ctx, "AssignRoomID| roomID assign failed, retry: %d", roomRetry)
	return 0, fmt.Errorf("roomID assign failed")
}

// RecycleRoomID 分配房间号
func (d *dao) RecycleRoomID(ctx context.Context, roomID uint32) error {
	key := keyRTCRoom(roomID)
	if _, err := d.redis.Do(ctx, "DEL", key); err != nil {
		err = fmt.Errorf("RecycleRoomID| redis del error: %v", err)
		log.ErrorContextf(ctx, "%v", err)
		return err
	}
	log.InfoContextf(ctx, "RecycleRoomID| redis del success, key:%s", key)
	return nil
}

// RenewRoomID 续期房间号
func (d *dao) RenewRoomID(ctx context.Context, roomID string) error {
	key := keyRTCStrRoom(roomID)
	ttl := config.GetRTCRoomTTL()
	if _, err := d.redis.Do(ctx, "EXPIRE", key, ttl); err != nil {
		log.ErrorContextf(ctx, "RenewRoomID|renew error: %+v, roomID: %s, ttl: %d", err, roomID, ttl)
		return err
	}
	log.InfoContextf(ctx, "RenewRoomID|renew success, key:%s", key)
	return nil
}

// SaveRTCClient 保存 TRC client
func (d *dao) SaveRTCClient(ctx context.Context, taskID string, cli *model.RTCClient) error {
	bs, err := jsoniter.Marshal(cli)
	if err != nil {
		log.ErrorContextf(ctx, "SaveRTCClient| marshal rtc token error: %+v, cli: %+v", err, cli)
		return err
	}
	ttl := config.GetRTCClientTTL()
	if _, err := redis.String(d.redis.Do(ctx, "SETEX", keyRTCClient(taskID), ttl, bs)); err != nil {
		log.ErrorContextf(ctx, "SaveRTCClient| save error: %+v, taskID: %s, cli: %+v, ttl: %d", err, taskID,
			cli, ttl)
		return err
	}
	log.InfoContextf(ctx, "SaveRTCClient| save success, taskID:%s,ttl: %v ", taskID, ttl)
	return nil
}

// GetRTCClient 获取RTC client
func (d *dao) GetRTCClient(ctx context.Context, taskID string) (*model.RTCClient, error) {
	t0 := time.Now()
	s, err := redis.String(d.redis.Do(ctx, "GET", keyRTCClient(taskID)))
	if err != nil && err != redis.ErrNil {
		log.ErrorContextf(ctx, "GetRTCClient| get client error: %+v, taskID: %s", err, taskID)
		return nil, err
	}
	if err == redis.ErrNil {
		return nil, nil
	}
	cli := &model.RTCClient{}
	if err := jsoniter.UnmarshalFromString(s, &cli); err != nil {
		log.ErrorContextf(ctx, "GetRTCClient| unmarshal client error: %+v, taskID: %s, data: %s", err, taskID, s)
		return nil, err
	}
	log.InfoContextf(ctx, "GetRTCClient| get client taskID: %s, cost:%s", taskID, time.Since(t0))
	return cli, nil
}

// RenewRTCClient 续期RTC client
func (d *dao) RenewRTCClient(ctx context.Context, taskID string) error {
	ttl := config.GetRTCClientTTL()
	if _, err := d.redis.Do(ctx, "EXPIRE", keyRTCClient(taskID), ttl); err != nil {
		log.ErrorContextf(ctx, "RenewRTCClient| renew error: %+v, taskID: %s, ttl: %d", err, taskID, ttl)
		return err
	}
	log.InfoContextf(ctx, "RenewRTCClient| renew success, taskID:%+v", taskID)
	return nil
}

// ClearRTCClient 清理RTC client
func (d *dao) ClearRTCClient(ctx context.Context, taskID string) error {
	if _, err := d.redis.Do(ctx, "DEL", keyRTCClient(taskID)); err != nil {
		log.ErrorContextf(ctx, "ClearRTCClient| clear error: %+v, taskID: %s", err, taskID)
		return err
	}
	log.InfoContextf(ctx, "ClearRTCClient| clear success, taskID:%+v", taskID)
	return nil
}

// getRTCMaxConcurrence 获取最大并发数
func getRTCMaxConcurrence(corpID uint64) uint {
	rtcConf := config.App().RTC
	if limit, ok := rtcConf.MaxConcurrence.WhiteList[corpID]; ok {
		return limit
	}
	return rtcConf.MaxConcurrence.Standard
}

// genRoomID 生成房间号
func (d *dao) genRoomID(ctx context.Context) uint32 {
	timestamp, seconds := getTodayBeginTimestamp()
	roomSeq, err := d.incrRoomID(ctx, timestamp)
	if err != nil {
		return genRandomRoomID(ctx)
	} else if roomSeq <= 0 {
		log.ErrorContextf(ctx, "genRoomID| room seq zero, seq:%d timestamp:%d", roomSeq, timestamp)
		return genRandomRoomID(ctx)
	} else if roomSeq > roomSeqLimit {
		log.ErrorContextf(ctx, "genRoomID| room seq exceed, seq:%d timestamp:%d", roomSeq, timestamp)
		return genRandomRoomID(ctx)
	}
	strRoomID := fmt.Sprintf("%d%05d", roomSeq, seconds)
	roomID, err := strconv.ParseInt(strRoomID, 10, 64)
	if err != nil || roomID <= 0 || roomID > roomIDLimit {
		log.ErrorContextf(ctx, "genRoomID| gen room error:%+v roomID:%s seq:%d seconds:%d", err, strRoomID,
			roomSeq, seconds)
		return genRandomRoomID(ctx)
	}
	log.InfoContextf(ctx, "genRoomID| gen room success, roomID:%s seq:%d seconds:%d", strRoomID, roomSeq,
		seconds)
	return uint32(roomID)
}

func (d *dao) incrRoomID(ctx context.Context, timestamp int64) (int64, error) {
	luaWithNum := `
		local current = redis.call("incr", KEYS[1])
		if tonumber(current) == 1 then
			redis.call("expire", KEYS[1], 60)
		end
		return current
	`
	script := redis.NewScript(1, luaWithNum)
	key := keyRTCRoomSeq(timestamp)
	args := []interface{}{key}
	v, err := redis.Int64(script.Do(ctx, d.redis, args...))
	if err != nil {
		log.ErrorContextf(ctx, "genRoomID| incr room seq error: %+v, key:%s", err, key)
		return 0, err
	}
	log.InfoContextf(ctx, "genRoomID| incr room seq success, key:%s, v:%d", key, v)
	return v, nil
}

func genRandomRoomID(ctx context.Context) uint32 {
	roomSeq := rand.Intn(roomSeqLimit) + 1
	_, seconds := getTodayBeginTimestamp()
	strRoomID := fmt.Sprintf("%d%05d", roomSeq, seconds)
	roomID, err := strconv.ParseInt(strRoomID, 10, 64)
	// 如果生成房间号失败，则使用前缀作为房间号
	if err != nil || roomID <= 0 || roomID > roomIDLimit {
		log.WarnContextf(ctx, "genRoomID| gen random room error, roomID:%s prefix:%d suffix:%d", strRoomID,
			roomSeq, seconds)
		roomID = int64(roomSeq)
	}
	log.InfoContextf(ctx, "genRoomID| gen random room success, roomID:%s prefix:%d suffix:%d", strRoomID,
		roomSeq, seconds)
	return uint32(roomID)
}

func getTodayBeginTimestamp() (int64, int64) {
	now := time.Now()
	timestamp := now.Unix()
	zeroTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	return timestamp, timestamp - zeroTime.Unix()
}
