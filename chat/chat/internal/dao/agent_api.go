package dao

import (
	"context"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	codeRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/code_interpreter_dispatcher"
	plugin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	pluginRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_exec_server"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// IAgent Agent接口
type IAgent interface {
	// ListTools 批量获取工具详情：描述、参数这些
	// ListTools(ctx context.Context, req *plugin.ListToolsReq) []*plugin.ToolInfo

	// ListPlugins 批量获取插件详情：主要用于获取icon。
	ListPlugins(ctx context.Context, req *plugin.ListPluginsReq) []*plugin.PluginInfo
	// ListAppTools 批量获取工具详情：描述、参数这些
	ListAppTools(ctx context.Context, req *plugin.ListAppToolsInfoReq) []*plugin.AppToolInfo
	// RunTool 运行工具
	RunTool(ctx context.Context, req *pluginRun.RunToolReq) (rsp *pluginRun.RunToolRsp, err error)
	// RunStreamTool 运行流式工具
	RunStreamTool(ctx context.Context, req *pluginRun.StreamRunToolReq,
		ch chan *pluginRun.StreamRunToolRsp, signal chan int) (err error)
	// SetAgentStatus 设置Agent状态
	SetAgentStatus(ctx context.Context, botBizID, sessionID string, status *model.AgentStatus) error
	// GetAgentStatus 获取Agent状态
	GetAgentStatus(ctx context.Context, botBizID, sessionID string) (*model.AgentStatus, error)
	// ClearAgentStatus 清除Agent状态
	ClearAgentStatus(ctx context.Context, botBizID, sessionID string) error
	// SetSessionInterrupt 设置对话为临时中断状态
	SetSessionInterrupt(ctx context.Context, botBizID, sessionID string, status *model.SessionInterruptStatus) error
	// SetSessionUnInterrupt 取消对话临时中断状态
	SetSessionUnInterrupt(ctx context.Context, botBizID, sessionID string) error
	// GetSessionInterruptStatus 获取对话的临时中断状态
	GetSessionInterruptStatus(ctx context.Context, botBizID, sessionID string) (*model.SessionInterruptStatus, error)
	// DescribeTool 查询工具详情
	DescribeTool(ctx context.Context, pluginID, toolID string) (*plugin.DescribeToolRsp, error)
	// DescribeAppAgentList 查询 app 下所有 agent 的列表，env 环境信息
	DescribeAppAgentList(ctx context.Context, appBizID string, env agent_config_server.EnvType) (
		*agent_config_server.DescribeAppAgentListRsp, error)
	// ReleasePod 释放 pod
	ReleasePod(ctx context.Context, uin, appBizID, sessionID string) error
	IAgentWorkflow
}

// IAgentWorkflow Agent工作流接口
type IAgentWorkflow interface {
	// GetAgentWorkflowInfo 获取Agent工作流信息
	GetAgentWorkflowInfo(ctx context.Context,
		req *KEP_WF.GetAgentWorkflowInfoReq) []*KEP_WF.GetAgentWorkflowInfoRsp_AgentWorkflowInfo
	// GetVarList 获取变量列表
	GetVarList(ctx context.Context, req *KEP.GetVarListReq) []*KEP.GetVarListRsp_Var
	// GetSystemVarList 	获取系统变量列表
	GetSystemVarList(ctx context.Context, req *KEP.GetSystemVarListReq) []*KEP.SystemVar
	// RunCode 运行代码
	RunCode(ctx context.Context, req *codeRun.ExecuteReq) (*codeRun.ExecuteRsp, error)
	// CodeRelease 资源释放
	CodeRelease(ctx context.Context, sessionID string)
}
