// chat
//
// @(#)plugin.go  Monday, May 20, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package dao

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/ivy/qbot/qbot/calc"
)

// CalcCall 计算器调用
func (d *dao) CalcCall(ctx context.Context, text string) (string, error) {
	t0 := time.Now()
	pf.StartElapsed(ctx, "calc.TextCalc")
	log.InfoContextf(ctx, "REQ|CalcCall request: %s", text)
	result, err := calc.TextCalc(text)
	pf.AppendSpanElapsed(ctx, "calc.TextCalc")
	clues.AddTrack4RPC(ctx, "calc.TextCalc", text, result, err, t0)
	log.InfoContextf(ctx, "RESP|TextCalc %v, ERR: %v, %s", result, err, time.Since(t0).String())
	if err != nil {
		log.ErrorContextf(ctx, "call calc.TextCalc error: %+v, text: %+v", err, text)
		return "", err
	}
	return result.String(), nil
}
