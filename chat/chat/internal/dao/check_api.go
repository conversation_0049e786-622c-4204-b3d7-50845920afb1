package dao

import (
	"context"

	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
)

// IInfoCheck 信息安全审核
type IInfoCheck interface {
	// Check 审核
	// -------------------------------------------------------------------------
	Check(ctx context.Context, req *infosec.CheckReq) (*infosec.CheckRsp, error)
	// BatchRequestCheck 批量拆分送审
	BatchRequestCheck(ctx context.Context, req *infosec.CheckReq, length int) ([]*infosec.CheckRsp, error)
	// CheckTextEvil 审核文本内容
	CheckTextEvil(ctx context.Context, botBizID, corpID uint64,
		recordID, content, appInfosecBizType string) (checkCode, checkType uint32)
}
