package dao

import (
	"context"
	"errors"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/naming/registry"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"google.golang.org/protobuf/encoding/protojson"
)

// PrefixDM DM Redis前缀
const PrefixDM = "qbot:chat:taskflow"

// keyTaskFlowStatus taskflow 状态 key
func keyTaskFlowStatus(sessionID string) string {
	return PrefixDM + ":" + sessionID
}

func (d *dao) GetSemantic(ctx context.Context, req *KEP_DM.GetSemanticRequest,
	ch chan *KEP_DM.GetSemanticReply) (err error) {
	last := &KEP_DM.GetSemanticReply{}

	defer close(ch)
	defer func() {
		if last != nil && !last.IsFinal {
			last.IsFinal = true
			ch <- last
		}
		if err != nil {
			if errors.Is(err, context.Canceled) {
				log.WarnContextf(ctx, "GetSemantic canceled: %v", err)
				return
			}
			log.ErrorContext(ctx, "GetSemantic error: %v", err)
			return
		}
	}()
	b, _ := protojson.Marshal(req)
	log.InfoContextf(ctx, "dm request is: %s", string(b))

	idx := 0
	node := &registry.Node{}
	// opts := []client.Option{WithTrpcSelector()}
	opts := []client.Option{client.WithSelectorNode(node)}
	stream, err := d.dmCli.GetSemantic(ctx, req, opts...)
	tm := codec.Message(ctx)
	clues.AddT(ctx, "dmCli.GetSemantic.codec.Message", clues.M{
		"CallerApp": tm.CallerApp(), "CallerServer": tm.CallerServer(), "CallerService": tm.CallerService(),
		"CalleeApp": tm.CalleeApp(), "CalleeServer": tm.CalleeServer(), "CalleeService": tm.CalleeService(),
		"Namespace": tm.Namespace(), "EnvName": tm.EnvName(), "CalleeSetName": tm.CalleeSetName(),
		"RemoteAddr":    tm.RemoteAddr().String(),
		"ServerRPCName": tm.ServerRPCName(),
		"node":          clues.ConvertNode(node),
	})
	if err != nil {
		if strings.Contains(err.Error(), "code:8000002") {
			log.WarnContextf(ctx, "GetSemantic error: %v", err)
			return nil
		} else {
			return err
		}
	}
	defer func() {
		if err := stream.CloseSend(); err != nil {
			log.WarnContextf(ctx, "GetSemantic CloseSend warn: %+v", err)
		} // 关闭发送
		log.InfoContextf(ctx, "GetSemantic CloseSend success.")
	}()
	for {
		last, err = stream.Recv()
		if idx%10 == 0 || (last != nil && last.IsFinal) { // 每10条 打一条日志
			b, _ = protojson.Marshal(last)
			log.InfoContextf(ctx, "dm reply [%d] is: %s", idx, string(b))
		}
		if err == nil && last != nil && last.Code == 0 {
			ch <- last
		}
		pf.AppendFullSpanElapsed(ctx, "dm_get_semantic_ms", "", "", pkg.Uin(ctx), -1)
		idx++
		if (last != nil && last.IsFinal) || err == io.EOF {
			return nil
		}
		if err != nil {
			if strings.Contains(err.Error(), "context canceled") {
				log.WarnContextf(ctx, "GetSemantic canceled: %v", err)
				err = nil
				return nil // todo 业务怎么处理这种情况 需要考虑下
			} else if strings.Contains(err.Error(), "code:8000002") {
				log.WarnContextf(ctx, "GetSemantic error: %v", err)
				err = nil
			}
			return err
		}
	}
}

// ClearSession 清空 botsession 关联
func (d *dao) ClearSession(ctx context.Context, req *KEP_DM.ClearSessionRequest) error {
	t0 := time.Now()
	opts := []client.Option{WithTrpcSelector()}
	resp, err := d.dmCli.ClearSession(ctx, req, opts...)
	clues.AddTrack4RPC(ctx, "dmCli.ClearSession", req, resp, err, t0)
	if err != nil {
		log.WarnContextf(ctx, "Invoke dm ClearSession error: %+v, req: %+v", err, req)
		return err
	}
	return nil
}

// IsTaskRelease 是否任务流已发布
func (d *dao) IsTaskRelease(ctx context.Context, botBizID uint64, env uint32) (bool, error) {
	req := &KEP.GetTaskFlowReleaseStatusReq{
		BotBizId: strconv.FormatUint(botBizID, 10),
		EnvTag:   env,
	}
	log.InfoContextf(ctx, "isTaskRelease req: %v", req)
	t0 := time.Now()
	status, err := d.configCli.GetTaskFlowReleaseStatus(ctx, req)
	clues.AddTrack4RPC(ctx, "configCli.GetTaskFlowReleaseStatus", req, status, err, t0)
	if err != nil {
		log.ErrorContextf(ctx, "isTaskRelease error: %+v", err)
		return false, err
	}
	log.InfoContextf(ctx, "isTaskRelease rsp: %v, time cost: %d", status.IsRelease, time.Since(t0))
	return status.IsRelease, nil

}

// SetTaskFlowStatus 设置在任务流中状态标记 一天过期，与DM一致。
func (d *dao) SetTaskFlowStatus(ctx context.Context, sessionID string, flag bool) error {
	_, err := d.redis.Do(ctx, "SET", keyTaskFlowStatus(sessionID), flag, "EX", 24*60*60)
	if err != nil {
		log.ErrorContextf(ctx, "SetTaskFlowStatus error: %+v, sessionID: %s", err, sessionID)
		return err
	}
	log.InfoContextf(ctx, "SetTaskFlowStatus success, sessionID: %s, flag: %t", sessionID, flag)
	return nil
}

// IsInTaskFlow 是否在任务流中
func (d *dao) IsInTaskFlow(ctx context.Context, sessionID string) bool {
	rsp, err := redis.Bool(d.redis.Do(ctx, "GET", keyTaskFlowStatus(sessionID)))
	if err != nil {
		return false
	}
	log.InfoContextf(ctx, "IsInTaskFlow success, sessionID: %s, flag: %t", sessionID, rsp)
	return rsp
}

const (
	// WhiteListNone 不在缓存中
	WhiteListNone = 0
	// WhiteListTask 白名单-任务型
	WhiteListTask = 1
	// WhiteListKnowledge 白名单-知识型
	WhiteListKnowledge = 2
)

// SetWhiteList 设置白名单
func (d *dao) SetWhiteList(ctx context.Context, corpID, botBizID uint64, flag int, timeout int) error {
	_, err := d.redis.Do(ctx, "SET", keyTaskFlowStatus(fmt.Sprintf("%d_%d", corpID, botBizID)), flag, "EX", timeout)
	if err != nil {
		log.ErrorContextf(ctx, "SetTaskFlowStatus error: %+v, corpID: %d, botBizID: %d", err, corpID, botBizID)
		return err
	}
	log.InfoContextf(ctx, "SetWhiteList success, corpID: %d, botBizID: %d, flag: %d, timeout: %d",
		corpID, botBizID, flag, timeout)
	return nil
}

// IsInWhiteList 是否在白名单中
func (d *dao) IsInWhiteList(ctx context.Context, corpID, botBizID uint64) int {
	rsp, err := redis.Int(d.redis.Do(ctx, "GET", keyTaskFlowStatus(fmt.Sprintf("%d_%d", corpID, botBizID))))
	if err != nil {
		return WhiteListNone
	}
	log.InfoContextf(ctx, "IsInWhiteList success, corpID: %d, botBizID: %d, flag: %d", corpID, botBizID, rsp)
	return rsp
}

// func (d *dao) GetAnswerFromDocs(ctx context.Context, req *KEP_DM.GetAnswerFromDocsRequest,
//	ch chan *KEP_DM.GetAnswerFromDocsReply, signal chan int) (err error) {
//	log.InfoContextf(ctx, "GetAnswerFromDocs request is: %s", helper.Object2String(req)) // 入口打请求参数日志
//	last := &KEP_DM.GetAnswerFromDocsReply{}
//
//	defer close(ch)
//	defer func() {
//		if last != nil && !last.Finished {
//			last.Finished = true
//			ch <- last
//		}
//	}()
//	b, _ := protojson.Marshal(req)
//	log.InfoContextf(ctx, "dm request is: %s", string(b))
//
//	stream, err := d.dmCli.GetAnswerFromDocs(ctx, req)
//	if err != nil {
//		log.ErrorContextf(ctx, "GetAnswerFromDocs client error: %+v", err)
//		return err
//	}
//	defer stream.CloseSend()
//	if err := stream.Send(req); err != nil {
//		log.ErrorContextf(ctx, "Send getAnswerFromDocs request error: %+v, req: %+v", err, req)
//		return err
//	}
//	idx := 0
//	for {
//		select {
//		case <-ctx.Done():
//			req.ReqType = KEP_DM.AnswerFromDocsRequest_TASK_CANCEL
//			_ = stream.Send(req)
//			return nil
//		case <-signal:
//			close(signal)
//			req.ReqType = KEP_DM.AnswerFromDocsRequest_TASK_CANCEL
//			err = stream.Send(req)
//			log.WarnContextf(ctx, "GetAnswerFromDocs send cancel: %+v, req: %s", err, helper.Object2String(req))
//			return nil
//		default:
//			last, err = stream.Recv()
//			b, _ = protojson.Marshal(last)
//			if idx%10 == 0 { // 每10条 打一条日志
//				log.InfoContextf(ctx, "dm reply is: %s", string(b))
//			}
//			if err != nil {
//				if errors.Is(err, context.Canceled) || strings.Contains(err.Error(), "context canceled") {
//					req.ReqType = KEP_DM.AnswerFromDocsRequest_TASK_CANCEL
//					err = stream.Send(req)
//					log.WarnContextf(ctx, "GetAnswerFromDocs canceled: %+v, req: %s", err, helper.Object2String(req))
//					err = nil
//					return nil
//				}
//				log.ErrorContextf(ctx, "GetAnswerFromDocs error: %+v, req: %+v", err, req)
//				return err
//			}
//
//			if err == nil && last != nil && last.Code == 0 {
//				ch <- last
//			}
//			idx++
//			if (last != nil && last.Finished) || err == io.EOF {
//				return nil
//			}
//		}
//	}
// }
