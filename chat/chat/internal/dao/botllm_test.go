package dao

import (
	"testing"
)

/**
    @author：cooper
    @date：2025/1/8
    @note：
**/

func Test_addClosingPassage(t *testing.T) {
	type args struct {
		str       string
		lastIndex int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test",
			args: args{
				str:       "aaaaa<passage [1]>\nabncdde</passage [1]>\n\n<passage [2]>\nabncd",
				lastIndex: 2,
			},
			want: "aaaaa<passage [1]>\nabncdde</passage [1]>\n\n<passage [2]>\nabncd\n</passage [2]>\n",
		},
		{
			name: "test2",
			args: args{
				str:       "aaaaa<passage [1]>\nabncdde</passage [1]>\n\n<passage [2]>\nabncd</pa",
				lastIndex: 2,
			},
			want: "aaaaa<passage [1]>\nabncdde</passage [1]>\n\n<passage [2]>\nabncd</pa\n</passage [2]>\n",
		},
		{
			name: "test3",
			args: args{
				str:       "aaaaa<passage [1]>\nabncdde</passage [1]>\n\n<passage [2]>\nabncd</pa\n</passage [2]>\n",
				lastIndex: 2,
			},
			want: "aaaaa<passage [1]>\nabncdde</passage [1]>\n\n<passage [2]>\nabncd</pa\n</passage [2]>\n",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := addClosingPassage(tt.args.str, tt.args.lastIndex); got != tt.want {
				t.Errorf("addClosingPassage() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getLastPassageIndex(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{
			name: "test",
			args: args{str: "aaaaa<passage [1]>\nabncdde</passage [1]>\n\n<passage [2]>\nabncdde</passage [2]>\n\n"},
			want: 2,
		},
		{
			name: "test2",
			args: args{str: "aaaaa<passage [1]>\nabncdde</passage [1]>\n\n<passage [2]>\nabncdde</pas"},
			want: 2,
		},
		{
			name: "test3",
			args: args{str: "aaaaa<passage [1]>\nabncdde</passage [1]>\n\n<passage [2]>\nabncd"},
			want: 2,
		},
		{
			name: "test4",
			args: args{str: "[passage 1]aaaaa\nabncd"},
			want: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getLastPassageIndex(tt.args.str); got != tt.want {
				t.Errorf("getLastPassageIndex() = %v, want %v", got, tt.want)
			}
		})
	}
}
