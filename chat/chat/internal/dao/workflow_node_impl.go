package dao

import (
	"context"
	"errors"
	"io"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"google.golang.org/protobuf/encoding/protojson"
)

// NodeDialogWorkflow 对话节点调试
func (d *dao) NodeDialogWorkflow(ctx context.Context, req *KEP_WF_DM.DebugWorkflowNodeDialogRequest,
	ch chan *KEP_WF_DM.DebugWorkflowNodeDialogReply, signal chan int) (err error) {
	var last *KEP_WF_DM.DebugWorkflowNodeDialogReply
	defer close(ch)

	// 补充最终响应
	defer func() {
		if last != nil && !last.IsFinal {
			last.IsFinal = true
			ch <- last
		}
	}()

	start := time.Now()
	reqBytes, _ := protojson.Marshal(req)
	log.InfoContextf(ctx, "NodeDialogWorkflow request: %s", string(reqBytes))

	opts := []client.Option{WithTrpcSelector()}
	cli, err := d.workflowCli.DebugWorkflowNodeDialog(ctx, opts...)
	if err != nil {
		log.ErrorContextf(ctx, "New NodeDialogWorkflow client error: %+v", err)
		return err
	}

	// 确保流关闭
	defer func() {
		if err := cli.CloseSend(); err != nil {
			log.ErrorContextf(ctx, "NodeDialogWorkflow CloseSend error: %+v", err)
		}
		log.InfoContextf(ctx, "NodeDialogWorkflow CloseSend done.")
	}()

	if err = cli.Send(req); err != nil {
		log.ErrorContextf(ctx, "Send NodeDialogWorkflow request error: %+v, req: %+v", err, req)
		return err
	}

	// 调用独立循环处理函数
	last, err = d.nodeDialogWorkflowLoop(ctx, req, cli, ch, signal, start)
	return err
}

// nodeDialogWorkflowLoop 独立处理对话节点调试的流式接收逻辑
func (d *dao) nodeDialogWorkflowLoop(ctx context.Context, req *KEP_WF_DM.DebugWorkflowNodeDialogRequest,
	cli KEP_WF_DM.WorkflowDm_DebugWorkflowNodeDialogClient, ch chan *KEP_WF_DM.DebugWorkflowNodeDialogReply,
	signal chan int, start time.Time) (last *KEP_WF_DM.DebugWorkflowNodeDialogReply, err error) {
	idx, logSpeed := 0, 0
	for {
		select {
		case <-ctx.Done():
			req.RequestType = KEP_WF_DM.RequestType_STOP
			_ = cli.Send(req)
			return last, nil
		case <-signal:
			close(signal)
			req.RequestType = KEP_WF_DM.RequestType_STOP
			err = cli.Send(req)
			log.WarnContextf(ctx, "NodeDialogWorkflow send cancel: %+v, req: %s", err, helper.Object2String(req))
			return last, nil
		default:
			last, err = cli.Recv()
			// 定期日志
			if idx%(10*logOutput[logSpeed]) == 0 || (last != nil && last.IsFinal) {
				rspBytes, _ := protojson.Marshal(last)
				log.InfoContextf(ctx, "NodeDialogWorkflow[%d] rsp: %s, cost: %s", idx, string(rspBytes), time.Since(start))
				if logSpeed < len(logOutput)-1 {
					logSpeed++
				}
			}
			if err == nil && last != nil && last.Code == 0 {
				ch <- last
			}

			// 处理流结束逻辑
			if (last != nil && last.GetIsFinal()) || err == io.EOF {
				if last.GetCode() != 0 {
					return last, pkg.ErrWorkflowRunError
				}
				return last, nil
			}

			// 错误处理
			if err != nil {
				if errors.Is(err, context.Canceled) || strings.Contains(errs.Msg(err), "context canceled") {
					req.RequestType = KEP_WF_DM.RequestType_STOP
					err = cli.Send(req)
					log.WarnContextf(ctx, "NodeDialogWorkflow send cancel: %+v, req: %s", err, helper.Object2String(req))
					return last, nil
				}
				log.ErrorContextf(ctx, "NodeDialogWorkflow error: %+v, req: %+v", err, req)
				return last, err
			}
			idx++
		}
	}
}
