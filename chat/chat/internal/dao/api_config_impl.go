package dao

import (
	"context"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// GetRejectAnswer 获取拒答内容
func (d *dao) GetRejectAnswer(ctx context.Context) ([]string, error) {
	sql := "SELECT pattern FROM t_reject_answer"
	var patterns []string
	if err := d.db.Select(ctx, &patterns, sql); err != nil {
		log.ErrorContextf(ctx, "Get reject answer error: %+v, sql: %s", err, sql)
		return nil, err
	}
	log.InfoContextf(ctx, "Get reject answer: %s", helper.Object2StringEscapeHTML(patterns))
	return patterns, nil
}

// GetPromptVersion 获取prompt版本
func (d *dao) GetPromptVersion(ctx context.Context) ([]*model.PromptVersion, error) {
	sql := "SELECT * FROM t_prompt_version"
	var versions []*model.PromptVersion
	if err := d.db.Select(ctx, &versions, sql); err != nil {
		log.ErrorContextf(ctx, "Get prompt version error: %+v, sql: %s", err, sql)
		return nil, err
	}
	log.InfoContextf(ctx, "Get prompt version: %s", helper.Object2StringEscapeHTML(versions))
	return versions, nil
}

const (
	keywordsFields = `id,robot_id,keywords,similarwords,is_deleted,create_time,update_time`

	getRobotKeywords = `
        SELECT 
            %s 
        FROM
            t_robot_keywords
        WHERE
            id >? AND update_time >= DATE_SUB(NOW(), INTERVAL ? SECOND) 
        ORDER BY 
            id DESC
        LIMIT ?
    `
)

// GetRobotKeywords 获取应用关键词信息
func (d *dao) GetRobotKeywords(ctx context.Context, robotID uint64, interval, limit int) (
	[]*model.RobotKeywords, error) {
	var robotKeywords []*model.RobotKeywords
	querySQL := fmt.Sprintf(getRobotKeywords, keywordsFields)
	args := make([]any, 0)
	args = append(args, robotID, interval, limit)
	if err := d.db.QueryToStructs(ctx, &robotKeywords, querySQL, args...); err != nil {
		log.ErrorContextf(ctx, "GetRobotKeywords|sql:%s args:%+v err:%+v", querySQL, args, err)
		return nil, err
	}
	return robotKeywords, nil
}

func keyRobotKeywords(robotID uint64) string {
	return fmt.Sprintf("qbot:chat:keywords:%d", robotID)
}

// UpdateRobotKeywordsCache 更新应用关键词缓存
func (d *dao) UpdateRobotKeywordsCache(ctx context.Context, keywords []*model.RobotKeywords) error {
	for _, v := range keywords {
		if v.IsDeleted {
			if _, err := d.redis.Do(ctx, "HDEL", keyRobotKeywords(v.RobotID), v.Similarwords); err != nil {
				log.WarnContextf(ctx, "UpdateRobotKeywordsCache DEL err:%+v,similarwords%s,id:%d", err,
					v.Similarwords, v.ID)
			}
		} else {
			if _, err := d.redis.Do(ctx, "HSET", keyRobotKeywords(v.RobotID), v.Similarwords,
				v.Keywords); err != nil {
				log.WarnContextf(ctx, "UpdateRobotKeywordsCache HSET err:%+v,similarwords%s,id:%d", err,
					v.Similarwords, v.ID)
			}
		}
	}
	return nil
}

// ReplaceRobotKeywords 替换应用关键词
func (d *dao) ReplaceRobotKeywords(ctx context.Context, robotID uint64, content string) string {
	key := keyRobotKeywords(robotID)
	isExist, err := redis.Bool(d.redis.Do(ctx, "EXISTS", key))
	if err != nil {
		log.WarnContextf(ctx, "ReplaceRobotKeywords EXISTS err:%+v,key:%v", err, key)
		return content
	}
	if !isExist {
		return content
	}
	keywords, err := redis.Strings(d.redis.Do(ctx, "HGETALL", key))
	if err != nil {
		log.WarnContextf(ctx, "ReplaceRobotKeywords HKEYS err:%+v,key:%v", err, key)
		return content
	}
	if len(keywords) == 0 || len(keywords)%2 > 0 {
		return content
	}
	for i := int(0); i < len(keywords); i = i + 2 {
		if strings.Contains(content, keywords[i]) {
			return strings.ReplaceAll(content, keywords[i], keywords[i+1])
		}
	}
	return content
}

// GetAgentToolConfig 获取所有 Agent 工具配置
func (d *dao) GetAgentToolConfig(ctx context.Context) ([]*model.AgentToolConfig, error) {
	const sql = `
        SELECT
            id,
            category,
            plugin_name,
            tool_name,
            mapping_template,
            display_type,
            display_param,
            example,
            create_time,
            update_time
        FROM agent_tool_config
    `
	var configs []*model.AgentToolConfig
	if err := d.db.Select(ctx, &configs, sql); err != nil {
		log.ErrorContextf(ctx, "GetAgentToolConfig error: %+v, sql: %s", err, sql)
		return nil, err
	}
	log.InfoContextf(ctx, "GetAgentToolConfig: %s", helper.Object2StringEscapeHTML(configs))
	return configs, nil
}
