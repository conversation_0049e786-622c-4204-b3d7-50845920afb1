package dao

import (
	"context"

	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// 这里用于获取固定配置相关的查询

// IConfigInfo 配置信息
type IConfigInfo interface {
	// GetPromptVersion 获取prompt版本
	GetPromptVersion(ctx context.Context) ([]*model.PromptVersion, error)
	// GetRejectAnswer 获取拒答内容
	GetRejectAnswer(ctx context.Context) ([]string, error)
	// GetAgentToolConfig 获取所有 Agent 工具配置
	GetAgentToolConfig(ctx context.Context) ([]*model.AgentToolConfig, error)
}
