package config

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/errors"
)

const (
	agentKey = "agent.yaml" // 对应我们具体想要获取的配置的键位key
)

// Agent Agent配置
type Agent struct {
	ThoughtIcon        string            `yaml:"thought_icon"`         // 思考图标
	WorkflowIcon       string            `yaml:"workflow_icon"`        // 工作流图标
	MaxIterations      int               `yaml:"max_iterations"`       // 最大迭代次数
	ThoughtRoundBefore int               `yaml:"thought_round_before"` // 保存之前的思考轮数，默认为1轮。加上当前轮数，总共为2轮
	CurrentStateSys    string            `yaml:"current_state_sys"`    // 当前状态系统
	CurrentStateUser   string            `yaml:"current_state_user"`   // 当前状态用户
	MainAgentTools     map[string]string `yaml:"main_agent_tools"`     // 主Agent工具
	PdlTools           map[string]string `yaml:"pdl_tools"`            // PDL工具
	UseAgent           []uint64          `yaml:"use_agent"`            // 是否开启Agent, 开发阶段mock，直接走Agent
	UsePdl             []uint64          `yaml:"use_pdl"`              // 是否使用PDL,开发阶段mock，直接走PDL
	RunCode            struct {
		ServiceAddr     string `yaml:"service_addr"`     // 运行代码服务地址
		ResultSeparator string `yaml:"result_separator"` // 运行代码结果分隔符
		CodeTemplate    string `yaml:"code_template"`    // 运行代码模板
	} `yaml:"run_code"` // 运行代码
	TipwordsOpt struct {
		HunyuanVersion string `yaml:"hunyuan_version"` // 使用的混元的模型版本
		Prompt         string `yaml:"prompt"`
	} `yaml:"tipwords_opt"` // 提示词一键优化配置

	GlobalTimeout   uint              `yaml:"global_timeout"` // 全局超时时间
	Procedure       map[string]string `yaml:"procedure"`      // 调用过程
	UnsafeDomain    []string          `yaml:"unsafe_domain"`
	MaxToolResponse int               `yaml:"max_tool_response"`
	AgentModelMap   map[string]string `yaml:"agent_model_map"` // agent模型映射
	McpUnsafeDomain []string          `yaml:"mcp_unsafe_domain"`
	// 中断超时小时数
	InterruptTimeoutHour uint            `yaml:"interrupt_timeout_hour"`
	QBConfig             QQBrowserConfig `yaml:"qb_config"`    // QQ浏览器配置
	SkipBilling          []uint64        `yaml:"skip_billing"` // 是否跳过计费
	SandboxURL           string          `yaml:"sandbox_url"`  // 沙箱地址
}

// AgentConfig Agent配置
var AgentConfig = &Agent{}

// QQBrowserConfig QQ浏览器配置
type QQBrowserConfig struct {
	QBAppBizID    []uint64 `yaml:"qb_app_biz_id"`
	SystemPrompt  string   `yaml:"system_prompt"` // 系统提示词
	QBModelName   string   `yaml:"qb_model_name"`
	MaxIterations int      `yaml:"max_iterations"` // 最大迭代次数
	BrowserTools  []string `yaml:"browser_tools"`  // 浏览器的工具列表
}

// AgentInit 初始化配置
func AgentInit() {
	log.Info("init config")
	// 拿到整个Agent.yaml文件中的内容
	resp, err := config.Get(providerName).Get(context.Background(), agentKey)
	if err != nil {
		log.ErrorContextf(context.Background(), "get Agent config failed. error:%v", err)
	}
	if err = initConfig(AgentConfig, resp); err != nil {
		log.ErrorContextf(context.Background(), "init Agent config failed. error:%v", err)
	}
	log.Info("init Agent config success, cfg：", AgentConfig)
	watchAgentConfigUpdateEvent(AgentConfig)
}

func watchAgentConfigUpdateEvent(configInstance interface{}) {
	resp, err := config.Get(providerName).Watch(context.Background(), agentKey)
	if err != nil {
		log.ErrorContextf(context.Background(), "watch  config failed. error:%v", err)
	}
	go func() {
		defer errors.PanicHandler()
		for r := range resp {
			initConfig(configInstance, r)
		}
	}()
}

// GetAgentConfig 获取工作流配置
func GetAgentConfig() *Agent {
	return AgentConfig
}

// GetInterruptTimeoutHour 获取工具中断的超时时间
func GetInterruptTimeoutHour() uint {
	if AgentConfig.InterruptTimeoutHour == 0 {
		return 24 // 默认24小时
	}
	return AgentConfig.InterruptTimeoutHour
}

// GetAgentGlobalTimeout 获取全局超时时间
func GetAgentGlobalTimeout() uint {
	if AgentConfig.GlobalTimeout == 0 {
		return 1800 // 默认30分钟
	}
	return AgentConfig.GlobalTimeout
}

// GetAgentProcedure 获取调用过程
func GetAgentProcedure(key string) string {
	return AgentConfig.Procedure[key]
}

// IsUsePdl 是否使用PDL
func IsUsePdl(botBizID uint64) bool {
	for _, id := range AgentConfig.UsePdl {
		if id == botBizID {
			return true
		}
	}
	return false
}

// IsUseAgent 是否使用Agent
func IsUseAgent(botBizID uint64) bool {
	for _, id := range AgentConfig.UseAgent {
		if id == botBizID {
			return true
		}
	}
	return false
}

// GetAgentThoughtRound 获取Agent思考轮数
func GetAgentThoughtRound() int {
	if AgentConfig.ThoughtRoundBefore < 1 {
		return 2 // 默认2轮
	}
	return AgentConfig.ThoughtRoundBefore
}

// GetUnsafeDomain 获取不安全域名
func GetUnsafeDomain() []string {
	if len(AgentConfig.UnsafeDomain) == 0 {
		return []string{"cos.ap-guangzhou.myqcloud.com"}
	}
	return AgentConfig.UnsafeDomain
}

// GetMaxToolResponse 获取最大工具响应
func GetMaxToolResponse() int {
	if AgentConfig.MaxToolResponse == 0 {
		return 10 * 1024 // 10K
	}
	return AgentConfig.MaxToolResponse
}

// GetAgentModelMap 获取agent模型映射
func GetAgentModelMap(modelName string) string {
	if AgentConfig.AgentModelMap == nil {
		return modelName
	}
	if v, ok := AgentConfig.AgentModelMap[modelName]; ok {
		return v
	}
	return modelName
}

// GetMCPUnsafeDomains 获取MCP不安全域名
func GetMCPUnsafeDomains() []string {
	if len(AgentConfig.McpUnsafeDomain) == 0 {
		return []string{}
	}
	return AgentConfig.McpUnsafeDomain
}

// IsQBAppBizID 是否是QQ浏览器的appBizID
func IsQBAppBizID(appBizID uint64) bool {
	for _, id := range AgentConfig.QBConfig.QBAppBizID {
		if id == appBizID {
			return true
		}
	}
	return false
}

// GetQBModelName 获取QQ浏览器模型名称
func GetQBModelName() string {
	if AgentConfig.QBConfig.QBModelName == "" {
		return "lke-deepseek-v3-agent"
	}
	return AgentConfig.QBConfig.QBModelName
}

// GetQBSystemPrompt 获取QQ浏览器系统提示词
func GetQBSystemPrompt() string {
	return AgentConfig.QBConfig.SystemPrompt
}

// IsAppIDSkipBilling 是否跳过计费
func IsAppIDSkipBilling(appBizID uint64) bool {
	for _, id := range AgentConfig.SkipBilling {
		if id == appBizID {
			return true
		}
	}
	return false
}
