package config

import (
	"testing"
)

/**
    @author：cooper
    @date：2025/4/27
    @note：
**/

func TestErrorDowngrade(t *testing.T) {
	type args struct {
		msg string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "test1",
			args: args{
				msg: "450040 err",
			},
			want: true,
		},
		{
			name: "test1",
			args: args{
				msg: "450041 err",
			},
			want: true,
		},
		{
			name: "test1",
			args: args{
				msg: "450044 err",
			},
			want: false,
		},
	}
	App().ErrorDowngradeConf = append(App().ErrorDowngradeConf, "450040")
	App().ErrorDowngradeConf = append(App().ErrorDowngradeConf, "450041")
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ErrorDowngrade(tt.args.msg); got != tt.want {
				t.Errorf("ErrorDowngrade() = %v, want %v", got, tt.want)
			}
		})
	}
}
