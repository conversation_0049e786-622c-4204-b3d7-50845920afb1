package config

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/errors"
)

const (
	workflowKey = "workflow.yaml" // 对应我们具体想要获取的配置的键位key
)

// Workflow 工作流配置
type Workflow struct {
	GlobalTimeout uint     `yaml:"global_timeout"` // 全局超时时间
	WhiteList     struct { // 白名单都放在这里
		Unchanged        []uint64 `yaml:"unchanged"`          // 进入工作流不跳出
		SkipQueryRewrite []uint64 `yaml:"skip_query_rewrite"` // 跳过query重写 for singleWorkflow
	} `yaml:"white_list"`
}

// WorkflowConfig 工作流配置
var WorkflowConfig = &Workflow{}

// WorkflowInit 初始化配置
func WorkflowInit() {
	log.Info("init config")
	// 拿到整个Workflow.yaml文件中的内容
	resp, err := config.Get(providerName).Get(context.Background(), workflowKey)
	if err != nil {
		log.ErrorContextf(context.Background(), "get workflow config failed. error:%v", err)
	}
	if err = initConfig(WorkflowConfig, resp); err != nil {
		log.ErrorContextf(context.Background(), "init workflow config failed. error:%v", err)
	}
	log.Info("init workflow config success, cfg：", WorkflowConfig)
	watchWorkflowConfigUpdateEvent(WorkflowConfig)
}

func watchWorkflowConfigUpdateEvent(configInstance interface{}) {
	resp, err := config.Get(providerName).Watch(context.Background(), workflowKey)
	if err != nil {
		log.ErrorContextf(context.Background(), "watch workflow config failed. error:%v", err)
	}
	go func() {
		defer errors.PanicHandler()
		for r := range resp {
			initConfig(configInstance, r)
		}
	}()
}

// GetWorkflowConfig 获取工作流配置
func GetWorkflowConfig() *Workflow {
	return WorkflowConfig
}

// IsWorkflowUnchanged 是否在工作流中
func IsWorkflowUnchanged(id uint64) bool {
	for _, v := range WorkflowConfig.WhiteList.Unchanged {
		if v == id {
			return true
		}
	}
	return false
}

// IsSkipQueryRewrite 是否跳过query重写
func IsSkipQueryRewrite(id uint64) bool {
	for _, v := range WorkflowConfig.WhiteList.SkipQueryRewrite {
		if v == id {
			return true
		}
	}
	return false
}

// GetGlobalTimeout 获取全局超时时间
func GetGlobalTimeout() uint {
	if WorkflowConfig.GlobalTimeout == 0 {
		return 1800 // 默认30分钟
	}
	return WorkflowConfig.GlobalTimeout
}
