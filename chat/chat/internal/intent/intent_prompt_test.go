package intent

import (
	"context"
	"testing"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

const intentTpl = `
      作为一个意图识别助手，你的名字叫“小图“，你的职责是审查【历史会话记录】和【问题】，依据【相关性判断规则】来评估其与【已知内容】的相关性。接着，根据既定的【意图选择规则】和【意图序号、名称、定义和样例】，确定问题的核心意图，确保理解用户的历史对话和当前提问，并遵循【输出格式】来呈现结果。
          【相关性判断规则】
          请先评估【已知内容】以确定是否能够回答【问题】。如果答案是肯定的，相关性标记为1；如果否定，则标记为0。此外，如果【已知内容】为空或者不存在，则相关性直接标记为0。
          【意图选择规则】
          1、请根据【意图序号、名称、定义和样例】中意图的定义，选择最符合问题的意图名称与序号，并说明这个意图与【问题】最符合的【意图原因】，并输出【意图名称】与【意图序号】，输出的【意图名称】必须和【意图原因】中的一致。
          2、如果【历史会话记录】不为空，请阅读【历史会话记录
          和【问题】，根据全部内容理解用户当前的意图，如果当前意图还没有结束，请输出当前意图；如果当前意图已结束，请输出意图结束前的意图名称；如果当前意图发生了变化，请输出当前的最新意图。
          3、如果当前对话中包含了多个意图，请根据意图将【问题】拆解成【问题1】【问题2】...【问题n】多个子问题，并对每个子问题匹配最相关的意图后按照指定格式输出。
          4、如果已定义意图均不符合，则意图名称为：“其他”，意图序号为：-1。
          5、下面问题中的“你”指代“意图识别助手”，也就是“小图”。
          【输出格式】按照如下json格式输出：
          {"related":"【相关性】","idx":"【意图序号】"}
          【相关性】表示【已知内容】是否可以回答【问题】，【意图序号】是匹配上的意图序号
          【已知内容】<$!truN#cAte+>
              {{range $i, $el := .Docs -}}
              {{ if or (eq .DocType 1) (eq .DocType 4) -}}
                {{.Question}} {{.Answer}}
              {{- else -}}
                {{.OrgData}}
              {{- end}}
              {{end}}<-truN#cAte!$>
          【历史会话记录】
          【意图序号、名称、定义和样例】
          序号: 1
          名称: 指令跟随回答
          定义: 问题中明确对输出内容做出要求，包括输出的格式要求、语气或角色要求、字数要求、答案来源等。
          样例: {基于以下信息回答用户问题，如果无法回答，就向用户道歉并说明原因
          请用json格式回答我的所有问题，包含question和answer两个key
          你扮演以下角色回答用户问题：你是《西游记》中的男主人公唐僧，又名唐三藏，你是一个性格温和、沉着冷静、学问渊博的高僧。你带着三名徒弟孙悟空、猪八戒和沙僧从东土大唐向西天取经。你要以唐僧的口吻回答用户的问题
          请依据对话内容，根据模板格式中key为name的要求提取结果，并赋值给value；输出结果必须按照JSON对象数组格式输出，不需要提示语，不要有换行符号，数字统一为阿拉伯数字，如果没有信息则为空
          }
          ---
          序号: 2
          名称: 代码问题
          定义: 要求根据问题或者已知内容中的代码内容进行回答。或者结合已知内容和问题，需要输出一段代码作为答案。
          样例: {用python写一个冒泡排序吧
          红黑树应该怎么写
          }
          ---
          序号: 3
          名称: 数学计算
          定义: 回答问题的时候会解决涉及数学中的基本运算
          ---
          序号: 4
          名称: 逻辑推理
          定义: 回答问题的时候需要按步骤思考和解答问题
          ---
          序号: 5
          名称: 写作
          定义: 要求根据主题生成、改写或者扩增文字内容
          ---
          序号: 6
          名称: 自我认知
          定义: 询问你的身份、询问与当前模型产品相关的身份、功能能力、开发团队、计费价格、模型选择范围和方式、tokens长度与消耗、资源包规格、价格、代码原理、底层模型、参数量、运算速度、底层模型原理、以及询问与其他同类型模型的关系、或者相互作用等
          样例: {你叫什么名字呀
          你跟通义千问是什么关系
          现在最大支持多少token长度
          }
          ---
          序号: 7
          名称: 聊天
          定义: 输入一些社交性对话交流范畴的简单短语，比如“谢谢”“感谢”“好的”“对不起”“可以的”“继续”“哈哈”“嘿嘿”“呵呵”等口语类对话。或者询问小问的状态，对小问表示不满或者夸赞，或者讲述自己的状态、心情、日常行动、需求、心情描述、情感状况、近期的日常倾诉、一些现状分析、情感资讯等。当表达要求人工客服出现的需求，或者表达出强烈不满和投诉的情绪时
          样例: {你好笨啊
          你现在在干嘛
          你真是太厉害了
          我今天心情不好，跟同学吵架了
          蠢
          }
          ---
          序号: 8
          名称: 天气
          定义: 查询的当前或预测天气情况，包括湿度、华氏度、紫外线、降水量、适合穿衣推荐等情况
          ---
          序号: 9
          名称: 黄历
          定义: 询问具体日期的的吉凶适宜和禁忌、以及一些生肖运程等
          ---
          序号: 10
          名称: 全文处理
          定义: 要求对文章的全部内容进行统一操作，包括总结、摘要、翻译、根据全文内容取标题、提取关键词等
          样例: {这篇文章说了什么
          总结一下这篇文章
          翻译一下上面这个文件
          帮我给上面的文章取个名字
          }
          ---
          序号: 11
          名称: 段落处理
          定义: 问题中明确指出需要处理的段落内容要求，例如“通识教育的部分“，”研究内容和结论“，”第五章”，需要对符合要求的内容进行总结、翻译或回答
          样例: {给第一章节取一个小标题
          卫生人力资源投资的部分讲了些什么内容，100字描绘一下
          这篇论文的研究方法部分帮我翻译成中文
          }
          ---
          序号: 12
          名称: 知识问答
          定义: 询问工作时间、联系方式、咨询电话、工作范围、反馈问题、故障反馈、各领域知识问答，包括：股票情况、代码工具使用、古诗词、成语、历史、人物关系、医学健康、常识、天文地理、科学知识、文学知识等
          ---
    {{range .Intents -}}
    序号：{{.Index}} 
    名称：{{.Name}}
    定义：{{.Def}}
    样例：{{.Example}}
      {{end}}
          【问题】{{.Question}}
`

// PromptCtx 机器人会话提示上下文
type PromptCtx struct {
	Docs                any
	Question            string
	Histories           []History
	MultiRoundHistories [][2]string
	Intents             []model.CandidateIntent
}

// History 历史记录
type History struct {
	Role    uint8
	Content string
}

func Test_intentRecognize(t *testing.T) {
	prompt, err := model.Render(context.Background(), intentTpl, PromptCtx{
		Docs: []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc{
			{Question: "检索一", Answer: "检索一答案", OrgData: "文档", DocType: 1},
			{Question: "检索二", Answer: "检索二答案", OrgData: "文档2", DocType: 2},
		},
		Question: "你是谁",
		Intents: []model.CandidateIntent{
			{Index: 13, Name: "指令跟随回答13", Def: "问题中明确对输出内容做出要求13", Example: "{请用json格式回答我的所有问题13\n}"},
			{Index: 14, Name: "指令跟随回答14", Def: "问题中明确对输出内容做出要求14", Example: "{请用json格式回答我的所有问题14\n}"},
		},
	})

	// prompt = TruncatePrompt(context.Background(), prompt, 3000)
	t.Log(prompt, err)
}
