// Package intent 意图识别
package intent

import (
	"context"

	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// ChatIntentAPI 意图识别
type ChatIntentAPI interface {
	// Recognize 识别
	Recognize(context.Context, *botsession.BotSession) model.IntentRsp
}

// Intent 意图识别
type Intent struct {
	dao dao.Dao
}

var (
	intent ChatIntentAPI
)

func init() {
	intent = &Intent{
		dao: dao.New(),
	}
}

// New .
func New() ChatIntentAPI {
	return intent
}
