// Package memory TODO
package memory

import (
	"context"

	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/multimodal"
)

// ChatMemoryAPI 记忆API
type ChatMemoryAPI interface {
	// GetChatHistories 获取对话历史(新版)
	GetChatHistories(ctx context.Context, bs *botsession.BotSession) (botsession.ChatHistories, error)
	// MakeMultiRoundHistories 已废弃
	MakeMultiRoundHistories(ctx context.Context, bs *botsession.BotSession, limit uint,
		useRewrite bool) ([][2]model.HisMessage, error)
	GetMultiModalHistories(ctx context.Context, bs *botsession.BotSession,
		useRewrite bool) ([][2]string, error)
	GetHistoryImagesAndFiles(ctx context.Context, sessionID string) ([]*model.ImageQueue, []*model.FileQueue)

	GetNewRealTimeFileQueues(files []*model.FileInfo) []*model.FileQueue

	GetNewImageQueues(ctx context.Context, bs *botsession.BotSession) []*model.ImageQueue

	UpdateMultiModalHistory(ctx context.Context, oldImageQueues []*model.ImageQueue,
		newImageQueues []*model.ImageQueue, sessionID string)

	UpdateRealTimeFileHistory(ctx context.Context, oldFileQueues []*model.FileQueue,
		newFileQueues []*model.FileQueue, sessionID string)
}

// Memory 历史对话相关
type Memory struct {
	dao        dao.Dao
	multiModal multimodal.ChatMultiModalAPI
}

var (
	memory ChatMemoryAPI
)

func init() {
	memory = &Memory{
		dao:        dao.New(),
		multiModal: multimodal.New(),
	}
}

// New .
func New() ChatMemoryAPI {
	return memory
}
