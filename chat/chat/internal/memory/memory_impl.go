package memory

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

// GetChatHistories 获取用户对话历史信息
func (m *Memory) GetChatHistories(ctx context.Context, bs *botsession.BotSession) (botsession.ChatHistories, error) {
	var histories botsession.ChatHistories
	types := []model.RecordType{bs.Type}
	limit := uint(config.App().Bot.HistoryLimit)
	if bs.ChatHistoryFromAPI != nil {
		histories = wrapChatHistoriesV2(bs.MakeChatAPIHistory())
		log.InfoContextf(ctx, "use api histories:%s", helper.Object2String(histories))
		return histories, nil
	}
	answerRecords, err := m.dao.GetLastNBotRecord(ctx, bs.App.AppBizId, bs.Session, limit, types)
	log.DebugContextf(ctx, "answerRecords:%v", helper.Object2String(answerRecords))
	if err != nil {
		return histories, err
	}
	if len(answerRecords) == 0 {
		return histories, nil
	}
	ids := make([]string, 0, len(answerRecords))
	for _, v := range answerRecords {
		ids = append(ids, v.RelatedRecordID)
	}
	questionRecords, err := m.dao.GetMsgRecordsByRecordID(ctx, ids, bs.App.AppBizId)
	if err != nil {
		return histories, err
	}
	if len(questionRecords) == 0 {
		return histories, pkg.ErrInvalidMsgRecord
	}
	histories.ChatStack = pairV2(answerRecords, questionRecords)
	log.InfoContextf(ctx, "GetChatHistories:%s", helper.Object2String(histories))
	return histories, nil
}

// MakeMultiRoundHistories 构造多轮历史
func (m *Memory) MakeMultiRoundHistories(ctx context.Context, bs *botsession.BotSession, limit uint,
	useRewrite bool) ([][2]model.HisMessage, error) {
	if limit == 0 {
		log.InfoContextf(ctx, "limit is 0")
		return nil, nil
	}
	if bs.ChatHistoryFromAPI != nil {
		histories := bs.MakeChatAPIHistory()
		bs.ChatHistories = wrapChatHistories(histories)
		log.InfoContextf(ctx, "use api histories:%s", helper.Object2String(histories))
		return histories, nil
	}
	types := []model.RecordType{bs.Type}
	records, err := m.dao.GetLastNBotRecord(ctx, bs.App.AppBizId, bs.Session, limit, types)
	log.DebugContextf(ctx, "records:%v", helper.Object2String(records))
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, nil
	}

	ids := make([]string, 0, len(records))
	for _, v := range records {
		ids = append(ids, v.RelatedRecordID)
	}

	relateds, err := m.dao.GetMsgRecordsByRecordID(ctx, ids, bs.App.AppBizId)
	if err != nil {
		return nil, err
	}
	if len(relateds) == 0 {
		return nil, pkg.ErrInvalidMsgRecord
	}
	p := pair(records, relateds, useRewrite)
	clues.AddTrackData(ctx, "eventbus.makeMultiRoundHistories()", map[string]any{
		"records": records, "relateds": relateds, "useRewrite": useRewrite, "result": p,
	})
	bs.ChatHistories = wrapChatHistories(p)
	return p, nil
}

func pair(answerRecords, questionRecords []model.MsgRecord, useRewrite bool) [][2]model.HisMessage {
	mAnswerRecord := make(map[string]model.MsgRecord, len(answerRecords))
	for _, msg := range answerRecords {
		if msg.RelatedRecordID != "" && msg.RecordID != "" {
			mAnswerRecord[msg.RelatedRecordID] = msg // 通过问题找答案
		}
	}
	sort.Slice(questionRecords, func(i, j int) bool {
		return questionRecords[i].CreateTime.Before(questionRecords[j].CreateTime)
	})
	pairs := make([][2]model.HisMessage, 0, len(answerRecords))
	for _, questionRecord := range questionRecords { // 通过问题找答案，确保答案是跟在问题后面
		answerRecord := mAnswerRecord[questionRecord.RecordID]
		answer := answerRecord.GetSafeContent()
		question := helper.When(useRewrite && questionRecord.RewroteContent != "", questionRecord.RewroteContent,
			questionRecord.Content)
		q := model.HisMessage{RecordID: questionRecord.RecordID, Content: question}
		if len(q.Content) < 1 && len(questionRecord.FileInfos) > 0 {
			temp := make([]*model.FileInfo, 0)
			err := jsoniter.UnmarshalFromString(questionRecord.FileInfos, &temp)
			if err != nil || len(temp) < 1 {
				log.ErrorContextf(context.Background(), "json.Unmarshal error: %v", err)
				temp = append(temp, &model.FileInfo{FileName: "未知文件"})
			}
			q.Content = "总结文档：" + temp[0].FileName
		}
		a := model.HisMessage{RecordID: answerRecord.RecordID, Content: answer, Intent: answerRecord.Intent,
			IntentCategory: answerRecord.IntentCategory}
		pairs = append(pairs, [2]model.HisMessage{q, a})
	}
	return pairs
}

// pairV2 构造问题答案对
func pairV2(answerRecords, questionRecords []model.MsgRecord) []botsession.ChatAtom {
	mAnswerRecord := make(map[string]model.MsgRecord, len(answerRecords))
	for _, msg := range answerRecords {
		if msg.RelatedRecordID != "" && msg.RecordID != "" {
			mAnswerRecord[msg.RelatedRecordID] = msg // 通过问题找答案
		}
	}
	var pairs []botsession.ChatAtom
	sort.Slice(questionRecords, func(i, j int) bool {
		return questionRecords[i].CreateTime.Before(questionRecords[j].CreateTime)
	})
	for _, questionRecord := range questionRecords { // 通过问题找答案，确保答案是跟在问题后面
		answerRecord := mAnswerRecord[questionRecord.RecordID]
		answer := answerRecord.GetSafeContent()
		oriQuery := questionRecord.Content
		rewriteQuery := questionRecord.RewroteContent
		if len(oriQuery) < 1 && len(questionRecord.FileInfos) > 0 {
			temp := make([]*model.FileInfo, 0)
			err := jsoniter.UnmarshalFromString(questionRecord.FileInfos, &temp)
			if err != nil || len(temp) < 1 {
				log.ErrorContextf(context.Background(), "json.Unmarshal error: %v", err)
				temp = append(temp, &model.FileInfo{FileName: "未知文件"})
			}
			oriQuery = "总结文档：" + temp[0].FileName
		}
		q := botsession.ChatMessage{Content: oriQuery, Role: chat.LLMRole_USER}
		a := botsession.ChatMessage{Content: answer, Role: chat.LLMRole_ASSISTANT}

		var chatItem botsession.ChatAtom
		chatItem.RecordID = answerRecord.RecordID
		chatItem.RelatedRecordID = questionRecord.RecordID
		chatItem.Intent = answerRecord.Intent
		chatItem.IntentCategory = answerRecord.IntentCategory
		chatItem.OriginQuery = oriQuery
		chatItem.RewriteQuery = rewriteQuery
		chatItem.Message = []botsession.ChatMessage{q, a}
		chatItem.HasOptionCards = len(answerRecord.OptionCards) > 4
		pairs = append(pairs, chatItem)
	}
	return pairs
}

// wrapChatHistories 构造聊天历史记录
func wrapChatHistories(pairs [][2]model.HisMessage) (histories []botsession.ChatHistory) {
	histories = make([]botsession.ChatHistory, 0, len(pairs))
	for _, v := range pairs {
		histories = append(histories, botsession.ChatHistory{
			User:      v[0].Content,
			Assistant: strings.ReplaceAll(v[1].Content, "\n", ""),
			Intention: v[1].Intent + "(" + model.GetRealCategory(v[1].IntentCategory) + ")",
		})
	}
	return histories
}

// wrapChatHistoriesV2 构造聊天历史记录
func wrapChatHistoriesV2(in [][2]model.HisMessage) (wrapedHistory botsession.ChatHistories) {
	wrapedHistory.ChatStack = make([]botsession.ChatAtom, 0, len(in))
	for _, v := range in {
		q := botsession.ChatMessage{Content: v[0].Content, Role: chat.LLMRole_USER}
		a := botsession.ChatMessage{Content: v[1].Content, Role: chat.LLMRole_ASSISTANT}
		wrapedHistory.ChatStack = append(wrapedHistory.ChatStack, botsession.ChatAtom{
			RecordID:        "",
			RelatedRecordID: "",
			Intent:          "",
			IntentCategory:  "",
			OriginQuery:     v[0].Content,
			RewriteQuery:    v[0].Content,
			Message:         []botsession.ChatMessage{q, a},
		})
	}
	return wrapedHistory
}

// GetMultiModalHistories 构造多模态历史记录 for Query改写
func (m *Memory) GetMultiModalHistories(ctx context.Context, bs *botsession.BotSession,
	useRewrite bool) ([][2]string, error) {
	if len(bs.FileInfos) > 0 {
		// 有文件，直接返回
		histories := make([][2]string, 0)
		return histories, nil
	}

	types := []model.RecordType{bs.Type}

	limit := uint(config.App().MultiModal.HistoryLimit)
	records, err := m.dao.GetLastNBotRecord(ctx, bs.App.AppBizId, bs.Session, limit, types)
	clues.AddTrackDataWithError(ctx, "dao.GetLastNBotRecord", map[string]any{
		"botsession": bs.Session, "limit": limit, "types": types, "records": records}, err)
	log.DebugContextf(ctx, "records:%v", helper.Object2String(records))
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, nil
	}

	ids := make([]string, 0, len(records))
	for _, v := range records {
		ids = append(ids, v.RelatedRecordID)
	}

	relates, err := m.dao.GetMsgRecordsByRecordID(ctx, ids, bs.App.AppBizId)
	clues.AddTrackDataWithError(ctx, "dao.GetMsgRecordsByRecordID", map[string]any{"ids": ids, "relates": relates}, err)
	if err != nil {
		return nil, err
	}
	if len(relates) == 0 {
		return nil, pkg.ErrInvalidMsgRecord
	}
	histories, chatHistories := wrapQueryRewriteHistories(records, relates, useRewrite)
	bs.ChatHistories = chatHistories
	clues.AddTrackData(ctx, "MakeMultiModalHistories()", map[string]any{
		"records": records, "relates": relates, "useRewrite": useRewrite, "histories": histories,
	})
	log.DebugContextf(ctx, "MultiModal Histories:%s", helper.Object2String(histories))
	return histories, nil
}

// wrapQueryRewriteHistories 生成问题答案对，records 是答案 relates 是问题
func wrapQueryRewriteHistories(answerRecords, questionRecords []model.MsgRecord,
	useRewrite bool) ([][2]string, []botsession.ChatHistory) {
	mAnswerRecord := make(map[string]model.MsgRecord, len(answerRecords))
	for _, msg := range answerRecords {
		if msg.RelatedRecordID != "" && msg.RecordID != "" {
			mAnswerRecord[msg.RelatedRecordID] = msg // 通过问题找答案
		}
	}
	historyList := make([][2]string, 0)
	chatHistories := make([]botsession.ChatHistory, 0)
	sort.Slice(questionRecords, func(i, j int) bool {
		return questionRecords[i].CreateTime.Before(questionRecords[j].CreateTime)
	})
	for _, questionRecord := range questionRecords { // 通过问题找答案，确保答案是跟在问题后面
		answerRecord := mAnswerRecord[questionRecord.RecordID]
		// 问题中仅包含图片,或者仅仅传了文件（摘要的场景）构造Query改写  过滤掉
		if helper.IsQueryOnlyImage(questionRecord.Content) || len(questionRecord.Content) == 0 {
			continue
		}
		question := helper.When(useRewrite && questionRecord.RewroteContent != "",
			questionRecord.RewroteContent, questionRecord.Content)
		historyList = append(historyList, [2]string{
			question, answerRecord.GetSafeContent(), // caption在答案里面
		})
		chatHistories = append(chatHistories, botsession.ChatHistory{
			User:      question,
			Assistant: strings.ReplaceAll(answerRecord.GetSafeContent(), "\n", ""),
			Intention: answerRecord.Intent + "(" + model.GetRealCategory(answerRecord.IntentCategory) + ")",
		})
	}
	log.DebugContextf(trpc.BackgroundContext(), "historyList:%v", helper.Object2String(historyList))
	return historyList, chatHistories
}

// GetHistoryImagesAndFiles 获取历史记录中的图片和文件
func (m *Memory) GetHistoryImagesAndFiles(ctx context.Context,
	sessionID string) ([]*model.ImageQueue, []*model.FileQueue) {
	// 获取历史记录中存储的图片
	imageHistory, err := m.dao.GetMultiModalHistory(ctx, sessionID)
	oldImageQueues := make([]*model.ImageQueue, 0) // 读取历史记录
	if err != nil || len(imageHistory) == 0 {
		// 没有，看看当前请求是否包含图片
	} else {
		_ = json.Unmarshal([]byte(imageHistory), &oldImageQueues)
	}
	// 获取历史记录中存储的文件
	fileHistory, err := m.dao.GetRealTimeFileHistory(ctx, sessionID)
	oldFileQueues := make([]*model.FileQueue, 0) // 读取历史记录
	if err != nil || len(fileHistory) == 0 {
		// 没有，看看当前请求是否包含文件
	} else {
		_ = json.Unmarshal([]byte(fileHistory), &oldFileQueues)
	}
	return oldImageQueues, oldFileQueues
}

// UpdateMultiModalHistory TODO
func (m *Memory) UpdateMultiModalHistory(ctx context.Context, oldImageQueues []*model.ImageQueue,
	newImageQueues []*model.ImageQueue, sessionID string) {
	finalImageQueues := make([]*model.ImageQueue, 0) // 合并得到最终的记录
	// Round+1 并保存,去掉超过上限的记录
	for _, image := range oldImageQueues {
		image.Round++
		if image.Round <= int(config.App().MultiModal.HistoryLimit) {
			finalImageQueues = append(finalImageQueues, image)
		}
	}

	finalImageQueues = append(finalImageQueues, newImageQueues...)
	b, _ := json.Marshal(finalImageQueues)
	_ = m.dao.SetMultiModalHistory(ctx, sessionID, string(b))
}

// UpdateRealTimeFileHistory TODO
func (m *Memory) UpdateRealTimeFileHistory(ctx context.Context, oldFileQueues []*model.FileQueue,
	newFileQueues []*model.FileQueue, sessionID string) {
	finalFileQueues := make([]*model.FileQueue, 0) // 合并得到最终的记录
	// Round+1 并保存,去掉超过上限的记录
	for _, file := range oldFileQueues {
		file.Round++
		if file.Round <= int(config.App().MultiModal.HistoryLimit) {
			finalFileQueues = append(finalFileQueues, file)
		}
	}

	finalFileQueues = append(finalFileQueues, newFileQueues...)
	b, _ := json.Marshal(finalFileQueues)
	_ = m.dao.SetRealTimeFileHistory(ctx, sessionID, string(b))
}

// GetNewRealTimeFileQueues 获取新的实时文档队列
func (m *Memory) GetNewRealTimeFileQueues(files []*model.FileInfo) []*model.FileQueue {
	docIDs := make([]uint64, 0)
	for _, fileInfo := range files {
		if fileInfo == nil {
			continue
		}
		docIDs = append(docIDs, helper.GetUint64FromString(fileInfo.DocID))
	}
	summary := make(map[string]string)
	if len(docIDs) > 0 {
		summary, _ = m.dao.GetMsgDocSummary(context.Background(), docIDs)
	}
	newFileQueues := make([]*model.FileQueue, 0)
	if len(files) == 0 {
		return newFileQueues
	}
	// 1个或多个文件
	for i, file := range files {
		tempSummary := "summary" // 兜底
		if i < len(summary) {
			tempSummary = summary[file.DocID]

		}
		fileQueue := &model.FileQueue{
			FileInfo: *file,
			Summary:  helper.TruncatedSummary(tempSummary), // 阶段，不用返回太长的摘要
			Round:    1,
		}
		newFileQueues = append(newFileQueues, fileQueue)
	}

	return newFileQueues
}

// GetNewImageQueues 获取新的图片队列
func (m *Memory) GetNewImageQueues(ctx context.Context, bs *botsession.BotSession) []*model.ImageQueue {
	newImageQueues := make([]*model.ImageQueue, 0)
	var caption string
	if len(bs.Images) > 0 {
		// 有图片
		req := &llmm.Request{
			RequestId:   model.RequestID(ctx, bs.RequestID, bs.RecordID),
			ModelName:   config.GetDialogMLLMModelName(bs.App.GetAppBizId()), // Query改写的时候，用小的10B模型
			AppKey:      fmt.Sprintf("%d", bs.Session.BotBizID),
			Messages:    make([]*llmm.Message, 0),
			PromptType:  llmm.PromptType_TEXT,
			RequestType: llmm.RequestType_ONLINE,
			Biz:         "cs",
		}
		rsp, err := m.multiModal.GetCaption(ctx, bs.Images, req, "")
		if err != nil || rsp == nil || rsp.Message == nil { // 忽略？
			caption = "caption"
		} else {
			caption = rsp.Message.Content
		}
	} else {
		return newImageQueues
	}
	bs.Caption = caption

	return processCaption(bs.Images, caption)
}

func processCaption(images []string, caption string) []*model.ImageQueue {
	newImageQueues := make([]*model.ImageQueue, 0)
	if len(images) == 1 { // 一张图片 就用当前的caption结果
		newImageQueues = append(newImageQueues, &model.ImageQueue{
			ImageURL: images[0],
			Caption:  caption,
			Round:    1,
		})
	} else {
		// 多张图片，获取caption的逻辑，看了一下，都是按照【图1】 【图2】这样开头
		captions := helper.ExtractCaption(len(images), caption)
		for i, image := range images {
			temp := "image" // 兜底
			if i < len(captions) {
				temp = captions[i]
			}
			imageQueue := &model.ImageQueue{
				ImageURL: image,
				Caption:  temp,
				Round:    1,
			}
			newImageQueues = append(newImageQueues, imageQueue)
		}
	}
	return newImageQueues

}
