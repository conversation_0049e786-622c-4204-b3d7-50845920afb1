// chat
//
// @(#)knowledge_summary_experience.go  Tuesday, February 27, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package event

import "git.woa.com/ivy/qbot/qbot/chat/helper"

// EventKnowledgeSummaryExperience 知识摘要体验事件
const EventKnowledgeSummaryExperience = "knowledge_summary_experience"

// KnowledgeSummaryExperienceEvent 知识摘要体验事件
type KnowledgeSummaryExperienceEvent struct {
	SessionID         string `json:"session_id"`
	RequestID         string `json:"request_id"`
	Content           string `json:"content"`
	StreamingThrottle int    `json:"streaming_throttle"`
	ModelName         string `json:"model_name"`      // 模型名称
	Method            uint32 `json:"method"`          // 输出方式 1：流式 2：非流式
	Requirement       uint32 `json:"requirement"`     // 输出要求 1：文本总结 2：自定义要求
	RequireCommand    string `json:"require_command"` // 自定义要求指令
}

// Name 事件名称
func (e KnowledgeSummaryExperienceEvent) Name() string {
	return EventKnowledgeSummaryExperience
}

// IsValid 判断事件是否合法
func (e KnowledgeSummaryExperienceEvent) IsValid() bool {
	return helper.CheckSessionID(e.SessionID)
}
