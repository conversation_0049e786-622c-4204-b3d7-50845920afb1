package event

import (
	"fmt"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
)

// EventTipWordOptimization 提示词一键优化事件
const EventTipWordOptimization = "tip_word_optimization"

const (
	// TipsMinLen 提示词的最小长度
	TipsMinLen = 2
)

// AgentRoleTipwordsOptEvent 提示词一键优化事件
type AgentRoleTipwordsOptEvent struct {
	SessionID    string `json:"session_id"`
	RequestID    string `json:"request_id"`
	BotAppKey    string `json:"bot_app_key"`
	VisitorBizID string `json:"visitor_biz_id"`
	Content      string `json:"content"`
}

// Name 事件名称
func (e AgentRoleTipwordsOptEvent) Name() string {
	return EventTipWordOptimization
}

// IsValid 判断请求是否合法
func (e AgentRoleTipwordsOptEvent) IsValid() error {
	if e.Content == "" {
		return errs.New(400, "请求参数错误, 提示词未填写。")
	}
	if utf8.RuneCountInString(e.Content) < TipsMinLen {
		return errs.New(400, fmt.Sprintf("请求参数错误, 提示词长度过短，应该大于%d个字符。", TipsMinLen))
	}
	if !helper.CheckSessionID(e.SessionID) {
		return errs.New(400, "请求参数错误, sessionID错误")
	}
	if e.BotAppKey == "" {
		return errs.New(400, "请求参数错误, AppKey未填写")
	}
	return nil
}
