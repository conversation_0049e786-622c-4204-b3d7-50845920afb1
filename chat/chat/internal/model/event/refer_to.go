package event

import "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"

// EventReferTo 请求参考来源事件
const EventReferTo = "refer_to"

// ReferToEvent 请求参考来源事件消息体
type ReferToEvent struct {
	Answer       string                                           `json:"answer"`
	Question     string                                           `json:"question"`
	<PERSON>s         []*bot_knowledge_config_server.MatchReferReq_Doc `json:"docs"`
	RecordID     string                                           `json:"record_id"`
	BotBizID     uint64                                           `json:"bot_biz_id"`
	IsRelease    bool                                             `json:"is_release"`
	Placeholders map[string]string                                `json:"placeholders"`
	DisableMatch bool                                             `json:"disable_match"`
}

// Name 事件名称
func (e ReferToEvent) Name() string {
	return EventReferTo
}
