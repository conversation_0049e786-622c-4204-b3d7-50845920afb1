package event

import (
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
)

// EventSSeRoleSend SSE 发送事件
const EventSSeRoleSend = "sse_role"

const (
	// TypeWorkflowDescPrompt TODO
	TypeWorkflowDescPrompt = "1"
	// TypeWorkflowParameterExtractor TODO
	TypeWorkflowParameterExtractor = "2"
	// TypeWorkflowLLMPrompt TODO
	TypeWorkflowLLMPrompt = "3"
	// TypeWorkflowLLMTagDesc TODO
	TypeWorkflowLLMTagDesc = "4"
	// TypeWorkflowIntentDesc TODO
	TypeWorkflowIntentDesc = "5"
)

// SSeRoleSendEvent SSE 发送事件
type SSeRoleSendEvent struct {
	OriginContent     string            `json:"origin_content"`
	BotAppKey         string            `json:"bot_app_key"`
	VisitorBizID      string            `json:"visitor_biz_id"`
	StreamingThrottle int               `json:"streaming_throttle"`
	SystemInfo        map[string]string `json:"system_info"`

	SessionID    string `json:"session_id"`
	RequestID    string `json:"request_id"`
	Content      string `json:"content"`
	ModelName    string `json:"model_name"`    // 模型名称
	WorkflowName string `json:"workflow_name"` // 工作流名称
	Type         string `json:"type"`          // 类型	1.工作流描述;2.参数提取节点 - 提示词;3.大模型节点提示词;4.标签提取节点，标签描述;5.意图识别节点
	LLMTagName   string `json:"llm_tag_name"`  // 大模型标签名称，type为4时有效
}

// RoleCommand 角色指令
type RoleCommand struct {
	RoleName        string
	SpeechStyle     []string
	Personality     []string
	Experience      []string
	CapabilityLimit string
	Skills          []Skill
}

// Skill 角色技能
type Skill struct {
	Name           string
	Description    string
	Implementation string
}

// Name 事件名称
func (e SSeRoleSendEvent) Name() string {
	return EventSSeRoleSend
}

// IsValid 判断请求是否合法
func (e SSeRoleSendEvent) IsValid() error {
	if e.Content == "" || utf8.RuneCountInString(e.Content) < 2 {
		log.Errorf("[param invalid] 请求参数错误, content未填写")
		return errs.New(400, "请求参数错误, content未填写")
	}
	if !helper.CheckSessionID(e.SessionID) {
		log.Errorf("[param invalid] 请求参数错误, sessionID错误")
		return errs.New(400, "请求参数错误, sessionID错误")
	}
	if e.Type == "" {
		// 角色描述类型
		if e.BotAppKey == "" {
			log.Errorf("[param invalid] 请求参数错误, AppKey未填写")
			return errs.New(400, "请求参数错误, AppKey未填写")
		}
	} else {
		// 工作流相关的类型
		if e.Type != "1" && e.Type != "2" && e.Type != "3" && e.Type != "4" && e.Type != "5" {
			log.Errorf("[param invalid] 请求参数错误, type类型错误, type:%s", e.Type)
			return errs.New(400, "请求参数错误, type类型错误")
		} else {
			if e.VisitorBizID == "" {
				log.Errorf("[param invalid] 请求参数错误, VisitorBizID未填写")
				return errs.New(400, "请求参数错误, 应用ID未填写")
			}
			// 工作流描述优化，需要工作流名称
			if e.Type == "1" {
				if e.WorkflowName == "" {
					log.Errorf("[param invalid] 请求参数错误, 工作流名称未填写")
					return errs.New(400, "请求参数错误, 工作流名称未填写")
				}
			}
		}
	}
	return nil
}
