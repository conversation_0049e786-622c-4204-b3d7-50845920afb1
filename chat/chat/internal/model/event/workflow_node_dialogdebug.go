package event

import (
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
)

// EventNodeDialog 工作流节点对话事件
const EventNodeDialog = "node_dialog"

// NodeDialogDebugEvent 对话事件消息体
type NodeDialogDebugEvent struct {
	RequestID       string            `json:"request_id"`
	Content         string            `json:"content"`
	SessionID       string            `json:"session_id"`
	CustomVariables map[string]string `json:"custom_variables"` // 自定义参数
	BotAppKey       string            `json:"bot_app_key"`
	VisitorBizID    string            `json:"visitor_biz_id"`
	WorkflowID      string            `json:"workflow_id"` // 工作流ID

	ChatHistory   string            `json:"chat_history"`   // 对话历史
	WorkflowInput map[string]string `json:"workflow_input"` // 工作流输入参数
	NodeInfo      string            `json:"node_info,omitempty"`
}

// IsValid 判断事件是否合法
func (n NodeDialogDebugEvent) IsValid() error {
	if n.SessionID == "" {
		log.Errorf("[param invalid] 请求参数错误, sessionID未填写")
		return errs.New(400, "请求参数错误, sessionID未填写")
	}
	if !helper.CheckSessionID(n.SessionID) {
		log.Errorf("[param invalid] 请求参数错误, sessionID错误")
		return errs.New(400, "请求参数错误, sessionID错误")
	}
	if len(n.VisitorBizID) > 128 {
		log.Errorf("[param invalid] VisitorBizID len:%d > 128", len(n.VisitorBizID))
		return errs.New(400, "请求参数错误, visitorBizID错误")
	}
	if n.BotAppKey == "" {
		log.Errorf("[param invalid] 请求参数错误, AppKey未填写")
		return errs.New(400, "请求参数错误, AppKey未填写")
	}
	if len(n.BotAppKey) > 128 {
		log.Errorf("[param invalid] 请求参数错误, AppKey错误")
		return errs.New(400, "请求参数错误, AppKey错误")
	}
	return nil
}
