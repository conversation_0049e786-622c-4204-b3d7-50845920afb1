package event

import (
	"context"
	"encoding/json"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// EventReply 回复/确认事件
const EventReply = "reply"

// ReplyEvent 回复/确认事件消息体
type ReplyEvent struct {
	RequestID       string             `json:"request_id"`
	SessionID       string             `json:"session_id"`
	Content         string             `json:"content"`
	FromName        string             `json:"from_name"`
	FromAvatar      string             `json:"from_avatar"`
	RecordID        string             `json:"record_id"`
	RelatedRecordID string             `json:"related_record_id"`
	Timestamp       int64              `json:"timestamp"`
	IsFinal         bool               `json:"is_final"`
	IsFromSelf      bool               `json:"is_from_self"`
	CanRating       bool               `json:"can_rating"`
	CanFeedback     bool               `json:"can_feedback"`
	IsEvil          bool               `json:"is_evil"`
	IsLLMGenerated  bool               `json:"is_llm_generated"`
	Knowledge       []ReplyKnowledge   `json:"knowledge"`
	ReplyMethod     model.ReplyMethod  `json:"reply_method"`
	IntentCategory  string             `json:"intent_category"`
	OptionCards     []string           `json:"option_cards"`            // 选项卡, 用于多轮对话,如果没有选项卡 为[]
	Tags            []*ReplyTag        `json:"tags,omitempty"`          // 命中标签列表
	TaskFlow        *TaskFlowDebugInfo `json:"task_flow,omitempty"`     // 任务流调试信息
	Workflow        *WorkflowDebugInfo `json:"work_flow,omitempty"`     // 工作流调试信息
	CustomParams    []string           `json:"custom_params,omitempty"` // 自定义参数, 用户透传用户自定义参数，如果没有自定义参数 为[]
	FileInfos       []*model.FileInfo  `json:"file_infos"`              // 文件信息
	Docs            []*KEP.Doc         `json:"docs"`                    // 文档信息
	QuoteInfos      []*model.QuoteInfo `json:"quote_infos"`             // 引用信息
	ExtraInfo       ReplyExtraInfo     `json:"extra_info"`              // 额外信息

	// 本地工具中断信息
	InterruptInfo *InterruptInfo `json:"interrupt_info"` // 中断信息，意味着必须要端上配合 chat 才能继续执行
}

// String 事件字符串
func (r ReplyEvent) String() string {
	return helper.Object2StringEscapeHTML(r)
}

// ReplyKnowledge 回复事件中的知识
type ReplyKnowledge struct {
	ID    string `json:"id"`
	Type  uint32 `json:"type"`
	SegID string `json:"seg_id,omitempty"`
}

// TaskFlowDebugInfo 任务流调试信息
type TaskFlowDebugInfo struct {
	TaskFlowName string                       `json:"task_flow_name"`
	TaskFlowID   string                       `json:"task_flow_id"`
	QueryRewrite string                       `json:"query_rewrite"`
	HitIntent    string                       `json:"hit_intent"`
	SlotInfo     map[string]*KEP_DM.ValueInfo `json:"slot_info"`
	APIResponse  map[string]*KEP_DM.ValueInfo `json:"api_response"`
	Type         ResponseType                 `json:"type"`
}

// WorkflowDebugInfo 工作流调试信息
type WorkflowDebugInfo struct {
	WorkflowName        string                 `json:"workflow_name,omitempty"`
	WorkflowID          string                 `json:"workflow_id,omitempty"`
	WorkflowRunID       string                 `json:"workflow_run_id,omitempty"` // 工作流运行ID
	OptionCards         []string               `json:"option_cards,omitempty"`    // 选项卡
	CurrentNode         *KEP_WF_DM.RunNodeInfo `json:"current_node,omitempty"`    // 当前节点
	Outputs             []string               `json:"outputs,omitempty"`         // 输出结果
	WorkflowReleaseTime int64                  `json:"workflow_release_time,omitempty"`
}

// ReplyExtraInfo 回复事件中的额外信息
type ReplyExtraInfo struct {
	EChartsInfo []string `json:"e_charts_info"`
}

// ResponseType 回复类型
type ResponseType uint8

// 回复方式
const (
	ResponseTypeLLM     ResponseType = 0 // 任务流回复
	ResponseTypeSilence ResponseType = 1 // 任务流静默
	ResponseTypeSwitch  ResponseType = 2 // 任务流拉回话术
	ResponseTypeCustom  ResponseType = 3 // 任务流自定义回复
)

// ReplyTag 回复事件中的标签
type ReplyTag struct {
	Name       string   `json:"name"`        // 标签名称
	ValueRange []string `json:"value_range"` // 命中标签的范围
}

// Name 事件名称
func (r ReplyEvent) Name() string {
	return EventReply
}

// ParseText2Tags 解析文本为标签
func ParseText2Tags(ctx context.Context, content string) []*ReplyTag {
	if len(content) == 0 {
		return nil
	}
	log.InfoContextf(ctx, "ParseText2Tags %v", content)
	var m map[string]any
	err := json.Unmarshal([]byte(content), &m)
	if err != nil {
		log.ErrorContextf(ctx, "ParseText2Tags|Unmarshal %v", err)
		return nil
	}
	log.InfoContextf(ctx, "ParseText2Tags|Unmarshal %v", m)
	if len(m) == 0 {
		return nil
	}
	var tags []*ReplyTag
	for k, v := range m {
		if len(k) == 0 {
			continue
		}
		e := &ReplyTag{
			Name: k,
		}
		v0, ok := v.([]any)
		log.Infof("ParseText2Tags|TYPE|%T|%+v|%v", v0, v0, ok)
		if !ok {
			tags = append(tags, e)
		}
		m0 := make(map[string]struct{}, len(v0))
		// tag 去重
		for i := range v0 {
			v2 := fmt.Sprintf("%v", v0[i])
			_, exists := m0[v2]
			if exists {
				continue
			}
			e.ValueRange = append(e.ValueRange, v2)
			m0[v2] = struct{}{}
		}
		tags = append(tags, e)
	}
	log.InfoContextf(ctx, "ParseText2Tags|parseText2Tags %v", tags)
	return tags
}

// InterruptInfo 中断返回的数据结构
type InterruptInfo struct {
	CurrentAgent string              `json:"current_agent"` // 当前正在执行的 agent 的 name
	ToolCalls    []*openapi.ToolCall `json:"tool_calls"`    // 需要本地调用的工具
}
