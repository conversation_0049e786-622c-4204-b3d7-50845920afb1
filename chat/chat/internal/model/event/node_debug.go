package event

import (
	"time"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// EventNodeDebugReply 工作流节点调试事件
const EventNodeDebugReply = "node_debug_reply"

// NodeDebugResponseEvent 工作流非对话节点调试事件
type NodeDebugResponseEvent struct {
	SessionID  string                 `json:"session_id"`  // 会话 ID
	RequestID  string                 `json:"request_id"`  // 请求 ID
	RecordID   string                 `json:"record_id"`   // 对应哪条会话, 会话 ID, 用于回答的消息存储使用, 可提前生成, 保存消息时使用
	Elapsed    uint32                 `json:"elapsed"`     // 当前请求执行时间, 单位 ms
	TokenCount uint32                 `json:"token_count"` // 当前请求消耗 token 数
	NodeInfo   *KEP_WF_DM.RunNodeInfo `json:"node_info"`   // 节点信息

	StartTime         time.Time `json:"-"` // 开始时间, 用于记录总耗时
	EventSource       string    `json:"-"` // 本次 token 统计的 event 来源
	FinanceSubBizType string    `json:"-"` // 计费子类型
}

// Name 事件名称
func (n *NodeDebugResponseEvent) Name() string {
	return EventNodeDebugReply
}
