// chat
//
// @(#)knowledge_summary.go  Tuesday, February 27, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package event

import "git.woa.com/ivy/qbot/qbot/chat/helper"

// EventKnowledgeSummary 知识摘要事件
const EventKnowledgeSummary = "knowledge_summary"

// KnowledgeSummaryEvent 知识摘要事件
type KnowledgeSummaryEvent struct {
	SessionID         string `json:"session_id"`
	RequestID         string `json:"request_id"`
	Content           string `json:"content"`
	StreamingThrottle int    `json:"streaming_throttle"`
	ModelName         string `json:"model_name"`      // 模型名称
	Method            uint32 `json:"method"`          // 输出方式 1：流式 2：非流式
	Requirement       uint32 `json:"requirement"`     // 输出要求 1：文本总结 2：自定义要求
	RequireCommand    string `json:"require_command"` // 自定义要求指令
}

// Name 事件名称
func (e KnowledgeSummaryEvent) Name() string {
	return EventKnowledgeSummary
}

// IsValid 判断事件是否合法
func (e KnowledgeSummaryEvent) IsValid() bool {
	return helper.CheckSessionID(e.SessionID)
}
