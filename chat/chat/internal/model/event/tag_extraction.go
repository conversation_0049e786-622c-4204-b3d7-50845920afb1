// chat
//
// @(#)tag_extraction.go  Tuesday, February 27, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package event

import "git.woa.com/ivy/qbot/qbot/chat/helper"

// EventTagExtraction 标签提取事件
const EventTagExtraction = "tag_extraction"

// TagExtractionEvent 标签提取事件
type TagExtractionEvent struct {
	SessionID         string `json:"session_id"`
	RequestID         string `json:"request_id"`
	Content           string `json:"content"`
	StreamingThrottle int    `json:"streaming_throttle"`
	ModelName         string `json:"model_name"` // 模型名称
	Tags              []Tag  `json:"tags"`       // 标签
}

// Tag 标签
type Tag struct {
	Name        string   `json:"name"`        // 标签名称
	Description string   `json:"description"` // 标签描述
	Values      []string `json:"values"`      // 标签取值范围
}

// Name 事件名称
func (e TagExtractionEvent) Name() string {
	return EventTagExtraction
}

// IsValid 判断事件是否合法
func (e TagExtractionEvent) IsValid() bool {
	return helper.CheckSessionID(e.SessionID)
}
