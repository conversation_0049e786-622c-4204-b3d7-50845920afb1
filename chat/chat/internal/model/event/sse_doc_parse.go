package event

import "git.woa.com/ivy/qbot/qbot/chat/helper"

// EventSseDocParse 文档解析事件
const EventSseDocParse = "doc_parse"

// SseDocParseEvent 文档解析事件
type SseDocParseEvent struct {
	BotAppKey string `json:"bot_app_key"`
	ShareCode string `json:"share_code"`
	SessionID string `json:"session_id"`
	RequestID string `json:"request_id"`
	CosBucket string `json:"cos_bucket"`
	FileName  string `json:"file_name"`
	CosURL    string `json:"cos_url"`
	FileType  string `json:"file_type"`
	Size      string `json:"size"`
	ETag      string `json:"e_tag"`
	CosHash   string `json:"cos_hash"`
}

// Name 事件名称
func (e SseDocParseEvent) Name() string {
	return EventSseDocParse
}

// IsValid 判断事件是否合法
func (e SseDocParseEvent) IsValid() bool {
	return helper.CheckSessionID(e.SessionID)
}
