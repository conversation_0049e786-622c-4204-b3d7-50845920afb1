package event

import (
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// EventRating 评价事件
const EventRating = "rating"

// RatingEvent 评价事件消息体
type RatingEvent struct {
	RecordID string          `json:"record_id"`
	Score    model.ScoreType `json:"score"`
	Reasons  []string        `json:"reasons"`
}

// Name 事件名称
func (e RatingEvent) Name() string {
	return EventRating
}

// IsValid 判断事件是否合法
func (e RatingEvent) IsValid() bool {
	if e.RecordID == "" {
		log.Errorf("[param invalid] RecordID is empty")
		return false
	}
	if e.Score != model.ScoreUpvote && e.Score != model.ScoreDownvote {
		log.Errorf("[param invalid] Score is invalid, score: %v", e.Score)
		return false
	}
	return true
}
