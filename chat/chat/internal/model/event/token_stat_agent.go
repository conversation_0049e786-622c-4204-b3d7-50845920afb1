package event

import (
	"fmt"
	"time"

	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
)

// NewAgentProcessingTSProcedure 创建进行中过程
func NewAgentProcessingTSProcedure(name string) Procedure {
	return Procedure{
		Name:   name,
		Title:  config.GetAgentProcedure(name), // 名字使用配置文件的配置
		Status: ProcedureStatusProcessing,
	}
}

// NewToolProcessingTSProcedure 创建进行中过程 for tool
func NewToolProcessingTSProcedure(name, tool, input string) Procedure {
	title := fmt.Sprintf(config.GetAgentProcedure(name), tool)
	return Procedure{
		Name:   name,
		Title:  title, // 名字使用配置文件的配置
		Status: ProcedureStatusProcessing,
		Debugging: ProcedureDebugging{Agent: AgentDebugInfo{
			Input: input,
		}},
	}
}

// NewAgentSuccessTSProcedure 创建成功过程
func NewAgentSuccessTSProcedure(name string, llmmStat *llmm.StatisticInfo,
	debugging ProcedureDebugging, usage []*TokenUsage) Procedure {
	return Procedure{
		Name:              name,
		Title:             config.GetAgentProcedure(name), // 名字使用配置文件的配置
		Status:            ProcedureStatusSuccess,
		LLMMStatisticInfo: llmmStat,
		InputCount:        llmmStat.GetInputTokens(),
		OutputCount:       llmmStat.GetOutputTokens(),
		Count:             llmmStat.GetTotalTokens(),
		TokenUsageDetails: usage,
		Debugging:         debugging,
	}
}

// NewToolSuccessTSProcedure 创建成功过程
func NewToolSuccessTSProcedure(name, output string, p Procedure) Procedure {
	p.Status = ProcedureStatusSuccess
	p.Debugging.Agent.Output = output
	return p
}

// UpdateAgentProcedure 过程更新, 允许重复
func (e *TokenStatEvent) UpdateAgentProcedure(p Procedure) {
	if e == nil {
		return
	}
	if len(p.Name) == 0 {
		return
	}

	var hasProcedure bool // 使用名字标识是否为同一个过程
	for i := range e.Procedures {
		if e.Procedures[i].Name == p.Name && e.Procedures[i].Status == ProcedureStatusProcessing { // 存在, 则更新状态, 补充 count
			e.Procedures[i].Status = p.Status
			e.Procedures[i].ResourceStatus = p.ResourceStatus
			if p.LLMMStatisticInfo != nil {
				s0 := e.Procedures[i].LLMMStatisticInfo
				if s0 == nil {
					s0 = p.LLMMStatisticInfo
				} else {
					s0.InputTokens += p.LLMMStatisticInfo.GetInputTokens()
					s0.OutputTokens += p.LLMMStatisticInfo.GetOutputTokens()
					s0.TotalTokens += p.LLMMStatisticInfo.GetTotalTokens()
				}
				e.Procedures[i].InputCount += p.LLMMStatisticInfo.GetInputTokens()
				e.Procedures[i].OutputCount += p.LLMMStatisticInfo.GetOutputTokens()
				e.Procedures[i].Count += p.LLMMStatisticInfo.GetTotalTokens()
				e.Procedures[i].LLMMStatisticInfo = s0
			}
			e.Procedures[i].Debugging = p.Debugging
			e.Procedures[i].TokenUsageDetails = p.TokenUsageDetails
			hasProcedure = true
			break
		}
	}
	if !hasProcedure {
		e.Procedures = append(e.Procedures, p)
	}

	if len(e.Procedures) == 0 {
		return
	}
	// 只要有一个失败, 就认为整体失败
	p0 := e.Procedures[len(e.Procedures)-1] // 取最后一个元素
	// 检查过程中, 是否有失败, 如果有, 则用失败的
	for i := 0; i < len(e.Procedures); i++ {
		if e.Procedures[i].Status == ProcedureStatusFailed {
			p0 = e.Procedures[i]
			break
		}
	}
	// 资源不可用产生的失败，只记录失败过程，不计入整个运行周期的失败
	if p0.Status == ProcedureStatusFailed && p0.ResourceStatus == ResourceStatusUnAvailable {
		p0.Status = ProcedureStatusSuccess
	}
	e.StatusSummary = p0.Status
	if p0.Status == ProcedureStatusFailed {
		e.StatusSummaryTitle = p0.Title + "失败"
	} else {
		e.StatusSummaryTitle = p0.Title
	}
	if p.LLMMStatisticInfo != nil {
		e.TokenCount += p.LLMMStatisticInfo.GetTotalTokens() // 总数更新
	}
	e.Elapsed = uint32(time.Since(e.StartTime).Milliseconds())
}
