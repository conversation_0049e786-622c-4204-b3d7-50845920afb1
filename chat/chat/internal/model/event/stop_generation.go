package event

import "git.code.oa.com/trpc-go/trpc-go/log"

// EventStopGeneration 停止生成事件
const EventStopGeneration = "stop_generation"

// StopGenerationEvent 停止生成事件消息体
type StopGenerationEvent struct {
	RequestID string `json:"request_id"`
	RecordID  string `json:"record_id"`
}

// Name 事件名称
func (e StopGenerationEvent) Name() string {
	return EventStopGeneration
}

// IsValid 判断事件是否合法
func (e StopGenerationEvent) IsValid() bool {
	if e.RecordID != "" {
		return true
	}
	log.Errorf("[param invalid] RecordID is empty")
	return false
}
