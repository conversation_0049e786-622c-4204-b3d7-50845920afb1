package event

// EventGreeting 欢迎语事件
const EventGreeting = "greeting"

// GreetingEvent 欢迎语事件消息体
type GreetingEvent struct {
	SessionID  string `json:"session_id"`
	Content    string `json:"content"`
	FromName   string `json:"from_name"`
	FromAvatar string `json:"from_avatar"`
	RecordID   string `json:"record_id"`
	Timestamp  int64  `json:"timestamp"`
}

// Name 事件名称
func (e GreetingEvent) Name() string {
	return EventGreeting
}
