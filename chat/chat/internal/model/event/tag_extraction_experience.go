// chat
//
// @(#)tag_extraction_experience.go  Tuesday, February 27, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package event

import "git.woa.com/ivy/qbot/qbot/chat/helper"

// EventTagExtractionExperience 标签提取体验事件
const EventTagExtractionExperience = "tag_extraction_experience"

// TagExtractionExperienceEvent 标签提取体验事件
type TagExtractionExperienceEvent struct {
	SessionID         string `json:"session_id"`
	RequestID         string `json:"request_id"`
	Content           string `json:"content"`
	StreamingThrottle int    `json:"streaming_throttle"`
	ModelName         string `json:"model_name"` // 模型名称
	Tags              []Tag  `json:"tags"`       // 标签
}

// Name 事件名称
func (e TagExtractionExperienceEvent) Name() string {
	return EventTagExtractionExperience
}

// IsValid 判断事件是否合法
func (e TagExtractionExperienceEvent) IsValid() bool {
	return helper.CheckSessionID(e.SessionID)
}
