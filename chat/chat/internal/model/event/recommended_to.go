package event

import "git.woa.com/ivy/qbot/qbot/chat/internal/model"

// EventRecommendedTo 请求推荐问事件
const EventRecommendedTo = "recommended_to"

// RecommendedToEvent 请求推荐问事件消息体
type RecommendedToEvent struct {
	Docs     []model.RecommendedDoc `json:"docs"`
	Question string                 `json:"question"`
	Answer   string                 `json:"answer"`
	RecordID string                 `json:"record_id"`
	Session  *model.Session         `json:"botsession"`
	BotBizID uint64                 `json:"bot_biz_id"`
	Type     model.RecordType       `json:"type"`
}

// Name 事件名称
func (rte RecommendedToEvent) Name() string {
	return EventRecommendedTo
}
