package event

import (
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// EventNodeDebug 工作流非对话节点事件
const EventNodeDebug = "node_debug"

// NodeDebugEvent 非对话事件消息体
type NodeDebugEvent struct {
	RequestID       string            `json:"request_id"`
	CustomVariables map[string]string `json:"custom_variables"` // 自定义参数
	BotAppKey       string            `json:"bot_app_key"`
	VisitorBizID    string            `json:"visitor_biz_id"`
	WorkflowID      string            `json:"workflow_id"` // 工作流ID

	WorkflowInput map[string]string `json:"workflow_input"` // 工作流输入参数
	NodeInfo      string            `json:"node_info,omitempty"`
	ToolInfo      ToolInputData     `json:"tool_info,omitempty"`
}

// ToolInputData 工具节点输入参数
type ToolInputData struct {
	Header string `json:"header,omitempty"` // API节点头信息JSON文本
	Query  string `json:"query,omitempty"`  // API节点URL参数JSON文本
	Body   string `json:"body,omitempty"`   // API或代码节点输入参数JSON文本
}

// IsValid 判断事件是否合法
func (n NodeDebugEvent) IsValid() error {
	if len(n.VisitorBizID) > 128 {
		log.Errorf("[param invalid] VisitorBizID len:%d > 128", len(n.VisitorBizID))
		return errs.New(400, "请求参数错误, visitorBizID错误")
	}
	if n.BotAppKey == "" {
		log.Errorf("[param invalid] 请求参数错误, AppKey未填写")
		return errs.New(400, "请求参数错误, AppKey未填写")
	}
	if len(n.BotAppKey) > 128 {
		log.Errorf("[param invalid] 请求参数错误, AppKey错误")
		return errs.New(400, "请求参数错误, AppKey错误")
	}
	return nil
}
