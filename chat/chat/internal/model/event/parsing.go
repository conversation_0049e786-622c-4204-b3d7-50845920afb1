package event

// EventParsing 文档解析事件
const EventParsing = "parsing"

// ParsingEvent 文档解析事件消息体
type ParsingEvent struct {
	Status       string `json:"status"`
	Process      int32  `json:"process"`
	DocID        string `json:"doc_id"`
	Timestamp    int64  `json:"timestamp"`
	IsFinal      bool   `json:"is_final"`
	TraceID      string `json:"trace_id"`
	SessionID    string `json:"session_id"`
	ErrorMessage string `json:"error_message"`
}

// Name 事件名称
func (e ParsingEvent) Name() string {
	return EventParsing
}
