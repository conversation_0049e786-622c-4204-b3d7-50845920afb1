// Package event 事件
package event

import (
	"errors"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

// EventError 错误事件
const EventError = "error"

// ErrorEvent 错误事件消息体
type ErrorEvent struct {
	Error     Error  `json:"error"`
	RequestID string `json:"request_id"`
}

// Name 事件名称
func (e ErrorEvent) Name() string {
	return EventError
}

// Error 错误
type Error struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// NewErrorEvent 创建错误事件
func NewErrorEvent(requestID string, err error) ErrorEvent {
	log.WarnContextf(trpc.BackgroundContext(), "reply error event: %+v, requestID: %s", err, requestID)
	if isInternalError(err) {
		err = pkg.ErrInternalServerError
	}
	return ErrorEvent{
		RequestID: requestID,
		Error:     Error{Message: errs.Msg(err), Code: errs.Code(err)},
	}
}

// isInternalError 判断是否内部错误
func isInternalError(err error) bool {
	if errors.Is(err, pkg.ErrBadRequest) || errors.Is(err, pkg.ErrForbidden) {
		return false
	}
	var e *errs.Error
	if !errors.As(err, &e) || (e.Type != errs.ErrorTypeBusiness || errs.Code(e) <= 1024) {
		return true
	}
	return false
}
