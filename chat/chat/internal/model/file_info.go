package model

import (
	knowledge "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
)

// FileInfo 文件信息
type FileInfo struct {
	FileName    string `json:"file_name"`
	FileSize    string `json:"file_size"`
	FileURL     string `json:"file_url"`
	FileType    string `json:"file_type"`
	DocID       string `json:"doc_id"`
	FullText    string // 文档全文
	IsTruncated bool   // 是否截断
	CreatedAt   int64  `json:"created_at,omitempty"` // 文件生成时间
}

// ToPbFileInfo 转为pb文件信息
func (f FileInfo) ToPbFileInfo() *pb.FileInfo {
	return &pb.FileInfo{
		FileName: f.FileName,
		FileSize: f.FileSize,
		FileURL:  f.FileURL,
		FileType: f.FileType,
		DocID:    f.DocID,
	}
}

// ToKnowledgeFileInfo 转为pb文件信息
func (f FileInfo) ToKnowledgeFileInfo() *knowledge.FileInfo {
	return &knowledge.FileInfo{
		FileName: f.FileName,
		FileSize: f.FileSize,
		FileUrl:  f.FileURL,
		FileType: f.FileType,
		DocId:    f.DocID,
	}
}

// FileQueue 文件队列
type FileQueue struct {
	FileInfo
	// 摘要
	Summary string `json:"summary"`
	// 轮次
	Round int `json:"round"`
}

// ImageQueue 图片队列
type ImageQueue struct {
	// 图片地址
	ImageURL string `json:"image_url"`
	// 图片描述
	Caption string `json:"caption"`
	// 轮次
	Round int `json:"round"`
}
