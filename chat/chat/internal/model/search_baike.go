package model

// BaikeDisplayV2 TODO
type BaikeDisplayV2 struct {
	Date      string           `json:"date"`
	Favicon   string           `json:"favicon"`
	KdJSONStr BaikeKdJSONStrV2 `json:"kdJsonStr"`
	Title     string           `json:"title"`
	URL       string           `json:"url"`
}

// BaikeKdJSONStrV2 .
type BaikeKdJSONStrV2 struct {
	ModuleList []BaikeModuleV2 `json:"module_list"`
}

// BaikeModuleV2 .
type BaikeModuleV2 struct {
	ItemList []BaikeItemV2 `json:"item_list"`
}

// BaikeItemV2 .
type BaikeItemV2 struct {
	Data BaikeDataV2 `json:"data"`
}

// BaikeDataV2 .
type BaikeDataV2 struct {
	BaikeURL   []string    `json:"baikeURL"`
	Card       BaikeCardV2 `json:"card"`
	OriPicList []string    `json:"oriPicList"`
	SubTitle   string      `json:"subTitle"`
	Title      string      `json:"title"`
}

// BaikeCardV2 .
type BaikeCardV2 struct {
	AbstractInfo string `json:"abstract_info"`
	Ambiguation  string `json:"ambiguation"`
	// CatalogList      []Catalog `json:"-"`
	// DynAbstract      string `json:"dynAbstract"`
	LemmaStructTitle string `json:"lemmaStructTitle"`
	SubTitle         string `json:"subTitle"`
	Title            string `json:"title"`
}

// Catalog .
type Catalog struct {
	URL   string `json:"url"`
	Value string `json:"value"`
}

// BaikeRootV2 .
type BaikeRootV2 struct {
	Display    BaikeDisplayV2 `json:"display"`
	VR         bool           `json:"vr"`
	VRCategory string         `json:"vr_category"`
	VRID       string         `json:"vrid"`
}

// BaikeRootV1 .
type BaikeRootV1 struct {
	Display BaikeDisplayV1 `json:"display"`
	VRID    string         `json:"vrid"`
}

// BaikeDisplayV1 .
type BaikeDisplayV1 struct {
	URL          string           `json:"url"`
	Title        string           `json:"title"`
	AbstractInfo string           `json:"abstract_info"`
	InfoBox      []BaikeInfoBoxV1 `json:"infobox"`
}

// BaikeInfoBoxV1 .
type BaikeInfoBoxV1 struct {
	Abstracts        string `json:"abstracts"`
	SubTitle         string `json:"subTitle"`
	LemmaStructTitle string `json:"lemmaStructTitle"`
	Ambiguation      string `json:"ambiguation"`
	AttributeListVa2 string `json:"attributeListVa2"`
}

// BaikeRootV3 .
type BaikeRootV3 struct {
	Display BaikeDisplayV3 `json:"display"`
	VRID    string         `json:"vrid"`
}

// BaikeDisplayV3 .
type BaikeDisplayV3 struct {
	URL          string `json:"url"`
	Title        string `json:"title"`
	Content      string `json:"content"`
	Data         string `json:"data"`
	ContentTitle string `json:"ContentTitle"`
	AbstractInfo string `json:"abstract_info"`
}
