package model

import (
	_ "embed" // embed 用于导入外部文档
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/encoding/protojson"
	"gopkg.in/yaml.v3"
)

// RegisterPDL 挂号
//
//go:embed tools/挂号.yaml
var RegisterPDL string

// RegisterAPI 挂号API
//
//go:embed tools/挂号_api_info.json
var RegisterAPI string

// OrderingProcessPDL 订餐流程
//
//go:embed tools/订餐流程.yaml
var OrderingProcessPDL string

// OrderingProcessAPI 订餐流程API
//
//go:embed tools/订餐流程_api_info.json
var OrderingProcessAPI string

// Config 定义与 YAML 字段对应的结构体
type Config struct {
	Name      string   `yaml:"Name"`
	Desc      string   `yaml:"Desc"`
	SLOTs     []Slot   `yaml:"SLOTs"`
	APIs      []API    `yaml:"APIs"`
	Answers   []Answer `yaml:"ANSWERs"`
	Procedure string   `yaml:"Procedure"`
}

// Slot slot字段
type Slot struct {
	Name string `yaml:"name"`
	Desc string `yaml:"desc"`
	Type string `yaml:"type"`
}

// API api字段
type API struct {
	Name         string   `yaml:"name"`
	Precondition []string `yaml:"precondition"`
}

// Answer answer字段
type Answer struct {
	Name string `yaml:"name"`
	Desc string `yaml:"desc,omitempty"`
}

// GetConfig 获取配置
func GetConfig(yamlStr string) *Config {
	cfg := &Config{}
	err := yaml.Unmarshal([]byte(yamlStr), &cfg)
	if err != nil {
		log.ErrorContextf(trpc.BackgroundContext(), "yaml unmarshal error: %v", err)
		return nil
	}
	return cfg
}

// ConvertPDL 转换PDL为模型需要的格式
func ConvertPDL(pdl string) string {
	cfg := GetConfig(pdl)
	slotStr := ""
	for _, slot := range cfg.SLOTs {
		slotStr += helper.Object2String(slot) + "\n"
	}
	answerStr := ""
	for _, answer := range cfg.Answers {
		answerStr += helper.Object2String(answer) + "\n"
	}

	res := "<Desc>\n" + cfg.Desc + "\n</Desc>\n" +
		"<SLOT>\n" + strings.Trim(slotStr, "\n") + "\n</SLOT>\n" +
		"<ANSWER>\n" + strings.Trim(answerStr, "\n") + "\n</ANSWER>\n" +
		"<Procedure>\n" + cfg.Procedure + "\n</Procedure>"
	return res
}

// ConvertAPIInfo2String 将API信息转换为字符串
func ConvertAPIInfo2String(apiInfo []*openapi.Tool) string {
	res := ""
	for _, tool := range apiInfo {
		res += helper.Object2String(tool) + "\n"
	}
	return strings.Trim(res, "\n")
}

//go:embed tools/response_to_user.json
var responseToUser string

//go:embed tools/workflow_main.json
var workflowMain string

// GetResponseToUser 获取工具响应
func GetResponseToUser() *openapi.Tool {
	res := &openapi.Tool{}
	data := []byte(config.AgentConfig.PdlTools["response_to_user"])
	err := jsoniter.Unmarshal(data, res)
	if err != nil {
		fmt.Printf("err: %v\n", err)
	}
	return res
}

// GetHandoffParentTool 获取转交到父节点的工具
func GetHandoffParentTool() *openapi.Tool {
	res := &openapi.Tool{}
	data := []byte(config.AgentConfig.PdlTools[SysToolSwitch2Main])
	err := jsoniter.Unmarshal(data, res)
	if err != nil {
		fmt.Printf("err: %v\n", err)
	}
	return res
}

// GetPDLDefaultTools 获取默认工具
func GetPDLDefaultTools(isDebug bool) (tools []*openapi.Tool) {
	tools = append(tools, GetResponseToUser())
	if !isDebug {
		tools = append(tools, GetHandoffParentTool())
	}
	return tools
}

// ConvertAPIInfo 将API信息转换为结构体
func ConvertAPIInfo(apiInfo string) *KEP_WF.PDLToolsInfo {
	// fmt.Printf("apiInfo: %v\n", apiInfo)
	apis := &KEP_WF.PDLToolsInfo{}
	err := protojson.Unmarshal([]byte(apiInfo), apis)
	if err != nil {
		fmt.Printf("error: %v", err)
	}
	fmt.Printf("apis: %v\n", helper.Object2String(apis.Tools))
	return apis
}

// ConvertToOpenAPI 将API信息转换为结构体
func ConvertToOpenAPI(apiInfo *KEP_WF.PDLToolsInfo) []*openapi.Tool {
	res := make([]*openapi.Tool, 0)
	for _, tool := range apiInfo.Tools {
		tmp := &openapi.Tool{
			Type: ToolTypeFunction,
			Function: &openapi.Function{
				Name:        tool.ToolName,
				Description: tool.ToolDesc,
				Parameters:  GetParameters(tool.GetInputs()),
			},
		}
		res = append(res, tmp)
	}
	return res
}

// GetParameters 获取参数
func GetParameters(input []*KEP_WF.InputParam) *openapi.Definition {
	res := &openapi.Definition{
		Type:       DataTypeObject,
		Properties: make(map[string]*openapi.Definition),
		Required:   getRequired(input),
	}
	for _, param := range input {
		res.Properties[param.Name] = &openapi.Definition{
			Type:        WorkflowTypeEnum2DataType[param.Type],
			Description: param.Desc,
			Properties:  nil,
		}
	}
	return res
}

// getRequired 获取必填参数
func getRequired(inputs []*KEP_WF.InputParam) []string {
	required := make([]string, 0)
	for _, param := range inputs {
		if param.GetIsRequired() {
			required = append(required, param.Name)
		}
	}
	return required
}
