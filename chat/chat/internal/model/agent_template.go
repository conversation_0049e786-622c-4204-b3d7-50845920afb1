package model

import (
	_ "embed" // embed 用于引入prompt资源文件
)

// MainAgent 主Agent模板
//
//go:embed prompts/main_agent.jinja
var MainAgent string

// MainAgentWithTaskGoal 主Agent模板带任务目标
//
//go:embed prompts/main_agent_with_taskgoal.jinja
var MainAgentWithTaskGoal string

// MainAgentWithWorkflow 主Agent模板带工作流
//
//go:embed prompts/main_agent_with_workflow.jinja
var MainAgentWithWorkflow string

// MainAgentWithWorkflowAndTaskGoal 主Agent模板带工作流和任务目标
//
//go:embed prompts/main_agent_with_workflow_and_taskgoal.jinja
var MainAgentWithWorkflowAndTaskGoal string

// ReachThinkingLimit 达到思考上限
//
//go:embed prompts/reach_thinking_limit.jinja
var ReachThinkingLimit string

// WorkflowPrompt 工作流模板
//
//go:embed prompts/pdl_agent.jinja
var WorkflowPrompt string
