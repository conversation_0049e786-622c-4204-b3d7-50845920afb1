// Package model TODO
// @Author: halelv
// @Date: 2024/7/9 21:59
package model

import "time"

// MsgRecordTokenStat 消息记录统计信息
type MsgRecordTokenStat struct {
	ID                 uint64    `db:"id"`                   // 主键ID
	RecordID           string    `db:"record_id"`            // 对应哪条会话, 会话 ID, 用于回答的消息存储使用, 可提前生成, 保存消息时使用
	TraceID            string    `db:"trace_id"`             // TraceID
	UsedCount          uint32    `db:"used_count"`           // token 已使用数
	FreeCount          uint32    `db:"free_count"`           // 免费 token 数
	OrderCount         uint32    `db:"order_count"`          // 订单总 token 数
	StatusSummary      string    `db:"status_summary"`       // 当前执行状态汇总, 参考常量 ProcedureStatus* (使用中, 成功, 失败)
	StatusSummaryTitle string    `db:"status_summary_title"` // 当前执行状态汇总后中文展示
	Elapsed            uint32    `db:"elapsed"`              // 当前请求执行时间, 单位 ms
	TokenCount         uint32    `db:"token_count"`          // 当前请求消耗 token 数
	Procedures         string    `db:"procedures"`           // 过程列表, 从详细中去重获得
	AgentThought       string    `db:"agent_thought"`        // Agent 思考事件
	IsDeleted          bool      `db:"is_deleted"`           // 是否删除：0否，1是
	CreateTime         time.Time `db:"create_time"`          // 创建MsgRecordTokenStat时间
	UpdateTime         time.Time `db:"update_time"`          // 更新时间
}
