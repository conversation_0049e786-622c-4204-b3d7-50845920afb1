package model

// Product 临时秘钥产品类型
type Product string

const (
	// ProductASR 语音识别临时秘钥
	ProductASR Product = "asr"
)

// TmpCredential 临时秘钥
type TmpCredential struct {
	Token        string `json:"token"`          // token
	TmpSecretID  string `json:"tmp_secret_id"`  // 临时证书密钥ID
	TmpSecretKey string `json:"tmp_secret_key"` // 临时证书密钥Key
	Expired      uint64 `json:"expired"`        // 过期时间戳
	AppID        uint32 `json:"app_id"`         // 临时秘钥的appid
}
