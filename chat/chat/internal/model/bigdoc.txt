[{"doc_id":24833,"doc_type":2,"related_id":25753115,"org_data":"N9A05-结算业务系统用户使用手册_V5.1.5: \n\n10.4.3 通知存款查询\n\u003e通知存款统计用于查询通知存款的指令及明细。\n\u003e页面下方会出现日期范围内通知存款指令的数量统计。\n\u003e如果状态选择\"正常\"，此页面显示的是存款的当前状态，存款已经全部支取则不在此页面显示。如果状态选择\"作废\"，则显示已经全部支取的存款记录。\n\u003e点击证实书编号可查看具体存取款的明细记录。\n点击[信息查询」-[客户存款」-[通知存款查询」，主界面显示出如下的页面:\n您好，转换的流程架构图如下：\n001524\n00202\n00203\n009\n1\n1001\n181116\n2001\n第三方方\n集团结算中心\n二建\n九恒星财务有限责任公司\n阿萨德回房间开\n九恒星集团有限公司\n101,367,790.80\n1,000,010,098.00\n104,823,709.60\n50,020,000.00\n47,577.10\n9,823.00\n1,000,010,098.00\n1,800.00\n101,367,790.结果类型:选择当前实际余额或者历史时点余额，默认为当前实际余额。选择当前实际余额查询结果显示当前余额;选择历史时点余额时，可选择统计日期差查询所选日期的历史余额。当交易日与起息日不一致是，统计到交易日。\n存入金额:显示证实书的存入金额。部分支取后的新证实书，为原始证实书的存入金额。\n[导出]:点击导出按钮，导出查询结果。\n","related_biz_id":1800948582813247662,"extra":{"emb_rank":1,"es_score":58.93209,"es_rank":1,"rerank_score":9.4375,"rerank_rank":1,"rrf_score":3.5,"rrf_rank":1},"similar_question_extra":{"similar_id":2342424,"similar_question":"这是一个相似问"},{"doc_id":24833,"doc_type":2,"related_id":25753384,"org_data":"N9A05-结算业务系统用户使用手册_V5.1.5: \n\n11.1存款证实书打印\n点击[批量打印」-[存款证实书打印」，主界面显示出如下的页面:\n您好，转换的流程架构图如下：\n该图片的流程架构图如下：\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n31\n32\n33\n34\n35\n36\n37\n38\n39\n40\n41\n42\n43\n44\n45\n46\n47\n48\n49\n50\n51\n52\n53\n54\n55\n56\n57\n58\n59\n60\n61\n62\n63\n64\n图308存款证实书打印\n11.2批量打印\n点击「批量打印」-[存款证实书打印」，主界面显示出如下的页面:\n这张图片显示的是一段文本，看起来像是一个网页的一部分。文本内容是：\n\n```\n第 1 页共 1 页\n显示 1-3 条，共 3 条\n```\n\n这段文本可能表示当前页面是第一页，共有1页，已经显示了1到3条记录，总共3条记录。\n您好，转换的流程架构图如下：\n+\nN\n我的桌面\n导航菜单\n客户:\n业务类型:\n请选择\n业务品种:\n请选择\n查询\n打印证据书\n打印清单\n2018-10-01\n2018-12-01\n3 总金额:1,900,000.00\n客户名称\n存款账号\n业务品种\n证实书号\n金额\n利率(%)\n开始日\n到期日\n原存书号\n原存入金\n客户名称\n存款账号\n业务品种\n证实书号\n金额\n利率(%)\n开始日\n到期日\n原存书号\n原存入金\n991320110201000\n定期存款三个月\nCD00023\n600,000.00\n1.100000\n2018-1查询条件:\n业务类型:选项:定期、通知、保证金定期、保证金通知\n业务品种:选项来源于:柜台结算-\u003e基础设置-\u003e业务品种定义\n按键:\n[查询]:根据筛选条件查询状态为\"正常\"的存款证实书。\n[打印证实书]:选中需要打印的记录，打印存款证实书，可批量打印。\n[打印清单]:选中需要打印记录，打印存款证实书清单。\n打印证实书页面，点击[预览打印]，进入打印页面，如下图，点击[取消]则取消打印。\n![图310打印预览](https://I0)打印清单页面，点击[预览打印]，进入打印页，如下图，点击[取消]则取消打印。\n| 业务标识号 | 起息日 | 止息日 | 计息日期 | 利息 | 天数 |\n| --- | --- | --- | --- | --- | --- |\n| BZJ-201601005 | 2016-08-01 | 2016-08-20 | 2016-08-21 | 28.67 | 20 |\n| BZJ-201601005 | 2016-08-21 | 2016-08-31 | 2016-08-31 | 15.77 | 11 |\n| BZJ-201601005 | 2016-07-111.3计息入账清单打印\n点击[批量打印」-[计息入账清单打印」，主界面显示出如下的页面:\n![图312计息入账清单打印](https://I1)按键:\n[查询]:点击查询在左半屏列出出符合查询条件的所有账户。点击单个账户右边显示该账户的计息记录。\n[打印]:打印出某一个账户的详细计息清单。\n12 消息提醒\n本章节对于结算系统的待处理交易和存款到期业务进行消息提醒。\n12.1结算交易待办提醒\n功能说明:结算交易消息提醒，对于结算业务系统中录入的交易需要复核、审批的交易进行提醒。\n操作说明:\n点击「首页」，在订阅和右上角的最新消息中都可以看到，主界面显示出如下的页面:\n您好，转换的流程架构图如下：\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n31\n32\n33\n34\n35\n36\n37\n38\n39\n40\n41\n42\n43\n44\n45\n46\n47\n48\n49\n50\n51\n52\n53\n54\n55\n56\n57\n58\n59\n60\n61\n62\n63\n64\n65\n66\n67按键:\n[MORE]:可查看所有结算待办交易以及详细信息，页面如下:\n![图314结算交易待办列表](https://I2)[检索]:刷新结算交易待办列表。\n12.2 通知证实书到期提醒\n功能说明:通知证实书到期提醒，提醒到期或逾期的存款，根据这些提醒进行通知支取交易录入进行支取操作。\n操作说明:\n点击「首页」，在订阅和右上角的最新消息中都可以看到，主界面显示出如下的页面:\n![图315通知证实书到期提醒](https://I3)按键:\n[MORE]:可查看所有通知证实书到期提醒以及详细信息，页面如下:\n![图316通知证实书到期列表](https://I4)[刷新]:刷新通知证实书到期列表。\n12.3 定期证实书到期提醒\n功能说明:定期证实书到期提醒，提醒将到期和逾期的定期存款，根据这些提醒进行定期支取交易录入进行支取操作。\n全机构系统参数设置:参数编码0009\"(智能)定期存款到期日为节假日时处理\"设置提前或顺延，支持定期证实书提前或顺延到期提醒。\n1、提前:非工作日到期的定期证实书在非工作日上一个工作日提醒\n2、顺延:非工作日到期的定期证实书在非工作日下一个工作日提醒\n操作说明:\n点击「首页」，在订阅和右上角的最新消息中都可以看到，主界面显示出如下的页面:\n这是一张白色背景的图片，上面有一个黑色的正方形。正方形的中心有一个小的黑色圆圈，圆圈里有三个小的黑色三角形。按键:\n[MORE]:可查看所有定期证实书到期提醒以及详细信息，页面如下:\n![图318定期证实书到期列表](https://I5)[刷新]:刷新定期证实书到期列表。\n12.4智能证实书到期提醒\n功能说明:智能证实书到期提醒，提醒到期和逾期期的智能存款，根据这些提醒进行智能定期支取交易录入进行支取操作。\n全机构系统参数设置:参数编码0009\"(智能)定期存款到期日为节假日时处理\"设置提前或顺延，支持智能定期证实书提前或顺延到期提醒。\n1、提前:非工作日到期的智能定期证实书在非工作日上一个工作日提醒\n2、顺延:非工作日到期的智能定期证实书在非工作日下一个工作日提醒\n操作说明:\n点击「首页」，在订阅和右上角的最新消息中都可以看到，主界面显示出如下的页面:\n这是一张空白的白色画布，上面没有任何的图像或文字。它看起来像是一个简单的空白模板，可以用来创建任何你想要的内容。如果你需要在纸上或电脑上创建一个空白的模板，这个图片就是一个很好的起点。按键:\n[MORE]:可查看所有智能证实书到期提醒以及详细信息，页面如下:\n![图320智能证实书到期列表](https://I6)[刷新]:刷新智能证实书到期列表。\n12.5保证金到期提醒\n功能说明:保证金存单到期提醒，提醒到期保证金存单，根据这些提醒进行保证金支取\n交易录入进行支取操作。\n全机构系统参数设置:参数编码0009\"(智能)定期存款到期日为节假日时处理\"设置提前或顺延，支持保证金存单提前或顺延到期提醒。\n1、提前:非工作日到期的保证金存单在非工作日上一个工作日提醒\n2、顺延:非工作日到期的保证金存单在非工作日下一个工作日提醒\n操作说明:\n点击「首页」，在订阅和右上角的最新消息中都可以看到，主界面显示出如下的页面:\n最新消息:保证金存款到期列表(共1笔)\n![图321保证金到期提醒](https://I7)按键:\n[MORE]:可查看所有保证金存单到期提醒以及详细信息，页面如下:\n![图322保证金存单到期列表](https://I8)[刷新]:刷新保证金存单到期列表。\n12.6账户到期提醒\n功能说明:账户开户时设置了账户到期日，账户到期提醒是根据全机构系统参数设置:参数编码0050\"账户到期提醒提醒天数\"的设置为N天，显示当天以及当天+N天以内的到期账户\n操作说明:\n点击[首页」，在订阅和右上角的最新消息中都可以看到，主界面显示出如下的页面:\n![图323账户到期提醒列表](https://I9)按键:\n密级:受控\n第315页共318页\n[MORE]:可查看所有账户到期提醒以及详细信息，页面如下:\n\n","related_biz_id":1800948582834219174,"org_data_placeholders":[{"key":"(https://I0)","value":"(https://lke.cloud.tencent.com/s/hru9QQhQ?size=max|417.7*191.3|0.70)"},{"key":"(https://I1)","value":"(https://lke.cloud.tencent.com/s/eZiozqud?size=max|455.3*115.7|0.76)"},{"key":"(https://I2)","value":"(https://lke.cloud.tencent.com/s/6GQk0mMO?size=max|416.7*75.7|0.70)"},{"key":"(https://I3)","value":"(https://lke.cloud.tencent.com/s/xgPXfb2O?size=max|416.7*102.7|0.70)"},{"key":"(https://I4)","value":"(https://lke.cloud.tencent.com/s/yG9nPAvu?size=max|415.0*44.0|0.70)"},{"key":"(https://I5)","value":"(https://lke.cloud.tencent.com/s/Y2SC8Jr5?size=max|417.3*151.7|0.70)"},{"key":"(https://I6)","value":"(https://lke.cloud.tencent.com/s/IwxAE4dp?size=max|415.0*121.0|0.70)"},{"key":"(https://I7)","value":"(https://lke.cloud.tencent.com/s/H52Gbq2r?size=max|410.3*60.7|0.69)"},{"key":"(https://I8)","value":"(https://lke.cloud.tencent.com/s/MqLkJIi2?size=max|414.7*61.3|0.70)"},{"key":"(https://I9)","value":"(https://lke.cloud.tencent.com/s/IKLX2LBq?size=max|370.0*100.0|0.62)"}],"extra":{"es_score":52.63999,"es_rank":2,"rerank_score":4.2460938,"rerank_rank":4,"rrf_score":1.2666667,"rrf_rank":4}},{"doc_id":24833,"doc_type":2,"related_id":25753135,"org_data":"N9A05-结算业务系统用户使用手册_V5.1.5: \n主界面显示如下:\n\n\u003ctable\u003e\n\u003ccaption\u003e图10.4.7-1 存取款情况查询\u003c/caption\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"13\" Rowspan=\"1\"\u003e\u003c/td\u003e\n\u003c/tr\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"13\" Rowspan=\"1\"\u003e统计期间: 2019-01-01 -2019-02-14 客户类型: 全部 所属板块: 全部 ▽ 客户: 客户名称:\u003c/td\u003e\n\u003c/tr\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"13\" Rowspan=\"1\"\u003e存款类型: 全部 v 业务品种: 方向: 全部 存款证实书号: 资金来源: 0 查询 导出\u003c/td\u003e\n\u003c/tr\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e□\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e客户编号\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"7\"\u003e\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e客户名称\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e存款类型\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e品种\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"7\"\u003e\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e存款证实书号\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e日期\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e方向\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e金额\u003c/td\u003e\n\u003ctd Colspan=\"2\" Rowspan=\"7\"\u003e\u003c/td\u003e\n\u003c/tr\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"2\"\u003e3001\u003c/td\u003e\n\u003ctd Colspan=\"2\" Rowspan=\"6\"\u003e\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"2\"\u003e九恒星科技股份有限公司 通知存款\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"6\"\u003e\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"2\"\u003e通知存款一天\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"2\"\u003e000000048e\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"5\"\u003e2019-01-03\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e存入\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e200.000.00\u003c/td\u003e\n\u003c/tr\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"5\"\u003e支取\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e200,000.00\u003c/td\u003e\n\u003c/tr\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e9906\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e测试专用6 定期存款\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"4\"\u003e定期存款三个月\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003eCD00019\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"3\"\u003e600,000.00\u003c/td\u003e\n\u003c/tr\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e9908\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e测试专用户811 定期存款\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003eCD00020\u003c/td\u003e\n\u003c/tr\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e9911\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e测试专用户9911 定期存款\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003eCD00021\u003c/td\u003e\n\u003c/tr\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e3001\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e九恒星科技股份有限公司 定期存款\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e000000027e\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e2019-01-18\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e10,000.00\u003c/td\u003e\n\u003c/tr\u003e\n\u003c/table\u003e","related_biz_id":1800948582817441930,"extra":{"emb_rank":20,"es_score":52.280323,"es_rank":4,"rerank_score":3.03125,"rerank_rank":7,"rrf_score":0.87023807,"rrf_rank":5},"similar_question_extra":{"similar_id":2342424,"similar_question":"这是一个相似问"},{"doc_id":24833,"doc_type":2,"related_id":25753137,"org_data":"N9A05-结算业务系统用户使用手册_V5.1.5: \n\u003ctable\u003e\n\u003ccaption\u003e图10.4.7-1 存取款情况查询\u003c/caption\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"13\" Rowspan=\"1\"\u003e\u003c/td\u003e\n\u003c/tr\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"4\"\u003e3001\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"2\"\u003e九恒星科技股份有限公司 通知存款\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"2\"\u003e通知存款一天\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e000000049e\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e2019-01-21\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"4\"\u003e存入\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e200,000.00\u003c/td\u003e\n\u003c/tr\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e000000051e\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"3\"\u003e2019-01-22\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e300,000.00\u003c/td\u003e\n\u003c/tr\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e九恒星科技股份有限公司 保证金活期存款\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"2\"\u003e保证金活期\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e50,000.00\u003c/td\u003e\n\u003c/tr\u003e\n\u003ctr\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e九恒星科技股份有限公司\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e保证金活期存款\u003c/td\u003e\n\u003ctd Colspan=\"1\" Rowspan=\"1\"\u003e70,000.00\u003c/td\u003e\n\u003c/tr\u003e\n\u003c/table\u003e\n\n按键:\n[查询]:根据统计期间、客户类型等查询条件查询出客户存款业务的存入及支取情况。\n","related_biz_id":1800948582817441932,"extra":{"emb_rank":17,"es_score":52.350834,"es_rank":3,"rerank_score":2.203125,"rerank_rank":13,"rrf_score":0.82539684,"rrf_rank":7}},{"doc_id":24833,"doc_type":2,"related_id":25751958,"org_data":"N9A05-结算业务系统用户使用手册_V5.1.5: \n\n5.4.11自动结转失败处理\n5.4.12 现金流补填.\n5.5资金扣划台账登记\n5.6损益类账户结转\n5.7授权码管理\n6表外业务\n6.1重空凭证种类定义\n6.2凭证库存及使用\n6.3凭证领出人库存.\n6.4凭证信息查询\n6.5凭证明细信息查询\n6.6登记簿种类定义\n6.7表外登记簿登记.\n7特殊处理\n7.1柜员日结回退\n7.2机构日结回退\n7.3反关机\n7.4反预提\n7.5反结息\n7.6数据整理\n8交易定义\n8.1交易分类\n8.2交易定义\n8.3交易现金流向设置\n8.4交易输出单据\n8.5交易流程设置\n8.6交易授权\n8.7联动交易规则设置\n8.8初始化交易要素\n9基础设置\n9.1自动预提息设置.\n9.2机构定义.\n9.3 业务品种定义\n9.4科目管理.\n9.5客户\n9.5.1税费收取规则设置.\n9.5.2结息合并入账规则设置\n9.5.3智能存款品种设置.\n9.6账户\n9.6.1自动开户策略.\n9.6.2利息种类设置\n9.6.3 账户性质定义.\n9.6.4账户开户规则定义\n9.6.5结算账户对照设置\n9.6.6账户余额初始.\n9.6.7台账户余额初始.\n9.7其他.\n9.7.1多维码管理.\n9.7.2 现金流向设置\n9.7.3 标准摘要定义\n9.7.4打印模板维护.\n9.7.5机构日结任务设置\n9.7.6全机构参数设置\n9.7.7本机构参数管理\n9.7.8自动开关机设置\n10信息查询\n10.1账户信息\n10.2.1账户查询\n10.2.2 台账户明细查询\n10.2.3账户协定存\n10.2.4新旧账号对照查询\n10.2.5 账户冻结封存查询\n10.2.6 冻结解冻记录查询\n10.2.7 调账记录查询.\n10.2.8存量不动户情况\n10.2.9 保证金协定查询\n2 账户余额积数.\n10.3.1 账户余额积数查询\n10.3.2账户平均余额查询\n10.3.3 客户账户余额查询\n10.3.4 账户余额变动查询\n10.3.5 账户积数调整查询\n10.3.6日均余额情况查询\n10.3.7资金变动情况查询\n10.3客户存款.\n10.4.1保证金存款查询\n10.4.2定期存款查询.\n10.4.3通知存款查询.\n10.4.4智能定期存款查询\n10.4.5客户存款分析\n10.4.6协定计息余额分析\n10.4.7存取款情况查询\n10.4.8客户日均存款查询\n10.4.9 客户存款变动情况\n10.4交易及记账\n10.5.1批量开户交易查询\n10.5.2交易列表查询\n10.5.3账户明细账查询\n10.5.4记账列表查询\n10.5.5记账凭证查询.\n10.5.6记账交易明细查询\n10.5.7批量销户交易查询\n10.5.8账务情况查询\n10.5.9结算单据查询\n10.5计息结息\n10.6.1历史欠息查询\n10.6.2预提结息记录查询.\n10.6.3贷款合同利息查询\n10.6.4利息清单查询\n10.6.5智能支取手续费查询.\n10.6 税费记录查询\n10.7账户利率\n10.8.1账户利率查询\n10.8.2账户利率统计\n10.8操作日志\n10.9.1开关机日志查询\n10.9.2任务日志查询\n10.9.3关键业务操作日志\n10.9 对私批量结果查询\n10.10资金扣划台账查询\n10.11受控账户状态查询\n10.12受控账户余额查询\n11批量打印\n11.1存款证实书打印\n11.2批量打印.\n11.3计息入账清单打印\n12消息提醒\n12.1结算交易待办提醒\n12.2 通知证实书到期提醒.\n12.3 定期证实书到期提醒.\n12.4 智能证实书到期提醒.\n12.5保证金到期提醒.\n12.6账户到期提醒.\n12.7资金冻结到期提醒\n12.8账户冻结到期提醒\n12.9自动预提结息结果\n1系统简介\n1.1系统适用的业务场景\n随着集团公司金融业务的不断发展，财务公司在柜台办理的结算业务相继不断增加，不同业务交易相继会产生纷繁的业务交易处理过程,结算业务系统建立了柜面结算业务处理的通用通道，满足业务需求灵活变更或再造业务流程，并涵盖客户业务发展，提升结算服务水平。\n系统提供账户管理，包括存款类账户、贷款类账户、存放同业账户、业务核算类账户的开立、修改、销户操作。其中成员单位活期存款通过账户开立直接办理业务，并设置账户利\n率和结息相关账户。\n提供银行收款、付款、内转、日常结算交易的录入、复核操作，交易复核通过后，生成相应凭证。系统针对交易和业务凭证，提供了多维度的查询功能。\n支持账户利率调整和积数调整，并提供账户预提利息、账户结息操作，满足结算核算处理。\n针对当日交易提供当日销复核操作，并提供当日销复核交易查询功能。\n提供日常业务处理，包括系统开机、关机，日间账单查阅及打印，日末未结束交易处理、日末不平账交易分析、日结汇总表打印等。\n提供多维度的数据查询，包括账户信息、账户明细账、账户余额、账户利率、结息记录、开关机日志等。\n1.2 系统业务流程图\n以定期存入为例，业务流程图如下:\n您好，转换的流程架构图如下：\n面。点击[已\n录入\n申请列表密级:受控\n1.3 专业术语解释\n![](https://I10)账户:是结算系统处理的基础，所有业务的发生必然以账户作为记账基点;账户是结算系统记账处理的基础;必然归属于某个开户单位;可以归属于科目、也可以不归属于科目;账户下可有台账户、也可以没有台账户。\n■台账户:是结算系统业务计量单元，\"业务\"一般是指有特定期限管理、按业务计息、前后业务管理需求的业务;台账户必然归属于账户;台账户的台账记录了业务的逐笔发生。\n■科目:在结算系统是作为数据汇总的一项口径，账户、台账户可填写归属科目。\n■业务品种:业务的明细分类，此明细分类必然归属于一项明细科目，结算业务发生时，记账凭证中的科目信息从业务品种的科目属性而得。\n■账户性质:是账户本身的分类，是结算系统对账户分类管理的基本口径。以上关系如下:\n| 字符 | 数值 |\n| --- | --- |\n| a | 96 |\n| b | 99 |\n| c | 99 |\n| d | 100 |\n| e | 100 |\n| f | 100 |\n| g | 100 |\n| h | 100 |\n| i | 100 |\n| j | 100 |\n| k | 100 |\n| l | 100 |\n| m | 100 |\n| n | 100 |\n| o | 100 |\n| p | 100 |\n| q | 100 |\n| r | 100 |\n| s | 100 |\n| t | 100 |\n| u | 100 |\n| v | 100 |\n| w | 100图1科目、业务、账户关系\n![](https://I11)台账户开户规则:\n![](https://I12)\u003e台账户开户有两种途径:手工开户及随交易自动开户。\n![](https://I13)台账户的计息方式、限额方式与上级所属账户相同，台账户的科目必须为上级账户所属科目的下级科目，且必然为明细科目。\n![](https://I12)一个账户下的台账户\"业务标识号\"必须唯一。\n![](https://I13)针对账户下某同一业务标识号发起的交易，系统均记录至该业务标识号下的台账记录表中。\n![](https://I14)当业务类交易发起且含业务账号、业务标识号时，系统自动检测该业务账号下是否有同业务标识号的台账户，如果有则在该业务标识号所代表的业务台账户下增加台账，否则先按业务标识号自动建立台账户，然后再在该台账户下记录台账。\n","related_biz_id":1800948579269060760,"org_data_placeholders":[{"key":"(https://I10)","value":"(https://lke.cloud.tencent.com/s/mN7ctvvH?size=min)"},{"key":"(https://I11)","value":"(https://lke.cloud.tencent.com/s/6P6XZGU3?size=min)"},{"key":"(https://I12)","value":"(https://lke.cloud.tencent.com/s/Gsclu4Sa?size=min)"},{"key":"(https://I13)","value":"(https://lke.cloud.tencent.com/s/nrhHRnTt?size=min)"},{"key":"(https://I14)","value":"(https://lke.cloud.tencent.com/s/bCnjU4vD?size=min)"}],"extra":{"es_score":51.71184,"es_rank":5,"rerank_score":3.3613281,"rerank_rank":6,"rrf_score":0.76190484,"rrf_rank":8}}]