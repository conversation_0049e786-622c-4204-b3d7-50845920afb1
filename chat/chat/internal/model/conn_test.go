package model

// func TestLabel_IsValid(t *testing.T) {
//	Convey("TestLabel_IsValid", t, func() {
//		patches := gomonkey.NewPatches()
//		defer patches.Reset()
//		patches.ApplyFunc(configx.MustGetWatched, func(key string, opts ...configx.WatchOption) any {
//			return config.Application{
//				Bot: struct {
//					Timeout             uint             `yaml:"timeout"`
//					QueueTimeout        uint             `yaml:"queue_timeout"`
//					RewriteThreshold    int              `yaml:"rewrite_threshold"`
//					BusyReplies         map[uint8]string `yaml:"busy_replies"`
//					EvilReply           string           `yaml:"evil_reply"`
//					RejectedReply       string           `yaml:"rejected_reply"`
//					ResponseChannelSize uint             `yaml:"response_channel_size"`
//					StopGeneration      struct {
//						KeyExpire     uint `yaml:"key_expire"`
//						CheckInterval uint `yaml:"check_interval"`
//					} `yaml:"stop_generation"`
//					Throttles struct {
//						Check     int `yaml:"check"`
//						Streaming int `yaml:"streaming"`
//					} `yaml:"throttles"`
//					MaxConcurrence struct {
//						Standard uint `yaml:"standard"`
//					} `yaml:"max_concurrence"`
//					RewriteHistoryLimit uint32 `yaml:"rewrite_history_limit"`
//					RewriteMessageLimit uint32 `yaml:"rewrite_message_limit"`
//					HistoryLimit        uint32 `yaml:"history_limit"`
//					SingleMessageLimit  uint32 `yaml:"single_message_limit"`
//					RoleDescription     string `yaml:"role_description"`
//					VisitorLabels       struct {
//						LabelCount       int `yaml:"label_count"`
//						LabelNameLength  int `yaml:"label_name_length"`
//						LabelValueCount  int `yaml:"label_value_count"`
//						LabelValueLength int `yaml:"label_value_length"`
//					} `yaml:"visitor_labels"`
//				}(struct {
//					Timeout             uint
//					QueueTimeout        uint
//					RewriteThreshold    int
//					BusyReplies         map[uint8]string
//					EvilReply           string
//					RejectedReply       string
//					ResponseChannelSize uint
//					StopGeneration      struct {
//						KeyExpire     uint `yaml:"key_expire"`
//						CheckInterval uint `yaml:"check_interval"`
//					}
//					Throttles struct {
//						Check     int `yaml:"check"`
//						Streaming int `yaml:"streaming"`
//					}
//					MaxConcurrence struct {
//						Standard uint `yaml:"standard"`
//					}
//					RewriteHistoryLimit uint32
//					RewriteMessageLimit uint32
//					HistoryLimit        uint32
//					SingleMessageLimit  uint32
//					RoleDescription     string
//					VisitorLabels       struct {
//						LabelCount       int `yaml:"label_count"`
//						LabelNameLength  int `yaml:"label_name_length"`
//						LabelValueCount  int `yaml:"label_value_count"`
//						LabelValueLength int `yaml:"label_value_length"`
//					}
//				}{
//					VisitorLabels: struct {
//						LabelCount       int `yaml:"label_count"`
//						LabelNameLength  int `yaml:"label_name_length"`
//						LabelValueCount  int `yaml:"label_value_count"`
//						LabelValueLength int `yaml:"label_value_length"`
//					}{
//						LabelCount:       10,
//						LabelNameLength:  5,
//						LabelValueCount:  5,
//						LabelValueLength: 5,
//					},
//				}),
//			}
//		})
//		Convey("标签名为空字符串", func() {
//			label := Label{}
//			So(label.IsValid(), ShouldBeFalse)
//		})
//		Convey("标签名正则校验不通过", func() {
//			label := Label{
//				Name: "非法",
//			}
//			So(label.IsValid(), ShouldBeFalse)
//		})
//		Convey("标签名长度超过限制", func() {
//			label := Label{
//				Name: "label_",
//			}
//			So(label.IsValid(), ShouldBeFalse)
//		})
//		Convey("标签值为空", func() {
//			label := Label{
//				Name: "label",
//			}
//			So(label.IsValid(), ShouldBeFalse)
//		})
//		Convey("标签值数量超过限制", func() {
//			label := Label{
//				Name:   "label",
//				Values: []string{"val1", "val2", "val3", "val4", "val5", "val6"},
//			}
//			So(label.IsValid(), ShouldBeFalse)
//		})
//		Convey("标签值长度超过限制", func() {
//			label := Label{
//				Name:   "label",
//				Values: []string{"val1", "val2", "val3", "val4", "value5"},
//			}
//			So(label.IsValid(), ShouldBeFalse)
//		})
//		Convey("合法标签", func() {
//			label := Label{
//				Name:   "label",
//				Values: []string{"val1", "val2", "val3", "val4", "val5"},
//			}
//			So(label.IsValid(), ShouldBeTrue)
//		})
//	})
// }
