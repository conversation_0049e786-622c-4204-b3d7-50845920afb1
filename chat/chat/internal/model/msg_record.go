package model

import (
	"context"
	"encoding/json"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	jsoniter "github.com/json-iterator/go"
)

// ReplyMethod 回复方式
type ReplyMethod uint8

// 回复方式
const (
	ReplyMethodModel          ReplyMethod = 1  // 大模型直接回复
	ReplyMethodBare           ReplyMethod = 2  // 保守回复, 未知问题回复
	ReplyMethodRejected       ReplyMethod = 3  // 拒答问题回复
	ReplyMethodEvil           ReplyMethod = 4  // 敏感回复
	ReplyMethodPriorityQA     ReplyMethod = 5  // 问答对直接回复, 已采纳问答对优先回复
	ReplyMethodGreeting       ReplyMethod = 6  // 欢迎语回复
	ReplyMethodBusy           ReplyMethod = 7  // 并发超限回复
	ReplyGlobalKnowledge      ReplyMethod = 8  // 全局干预知识
	ReplyMethodTaskFlow       ReplyMethod = 9  // 任务流程过程回复, 当历史记录中 task_flow.type = 0 时, 为大模型回复
	ReplyMethodTaskAnswer     ReplyMethod = 10 // 任务流程答案回复
	ReplyMethodSearch         ReplyMethod = 11 // 搜索引擎回复
	ReplyMethodDecorator      ReplyMethod = 12 // 知识润色后回复
	ReplyMethodImage          ReplyMethod = 13 // 图片理解回复
	ReplyMethodFile           ReplyMethod = 14 // 实时文档回复
	ReplyMethodClarifyConfirm ReplyMethod = 15 // 澄清确认回复
	ReplyMethodWorkflow       ReplyMethod = 16 // 工作流回复
	ReplyMethodWorkflowAnswer ReplyMethod = 17 // 工作流运行结束
	ReplyMethodAgent          ReplyMethod = 18 // 智能体回复
	ReplyMethodMultiIntent    ReplyMethod = 19 // 多意图回复
	ReplyMethodInterrupt      ReplyMethod = 20 // 中断回复
)

// ModelType 模型类型
type ModelType string

// 模型类型名称
const (
	ModelTypeSummary                    ModelType = "summary"
	ModelTypeAbstract                   ModelType = "abstract"
	ModelTypeMessage                    ModelType = "normal_message"
	ModelTypeTransfer                   ModelType = "transfer"
	ModelTypeQueryRewrite               ModelType = "query_rewrite"                        // 改写
	ModelTypeComplexQueryRewrite        ModelType = "complex_query_rewrite"                // 复杂query改写
	ModelTypeDeepSeekQueryRewrite       ModelType = "deep_seek_query_rewrite"              // deepseek改写
	ModelTypeIntentRecognize            ModelType = "intent_recognize"                     // 意图识别
	ModelTypeDeepSeekIntentRecognize    ModelType = "deep_seek_intent_recognize"           // deepseek意图识别
	ModelTypeMessageNonGeneralKnowledge ModelType = "normal_message_non_general_knowledge" // 拒答
	ModelTypeSummaryRecognize           ModelType = "summary_recognize"                    // 知识摘要
	ModelTypeClassifyExtract            ModelType = "classify_extract"                     // 标签提取
	ModelTypeRecommended                ModelType = "recommended"                          // 推荐模型
	ModelTypeMLLMComprehension          ModelType = "mllm_comprehension"                   // 多模态阅读理解
	ModelTypeFaqDef                     ModelType = "faq_def"                              // faq定义
	ModelTypeFaqReject                  ModelType = "faq_reject_prompt"                    // faq拒答prompt
	ModelTypeFaqSystem                  ModelType = "faq_system_prompt"                    // 自定义的faq系统prompt
	ModelTypeRealTimeDoc                ModelType = "real_time_doc"                        // 实时文档问答
	ModelTypeDsR1SummaryPrompt          ModelType = "ds_r1_summary_prompt"                 // deepseek r1总结
	ModelTypeMLLMDialog                 ModelType = "mllm_dialog"                          // 图文问答
)

// DefaultFaqSystemPrompt 默认的faq系统prompt，如果是它，说明用户没设置过
const DefaultFaqSystemPrompt = "default_faq_system_prompt"

// Agent相关的模型类型
const (
	ModelTypeAgentMain                 ModelType = "main_agent"
	ModelTypeAgentSub                  ModelType = "sub_agent"
	ModelTypeAgentMainTaskGoal         ModelType = "main_agent_with_taskgoal"
	ModelTypeAgentMainWorkflow         ModelType = "main_agent_with_workflow"
	ModelTypeAgentMainWorkflowTaskGoal ModelType = "main_agent_with_workflow_and_taskgoal"
	ModelTypeAgentMainReachLimit       ModelType = "reach_thinking_limit"
	ModelTypeAgentWorkflow             ModelType = "pdl_agent"

	ModelTypeAgentMainR1                 ModelType = "main_agent_R1"
	ModelTypeAgentMainTaskGoalR1         ModelType = "main_agent_with_taskgoal_R1"
	ModelTypeAgentMainWorkflowR1         ModelType = "main_agent_with_workflow_R1"
	ModelTypeAgentMainWorkflowTaskGoalR1 ModelType = "main_agent_with_workflow_and_taskgoal_R1"
)

// RecordType 消息类型
type RecordType uint8

// 消息记录类型
const (
	RecordTypeMessage    RecordType = 1
	RecordTypeSearch     RecordType = 2
	RecordTypeExperience RecordType = 3
	// RecordTypeGreeting   RecordType = 4
	// RecordTypeSummary    RecordType = 5
	RecordTypeGreeting RecordType = 6
	RecordTypeOPDebug  RecordType = 10
)

// SourceType 消息发送接收人类型
type SourceType uint8

// 消息发送接收人类型
const (
	SourceTypeSeat       SourceType = 1  // 已废弃
	SourceTypeVisitor    SourceType = 2  // 页面对话，腾讯云账号登录
	SourceTypeExpVisitor SourceType = 4  // 页面对话，手机号登录
	SourceTypeAPIVisitor SourceType = 5  // API接入
	SourceTypeRobot      SourceType = 65 // 机器人回复
)

// ScoreType 评分类型
type ScoreType uint8

// 评分类型
const (
	ScoreNone     ScoreType = 0
	ScoreUpvote   ScoreType = 1
	ScoreDownvote ScoreType = 2
)

// MsgSource 消息来源
type MsgSource struct {
	ID   uint64
	Type SourceType
}

// MsgStatisticInfo 消息统计信息
type MsgStatisticInfo struct {
	BotBizID      uint64 `db:"bot_biz_id"`
	StatisticInfo string `db:"statistic_info"`
}

// MsgRecord 消息记录
type MsgRecord struct {
	ID               uint64      `db:"id"`
	Type             RecordType  `db:"type"`
	BotBizID         uint64      `db:"bot_biz_id"`
	RecordID         string      `db:"record_id"`
	SessionID        string      `db:"session_id"`
	ToID             uint64      `db:"to_id"`
	ToType           SourceType  `db:"to_type"`
	FromID           uint64      `db:"from_id"`
	FromType         SourceType  `db:"from_type"`
	Content          string      `db:"content"`
	Caption          string      `db:"caption"`
	FileInfos        string      `db:"file_infos"`
	StatisticInfo    string      `db:"statistic_info"`
	Labels           string      `db:"labels"`
	Intent           string      `db:"intent"`
	IntentCategory   string      `db:"intent_category"`
	RewroteContent   string      `db:"rewrote_content"`
	HasRead          bool        `db:"has_read"`
	Reference        string      `db:"reference"`
	Prompt           string      `db:"prompt"`
	RewritePrompt    string      `db:"rewrite_prompt"`
	ReplyMethod      ReplyMethod `db:"reply_method"`
	Knowledge        string      `db:"knowledge"`
	RejectedQuestion string      `db:"rejected_question"`
	Score            ScoreType   `db:"score"`
	Reason           string      `db:"reason"`
	IsDeleted        bool        `db:"is_deleted"`
	RelatedRecordID  string      `db:"related_record_id"`
	ResultCode       uint32      `db:"result_code"`
	ResultType       uint32      `db:"result_type"`
	RatingTime       time.Time   `db:"rating_time"`
	CreateTime       time.Time   `db:"create_time"`
	UpdateTime       time.Time   `db:"update_time"`
	CfgVersionID     uint64      `db:"cfg_version_id"`
	TraceID          string      `db:"trace_id"`
	TokenStat        string      `db:"token_stat"`   // 当次使用 token 统计
	OptionCards      string      `db:"option_cards"` // 选项卡, 用于多轮对话,如果没有选项卡 为[]
	TaskFlow         string      `db:"task_flow"`    // 对应 event.TaskFlowDebugInfo
	QuoteInfos       string      `db:"quote_infos"`  // 引用信息
}

// IsLLMGenerated 判断是否由大模型生成
func (r MsgRecord) IsLLMGenerated() bool {
	return r.IsFromBot() && !r.IsBotEvil() && r.ReplyMethod != ReplyMethodBare &&
		r.ReplyMethod != ReplyMethodRejected && r.ReplyMethod != ReplyMethodEvil &&
		r.ReplyMethod != ReplyMethodPriorityQA && r.ReplyMethod != ReplyMethodGreeting &&
		r.ReplyMethod != ReplyMethodBusy && r.ReplyMethod != ReplyGlobalKnowledge
}

// NeedUpdateSession 判断是否需要更新会话
func (r MsgRecord) NeedUpdateSession() bool {
	if r.FromType == SourceTypeAPIVisitor {
		return false
	}
	if r.Type != RecordTypeMessage && r.Type != RecordTypeExperience && r.Type != RecordTypeGreeting {
		return false
	}
	if r.IsHumanEvil() {
		return false
	}
	return true
}

// IsBotEvil 判断是否是机器人敏感词
func (r MsgRecord) IsBotEvil() bool {
	return r.FromType == SourceTypeRobot && r.ResultCode == ispkg.ResultEvil
}

// IsHumanEvil 判断是否是人工敏感词
func (r MsgRecord) IsHumanEvil() bool {
	return r.FromType != SourceTypeRobot && r.ResultCode == ispkg.ResultEvil
}

// GetSafeContent 获取安全的回复内容
func (r MsgRecord) GetSafeContent() string {
	content := helper.When(r.IsBotEvil(), config.App().Bot.EvilReply, r.Content)
	if content == "" {
		if r.Caption != "" { // 处理图像问答的特殊逻辑
			content = r.Caption
		}
		// FIXME gaussguan 临时去掉
		// if content == "" && len(r.FileInfos) != 0 { // 处理文件的特殊逻辑
		//	content = r.FileInfos
		// }
	}
	return content
}

// GetReasons 获取消息记录评价原因
func (r MsgRecord) GetReasons() []string {
	if r.Reason == "" {
		return nil
	}
	var reasons []string
	if err := jsoniter.UnmarshalFromString(r.Reason, &reasons); err != nil {
		log.Errorf("Get reasons error: %+v, data: %s", err, r.Reason)
		return nil
	}
	return reasons
}

// GetVisitorID 获取消息记录关联的访客 ID
func (r MsgRecord) GetVisitorID() uint64 {
	if r.ToType == SourceTypeVisitor || r.ToType == SourceTypeExpVisitor {
		return r.ToID
	}
	if r.FromType == SourceTypeVisitor || r.FromType == SourceTypeExpVisitor {
		return r.FromID
	}
	return 0
}

// IsTo 判断消息是否发送给用户
func (r MsgRecord) IsTo(typ SourceType, id uint64) bool {
	if r.ToType == typ && r.ToID == id {
		return true
	}
	return false
}

// IsToAPIVisitor 判断消息是否发送给API访客
func (r MsgRecord) IsToAPIVisitor() bool {
	return r.ToType == SourceTypeAPIVisitor
}

// CanMsgRating 根据消息类型和回复方式判断消息是否可评价 只有知识问答才有评价
func CanMsgRating(typ RecordType, replyMethod ReplyMethod, appType string) bool {
	return (typ == RecordTypeMessage || typ == RecordTypeSearch) &&
		(replyMethod != ReplyMethodGreeting && replyMethod != ReplyMethodBusy && replyMethod != ReplyMethodFile) &&
		appType == AppTypeKnowledgeQA
}

// CanMsgFeedback 根据消息类型和回复方式判断消息是否可反馈
func CanMsgFeedback(typ RecordType, replyMethod ReplyMethod) bool {
	// V2.6.0 任务流程允许提交反馈注释修改
	// if replyMethod == ReplyMethodTaskFlow || replyMethod == ReplyMethodTaskAnswer {
	//	return false
	// }
	return typ == RecordTypeExperience &&
		(replyMethod != ReplyMethodGreeting && replyMethod != ReplyMethodBusy) // && replyMethod != ReplyMethodTaskFlow
}

// GetCanRating 判断消息是否可以评价
func (r MsgRecord) GetCanRating(ctx context.Context, appType string) bool {
	isFromBot := r.IsFromBot()
	canRate := CanMsgRating(r.Type, r.ReplyMethod, appType)
	log.InfoContextf(ctx, "isFromBot:%+v,canRate:%+v,finalRsult:%+v", isFromBot, canRate, isFromBot && canRate)
	return isFromBot && canRate
}

// IsFromBot 判断消息是否来源于机器人
func (r MsgRecord) IsFromBot() bool {
	return r.FromType == SourceTypeRobot
}

// IsFromVisitor 判断消息是否来源于访客
func (r MsgRecord) IsFromVisitor() bool {
	return r.FromType == SourceTypeVisitor || r.FromType == SourceTypeExpVisitor
}

// IsFrom 判断消息是否来源于用户
func (r MsgRecord) IsFrom(typ SourceType, id uint64) bool {
	if r.FromType == typ && r.FromID == id {
		return true
	}
	// 访客自己的问题
	if r.FromType == SourceTypeAPIVisitor && (typ == SourceTypeVisitor || typ == SourceTypeExpVisitor) &&
		r.RelatedRecordID == "" {
		return true
	}
	return false
}

// GetReferences 获取参考来源
func (r MsgRecord) GetReferences() []Reference {
	if len(r.Reference) == 0 {
		return nil
	}

	var refs []Reference
	if err := jsoniter.UnmarshalFromString(r.Reference, &refs); err != nil {
		log.Errorf("Unmarshal reference error: %+v, data: %s", err, r.Reference)
		return nil
	}

	// 历史数据没有 Type，其正确类型应该是 DocTypeDoc
	return refs
}

// GetFileInfos 获取文件信息
func (r MsgRecord) GetFileInfos() []FileInfo {
	if len(r.FileInfos) == 0 {
		return nil
	}

	var fileInfos []FileInfo
	if err := jsoniter.UnmarshalFromString(r.FileInfos, &fileInfos); err != nil {
		log.Errorf("Unmarshal file infos error: %+v, data: %s", err, r.FileInfos)
		return nil
	}

	return fileInfos
}

// GetQuoteInfos 获取引用信息
func (r MsgRecord) GetQuoteInfos() []QuoteInfo {
	if len(r.QuoteInfos) == 0 {
		return nil
	}
	var quoteInfo []QuoteInfo
	if err := jsoniter.UnmarshalFromString(r.QuoteInfos, &quoteInfo); err != nil {
		log.Errorf("Unmarshal quote infos error: %+v, data: %s", err, r.QuoteInfos)
		return nil
	}
	return quoteInfo
}

// QuoteInfo 引用信息
type QuoteInfo struct {
	Position int `json:"position"`
	Index    int `json:"index"`
}

// ToPbQuoteInfo 转为pb引用信息
func (q QuoteInfo) ToPbQuoteInfo() *pb.QuoteInfo {
	return &pb.QuoteInfo{
		Position: uint32(q.Position),
		Index:    uint64(q.Index),
	}
}

// ToPbMsgRecord 转为 pb 消息记录
func (r MsgRecord) ToPbMsgRecord(ctx context.Context,
	typ SourceType, userID uint64, bots map[uint64]*AppListInfo, staffs map[uint64]CorpStaff,
) *pb.MsgRecord {
	var name, avatar, appType string
	if r.FromType != SourceTypeRobot {
		name, avatar = staffs[r.FromID].NickName, staffs[r.FromID].Avatar
	} else if bot := bots[r.FromID]; bot != nil {
		name, avatar, appType = bot.GetName(), bot.GetAvatar(), bot.GetAppType()
	}
	content := r.GetSafeContent()
	if content == "" && r.Caption != "" { // 多模态
		content = r.Caption
	}
	content, _, _ = helper.MatchSearchResults(ctx, content)
	images := make([]string, 0)
	msg := &pb.MsgRecord{
		Type:            uint32(r.Type),
		Content:         content,
		SessionId:       r.SessionID,
		RecordId:        r.RecordID,
		RelatedRecordId: r.RelatedRecordID,
		FromName:        name,
		FromAvatar:      avatar,
		IsFromSelf:      r.IsFrom(typ, userID),
		Timestamp:       r.CreateTime.Unix(),
		ReplyMethod:     uint32(r.ReplyMethod), // 各种类型回复
		Score:           uint32(r.Score),
		HasRead:         helper.When(r.IsTo(typ, userID), r.HasRead, true),
		CanRating:       r.GetCanRating(ctx, appType),          // 点赞, 点踩
		CanFeedback:     CanMsgFeedback(r.Type, r.ReplyMethod), // 是否展示反馈按钮
		IsLlmGenerated:  r.IsLLMGenerated(),                    // 是否大模型生成
		References:      helper.Map(r.GetReferences(), Reference.ToPbReference),
		Reasons:         r.GetReasons(),
		ImageUrls:       images,
		FileInfos:       helper.Map(r.GetFileInfos(), FileInfo.ToPbFileInfo),
		QuoteInfos:      helper.Map(r.GetQuoteInfos(), QuoteInfo.ToPbQuoteInfo),
	}
	if len(r.TokenStat) > 0 {
		var ts pb.TokenStat
		err := json.Unmarshal([]byte(r.TokenStat), &ts)
		if err == nil {
			msg.TokenStat = &ts
		} else {
			log.WarnContextf(ctx, "ToPbMsgRecord.json.Unmarshal %s", err.Error())
		}
	}
	if len(r.OptionCards) == 0 || r.OptionCards == "[]" {
		msg.OptionCards = []string{}
	} else {
		_ = json.Unmarshal([]byte(r.OptionCards), &msg.OptionCards)
	}
	if len(r.TaskFlow) > 0 {
		_ = json.Unmarshal([]byte(r.TaskFlow), &msg.TaskFlow)
	}
	return msg
}

// ToUnsatisfiedReplyContext 转为不满意回复上下文
func (r MsgRecord) ToUnsatisfiedReplyContext() *bot_knowledge_config_server.UnsatisfiedReplyContext {
	res := &bot_knowledge_config_server.UnsatisfiedReplyContext{
		RecordId:    r.RecordID,
		IsVisitor:   r.IsFromVisitor(),
		IsRobot:     r.IsFromBot(),
		FromId:      r.FromID,
		Content:     r.GetSafeContent(),
		FileInfos:   helper.Map(r.GetFileInfos(), FileInfo.ToKnowledgeFileInfo),
		ReplyMethod: uint32(r.ReplyMethod),
	}
	// FIXME gaussguan 临时去掉
	// if res.Content == "" && len(r.FileInfos) > 0 {
	//	res.Content = r.FileInfos
	// }
	// if res.Content == "" && len(r.ImageURL) > 0 {
	//	res.Content = config.App().MultiModal.GetCaptionPrompt
	// }
	return res
}

// GetMsgRecordParam 获取消息记录参数
type GetMsgRecordParam struct {
	FirstID           uint64    // 改用tdsql，废弃该字段
	FirstIDCreateTime time.Time // tdsql,用该字段
	LastID            uint64
	LastIDCreateTime  time.Time // tdsql,用该字段
	IncludeStart      bool
	IncludeBotEvil    bool
	Count             uint32
	Types             []RecordType
	SessionID         string
	BotBizID          uint64
}
