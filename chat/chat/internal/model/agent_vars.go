package model

import "reflect"

// SystemVar 系统变量
type SystemVar struct {
	UserQuery    string
	RewriteQuery string
	ChatHistory  string
	CurrentTime  string
}

// GetFieldValue 通过反射动态获取指定字段的值
func GetFieldValue(sysVar SystemVar, fieldName string) string {
	v := reflect.ValueOf(sysVar)
	field := v.FieldByName(fieldName)
	if !field.IsValid() || field.Kind() != reflect.String {
		return ""
	}
	return field.String()
}
