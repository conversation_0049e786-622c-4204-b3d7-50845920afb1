{"Tools": [{"ToolName": "预定接口", "ToolDesc": "", "Inputs": [{"Name": "customerName", "Type": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.订餐用户称呼"}}, "Desc": "订餐用户称呼", "IsRequired": true}, {"Name": "contactInfo", "Type": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.联系方式"}}, "Desc": "联系方式", "IsRequired": true}, {"Name": "date", "Type": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e8a662b3-a5dd-ae74-d814-455a8b89dbd6", "JsonPath": "Output.预定日期"}}, "Desc": "预定日期", "IsRequired": true}, {"Name": "mealType", "Type": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e20a43d8-a79f-98fb-3425-429891b513d9", "JsonPath": "Output.用餐餐段"}}, "Desc": "用餐餐段", "IsRequired": true}, {"Name": "numberOfPeople", "Type": "INT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "1497843f-fc9c-b791-2d3b-c4fb8d8d9238", "JsonPath": "Output.用餐人数"}}, "Desc": "用餐人数", "IsRequired": true}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Required": [], "Properties": [{"Title": "code", "Type": "INT", "Required": [], "Properties": [], "Desc": "预定结果"}, {"Title": "data", "Type": "OBJECT", "Required": [], "Properties": [{"Title": "tableNumber", "Type": "INT", "Required": [], "Properties": [], "Desc": "预定桌号"}], "Desc": ""}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "ea7d2b6e-6246-c4b3-189e-477fe591184e", "NodeName": "预定接口", "NodeDesc": "", "NodeType": "TOOL", "ToolNodeData": {"API": {"URL": "https://ai.zhidianfan.com/api/bookings/book", "Method": "GET", "authType": "NONE", "KeyLocation": "HEADER", "KeyParamName": "", "KeyParamValue": ""}, "Header": [{"ParamName": "Authorization", "ParamDesc": "鉴权密钥", "ParamType": "STRING", "Input": {"InputType": "USER_INPUT", "UserInputValue": {"Values": ["eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTcxOTI3OTE4NywiZXhwIjoxNzE5ODgzOTg3fQ.cuT1xkm4eDx5B_90ywBQd0l5gNBOoFNinqe_njdsIKs"]}}, "IsRequired": false, "SubParams": []}], "Query": [{"ParamName": "customerName", "ParamDesc": "订餐用户称呼", "ParamType": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.订餐用户称呼"}}, "IsRequired": true, "SubParams": []}, {"ParamName": "contactInfo", "ParamDesc": "联系方式", "ParamType": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.联系方式"}}, "IsRequired": true, "SubParams": []}, {"ParamName": "date", "ParamDesc": "预定日期", "ParamType": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e8a662b3-a5dd-ae74-d814-455a8b89dbd6", "JsonPath": "Output.预定日期"}}, "IsRequired": true, "SubParams": []}, {"ParamName": "mealType", "ParamDesc": "用餐餐段", "ParamType": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e20a43d8-a79f-98fb-3425-429891b513d9", "JsonPath": "Output.用餐餐段"}}, "IsRequired": true, "SubParams": []}, {"ParamName": "numberOfPeople", "ParamDesc": "用餐人数", "ParamType": "INT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "1497843f-fc9c-b791-2d3b-c4fb8d8d9238", "JsonPath": "Output.用餐人数"}}, "IsRequired": true, "SubParams": []}], "Body": []}, "Inputs": [], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Required": [], "Properties": [{"Title": "code", "Type": "INT", "Required": [], "Properties": [], "Desc": "预定结果"}, {"Title": "data", "Type": "OBJECT", "Required": [], "Properties": [{"Title": "tableNumber", "Type": "INT", "Required": [], "Properties": [], "Desc": "预定桌号"}], "Desc": ""}], "Desc": "输出内容"}], "NextNodeIDs": []}]}, {"ToolName": "查询附近门店", "ToolDesc": "", "Inputs": [{"Name": "customerName", "Type": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.订餐用户称呼"}}, "Desc": "订餐用户称呼", "IsRequired": false}, {"Name": "contactInfo", "Type": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.联系方式"}}, "Desc": "联系方式", "IsRequired": false}, {"Name": "date", "Type": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e8a662b3-a5dd-ae74-d814-455a8b89dbd6", "JsonPath": "Output.预定日期"}}, "Desc": "预定日期", "IsRequired": false}, {"Name": "mealType", "Type": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e20a43d8-a79f-98fb-3425-429891b513d9", "JsonPath": "Output.用餐餐段"}}, "Desc": "用餐餐段", "IsRequired": false}, {"Name": "numberOfPeople", "Type": "INT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "1497843f-fc9c-b791-2d3b-c4fb8d8d9238", "JsonPath": "Output.用餐人数"}}, "Desc": "用餐人数", "IsRequired": false}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Required": [], "Properties": [{"Title": "code", "Type": "INT", "Required": [], "Properties": [], "Desc": "预定结果"}, {"Title": "data", "Type": "OBJECT", "Required": [], "Properties": [{"Title": "tableNumber", "Type": "INT", "Required": [], "Properties": [], "Desc": "预定桌号"}], "Desc": ""}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "aef2c87b-2699-6538-b11f-9d147cbc5b75", "NodeName": "查询附近门店", "NodeDesc": "", "NodeType": "TOOL", "ToolNodeData": {"API": {"URL": "https://ai.zhidianfan.com/api/bookings/search-nearby", "Method": "GET", "authType": "NONE", "KeyLocation": "HEADER", "KeyParamName": "", "KeyParamValue": ""}, "Header": [], "Query": [{"ParamName": "customerName", "ParamDesc": "订餐用户称呼", "ParamType": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.订餐用户称呼"}}, "IsRequired": false, "SubParams": []}, {"ParamName": "contactInfo", "ParamDesc": "联系方式", "ParamType": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.联系方式"}}, "IsRequired": false, "SubParams": []}, {"ParamName": "date", "ParamDesc": "预定日期", "ParamType": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e8a662b3-a5dd-ae74-d814-455a8b89dbd6", "JsonPath": "Output.预定日期"}}, "IsRequired": false, "SubParams": []}, {"ParamName": "mealType", "ParamDesc": "用餐餐段", "ParamType": "STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e20a43d8-a79f-98fb-3425-429891b513d9", "JsonPath": "Output.用餐餐段"}}, "IsRequired": false, "SubParams": []}, {"ParamName": "numberOfPeople", "ParamDesc": "用餐人数", "ParamType": "INT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "1497843f-fc9c-b791-2d3b-c4fb8d8d9238", "JsonPath": "Output.用餐人数"}}, "IsRequired": false, "SubParams": []}], "Body": []}, "Inputs": [], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Required": [], "Properties": [{"Title": "code", "Type": "INT", "Required": [], "Properties": [], "Desc": "预定结果"}, {"Title": "data", "Type": "OBJECT", "Required": [], "Properties": [{"Title": "tableNumber", "Type": "INT", "Required": [], "Properties": [], "Desc": "预定桌号"}], "Desc": ""}], "Desc": "输出内容"}], "NextNodeIDs": []}]}]}