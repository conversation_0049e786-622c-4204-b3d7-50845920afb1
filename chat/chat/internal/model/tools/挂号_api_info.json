{"Tools": [{"ToolName": "医院名称归一化", "Inputs": [{"Name": "hospital", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "afb1f0c2-25c8-4891-4372-5dcdb1da1331", "JsonPath": "Output.医院名称"}}, "Desc": "医院名称"}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "Content", "Desc": "大模型运行后输出内容"}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "63a5ebb1-4f0d-872e-0abb-28f14bbcad13", "NodeName": "医院名称归一化", "NodeType": "LLM", "LLMNodeData": {"ModelName": "cs-normal-70b", "Temperature": 0.7, "TopP": 0.4, "MaxTokens": 6000, "Prompt": "根据<格式要求>处理<槽位值>中的槽位取值。\n\n<槽位名>\n医院名称\n</槽位名>\n\n<槽位值>\n{{hospital}}\n</槽位值>\n\n<槽位取值范围>\n北京积水潭医院，北京天坛医院，北京安贞医院，北京协和医院，北京中医药大学东方医院，北京朝阳医院，北京中日友好医院，北京世纪坛医院，北京大学人民医院，北京301医院，北京宣武医院，北京儿童医院，北京大学第一医院\n</槽位取值范围>\n\n<格式要求>\n- 如果<槽位取值范围>中存在与<槽位值>匹配的元素，则返回列表中的最匹配的元素作为处理后的槽位值。否则保持当前槽位值不变。\n- 只返回处理后的槽位值。\n</格式要求>"}, "Inputs": [{"Name": "hospital", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "afb1f0c2-25c8-4891-4372-5dcdb1da1331", "JsonPath": "Output.医院名称"}}, "Desc": "医院名称"}]}]}, {"ToolName": "科室名称归一化", "Inputs": [{"Name": "department_name", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "afb1f0c2-25c8-4891-4372-5dcdb1da1331", "JsonPath": "Output.科室名称"}}, "Desc": "科室名称"}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "Content", "Desc": "大模型运行后输出内容"}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "e58ea76c-32b4-ba0c-9960-391420e93f61", "NodeName": "科室名称归一化", "NodeType": "LLM", "LLMNodeData": {"ModelName": "cs-normal-70b", "Temperature": 0.7, "TopP": 0.4, "MaxTokens": 6000, "Prompt": "根据<格式要求>处理<槽位值>中的槽位取值。\n\n<槽位名>\n科室名称\n</槽位名>\n\n<槽位值>\n{{department_name}}\n</槽位值>\n\n<槽位取值范围>\n营养科，心内科，急诊科，核医学科，神经科，眼科，感染疾病科，泌尿科，整形外科，肿瘤内科，针灸科，消化内科，肾内科，妇科，干部医疗科，内分泌科，妇产科，新生儿科，耳鼻喉科，儿科，口腔科，心血管外科，肿瘤科，康复医学科，骨科，心血管内科，风湿免疫科，乳腺外科，心外科，老年医学科，普通外科，中医科，疼痛科，精神心理科，神经外科，皮肤科，呼吸内科，胸外科\n</槽位取值范围>\n\n<格式要求>\n- 如果<槽位取值范围>中存在与<槽位值>匹配的元素，则返回列表中的最匹配的元素作为处理后的槽位值。否则保持当前槽位值不变。\n- 只返回处理后的槽位值。\n</格式要求>"}, "Inputs": [{"Name": "department_name", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "afb1f0c2-25c8-4891-4372-5dcdb1da1331", "JsonPath": "Output.科室名称"}}, "Desc": "科室名称"}]}]}, {"ToolName": "校验挂号医院", "Inputs": [{"Name": "hos_name", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "63a5ebb1-4f0d-872e-0abb-28f14bbcad13", "JsonPath": "Output.Content"}}, "Desc": "医院名称", "IsRequired": true}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "type", "Desc": "医院存在类型"}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "cc5e6402-0a10-8845-1cbc-edd08b468a42", "NodeName": "校验挂号医院", "NodeType": "TOOL", "ToolNodeData": {"API": {"URL": "http://8.134.222.238:8091/check-hospital-exist", "Method": "GET"}, "Query": [{"ParamName": "hos_name", "ParamDesc": "医院名称", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "63a5ebb1-4f0d-872e-0abb-28f14bbcad13", "JsonPath": "Output.Content"}}, "IsRequired": true}]}, "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "type", "Desc": "医院存在类型"}], "Desc": "输出内容"}]}]}, {"ToolName": "科室校验", "Inputs": [{"Name": "keshi_name", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e58ea76c-32b4-ba0c-9960-391420e93f61", "JsonPath": "Output.Content"}}, "Desc": "科室名称", "IsRequired": true}, {"Name": "hos_name", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "63a5ebb1-4f0d-872e-0abb-28f14bbcad13", "JsonPath": "Output.Content"}}, "Desc": "医院名称", "IsRequired": true}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "type", "Desc": "科室存在状态"}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "bf5ea374-2dca-fa36-842d-e236e63d2260", "NodeName": "科室校验", "NodeType": "TOOL", "ToolNodeData": {"API": {"URL": "http://8.134.222.238:8091/check-department-exist", "Method": "GET"}, "Query": [{"ParamName": "keshi_name", "ParamDesc": "科室名称", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e58ea76c-32b4-ba0c-9960-391420e93f61", "JsonPath": "Output.Content"}}, "IsRequired": true}, {"ParamName": "hos_name", "ParamDesc": "医院名称", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "63a5ebb1-4f0d-872e-0abb-28f14bbcad13", "JsonPath": "Output.Content"}}, "IsRequired": true}]}, "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "type", "Desc": "科室存在状态"}], "Desc": "输出内容"}]}]}, {"ToolName": "查询对应科室可选医院", "Inputs": [{"Name": "keshi_name", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e58ea76c-32b4-ba0c-9960-391420e93f61", "JsonPath": "Output.Content"}}, "Desc": "科室名称", "IsRequired": true}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "hos_name", "Desc": "可选医院名称"}, {"Title": "num", "Type": "INT", "Desc": "可选医院数量"}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "23a13785-d8c7-c74c-87ef-d1aa7a4e09ef", "NodeName": "查询对应科室可选医院", "NodeType": "TOOL", "ToolNodeData": {"API": {"URL": "http://8.134.222.238:8091/get-hospital-by-department", "Method": "GET"}, "Query": [{"ParamName": "keshi_name", "ParamDesc": "科室名称", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e58ea76c-32b4-ba0c-9960-391420e93f61", "JsonPath": "Output.Content"}}, "IsRequired": true}]}, "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "hos_name", "Desc": "可选医院名称"}, {"Title": "num", "Type": "INT", "Desc": "可选医院数量"}], "Desc": "输出内容"}]}]}, {"ToolName": "指定时间号源查询", "Inputs": [{"Name": "hos_name", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "63a5ebb1-4f0d-872e-0abb-28f14bbcad13", "JsonPath": "Output.Content"}}, "Desc": "医院名称", "IsRequired": true}, {"Name": "keshi_name", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e58ea76c-32b4-ba0c-9960-391420e93f61", "JsonPath": "Output.Content"}}, "Desc": "科室名称", "IsRequired": true}, {"Name": "time", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "20ef4149-94da-b90c-e550-2ebcecc5a50a", "JsonPath": "Output.挂号时间"}}, "Desc": "挂号时间", "IsRequired": true}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "num", "Type": "INT", "Desc": "可挂号数量"}, {"Title": "haolist", "Type": "ARRAY_STRING", "Desc": "可挂号列表"}, {"Title": "zhuan_num", "Type": "INT", "Desc": "专家号数量"}, {"Title": "pu_num", "Type": "INT", "Desc": "普通号数量"}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "f16663d8-4b81-3679-e2dc-8bbc3b9157ac", "NodeName": "指定时间号源查询", "NodeType": "TOOL", "ToolNodeData": {"API": {"URL": "http://8.134.222.238:8091/get-appointment-info", "Method": "GET"}, "Query": [{"ParamName": "hos_name", "ParamDesc": "医院名称", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "63a5ebb1-4f0d-872e-0abb-28f14bbcad13", "JsonPath": "Output.Content"}}, "IsRequired": true}, {"ParamName": "keshi_name", "ParamDesc": "科室名称", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e58ea76c-32b4-ba0c-9960-391420e93f61", "JsonPath": "Output.Content"}}, "IsRequired": true}, {"ParamName": "time", "ParamDesc": "挂号时间", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "20ef4149-94da-b90c-e550-2ebcecc5a50a", "JsonPath": "Output.挂号时间"}}, "IsRequired": true}]}, "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "num", "Type": "INT", "Desc": "可挂号数量"}, {"Title": "haolist", "Type": "ARRAY_STRING", "Desc": "可挂号列表"}, {"Title": "zhuan_num", "Type": "INT", "Desc": "专家号数量"}, {"Title": "pu_num", "Type": "INT", "Desc": "普通号数量"}], "Desc": "输出内容"}]}]}, {"ToolName": "同医院同科室其他日期号源", "Inputs": [{"Name": "hos_name", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "63a5ebb1-4f0d-872e-0abb-28f14bbcad13", "JsonPath": "Output.Content"}}, "Desc": "医院名称", "IsRequired": true}, {"Name": "keshi_name", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e58ea76c-32b4-ba0c-9960-391420e93f61", "JsonPath": "Output.Content"}}, "Desc": "科室名称", "IsRequired": true}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "data", "Type": "ARRAY_OBJECT", "Properties": [{"Title": "time", "Desc": "可挂号日期"}, {"Title": "num_type", "Desc": "号类"}], "Desc": "可挂号日期信息"}, {"Title": "num", "Type": "INT", "Desc": "可挂号日期数量"}, {"Title": "data_num_type", "Type": "ARRAY_OBJECT", "Desc": "号类信息"}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "b892a84e-c6ee-c230-4573-efc4f2a822b7", "NodeName": "同医院同科室其他日期号源", "NodeType": "TOOL", "ToolNodeData": {"API": {"URL": "http://8.134.222.238:8091/get-available-date", "Method": "GET"}, "Query": [{"ParamName": "hos_name", "ParamDesc": "医院名称", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "63a5ebb1-4f0d-872e-0abb-28f14bbcad13", "JsonPath": "Output.Content"}}, "IsRequired": true}, {"ParamName": "keshi_name", "ParamDesc": "科室名称", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e58ea76c-32b4-ba0c-9960-391420e93f61", "JsonPath": "Output.Content"}}, "IsRequired": true}]}, "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "data", "Type": "ARRAY_OBJECT", "Properties": [{"Title": "time", "Desc": "可挂号日期"}, {"Title": "num_type", "Desc": "号类"}], "Desc": "可挂号日期信息"}, {"Title": "num", "Type": "INT", "Desc": "可挂号日期数量"}, {"Title": "data_num_type", "Type": "ARRAY_OBJECT", "Desc": "号类信息"}], "Desc": "输出内容"}]}]}, {"ToolName": "获取挂号时间和号类", "Inputs": [{"Name": "data", "Type": "ARRAY_OBJECT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "b892a84e-c6ee-c230-4573-efc4f2a822b7", "JsonPath": "Output.data"}}, "Desc": "可挂号日期信息"}, {"Name": "data_num_type", "Type": "ARRAY_OBJECT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "b892a84e-c6ee-c230-4573-efc4f2a822b7", "JsonPath": "Output.data_num_type"}}, "Desc": "号类信息"}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "g_time", "Desc": "挂号时间"}, {"Title": "num_type", "Desc": "号类"}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "00edda9b-dda5-5f11-b846-6955959d0be8", "NodeName": "获取挂号时间和号类", "NodeType": "CODE_EXECUTOR", "CodeExecutorNodeData": {"Code": "\n# 仅支持数据转换或运算等操作, 请勿手动import, 已引入numpy和pandas以及部分内置的运算相关的包；不支持IO操作，如读取文件，网络通信等。\n# 请保存函数名为main,输入输出均为dict；最终结果会以json字符串方式返回，请勿直接返回不支持json.dumps的对象（numpy和pandas已增加额外处理）\ndef main(params: dict) -> dict:\n    data_time = params.get(\"data\", [])\n    if len(data_time) == 0:\n        return {'g_time':'', 'num_type':''}\n    return {\n        'g_time': data_time[0][\"time\"],\n        \"num_type\": data_time[0][\"num_type\"]\n    }\n"}, "Inputs": [{"Name": "data", "Type": "ARRAY_OBJECT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "b892a84e-c6ee-c230-4573-efc4f2a822b7", "JsonPath": "Output.data"}}, "Desc": "可挂号日期信息"}, {"Name": "data_num_type", "Type": "ARRAY_OBJECT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "b892a84e-c6ee-c230-4573-efc4f2a822b7", "JsonPath": "Output.data_num_type"}}, "Desc": "号类信息"}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "g_time", "Desc": "挂号时间"}, {"Title": "num_type", "Desc": "号类"}], "Desc": "输出内容"}]}]}, {"ToolName": "本医院挂号执行", "Inputs": [{"Name": "id_num", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "8784bc99-a061-deb0-f781-99f8ebeed85e", "JsonPath": "Output.身份证号"}}, "Desc": "身份证号", "IsRequired": true}, {"Name": "num_type", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "8784bc99-a061-deb0-f781-99f8ebeed85e", "JsonPath": "Output.号类"}}, "Desc": "号类", "IsRequired": true}, {"Name": "hos_name", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "63a5ebb1-4f0d-872e-0abb-28f14bbcad13", "JsonPath": "Output.Content"}}, "Desc": "医院名称", "IsRequired": true}, {"Name": "keshi_name", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e58ea76c-32b4-ba0c-9960-391420e93f61", "JsonPath": "Output.Content"}}, "Desc": "科室名称", "IsRequired": true}, {"Name": "time", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "20ef4149-94da-b90c-e550-2ebcecc5a50a", "JsonPath": "Output.挂号时间"}}, "Desc": "挂号时间", "IsRequired": true}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "type", "Type": "INT", "Desc": "挂号状态"}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "a464f951-8077-39ce-db6b-532a7cad255b", "NodeName": "本医院挂号执行", "NodeType": "TOOL", "ToolNodeData": {"API": {"URL": "http://8.134.222.238:8091/appointment", "Method": "GET"}, "Query": [{"ParamName": "id_num", "ParamDesc": "身份证号", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "8784bc99-a061-deb0-f781-99f8ebeed85e", "JsonPath": "Output.身份证号"}}, "IsRequired": true}, {"ParamName": "num_type", "ParamDesc": "号类", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "8784bc99-a061-deb0-f781-99f8ebeed85e", "JsonPath": "Output.号类"}}, "IsRequired": true}, {"ParamName": "hos_name", "ParamDesc": "医院名称", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "63a5ebb1-4f0d-872e-0abb-28f14bbcad13", "JsonPath": "Output.Content"}}, "IsRequired": true}, {"ParamName": "keshi_name", "ParamDesc": "科室名称", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e58ea76c-32b4-ba0c-9960-391420e93f61", "JsonPath": "Output.Content"}}, "IsRequired": true}, {"ParamName": "time", "ParamDesc": "挂号时间", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "20ef4149-94da-b90c-e550-2ebcecc5a50a", "JsonPath": "Output.挂号时间"}}, "IsRequired": true}]}, "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "type", "Type": "INT", "Desc": "挂号状态"}], "Desc": "输出内容"}]}]}, {"ToolName": "其他医院号源推荐", "Inputs": [{"Name": "keshi_name", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e58ea76c-32b4-ba0c-9960-391420e93f61", "JsonPath": "Output.Content"}}, "Desc": "科室名称", "IsRequired": true}, {"Name": "time", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "20ef4149-94da-b90c-e550-2ebcecc5a50a", "JsonPath": "Output.挂号时间"}}, "Desc": "挂号时间", "IsRequired": true}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "num", "Type": "INT", "Desc": "号源数量"}, {"Title": "hos_name", "Type": "ARRAY_STRING", "Desc": "医院名称"}, {"Title": "doc_name", "Type": "ARRAY_STRING", "Desc": "挂号医生"}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "ee62de82-f6f3-3ffe-6763-2f3e0b870288", "NodeName": "其他医院号源推荐", "NodeType": "TOOL", "ToolNodeData": {"API": {"URL": "http://8.134.222.238:8091/get-other-hospital", "Method": "GET"}, "Query": [{"ParamName": "keshi_name", "ParamDesc": "科室名称", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e58ea76c-32b4-ba0c-9960-391420e93f61", "JsonPath": "Output.Content"}}, "IsRequired": true}, {"ParamName": "time", "ParamDesc": "挂号时间", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "20ef4149-94da-b90c-e550-2ebcecc5a50a", "JsonPath": "Output.挂号时间"}}, "IsRequired": true}]}, "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "num", "Type": "INT", "Desc": "号源数量"}, {"Title": "hos_name", "Type": "ARRAY_STRING", "Desc": "医院名称"}, {"Title": "doc_name", "Type": "ARRAY_STRING", "Desc": "挂号医生"}], "Desc": "输出内容"}]}]}]}