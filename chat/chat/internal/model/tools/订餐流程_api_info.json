{"Tools": [{"ToolName": "预定接口", "Inputs": [{"Name": "customerName", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.订餐用户称呼"}}, "Desc": "订餐用户称呼", "IsRequired": true}, {"Name": "contactInfo", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.联系方式"}}, "Desc": "联系方式", "IsRequired": true}, {"Name": "date", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e8a662b3-a5dd-ae74-d814-455a8b89dbd6", "JsonPath": "Output.预定日期"}}, "Desc": "预定日期", "IsRequired": true}, {"Name": "mealType", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e20a43d8-a79f-98fb-3425-429891b513d9", "JsonPath": "Output.用餐餐段"}}, "Desc": "用餐餐段", "IsRequired": true}, {"Name": "numberOfPeople", "Type": "INT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "1497843f-fc9c-b791-2d3b-c4fb8d8d9238", "JsonPath": "Output.用餐人数"}}, "Desc": "用餐人数", "IsRequired": true}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "code", "Type": "INT", "Desc": "预定结果"}, {"Title": "data", "Type": "OBJECT", "Properties": [{"Title": "tableNumber", "Type": "INT", "Desc": "预定桌号"}]}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "ea7d2b6e-6246-c4b3-189e-477fe591184e", "NodeName": "预定接口", "NodeType": "TOOL", "ToolNodeData": {"API": {"URL": "https://ai.zhidianfan.com/api/bookings/book", "Method": "GET"}, "Header": [{"ParamName": "Authorization", "ParamDesc": "鉴权密钥", "Input": {"UserInputValue": {"Values": ["eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImlhdCI6MTcxOTI3OTE4NywiZXhwIjoxNzE5ODgzOTg3fQ.cuT1xkm4eDx5B_90ywBQd0l5gNBOoFNinqe_njdsIKs"]}}}], "Query": [{"ParamName": "customerName", "ParamDesc": "订餐用户称呼", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.订餐用户称呼"}}, "IsRequired": true}, {"ParamName": "contactInfo", "ParamDesc": "联系方式", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.联系方式"}}, "IsRequired": true}, {"ParamName": "date", "ParamDesc": "预定日期", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e8a662b3-a5dd-ae74-d814-455a8b89dbd6", "JsonPath": "Output.预定日期"}}, "IsRequired": true}, {"ParamName": "mealType", "ParamDesc": "用餐餐段", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e20a43d8-a79f-98fb-3425-429891b513d9", "JsonPath": "Output.用餐餐段"}}, "IsRequired": true}, {"ParamName": "numberOfPeople", "ParamDesc": "用餐人数", "ParamType": "INT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "1497843f-fc9c-b791-2d3b-c4fb8d8d9238", "JsonPath": "Output.用餐人数"}}, "IsRequired": true}]}, "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "code", "Type": "INT", "Desc": "预定结果"}, {"Title": "data", "Type": "OBJECT", "Properties": [{"Title": "tableNumber", "Type": "INT", "Desc": "预定桌号"}]}], "Desc": "输出内容"}]}]}, {"ToolName": "查询附近门店", "Inputs": [{"Name": "customerName", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.订餐用户称呼"}}, "Desc": "订餐用户称呼"}, {"Name": "contactInfo", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.联系方式"}}, "Desc": "联系方式"}, {"Name": "date", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e8a662b3-a5dd-ae74-d814-455a8b89dbd6", "JsonPath": "Output.预定日期"}}, "Desc": "预定日期"}, {"Name": "mealType", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e20a43d8-a79f-98fb-3425-429891b513d9", "JsonPath": "Output.用餐餐段"}}, "Desc": "用餐餐段"}, {"Name": "numberOfPeople", "Type": "INT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "1497843f-fc9c-b791-2d3b-c4fb8d8d9238", "JsonPath": "Output.用餐人数"}}, "Desc": "用餐人数"}], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "code", "Type": "INT", "Desc": "预定结果"}, {"Title": "data", "Type": "OBJECT", "Properties": [{"Title": "tableNumber", "Type": "INT", "Desc": "预定桌号"}]}], "Desc": "输出内容"}], "ToolNodes": [{"NodeID": "aef2c87b-2699-6538-b11f-9d147cbc5b75", "NodeName": "查询附近门店", "NodeType": "TOOL", "ToolNodeData": {"API": {"URL": "https://ai.zhidianfan.com/api/bookings/search-nearby", "Method": "GET"}, "Query": [{"ParamName": "customerName", "ParamDesc": "订餐用户称呼", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.订餐用户称呼"}}}, {"ParamName": "contactInfo", "ParamDesc": "联系方式", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "af191186-d218-7029-3e2d-f546c6811b31", "JsonPath": "Output.联系方式"}}}, {"ParamName": "date", "ParamDesc": "预定日期", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e8a662b3-a5dd-ae74-d814-455a8b89dbd6", "JsonPath": "Output.预定日期"}}}, {"ParamName": "mealType", "ParamDesc": "用餐餐段", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "e20a43d8-a79f-98fb-3425-429891b513d9", "JsonPath": "Output.用餐餐段"}}}, {"ParamName": "numberOfPeople", "ParamDesc": "用餐人数", "ParamType": "INT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "1497843f-fc9c-b791-2d3b-c4fb8d8d9238", "JsonPath": "Output.用餐人数"}}}]}, "Outputs": [{"Title": "Output", "Type": "OBJECT", "Properties": [{"Title": "code", "Type": "INT", "Desc": "预定结果"}, {"Title": "data", "Type": "OBJECT", "Properties": [{"Title": "tableNumber", "Type": "INT", "Desc": "预定桌号"}]}], "Desc": "输出内容"}]}]}]}