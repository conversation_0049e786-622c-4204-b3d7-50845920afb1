Name: 同程开发票
Desc: 咨询开具发票的方法
SLOTs:
    - name: 发票类型
      desc: 发票的类型，包含电子普票、纸质普票、纸质专票三种
      type: string
    - name: 发票类型
      desc: 发票的类型，参数值只支持“电子普票”、“纸质普票”、“纸质专票”三种，请不要提取其他内容。
      type: string
    - name: 会员卡ID
      desc: 用户的会员卡ID号码，一般是一串阿拉伯数字
      type: string
    - name: 订单编号
      desc: 订单的编号，一般是一串3到6位的阿拉伯数字，比如：123456，12345等等。如果用户提取出来的订单编号不是3到6位的阿拉伯数字时，订单编号取值为空\"\"
      type: string
    - name: 姓名
      desc: 用户提供的姓名
      type: string
APIs:
    - name: 查询开票方式
    - name: 开票进度查询
      precondition:
        - 查询开票方式
    - name: 会员身份查询
      precondition:
        - 开票进度查询
ANSWERs:
    - name: 结束回复1
      desc: 您的同程开发票操作已成功执行，开票方式为{{invoicingMethod}}   。
    - name: 兜底回复
      desc: 兜底回复
    - name: 结束回复3
      desc: |-
        您存在发票信息，请前往mis售后地图查看详细进度，根据用户实际咨询场景回复不同话术\\\\~  

        http://nyd.mis.elong.com:8080/aftersale/index?orderId=1954364687&type=1【按钮】
    - name: 兜底回复1
      desc: 兜底回复1
    - name: 兜底回复2
      desc: 兜底回复2
    - name: 白金电子普票开具
      desc: |-
        电子普票支持的开票内容为代订住宿费或者代订房费。

        可 [点击这里](http://nyd.mis.elong.com/invoice/electronic?orderIds=1959238331) 提交开通电子普票的申请，跳转对应订单开发票页面；提交后，离店日24小时内发送至您的邮箱。

        【疑问：超链接提示forbidden，是否是人工内网操作帮助客户提交发票，而非客户自行申请？机器人自动操作后预期提供接口还是转工单继续人工开票？】
    - name: 白金纸质发票开具
      desc: |-
        纸质普票支持的开票内容为代订住宿费或者代订房费。  

        可[点击这里](https://www.baidu.com/)提交开通纸质普票的申请，跳转对应订单开发票页面；提交后，离店后5个工作日内送达。

        【疑问：超链接提示forbidden，是否是人工内网操作帮助客户提交发票，而非客户自行申请？机器人自动操作后预期提供接口还是转工单继续人工开票？】
    - name: 白金纸质专票开具
      desc: |-
        纸质专票开通【疑问：无后续流程，是否人工根据下述对客话术收集的信息直接开具？机器人自动操作后预期提供接口还是转工单继续人工开票】

        对客话术：  

        ①纸质专票烦您提供：发票抬头、纳税人识别号、注册地址、公司电话、开户银行、银行账号、发票金额  

        ②是否需要备注订单信息（酒店名称、入住日期、离店日期）呢？  

        ③请您提供下收件人、联系电话、地址（省市区）哦

        /
    - name: 兜底回复3
      desc: 兜底回复3
    - name: 非白金电子票开具
      desc: |-
        电子普票支持的开票内容为代订住宿费或者代订房费。  

        可[点击这里](https://www.baidu.com/)提交开通电子普票的申请，跳转对应订单开发票页面；提交后，离店日24小时内发送至您的邮箱。

        【疑问：超链接提示forbidden，是否是人工内网操作帮助客户提交发票，而非客户自行申请？机器人自动操作后预期提供接口还是转工单继续人工开票？】
    - name: 非白金纸质普票
      desc: |-
        纸质普票支持的开票内容为代订住宿费或者代订房费。

        可 [点击这里](https://www.baidu.com/) 提交开通纸质普票的申请，跳转对应订单开发票页面；提交后，离店后5个工作日内送达。

        根据您的会员等级，您申请纸质发票邮寄需要10元邮费哦，申请成功后会给您订单手机号发送支付短链，您可点开短链支付。
    - name: 非白金纸质专票
      desc: |-
        纸质专票支持的开票内容为代订住宿费或者代订房费。

        可 [点击这里](https://www.baidu.com/) 提交开通纸质专票的申请，跳转对应订单开发票页面；提交后，离店后5个工作日内送达。

        根据您的会员等级，您申请纸质发票邮寄需要10元邮费哦，申请成功后会给您订单手机号发送支付短链，您可点开短链支付。
    - name: 兜底回复4
      desc: 兜底回复4
Procedure: |4
    # 提取订单编号、会员卡ID和姓名参数
    [order_id, card_id, name] = API.订单、会员卡id、姓名参数()

    # 查询开票方式
    [invoicing_method] = API.查询开票方式([order_id])

    # 条件判断1：根据开票方式进行不同处理
    if invoicing_method == \"平台开具\":
        # 开票进度查询
        [invoicing_progress] = API.开票进度查询([name])

        # 条件判断3：根据开票进度进行不同处理
        if invoicing_progress is None:
            ANSWER.结束回复3()
        else:
            # 会员身份查询
            [card_type] = API.会员身份查询([card_id])

            # 条件判断4：根据卡类型进行不同处理
            if card_type == \"白金卡\":
                # 白金卡开票类型澄清
                [发票类型] = API.白金卡开票类型澄清()

                # 条件判断5：根据发票类型进行不同处理
                if 发票类型 == \"电子普票\":
                    ANSWER.白金电子普票开具()
                elif 发票类型 == \"纸质普票\":
                    ANSWER.白金纸质发票开具()
                elif 发票类型 == \"纸质专票\":
                    ANSWER.白金纸质专票开具()
                else:
                    ANSWER.兜底回复3()
            elif card_type == \"非白金卡\":
                # 非白金卡开票类型澄清
                [发票类型] = API.非白金卡开票类型澄清()

                # 条件判断6：根据发票类型进行不同处理
                if 发票类型 == \"电子普票\":
                    ANSWER.非白金电子票开具()
                elif 发票类型 == \"纸质普票\":
                    ANSWER.非白金纸质普票()
                elif 发票类型 == \"纸质专票\":
                    ANSWER.非白金纸质专票()
                else:
                    ANSWER.兜底回复4()
            else:
                ANSWER.兜底回复2()
    elif invoicing_method == \"酒店开具\":
        ANSWER.结束回复1()
    else:
        ANSWER.兜底回复()
