[{"type": "function", "function": {"name": "wait_for_user_login", "description": "when the system detects a login page, this tool allows user intervention for logging in and waits for the operation result.", "parameters": {"type": "object", "properties": {"message": {"type": "string", "description": "a sentence organized to inform the user what they need to do currently"}, "current_web_title": {"type": "string", "description": "the title of the webpage where the login page is detected"}, "current_web_url": {"type": "string", "description": "the URL of the webpage where the login page is detected"}}, "required": ["message", "current_web_title", "current_web_url"]}}}, {"type": "function", "function": {"name": "task_done", "description": "Complete task - because you have finished the task user provided or you have reached step limit.", "parameters": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Whether the task is finished or not. Return False if you cannot finish the task in limited steps."}, "task_completion_description": {"type": "string", "description": "One sentence that describes the task progress"}}, "required": ["success", "task_completion_description"]}}}, {"type": "function", "function": {"name": "click_element", "description": "Click on an element in the current tab. DO NOT use this tool if you need to download.", "parameters": {"properties": {"index": {"description": "The index of the element to click.", "type": "number"}}, "required": ["index"], "type": "object"}}}, {"type": "function", "function": {"name": "download_file", "description": "Download a file in the current page with given element index. Note that you SHOULD NOT use this tool for the result of search tool because the search results are not interactive elements!", "parameters": {"properties": {"index": {"description": "The index of element (file) to download.", "type": "number"}}, "required": ["index"], "type": "object"}}}, {"type": "function", "function": {"name": "get_dropdown_options", "description": "Get all options from a native dropdown.", "parameters": {"properties": {"index": {"description": "The index of the dropdown element.", "type": "number"}}, "required": ["index"], "type": "object"}}}, {"type": "function", "function": {"name": "go_back", "description": "Go back to the previous page in the browser.", "parameters": {"type": "object"}}}, {"type": "function", "function": {"name": "go_to_url", "description": "Navigate to URL in the current tab.", "parameters": {"properties": {"url": {"description": "The URL to navigate to.", "type": "string"}}, "required": ["url"], "type": "object"}}}, {"type": "function", "function": {"name": "input_text", "description": "Input text into a input interactive element.", "parameters": {"properties": {"index": {"description": "The index of the input element to interact with.", "type": "number"}, "text": {"description": "The text to input into the input element. DO NOT add search engine features like \"site:\" \"filetype:\" into an inputbox!", "type": "string"}}, "required": ["index", "text"], "type": "object"}}}, {"type": "function", "function": {"name": "scroll_down", "description": "Scroll down the page by pixel amount - if no amount is specified, scroll down one page.", "parameters": {"properties": {"amount": {"description": "The number of pixels to scroll down.", "type": "number"}}, "type": "object"}}}, {"type": "function", "function": {"name": "scroll_to_bottom", "description": "Scrolls the current webpage to the bottom. This can be useful for loading additional content on infinite scrolling pages or for reaching the footer quickly.", "parameters": {"type": "object"}}}, {"type": "function", "function": {"name": "scroll_to_text", "description": "Searches for the specified text on the current webpage and scrolls to its location. If the function is called multiple times with the same text, it will navigate to the next occurrence of the text on subsequent calls.", "parameters": {"properties": {"text": {"description": "The text to scroll to.", "type": "string"}}, "required": ["text"], "type": "object"}}}, {"type": "function", "function": {"name": "scroll_to_top", "description": "Scrolls the current webpage to the top. This is useful for quickly returning to the beginning of the page.", "parameters": {"type": "object"}}}, {"type": "function", "function": {"name": "scroll_up", "description": "Scroll up the page by pixel amount - if no amount is specified, scroll up one page.", "parameters": {"properties": {"amount": {"description": "The number of pixels to scroll up.", "type": "number"}}, "type": "object"}}}, {"type": "function", "function": {"name": "select_dropdown_option", "description": "Select dropdown option for interactive element index by the text of the option you want to select.", "parameters": {"properties": {"index": {"description": "The index of the dropdown element.", "type": "number"}, "text": {"description": "The text of the option you want to select.", "type": "string"}}, "required": ["index", "text"], "type": "object"}}}, {"type": "function", "function": {"name": "wait", "description": "Wait for x seconds, default 3. You can use this to wait for a page to load.", "parameters": {"properties": {"seconds": {"description": "The number of seconds to wait.", "type": "number"}}, "type": "object"}}}, {"type": "function", "function": {"name": "search", "description": "Use search engine to search information for the given query.", "parameters": {"properties": {"query": {"description": "The query to be searched.", "type": "string"}}, "required": ["query"], "type": "object"}}}]