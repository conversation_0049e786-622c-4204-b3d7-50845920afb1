package model

// Stock 表示股票信息的结构体
type Stock struct {
	Display    StockDisplayInfo `json:"display"`     // 展示相关的信息
	JSONData   JSONDataInfo     `json:"jsonData"`    // JSON数据，包含基础信息和显示信息
	Vr         bool             `json:"vr"`          // 是否为垂直结果
	VrCategory string           `json:"vr_category"` // 垂直结果分类
	Vrid       string           `json:"vrid"`        // 垂直结果ID
}

// StockDisplayInfo 包含展示部分的详细信息
type StockDisplayInfo struct {
	ContentTitle interface{} `json:"ContentTitle"`  // 内容标题
	AbstractInfo interface{} `json:"abstract_info"` // 摘要信息
	Content      interface{} `json:"content"`       // 内容详情
	Date         interface{} `json:"date"`          // 日期
	Favicon      interface{} `json:"favicon"`       // 网站图标
	Title        string      `json:"title"`         // 标题
	URL          string      `json:"url"`           // URL链接
}

// JSONDataInfo 包含JSON数据的详细信息
type JSONDataInfo struct {
	BaseInfo    BaseInfoData    `json:"base_info"`    // 基础信息
	DisplayInfo DisplayInfoData `json:"display_info"` // 显示信息
}

// BaseInfoData 包含股票的基本信息
type BaseInfoData struct {
	Title string `json:"title"` // 股票的标题
	URL   string `json:"url"`   // 股票的URL链接
}

// DisplayInfoData 包含股票的展示信息
type DisplayInfoData struct {
	Group []StockGroupInfo `json:"group"` // 股票组信息
}

// StockGroupInfo 包含股票组的详细信息
type StockGroupInfo struct {
	Alias        string `json:"alias"`         // 股票别名
	CodeStock    string `json:"code_stock"`    // 股票代码
	KeyStatus    string `json:"key_status"`    // 股票状态
	Name         string `json:"name"`          // 股票名称
	NameExchange string `json:"name_exchange"` // 交易所名称
	NumberClosed string `json:"number_closed"` // 收盘价
	NumberHigh   string `json:"number_high"`   // 最高价
	NumberLow    string `json:"number_low"`    // 最低价
	NumberOpen   string `json:"number_open"`   // 开盘价
	Price        string `json:"price"`         // 当前价格
	Time         string `json:"time"`          // 时间
	Type         string `json:"type"`          // 类型
}
