package model

import "encoding/json"

var (
	// SearchEngineTypeYuanBao 元宝搜索
	SearchEngineTypeYuanBao uint32 = 0
	// SearchEngineTypeSogou 搜狗搜索
	SearchEngineTypeSogou uint32 = 1
)

// CalendarRoot .
type CalendarRoot struct {
	VrID    string          `json:"vrid"`
	Display CalendarDisplay `json:"display"`
}

// CalendarDisplay .
type CalendarDisplay struct {
	URL      string          `json:"url"`
	Title    string          `json:"title"`
	Holidays json.RawMessage `json:"holidays"`
	Date     string          `json:"date"`
}

// CommonDisplay .
type CommonDisplay struct {
	Date    string          `json:"date"`
	Subitem json.RawMessage `json:"subitem"`
	Title   string          `json:"title"`
	URL     string          `json:"url"`
}

// CommonRoot .
type CommonRoot struct {
	Display    CommonDisplay `json:"display"`
	Vr         bool          `json:"vr"`
	VrCategory string        `json:"vr_category"`
	Vrid       string        `json:"vrid"`
}

// StarRootV3 .
type StarRootV3 struct {
	Display    json.RawMessage `json:"display"`
	JSONData   StarJsonDataV3  `json:"jsonData"`
	Vr         bool            `json:"vr"`
	VrCategory string          `json:"vr_category"`
	Vrid       string          `json:"vrid"`
}

// StarJsonDataV3 .
type StarJsonDataV3 struct {
	BaseInfo    BaseInfo        `json:"base_info"`
	DisplayInfo json.RawMessage `json:"display_info"`
}

// BaseInfo .
type BaseInfo struct {
	Title string `json:"title"`
	URL   string `json:"url"`
}

// GoldRootV2 .
type GoldRootV2 struct {
	Display    json.RawMessage `json:"display"`
	JSONData   GoldJSONDataV2  `json:"jsonData"`
	Vr         bool            `json:"vr"`
	VrCategory string          `json:"vr_category"`
	Vrid       string          `json:"vrid"`
}

// GoldJSONDataV2 .
type GoldJSONDataV2 struct {
	BaseInfo    BaseInfo        `json:"base_info"`
	DisplayInfo json.RawMessage `json:"display_info"`
}

// CommonJSONData .
type CommonJSONData struct {
	BaseInfo    BaseInfo        `json:"base_info"`
	DisplayInfo json.RawMessage `json:"display_info"`
}

// CarRootV2 .
type CarRootV2 struct {
	Display    json.RawMessage `json:"display"`
	JSONData   CommonJSONData  `json:"jsonData"`
	Vr         bool            `json:"vr"`
	VrCategory string          `json:"vr_category"`
	Vrid       string          `json:"vrid"`
}

// CarRootV1 .
type CarRootV1 struct {
	Display CarDisplayV1 `json:"display"`
}

// CarDisplayV1 .
type CarDisplayV1 struct {
	Date  string          `json:"date"`
	Group json.RawMessage `json:"group"`
	Title string          `json:"title"`
	URL   string          `json:"url"`
}

// ExchangerateRoot .
type ExchangerateRoot struct {
	Display    json.RawMessage `json:"display"`
	JSONData   CommonJSONData  `json:"jsonData"`
	Vr         bool            `json:"vr"`
	VrCategory string          `json:"vr_category"`
	Vrid       string          `json:"vrid"`
}

// MedicalRootV1 .
type MedicalRootV1 struct {
	Display    MedicalDisplayV1 `json:"display"`
	VR         bool             `json:"vr"`
	VRCategory string           `json:"vr_category"`
	VRID       string           `json:"vrid"`
}

// MedicalDisplayV1 .
type MedicalDisplayV1 struct {
	Date       string          `json:"date"`
	SubDisplay json.RawMessage `json:"subDisplay"`
	Title      string          `json:"title"`
	URL        string          `json:"url"`
}
