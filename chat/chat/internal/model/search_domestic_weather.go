package model

// DomesticWeather 国内天气结果
type DomesticWeather struct {
	Display    Display  `json:"display"`
	JSONData   JSONData `json:"jsonData"`
	Vr         bool     `json:"vr"`
	VrCategory string   `json:"vr_category"`
	Vrid       string   `json:"vrid"`
}

// Display 表示展示信息的结构体
type Display struct {
	ContentTitle interface{} `json:"ContentTitle"`
	AbstractInfo interface{} `json:"abstract_info"`
	Content      interface{} `json:"content"`
	Date         interface{} `json:"date"`
	Favicon      interface{} `json:"favicon"`
	Title        string      `json:"title"`
	URL          string      `json:"url"`
}

// JSONData 包含JSON数据的详细信息
type JSONData struct {
	DisplayInfo DisplayInfo `json:"display_info"`
}

// DisplayInfo 包含展示信息的详细信息
type DisplayInfo struct {
	Day      []Day  `json:"day"`
	Hour     []Hour `json:"hour"`
	Location string `json:"location"`
	URL      string `json:"url"`
}

// Day 表示每天的天气信息
type Day struct {
	Date             string `json:"date"`
	DateDay          string `json:"date_day"`
	DayWind          string `json:"day_wind"`
	DayWindother     string `json:"day_windother"`
	DescriptionDay   string `json:"description_day"`
	DescriptionNight string `json:"description_night"`
	Forbidden        string `json:"forbidden"`
	Humidity         string `json:"humidity"`
	NightWind        string `json:"night_wind"`
	NightWindother   string `json:"night_windother"`
	NumberHigh       string `json:"number_high"`
	NumberLow        string `json:"number_low"`
	OtherTemperature string `json:"other_temperature"`
	OtherWeek        string `json:"other_week"`
	Summary          string `json:"summary"`
	TimeMonth        string `json:"time_month"`
	TimeSunrise      string `json:"time_sunrise"`
	TimeSunset       string `json:"time_sunset"`
	TypeWind         string `json:"type_wind"`
	TypeWindlevel    string `json:"type_windlevel"`
	Typhoon          string `json:"typhoon"`
	ValueCar         string `json:"value_car"`
	ValueSky         string `json:"value_sky"`
	Week             string `json:"week"`
}

// Hour 表示每小时的天气信息
type Hour struct {
	DayHour       string `json:"day_hour"`
	State         string `json:"state"`
	Temperature   string `json:"temperature"`
	Time          string `json:"time"`
	ValueHourPm25 string `json:"value_hour_pm25"`
}
