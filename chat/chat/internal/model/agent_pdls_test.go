package model

import (
	"bytes"
	"context"
	_ "embed" // embed 用于导入外部文档
	"fmt"
	"strings"
	"testing"
	"text/template"

	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"github.com/Masterminds/sprig/v3"
	"github.com/stretchr/testify/require"
)

const yamlStr = `
Name: 新闻查询
Desc: 根据特定需求查询新闻

SLOTs:
- name: news_location
  desc: 新闻发生地
  type: string
- name: news_type
  desc: 新闻类型
  type: string
- name: news_time
  desc: 新闻时间
  type: string

APIs:
- name: check_location
  precondition: []
- name: query_news
  precondition: [check_location]

ANSWERs:
- name: 获取新闻成功
  desc: 查询成功，返回查询到的新闻列表
- name: 获取新闻失败
  desc: 查询失败，转为播报当日头条新闻
- name: 其他自由回复问题
- name: 请用户提供必要信息

Procedure: |
  while True:
      [news_location] = ANSWER.请用户提供必要信息()
      if API.check_location([news_location]) == True:
          break
  [news_type, news_time] = ANSWER.请用户提供必要信息()
  [news_list] = API.query_news([news_location, news_type, news_time])
  if news_list is not None:
      ANSWER.获取新闻成功()
  else:
      ANSWER.获取新闻失败()

`

// TestGetConfig 测试获取配置
func TestGetConfig(t *testing.T) {
	t.Run("test get yaml config", func(t *testing.T) {
		// 测试yaml解析
		cfg := GetConfig(yamlStr)
		require.EqualValues(t, "新闻查询", cfg.Name)
		require.EqualValues(t, "根据特定需求查询新闻", cfg.Desc)
		// t.Logf("cfg is:  %v\n", cfg)
	})
}

func TestGetPDLDefaultTools(t *testing.T) {
	// 按照【图1】【图2】这种格式提取图片描述
	t.Run("test get pdl default tools", func(t *testing.T) {
		res := GetPDLDefaultTools(false)
		t.Logf("GetResponseToUser tools is:  %s\n", helper.Object2String(res[0]))
		t.Logf("GetWorkflowMain tools is:  %s\n", helper.Object2String(res[1]))
	})

}

func TestConvertAPIInfo(t *testing.T) {
	t.Run("test convert api info", func(t *testing.T) {
		// 测试yaml解析
		res := ConvertAPIInfo(OrderingProcessAPI)
		t.Logf("res is:  %v\n", res)
		require.NotNil(t, res)
	})

	t.Run("test convert to openAPI", func(t *testing.T) {
		res := ConvertToOpenAPI(ConvertAPIInfo(OrderingProcessAPI))
		t.Logf("ConvertToOpenAPI is:  %s\n", helper.Object2String(res))
	})
}

// WorkflowCtx 工作流上下文
type WorkflowCtx struct {
	WorkflowName string
	APIInfos     string
	PDL          string
}

// Render 渲染 Prompt
func RenderTest(ctx context.Context, tpl string, req any) (string, error) {
	// 去除模版每行中的空白符
	lines := strings.Split(tpl, "\n")
	for i := range lines {
		lines[i] = strings.TrimSpace(lines[i])
	}
	tpl = strings.Join(lines, "\n")

	funcMap := template.FuncMap{
		"addOne": func(i int) int {
			return i + 1
		},
	}

	e, err := template.New("").Funcs(sprig.TxtFuncMap()).Funcs(funcMap).Parse(tpl)
	if err != nil {
		fmt.Printf("Compile template error: %+v, tpl: %s", err, tpl)

		return "", pkg.ErrRenderTemplate
	}
	b := &bytes.Buffer{}
	if err := e.Execute(b, req); err != nil {
		fmt.Printf("Execute template error: %+v, tpl: %s, req: %+v", err, tpl, req)
		return "", pkg.ErrRenderTemplate
	}
	return b.String(), nil
}

// TestPDLPromptRender 测试PDL提示词渲染
func TestPDLPromptRender(t *testing.T) {
	t.Run("test pdl prompt render", func(t *testing.T) {
		prompt, err := RenderTest(context.Background(), WorkflowPrompt, WorkflowCtx{
			WorkflowName: "挂号",
			APIInfos:     helper.Object2String(ConvertToOpenAPI(ConvertAPIInfo(OrderingProcessAPI))),
			PDL:          OrderingProcessPDL,
		})
		require.NoError(t, err)
		t.Logf("prompt is:  %s\n", prompt)
	})
}

// TestConvertPDL
func TestConvertPDL(t *testing.T) {
	t.Run("test convert pdl", func(t *testing.T) {
		res := ConvertPDL(RegisterPDL)
		t.Logf("res is:  \n%s\n", res)
	})
}
