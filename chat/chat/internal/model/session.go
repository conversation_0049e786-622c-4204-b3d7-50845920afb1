package model

import (
	"time"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"github.com/google/uuid"
)

// SessionType 会话类型
type SessionType uint8

// 会话类型
const (
	SessionTypeAny        SessionType = 0  // 不限
	SessionTypeNormal     SessionType = 1  // 访客端体验
	SessionTypeExperience SessionType = 2  // 评测
	SessionTypeAPI        SessionType = 5  // API 访客
	SessionTypeWorkflow   SessionType = 10 // 工作流调试
)

// SessionTypeToDmEnv 运行环境
var SessionTypeToDmEnv = map[SessionType]KEP_DM.RunEnvType{
	SessionTypeNormal:     KEP_DM.RunEnvType_Product,
	SessionTypeExperience: KEP_DM.RunEnvType_Sandbox,
	SessionTypeWorkflow:   KEP_DM.RunEnvType_Sandbox,
	SessionTypeAPI:        KEP_DM.RunEnvType_Product,
}

// SessionTypeToWfEnv 运行环境
var SessionTypeToWfEnv = map[SessionType]KEP_WF_DM.RunEnvType{
	SessionTypeNormal:     KEP_WF_DM.RunEnvType_PRODUCT,
	SessionTypeExperience: KEP_WF_DM.RunEnvType_SANDBOX,
	SessionTypeWorkflow:   KEP_WF_DM.RunEnvType_SANDBOX,
	SessionTypeAPI:        KEP_WF_DM.RunEnvType_PRODUCT,
}

// SessionTypeToConnType 连接类型
var SessionTypeToConnType = map[SessionType]ConnType{
	SessionTypeNormal:     ConnTypeVisitor,
	SessionTypeExperience: ConnTypeExperience,
	SessionTypeWorkflow:   ConnTypeExperience,
	SessionTypeAPI:        ConnTypeAPIVisitor,
}

// Session 会话
type Session struct {
	ID             uint64      `db:"id"`
	Type           SessionType `db:"type"`
	SessionID      string      `db:"session_id"`
	LastRecordID   string      `db:"last_record_id"`
	BotID          uint64      `db:"bot_id"`
	BotBizID       uint64      `db:"bot_biz_id"`
	SeatID         uint64      `db:"seat_id"`
	SeatBizID      uint64      `db:"seat_biz_id"`
	VisitorID      uint64      `db:"visitor_id"`
	VisitorBizID   uint64      `db:"visitor_biz_id"`
	IsClosed       bool        `db:"is_closed"`
	IsTransfered   bool        `db:"is_transfered"`
	IsDeleted      bool        `db:"is_deleted"`
	LastVisitTime  time.Time   `db:"last_visit_time"`
	UntransferTime time.Time   `db:"untransfer_time"`
	ResetTime      time.Time   `db:"reset_time"`
	CreateTime     time.Time   `db:"create_time"`
	UpdateTime     time.Time   `db:"update_time"`
}

// NewSession 创建新会话
func NewSession(typ SessionType, visitor, bot, seat ID) *Session {
	return &Session{
		Type:         typ,
		SessionID:    uuid.NewString(),
		BotID:        bot.ID,
		BotBizID:     bot.BizID,
		SeatID:       seat.ID,
		SeatBizID:    seat.BizID,
		VisitorID:    visitor.ID,
		VisitorBizID: visitor.BizID,
		ResetTime:    time.Date(1000, 1, 1, 0, 0, 0, 0, time.Local),
	}
}

// IsSeat 判断会话是否归属于某个坐席
func (s *Session) IsSeat(id uint64) bool {
	return s != nil && s.SeatID == id
}

// IsVisitor 判断会话是否归属于某个访客
func (s *Session) IsVisitor(id uint64) bool {
	return s != nil && s.VisitorID == id
}

// IsTransferedToSeat 判断会话是否被转到坐席端
func (s *Session) IsTransferedToSeat() bool {
	return s != nil && !s.IsClosed && s.IsTransfered
}

// CurrentSeatID 获取当前会话坐席 ID，如果会话已结束或未被转人工则为 0
func (s *Session) CurrentSeatID() uint64 {
	if !s.IsTransferedToSeat() {
		return 0
	}
	return s.SeatID
}

// NeedGreeting 判断该会话是否需要下发欢迎语
func (s *Session) NeedGreeting() bool {
	cfg := config.App().Session
	return !s.IsTransfered && time.Since(s.LastVisitTime).Seconds() > float64(cfg.GreetingInterval)
}

// SearchNetwork 是否开启联网
type SearchNetwork string

const (
	// SearchNetworkDefault 不传，默认跟随APP配置
	SearchNetworkDefault SearchNetwork = ""
	// SearchNetworkEnabled 开启联网
	SearchNetworkEnabled SearchNetwork = "enable"
	// SearchNetworkDisabled 关闭联网
	SearchNetworkDisabled SearchNetwork = "disable"
)

// ChatStream 是否开启流式输出
type ChatStream string

const (
	// ChatStreamDefault 不传，默认跟随APP配置
	ChatStreamDefault ChatStream = ""
	// ChatStreamEnabled 开启流式输出
	ChatStreamEnabled ChatStream = "enable"
	// ChatStreamDisabled 关闭流式输出，即非流式输出
	ChatStreamDisabled ChatStream = "disable"
)

// WorkflowStatus 是否开启工作流
type WorkflowStatus string

const (
	// WorkflowStatusDefault 页面上没有工作流状态的配置，不传，默认开启工作流
	WorkflowStatusDefault WorkflowStatus = ""
	// WorkflowStatusEnabled 开启工作流
	WorkflowStatusEnabled WorkflowStatus = "enable"
	// WorkflowStatusDisabled 关闭工作流
	WorkflowStatusDisabled WorkflowStatus = "disable"
)

// DialogUserType 对话段用户类型
type DialogUserType string

const (
	// DialogUserTypeExperience C端体验用户
	DialogUserTypeExperience DialogUserType = "experience"
	// DialogUserTypeStaff 腾讯云企业用户
	DialogUserTypeStaff DialogUserType = "staff"
)

// 输出方式 1：流式 2：非流式
var (
	ChatOutputMethodStream    uint32 = 1 // 输出方式 1：流式
	ChatOutputMethodNonStream uint32 = 2 // 输出方式 2：非流式
)

// AutoPlay 自动播放
type AutoPlay string

const (
	// AutoPlayDefault 不传，默认跟随APP配置
	AutoPlayDefault AutoPlay = ""
	// AutoPlayEnabled 开启自动播放
	AutoPlayEnabled AutoPlay = "enable"
	// AutoPlayDisabled 关闭自动播放
	AutoPlayDisabled AutoPlay = "disable"
)

// UserDialogConfig 用户应用配置
type UserDialogConfig struct {
	BusinessID     uint64         `db:"business_id"` // 用户应用配置ID
	UserBizID      uint64         `db:"user_biz_id"` // 用户ID
	UserType       DialogUserType `db:"user_type"`   // 用户类型：experience: C端体验用户，staff：腾讯云企业用户
	BotBizID       uint64         `db:"bot_biz_id"`  // 应用ID
	ModelName      string         `db:"model_name"`
	SearchNetwork  SearchNetwork  `db:"search_network"`
	Stream         ChatStream     `db:"stream"`          //  是否开启流式输出
	WorkflowStatus WorkflowStatus `db:"workflow_status"` // 是否开启工作流
	AutoPlay       AutoPlay       `db:"auto_play"`
}
