// Package model 定义数据模型或方法，不应包含业务逻辑
package model

import "time"

// RecommendQuestion 推荐问题
type RecommendQuestion struct {
	ID         int64     `db:"id"`
	Question   string    `db:"question"`
	CreateTime time.Time `db:"create_time"`
	UpdateTime time.Time `db:"update_time"`
}

// ExperienceRecommendQuestion 体验中心推荐问题
type ExperienceRecommendQuestion struct {
	ID         int64     `db:"id"`
	RobotID    int64     `db:"robot_id"`
	Question   string    `db:"question"`
	Content    string    `db:"content"`
	ShowType   uint32    `db:"show_type"`
	CreateTime time.Time `db:"create_time"`
	UpdateTime time.Time `db:"update_time"`
}
