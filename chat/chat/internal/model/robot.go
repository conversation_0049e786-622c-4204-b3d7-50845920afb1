package model

import (
	"bytes"
	"context"
	"strings"
	"text/template"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"github.com/Masterminds/sprig/v3"
	"go.opentelemetry.io/otel/trace"
)

// Prompt 截断特殊标记
const (
	TruncStart  = "<$!truN#cAte+>"
	TruncFinish = "<-truN#cAte!$>"
)

// Prompt 截断特殊标记长度
var (
	TruncLen       = TruncStartLen + TruncFinishLen
	TruncStartLen  = len(TruncStart)
	TruncFinishLen = len(TruncFinish)
)

// Patterns 拒答模式
var Patterns []string

// PromptVersionMap 问答版本
var PromptVersionMap []*PromptVersion

// AgentToolConfigs 工具配置
var AgentToolConfigs []*AgentToolConfig

// 机器人过滤器名称
const (
	FilterKeyPreviewQuestion = "search_preview_question" // 评测问答对 (可以直接回复)
	FilterKeyReleaseQuestion = "search_release_question" // 正式问答对 (可以直接回复)
	FilterKeySearchEngine    = "search_engine"           // 搜索引擎
	FilterKeySearchPreview   = "search_preview"          // 评测端知识库配置
	FilterKeySearchRelease   = "search_release"          // 发布端知识库配置
)

// 检索范围
const (
	SearchScopeAll = 0 // 不传为检索QA和文档
	SearchScopeQA  = 1 // 只检索QA
	SearchScopeDoc = 2 // 只检索文档
)

// 回复灵活度
const (
	ReplyFlexibilityLLM        = 2 // 模型润色
	ReplyFlexibilityQAPriority = 1 // 已采纳问答对直接回复
)

// TraceID 获取 Trace ID
func TraceID(ctx context.Context) string {
	return trace.SpanContextFromContext(ctx).TraceID().String()
}

// RequestID 获取大模型请求 ID
func RequestID(ctx context.Context, sessionID, recordID string) string {
	return TraceID(ctx) + ":" + sessionID + ":" + recordID
}

// Render 渲染 Prompt
func Render(ctx context.Context, tpl string, req any) (string, error) {
	// 去除模版每行中的空白符
	lines := strings.Split(tpl, "\n")
	for i := range lines {
		lines[i] = strings.TrimSpace(lines[i])
	}
	tpl = strings.Join(lines, "\n")

	funcMap := template.FuncMap{
		"addOne": func(i int) int {
			return i + 1
		},
	}

	e, err := template.New("").Funcs(sprig.TxtFuncMap()).Funcs(funcMap).Parse(tpl)
	if err != nil {
		log.ErrorContextf(ctx, "Compile template error: %+v, tpl: %s", err, tpl)
		return "", pkg.ErrRenderTemplate
	}
	b := &bytes.Buffer{}
	if err := e.Execute(b, req); err != nil {
		log.ErrorContextf(ctx, "Execute template error: %+v, tpl: %s, req: %+v", err, tpl, req)
		return "", pkg.ErrRenderTemplate
	}
	return b.String(), nil
}

// HisMessage 历史记录消息
type HisMessage struct {
	RecordID       string // 记录ID
	Content        string // 内容
	Intent         string // 意图
	IntentCategory string // 意图达成方式
}

// TruncateQAHistories 截断历史记录histories,倒序遍历，超过wordsCount以后就截断返回
func TruncateQAHistories(ctx context.Context, histories [][2]HisMessage, wordsCount int) [][2]HisMessage {
	length := len(histories)
	totalLength := 0                   // 当前总长度
	for i := length - 1; i >= 0; i-- { // 总数2000
		if len([]rune(histories[i][0].Content)) >= wordsCount-totalLength { // 问题已经超了
			log.InfoContextf(ctx, "histories after truncated: %s", helper.Object2String(histories[i+1:length]))
			return histories[i+1 : length] // i+1 把问题一起丢掉
		}
		totalLength += len([]rune(histories[i][0].Content))
		if len([]rune(histories[i][1].Content)) > wordsCount-totalLength { // 答案超了, 等于不算超
			histories[i][1].Content = TruncateSentence([]rune(histories[i][1].Content)[:wordsCount-totalLength])
		}
		totalLength += len([]rune(histories[i][1].Content))
		if totalLength >= wordsCount {
			log.InfoContextf(ctx, "histories after truncated: %s", helper.Object2String(histories[i:length]))
			return histories[i:length] // 保留截断的部分
		}
	}
	return histories
}

// TruncateHistories 调整历史记录上线 for DM
func TruncateHistories(histories [][2]HisMessage, limit int) [][2]HisMessage {
	length := len(histories)
	totalLength := 0
	singleMessageLimit := int(config.App().Bot.SingleMessageLimit)
	for i := length; i > 0; i-- { // DM限制单条150，总数2000
		if len([]rune(histories[i-1][0].Content)) > singleMessageLimit {
			histories[i-1][0].Content = TruncateSentence([]rune(histories[i-1][0].Content)[:singleMessageLimit/2]) +
				string([]rune(histories[i-1][0].Content)[len([]rune(histories[i-1][0].Content))-singleMessageLimit/2:])
		}
		if len([]rune(histories[i-1][1].Content)) > singleMessageLimit {
			histories[i-1][1].Content = TruncateSentence([]rune(histories[i-1][1].Content)[:singleMessageLimit/2]) +
				string([]rune(histories[i-1][1].Content)[len([]rune(histories[i-1][1].Content))-singleMessageLimit/2:])
		}
		totalLength += len([]rune(histories[i-1][0].Content)) + len([]rune(histories[i-1][1].Content))
		if totalLength > limit {
			return histories[i:length]
		}
	}
	return histories
}

// TruncateSentence 按标点符号截断句子
func TruncateSentence(sentence []rune) string {
	// 标点符号map
	punctuationMarksMap := map[rune]bool{'。': true, '，': true, '！': true, '？': true, '；': true, '：': true,
		'、': true, ',': true, '.': true, '!': true, '?': true, ';': true, ':': true, '…': true, ' ': true}
	length := len(sentence)
	for i := length; i > 0; i-- {
		if _, ok := punctuationMarksMap[sentence[i-1]]; ok { // 如果没有标点 原样返回
			sentence = sentence[:i-1] // 丢弃标点符号
			break
		}
	}
	return string(sentence)
}
