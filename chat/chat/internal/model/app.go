package model

import (
	"context"
	"fmt"
	"slices"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"github.com/google/uuid"
	"golang.org/x/exp/rand"
)

// App 应用
type App struct {
	*admin.GetAppInfoRsp
	IsExclusive bool // 是否专属并发
}

// AppListInfo 应用列表信息
type AppListInfo struct {
	*admin.GetAppsByBizIDsRsp_AppInfo
}

const (
	// AppTypeKnowledgeQA 知识问答
	AppTypeKnowledgeQA string = "knowledge_qa"
	// AppTypeSummary 知识摘要
	AppTypeSummary string = "summary"
	// AppTypeClassify 知识标签提取
	AppTypeClassify string = "classify"
	// AppStatusStopped 停用
	AppStatusStopped int64 = 3 // 停用

	// InfoSecCheckMaxContentLength 最大送审文本长度
	InfoSecCheckMaxContentLength int = 10000
	// CheckContextSize 送审拆分上下文长度
	CheckContextSize int = 50
	// MedicalModelName 医疗行业大模型名称
	MedicalModelName string = "medical-6b"
)

// 应用模式 standard:标准模式, agent: agent模式，single_workflow：单工作流模式
const (
	// AppModeStandard 标准模式
	AppModeStandard string = "standard"
	// AppModeAgent agent模式
	AppModeAgent string = "agent"
	// AppModeSingleWorkflow 单工作流模式
	AppModeSingleWorkflow string = "single_workflow"
)

// GetMaxConcurrence 获取最大并发数
func (a *App) GetMaxConcurrence(botBizID uint64) uint {
	if limit, ok := config.App().Bot.MaxConcurrence.WhiteList[botBizID]; ok {
		return limit
	}
	return config.App().Bot.MaxConcurrence.Standard
}

// NewGreetingRecord 构造欢迎语消息记录
func (a *App) NewGreetingRecord(sessionID string, id uint64, typ SourceType) MsgRecord {
	now := time.Now()
	return MsgRecord{
		Type:             RecordTypeGreeting,
		RecordID:         uuid.NewString(),
		SessionID:        sessionID,
		ToID:             id,
		ToType:           typ,
		FromID:           a.GetId(),
		FromType:         SourceTypeRobot,
		Content:          a.GetGreeting(),
		Reference:        "null",
		ReplyMethod:      ReplyMethodGreeting,
		Knowledge:        "null",
		RejectedQuestion: "null",
		CreateTime:       now,
		UpdateTime:       now,
	}
}

// GetAgentPrompt 获取 agent 模式下的人提示词模版
func (a *App) GetAgentPrompt(ctx context.Context, typ ModelType) string {
	mapModels := a.GetKnowledgeQa().GetModel()
	if m, ok := mapModels[string(typ)]; ok {
		return m.GetPrompt()
	}
	log.ErrorContextf(ctx, "can not found agent prompt for %s", typ)
	// 没有找到使用默认的模版配置
	if am, ok := mapModels[string(ModelTypeMessage)]; ok {
		return am.GetPrompt()
	}
	return ""
}

// GetModel 获取模型配置， 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classifys-知识标签提取
func (a *App) GetModel(ctx context.Context, appType string, typ ModelType) *AppModel {
	mapModels := map[string]*admin.AppModelInfo{}
	if appType == AppTypeKnowledgeQA {
		mapModels = a.GetKnowledgeQa().GetModel()
	} else if appType == AppTypeSummary {
		mapModels = a.GetSummary().GetModel()
	} else if appType == AppTypeClassify {
		mapModels = a.GetClassify().GetModel()
	}
	log.DebugContextf(ctx, "GetModel for [appType: %s], %+v", appType, typ)
	if m, ok := mapModels[string(typ)]; ok {
		am := &AppModel{
			AppModelInfo: m,
			RoleDesc:     a.RoleDescription(),
			BotBizID:     a.GetAppBizId(),
			ModelType:    typ,
		}
		log.DebugContextf(ctx, "GetModel for [appType:%s,modelType:%s], model is: %s",
			appType, typ, helper.Object2String(am))
		return am
	}
	am := &AppModel{
		AppModelInfo: mapModels[string(ModelTypeMessage)],
		RoleDesc:     a.RoleDescription(),
		BotBizID:     a.GetAppBizId(),
		ModelType:    ModelTypeMessage,
	}
	log.DebugContextf(ctx, "GetModel for [appType:%s,modelType:%s], model is: %s", appType, typ, helper.Object2String(am))
	return am
}

// GetMessageModelType 获取会话模型配置。
// 何时使用拒答Prompt？
// 1. 使用保守回复用拒答Prompt；
// 2. 搜索引擎有开启用拒答Prompt；
// 3. 金融行业大模型知识包；
func (a *App) GetMessageModelType() ModelType {
	modelType := ModelTypeMessage
	// 使用保守回复用拒答Prompt；或者  搜索引擎有开启用拒答Prompt
	if !a.GetKnowledgeQa().GetOutput().GetUseGeneralKnowledge() || a.GetKnowledgeQa().UseSearchEngine {
		modelType = ModelTypeMessageNonGeneralKnowledge
	}
	return modelType
}

// GetDomainMessageModelType 获取领域消息模型类型
// 当使用行业知识包时，何时使用拒答Prompt？
// 1. 使用保守回复用拒答Prompt；
// 2. 搜索引擎有开启用拒答Prompt；
// func (a *App) GetDomainMessageModelType() ModelType {
//	modelType := ModelTypeMessage
//	// 使用保守回复用拒答Prompt；或者  搜索引擎有开启用拒答Prompt
//	if !a.GetKnowledgeQa().GetOutput().GetUseGeneralKnowledge() || a.GetKnowledgeQa().UseSearchEngine {
//		modelType = ModelTypeMessageNonGeneralKnowledge
//	}
//	return modelType
// }

// GetSummaryModelType 获取知识摘要模型类型
func (a *App) GetSummaryModelType() ModelType {
	return ModelTypeSummaryRecognize
}

// GetClassifyModelType 获取标签提取模型类型
func (a *App) GetClassifyModelType() ModelType {
	return ModelTypeClassifyExtract
}

// GetModelName 获取模型名称
func (a *App) GetModelName() string {
	if a == nil {
		return ""
	}
	var m *admin.AppModelInfo
	var ok bool
	// 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
	switch a.GetAppType() {
	case AppTypeKnowledgeQA:
		m, ok = a.GetKnowledgeQa().GetModel()[string(a.GetMessageModelType())]
	case AppTypeSummary:
		m, ok = a.GetSummary().GetModel()[string(a.GetSummaryModelType())]
	case AppTypeClassify:
		m, ok = a.GetClassify().GetModel()[string(a.GetClassifyModelType())]
	default:
		log.WarnContextf(trpc.BackgroundContext(), "GetModelName, unknown app type: %s", a.GetAppType())
	}
	var mn string
	if ok {
		mn = m.GetModelName()
	}
	return mn
}

// GetMainModelName 主模型信息, 应用的配置所选模型
func (a *App) GetMainModelName() string {
	if a == nil {
		return ""
	}
	var m *admin.AppModelInfo
	var ok bool
	// 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
	switch a.GetAppType() {
	case AppTypeKnowledgeQA:
		m, ok = a.GetKnowledgeQa().GetModel()[string(ModelTypeMessage)]
	case AppTypeSummary:
		m, ok = a.GetSummary().GetModel()[string(a.GetSummaryModelType())]
	case AppTypeClassify:
		m, ok = a.GetClassify().GetModel()[string(a.GetClassifyModelType())]
	}
	var mn string
	if ok {
		mn = m.GetModelName()
	}
	return mn
}

// GetAgentMainModelName 获取Agent主模型信息, 应用的配置所选模型
func (a *App) GetAgentMainModelName() string {
	if a == nil {
		return ""
	}
	var m *admin.AppModelInfo
	var ok bool
	// 应用类型；knowledge_qa-知识问答管理；summary-知识摘要；classify-知识标签提取
	switch a.GetAppType() {
	case AppTypeKnowledgeQA:
		m, ok = a.GetKnowledgeQa().GetModel()[string(ModelTypeAgentMain)]
	}
	var mn string
	if ok {
		mn = m.GetModelName()
	}
	return mn
}

// IsQAPriority 判断是否已采纳问答对优先回复
func (a *App) IsQAPriority() bool {
	return a.GetKnowledgeQa().GetReplyFlexibility() == ReplyFlexibilityQAPriority
}

// IsTransferKeyword 判断是否关键词转人工
func (a *App) IsTransferKeyword(content string) bool {
	if !a.GetKnowledgeQa().CanTransferKeyword {
		return false
	}
	for _, keyword := range a.GetKnowledgeQa().GetTransferKeywords() {
		if strings.Contains(content, keyword) {
			return true
		}
	}
	return false
}

// RoleDescription 角色描述
func (a *App) RoleDescription() string {
	if a.KnowledgeQa != nil && len(a.GetKnowledgeQa().RoleDescription) != 0 {
		return a.GetKnowledgeQa().RoleDescription
	}
	// return config.App().Bot.RoleDescription // 产品：先不给默认的
	return ""
}

// IsQARelease 问答是否发布.查看QA版本是否为0，为0则未发布
func (a *App) IsQARelease() bool {
	if a.GetKnowledgeQa().GetQaVersion() == 0 {
		log.InfoContextf(trpc.BackgroundContext(), "IsQARelease false, qaVersion: %d",
			a.GetKnowledgeQa().GetQaVersion())
		return false
	}
	log.DebugContextf(trpc.BackgroundContext(), "IsQARelease true, qaVersion: %d",
		a.GetKnowledgeQa().GetQaVersion())
	return true
}

// CanUsePlaceholder 是否可以使用占位符
func (a *App) CanUsePlaceholder() bool {
	if a.KnowledgeQa != nil && a.GetKnowledgeQa().GetModel() != nil {
		mapModels := a.GetKnowledgeQa().GetModel()
		var modelName string
		if m, ok := mapModels[string(ModelTypeMessage)]; ok {
			modelName = m.GetModelName()
		}
		for _, v := range config.App().UsePlaceholderModels {
			log.Debugf("CanUsePlaceholder - now use: %s, in config: %s", modelName, v)
			if modelName == v {
				return true
			}
		}
	}
	return false
}

// CanUseFinancialKnowledge 是否使用金融行业大模型知识包
func (a *App) CanUseFinancialKnowledge() bool {
	// 先看是否金融大模型
	var modelName string
	if a.KnowledgeQa != nil && a.GetKnowledgeQa().GetModel() != nil {
		mapModels := a.GetKnowledgeQa().GetModel()
		if m, ok := mapModels[string(ModelTypeMessage)]; ok {
			modelName = m.GetModelName()
		}
	}
	if modelName != "finance-13b" {
		return false // 不是金融行业大模型，不用行业知识包
	}
	for _, v := range config.App().DisableFinancialKnowledge {
		if a.AppBizId == v {
			log.Debugf("DisableDomainKnowledge for appID: %d", v)
			return false // 金融行业大模型知识包禁用
		}
	}
	return true
}

// CanUseMedicalKnowledge 是否使用医疗行业大模型知识包
func (a *App) CanUseMedicalKnowledge() bool {
	// 先看是否医疗大模型
	var modelName string
	if a.KnowledgeQa != nil && a.GetKnowledgeQa().GetModel() != nil {
		mapModels := a.GetKnowledgeQa().GetModel()
		if m, ok := mapModels[string(ModelTypeMessage)]; ok {
			modelName = m.GetModelName()
		}
	}
	if modelName != MedicalModelName {
		return false // 不是医疗行业大模型，不用行业知识包
	}
	for _, v := range config.App().DisableMedicalKnowledge {
		if a.AppBizId == v {
			log.Debugf("DisableDomainKnowledge for appID: %d", v)
			return false // 医疗行业大模型知识包禁用
		}
	}
	return true

}

// IsDomainModel 是否是行业大模型
func (a *App) IsDomainModel() bool {
	if a.KnowledgeQa != nil && a.GetKnowledgeQa().GetModel() != nil {
		mapModels := a.GetKnowledgeQa().GetModel()
		if m, ok := mapModels[string(ModelTypeMessage)]; ok {
			modelName := m.GetModelName()
			if modelName == "finance-13b" || modelName == MedicalModelName { // todo 加配置
				return true
			}
		}
	}
	return false
}

// IsMedicalModel 是否是医疗行业大模型
func (a *App) IsMedicalModel() bool {
	if a.KnowledgeQa != nil && a.GetKnowledgeQa().GetModel() != nil {
		mapModels := a.GetKnowledgeQa().GetModel()
		if m, ok := mapModels[string(ModelTypeMessage)]; ok {
			modelName := m.GetModelName()
			if modelName == MedicalModelName {
				return true
			}
		}
	}
	return false
}

// IsSupportMultiIntent 是否支持多意图
func (a *App) IsSupportMultiIntent(enableMultiIntent bool) bool {
	if enableMultiIntent && a.GetKnowledgeQa().GetMultipleIntent() {
		return true
	}
	return false
}

// GetGreeting 获取欢迎语
func (a *App) GetGreeting() string {
	if a == nil {
		return ""
	}
	if a.GetAppType() == AppTypeKnowledgeQA {
		return a.GetKnowledgeQa().GetGreeting()
	}
	if a.GetAppType() == AppTypeSummary {
		return a.GetSummary().GetGreeting()
	}
	if a.GetAppType() == AppTypeClassify {
		return a.GetClassify().GetGreeting()
	}

	return ""
}

// IsAppTypeSummary 是否是知识摘要
func (a *App) IsAppTypeSummary() bool {
	if a == nil {
		return false
	}
	return a.GetAppType() == AppTypeSummary
}

// IsAppTypeClassify 是否是知识标签提取
func (a *App) IsAppTypeClassify() bool {
	if a == nil {
		return false
	}
	return a.GetAppType() == AppTypeClassify
}

// GetQaConfidence 获取QA置信度信息
func (a *App) GetQaConfidence(filterKey string) float32 {
	if len(a.GetKnowledgeQa().GetFilters()) == 0 {
		return 0
	}
	filters, ok := a.GetKnowledgeQa().GetFilters()[filterKey]
	if !ok {
		return 0
	}
	for _, v := range filters.GetFilter() {
		if v.GetDocType() == DocTypeQA {
			return v.GetConfidence()
		}
	}
	return 0
}

// IsNewWorkflow 是否新版本工作流
func (a *App) IsNewWorkflow() bool {
	return a.GetKnowledgeQa().GetWorkflow().GetIsEnabled()
}

// GetHistoryLimit 获取历史会话限制
func (a *App) GetHistoryLimit() uint32 {
	if a.KnowledgeQa != nil && a.GetKnowledgeQa().GetModel() != nil {
		mapModels := a.GetKnowledgeQa().GetModel()
		if m, ok := mapModels[string(ModelTypeMessage)]; ok {
			return m.GetHistoryLimit()
		}
	}
	return 0
}

// IsDeepSeekModel 是否是深度学习模型
func (a *App) IsDeepSeekModel(ctx context.Context) bool {
	modelName := a.GetModelName()
	for _, name := range config.App().DeepSeekConf.ModelName {
		if name == modelName {
			log.InfoContextf(ctx, "is deep seek model: %s", modelName)
			return true
		}
	}
	return false
}

// IsDeepSeekModeAndHasThink 是否是深度学习模型，且有思维链
func (a *App) IsDeepSeekModeAndHasThink() bool {
	modelName := a.GetModelName()
	if len(config.App().DeepSeekConf.ModelHasThink) == 0 {
		return false
	}
	return slices.Contains(config.App().DeepSeekConf.ModelHasThink, modelName)
}

// IsEnableDocSearch 知识库是否开启文档检索
func (a *App) IsEnableDocSearch(env string) bool {
	switch env {
	case "experience": // 评测端
		cfg, ok := a.GetKnowledgeQa().GetFilters()[FilterKeySearchPreview]
		if ok {
			for _, filter := range cfg.GetFilter() {
				if filter.GetDocType() == DocTypeSegment && filter.GetIsEnable() {
					return true
				}
			}
		}
	case "send": // 发布端
		cfg, ok := a.GetKnowledgeQa().GetFilters()[FilterKeySearchRelease]
		if ok {
			for _, filter := range cfg.GetFilter() {
				if filter.GetDocType() == DocTypeSegment && filter.GetIsEnable() {
					return true
				}
			}
		}
	}
	return false
}

// GetCombinedKnowledgeRetrieval 是否开启知识库综合检索
func (a *App) GetCombinedKnowledgeRetrieval() bool {
	support := a.GetKnowledgeQa().GetCombinedKnowledgeRetrieval()
	if support {
		return true
	}
	if config.IsNoFaq(a.GetAppBizId()) { // 如果意图不送faq, 则默认开启组合知识问答
		return true
	}
	return false
}

// AppModel 模型配置
type AppModel struct {
	*admin.AppModelInfo
	RoleDesc    string
	BotBizID    uint64
	PromptLimit int
	ModelType   ModelType
}

// InferParams 模型推理扩展参数
type InferParams struct {
	SearchInfo               bool     `json:"search_info,omitempty"`
	Citation                 bool     `json:"citation,omitempty"`                  // 引文
	EnableInstructionSearch  bool     `json:"enable_instruction_search,omitempty"` // 开启指令遵循搜索
	EnableEnhancement        bool     `json:"enable_enhancement,omitempty"`        // 开启指令遵循搜索
	StopWordsList            []string `json:"stop_words_list,omitempty"`
	TopK                     uint32   `json:"top_k,omitempty"`
	TopP                     float32  `json:"top_p,omitempty"`
	Temperature              float32  `json:"temperature,omitempty"`
	Seed                     *int     `json:"seed,omitempty"`
	JSONStartMark            string   `json:"json_start_mark,omitempty"`
	ExcludeSearchEngineTypes []string `json:"exclude_search_engine_types,omitempty"` // 排除搜索引擎类型
	FixedSearchEngineTypes   []string `json:"fixed_search_engine_types,omitempty"`   // 固定搜索引擎类型
}

// GetSysPrompt 获取系统提示
func (m *AppModel) GetSysPrompt(useRole, isSelfAwareness bool, sysRole string) string {
	if len(sysRole) > 0 {
		if isSelfAwareness && m.IsShenNongModel() {
			return sysRole
		}
		return sysRole
	} else {
		role := strings.TrimSpace(m.RoleDesc)
		if isSelfAwareness && role == "" {
			role = config.GetBotRoleDescription(m.ModelName) // 自我认知场景给默认值，其他不给。
		}
		if useRole && role != "" && m.ModelName != config.App().HunYuanConfig.HunYuanStandard { // 混元标准版不支持角色描述
			return role
		}
	}
	return ""
}

// WrapMessages 包装message消息
func (m *AppModel) WrapMessages(sysRole string, histories [][2]HisMessage, prompt string) []*llmm.Message {
	var messages []*llmm.Message
	// 1. 系统提示
	if len(sysRole) > 0 {
		messages = append(messages, &llmm.Message{Role: llmm.Role_SYSTEM, Content: sysRole})
	}
	// 2. 历史对话
	for _, pair := range histories {
		messages = append(messages,
			&llmm.Message{Role: llmm.Role_USER, Content: pair[0].Content},
			&llmm.Message{Role: llmm.Role_ASSISTANT, Content: pair[1].Content},
		)
	}
	// 3. 用户问题
	if prompt != "" {
		messages = append(messages, &llmm.Message{Role: llmm.Role_USER, Content: prompt})
	}
	return messages
}

// GetModelParams 获取模型参数
func (m *AppModel) GetModelParams() string {
	if m == nil {
		return ""
	}
	params := InferParams{}
	if m.GetTopK() > 0 {
		params.TopK = m.GetTopK()
	}
	if m.GetTopP() > 0 {
		params.TopP = m.GetTopP()
	}
	if m.GetTemperature() > 0 {
		params.Temperature = m.GetTemperature()
	}
	return helper.Object2String(params)
}

// GetAgentModelParams 获取模型参数
func (m *AppModel) GetAgentModelParams() string {
	if m == nil {
		return ""
	}
	params := InferParams{}
	if m.GetTopK() > 0 {
		params.TopK = m.GetTopK()
	}
	if m.GetTopP() > 0 {
		params.TopP = m.GetTopP()
	}
	if m.GetTemperature() > 0 {
		params.Temperature = m.GetTemperature()
	}
	if strings.HasPrefix(m.GetModelName(), "lke-deepseek") {
		params.JSONStartMark = "Action Input: "
	}
	return helper.Object2String(params)
}

// CreateModelParamsObject .
func (m *AppModel) CreateModelParamsObject(enableRandomSeed bool) *InferParams {
	if m == nil {
		return nil
	}
	params := &InferParams{}
	if m.GetTopK() > 0 {
		params.TopK = m.GetTopK()
	}
	if m.GetTopP() > 0 {
		params.TopP = m.GetTopP()
	}
	if m.GetTemperature() > 0 {
		params.Temperature = m.GetTemperature()
	}
	if enableRandomSeed {
		params.WithSeed(rand.Intn(10000) + 1)
	}
	return params
}

// WithSeed .
func (p *InferParams) WithSeed(seed int) *InferParams {
	p.Seed = new(int)
	*p.Seed = seed
	return p
}

// WithStopWordsList .
func (p *InferParams) WithStopWordsList(list []string) *InferParams {
	p.StopWordsList = list
	return p
}

// GenerateSearchParams 获取搜索参数
func (m *AppModel) GenerateSearchParams() string {
	mParams := map[string]bool{"search_info": true, "citation": true}
	return helper.Object2String(mParams)
}

// NewLLMRequest 构造 LLM 请求
func (m *AppModel) NewLLMRequest(requestID string, messages []*llmm.Message) *llmm.Request {
	return &llmm.Request{
		RequestId:   requestID,
		ModelName:   m.GetModelName(),
		AppKey:      fmt.Sprintf("%d", m.BotBizID),
		Messages:    messages,
		ModelParams: m.GetModelParams(),
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
	}
}

// NewLLMRequestWithModelParams 构造 LLM 请求
func (m *AppModel) NewLLMRequestWithModelParams(requestID string, messages []*llmm.Message,
	modelParams *InferParams) *llmm.Request {
	modelParamsStr := helper.Object2String(modelParams)
	return &llmm.Request{
		RequestId:   requestID,
		ModelName:   m.GetModelName(),
		AppKey:      fmt.Sprintf("%d", m.BotBizID),
		Messages:    messages,
		ModelParams: modelParamsStr,
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
	}
}

// NewHunYuanSearchRequest 混元请求
func (m *AppModel) NewHunYuanSearchRequest(
	ctx context.Context, session *Session, recordID, query, sysRole string,
) *llmm.Request {
	var messages []*llmm.Message
	var inferParams InferParams
	inferParams.SearchInfo = true
	inferParams.Citation = true
	inferParams.EnableEnhancement = true
	if sysRole == "" { // 增加角色扮演描述
		sysRole = strings.TrimSpace(m.RoleDesc)
	}
	if sysRole != "" {
		messages = append(messages, &llmm.Message{Role: llmm.Role_SYSTEM, Content: sysRole})
		if config.IsEnableInstructionSearch(m.BotBizID) { // 白名单控制, 有系统提示，需要开启
			inferParams.EnableInstructionSearch = true
		}
	}

	messages = append(messages, &llmm.Message{Role: llmm.Role_USER, Content: query})
	return &llmm.Request{
		RequestId:   RequestID(ctx, session.SessionID, recordID),
		ModelName:   m.GetModelName(),
		AppKey:      fmt.Sprintf("%d", m.BotBizID),
		Messages:    messages,
		ModelParams: helper.Object2String(inferParams),
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
	}
}

// NewYuanBaoSearchRequest 元宝联网请求
func (m *AppModel) NewYuanBaoSearchRequest(requestID string, messages []*llmm.Message) *llmm.Request {
	var inferParams InferParams
	inferParams.SearchInfo = true
	inferParams.Citation = true
	inferParams.EnableEnhancement = true
	inferParams.ExcludeSearchEngineTypes = config.App().DeepSeekConf.ExcludeSearchEngine // 排除微信公众号

	return &llmm.Request{
		RequestId:   requestID,
		ModelName:   m.GetModelName(),
		AppKey:      fmt.Sprintf("%d", m.BotBizID),
		Messages:    messages,
		ModelParams: helper.Object2String(inferParams),
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
	}
}

// NewPOTMathRequest pot math 请求
func (m *AppModel) NewPOTMathRequest(ctx context.Context, session *Session,
	recordID, query, sysRole string, modelParams *InferParams) *llmm.Request {
	messages := make([]*llmm.Message, 0)
	if len(sysRole) > 0 {
		sysRoleMsg := &llmm.Message{Role: llmm.Role_SYSTEM, Content: sysRole}
		messages = append(messages, sysRoleMsg)
	}
	messages = append(messages, &llmm.Message{Role: llmm.Role_USER, Content: query})
	modelParamsStr := helper.Object2String(modelParams)
	return &llmm.Request{
		RequestId:   RequestID(ctx, session.SessionID, recordID),
		ModelName:   m.GetModelName(),
		AppKey:      fmt.Sprintf("%d", m.BotBizID),
		Messages:    messages,
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
		// ModelParams: "{\"stop_words_list\":[\"→\"]}", // v2.5 新加字段
		ModelParams: modelParamsStr,
	}
}

// NewMedicalRequest 医疗行业模型
func (m *AppModel) NewMedicalRequest(
	ctx context.Context, session *Session, recordID, query, sysRole string,
) *llmm.Request {
	var messages []*llmm.Message
	if len(sysRole) > 0 {
		messages = append(messages, &llmm.Message{Role: llmm.Role_SYSTEM, Content: sysRole})
	}
	messages = append(messages, &llmm.Message{Role: llmm.Role_USER, Content: query})
	return &llmm.Request{
		RequestId:   RequestID(ctx, session.SessionID, recordID),
		ModelName:   MedicalModelName,
		AppKey:      fmt.Sprintf("%d", m.BotBizID),
		Messages:    messages,
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
	}
}

// NewAgentRequest 智能体请求参数
func (m *AppModel) NewAgentRequest(requestID string, messages []*AgentMemory,
	tools []*openapi.Tool, prompt string) *llmm.Request {
	newMessage := make([]*llmm.Message, 0)
	for _, v := range messages {
		newMessage = append(newMessage, v.Message)
	}
	return &llmm.Request{
		RequestId:   requestID,
		ModelName:   m.GetModelName(),
		AppKey:      fmt.Sprintf("%d", m.BotBizID),
		Messages:    newMessage,
		Tools:       tools,
		ExtraParams: &openapi.ExtraParams{PromptTemplate: prompt},
		ModelParams: m.GetAgentModelParams(),
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
	}
}

// RenderMedicalPrompt 医疗Prompt
func (m *AppModel) RenderMedicalPrompt(ctx context.Context, req any) (string, error) {
	prompt, err := Render(ctx, TplMedical, req)
	if err != nil {
		return "", err
	}
	return TruncatePrompt(ctx, prompt, int(m.GetPromptWordsLimit())), nil

}

// RenderPrompt 渲染 Prompt 并处理截断
func (m *AppModel) RenderPrompt(ctx context.Context, req any) (string, error) {
	tpl := m.GetPrompt()
	if m.ModelType == ModelTypeMessageNonGeneralKnowledge || m.ModelType == ModelTypeMessage {
		for _, botID := range config.App().Model.WhiteList {
			if botID == m.BotBizID && m.ModelName != config.GetMultiModalComprehensionModelName(m.BotBizID) { // 在白名单中
				tpl = config.App().Model.PromptList[string(m.ModelType)]
			}
		}
	}
	prompt, err := Render(ctx, tpl, req)
	if err != nil {
		return "", err
	}
	if m.PromptLimit == 0 {
		m.PromptLimit = int(m.GetPromptWordsLimit())
	}
	log.InfoContextf(ctx, "RenderPrompt, prompt length: %v, limit: %d", len([]rune(prompt)), m.PromptLimit)
	return TruncatePrompt(ctx, prompt, m.PromptLimit), nil
}

// RenderPromptWithoutTruncate 渲染 Prompt
func (m *AppModel) RenderPromptWithoutTruncate(ctx context.Context, req any) (string, int, error) {
	tpl := m.GetPrompt()
	if m.ModelType == ModelTypeMessageNonGeneralKnowledge || m.ModelType == ModelTypeMessage {
		temp := m.GetPromptByModelName(ctx, m.ModelName, string(m.ModelType))
		if temp != "" {
			tpl = temp
		}

		for _, botID := range config.App().Model.WhiteList {
			if botID == m.BotBizID { // 在白名单中
				tpl = config.App().Model.PromptList[string(m.ModelType)]
			}
		}
	}
	prompt, err := Render(ctx, tpl, req)
	if err != nil {
		return "", 0, err
	}
	if m.PromptLimit == 0 {
		m.PromptLimit = int(m.GetPromptWordsLimit())
	}
	log.InfoContextf(ctx, "RenderPrompt, model name:%s, prompt length: %v, limit: %d", m.GetModelName(),
		len([]rune(prompt)), m.PromptLimit)
	return prompt, m.PromptLimit, nil
}

// IsShenNongModel 是否神农大模型
func (m *AppModel) IsShenNongModel() bool {
	return strings.HasPrefix(m.GetModelName(), "cs-normal")
}

// RenderPrompt 渲染 Prompt 并处理截断
func RenderPrompt(ctx context.Context, promptTmpl string, req any, limit int) (string, error) {
	prompt, err := Render(ctx, promptTmpl, req)
	if err != nil {
		return "", err
	}
	return TruncatePrompt(ctx, prompt, limit), nil
}

// TruncatePrompt 调整 prompt 长度
func TruncatePrompt(ctx context.Context, prompt string, limit int) string {
	runes := []rune(prompt)
	totalLen, limit := len(runes), limit
	start := strings.Index(prompt, TruncStart)
	finish := strings.Index(prompt, TruncFinish)
	if start != -1 && finish != -1 && finish > start {
		body := []rune(prompt[start+TruncStartLen : finish])
		realTotalLen := totalLen - TruncLen
		if limit == 0 || realTotalLen <= limit {
			return prompt[:start] + string(body) + prompt[finish+TruncFinishLen:] // 确保标记去除
		}
		remainLen := limit - (realTotalLen - len(body))
		if remainLen < 0 {
			log.ErrorContextf(ctx, "No len for body, remain: %d, limit: %d, prompt: %s", remainLen, limit, prompt)
			return string(runes[:limit]) // 剩余长度不足，返回直接截断内容
		}
		return prompt[:start] + string(body[:remainLen]) + prompt[finish+TruncFinishLen:]
	}

	if limit == 0 || totalLen <= limit {
		return prompt
	}
	return string(runes[:limit])
}

// TplMedical 医疗Prompt模板
const TplMedical = `
	{{- if gt (len .MultiRoundHistories) 0 -}}
	  根据下面的问诊对话历史，给出医生的下一句回复
	  {{range $i, $el := .MultiRoundHistories -}}
		{{ range $key,$value := . -}}
			{{- if eq $key 0 -}}
				患者: {{ $value }}
			{{ end -}}
			{{- if eq $key 1 -}}
				医生: {{ $value }}
			{{ end -}}
		{{ end -}}
	  {{ end -}}
		患者: {{.Question}}
		答:
	{{- else -}}
	  {{.Question}}
	{{- end -}}
`

// GetPromptByModelName 根据模型名称获取提示
func (m *AppModel) GetPromptByModelName(ctx context.Context, modelName, modelType string) string {
	for _, item := range PromptVersionMap {
		if item.ModelName == modelName && item.ModelType == modelType {
			log.DebugContextf(ctx, "GetPromptByModelName modelName: %s modelType: %s ok.", modelName, modelType)
			return m.GetPrompts()[item.Version]
		}
	}
	return ""
}

// RenderPromptWithoutTruncateTemp 渲染 Prompt for V2.6.1 临时解决
func (m *AppModel) RenderPromptWithoutTruncateTemp(ctx context.Context, req any) (string, int, error) {
	tpl := m.GetPrompt()
	if m.ModelType == ModelTypeMessageNonGeneralKnowledge || m.ModelType == ModelTypeMessage {
		temp := m.GetPromptByModelName(ctx, m.ModelName, string(m.ModelType))
		if temp != "" {
			tpl = temp
		}
		// 删除白名单逻辑
		// for _, botID := range config.App().Model.WhiteList {
		//	if botID == m.BotBizID { // 在白名单中
		//		tpl = config.App().Model.PromptList[string(m.ModelType)]
		//	}
		// }
	}
	prompt, err := Render(ctx, tpl, req)
	if err != nil {
		return "", 0, err
	}
	if m.PromptLimit == 0 {
		m.PromptLimit = int(m.GetPromptWordsLimit())
	}
	log.InfoContextf(ctx, "RenderPrompt, prompt length: %v, limit: %d", len([]rune(prompt)), m.PromptLimit)
	return prompt, m.PromptLimit, nil
}
