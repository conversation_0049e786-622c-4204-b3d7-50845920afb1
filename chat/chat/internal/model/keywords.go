package model

import "time"

// RobotKeywords 应用关键词
type RobotKeywords struct {
	ID           uint64    `db:"id"`           // 主键ID
	RobotID      uint64    `db:"robot_id"`     // 应用ID
	Keywords     string    `db:"keywords"`     // 关键词
	Similarwords string    `db:"similarwords"` // 相似词
	IsDeleted    bool      `db:"is_deleted"`   // 是否删除：0否，1是
	CreateTime   time.Time `db:"create_time"`  // 创建MsgRecordTokenStat时间
	UpdateTime   time.Time `db:"update_time"`  // 更新时间
}
