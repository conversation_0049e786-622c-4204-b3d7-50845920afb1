package model

import (
	"strings"

	jsoniter "github.com/json-iterator/go"
)

// VerticalResult 垂类结果
type VerticalResult struct {
	Vr         bool   `json:"vr"`
	VrCategory string `json:"vr_category"`
	Vrid       string `json:"vrid"`
}

// New 根据字符串创建VerticalResult
func New(pageStr string) *VerticalResult {
	vr := &VerticalResult{}
	_ = jsoniter.Unmarshal([]byte(pageStr), vr)
	return vr
}

// IsVerticalResult 是否是垂类结果
func (v *VerticalResult) IsVerticalResult() bool {
	return v != nil && v.Vr && v.VrCategory != "" && v.Vrid != ""
}

// IsDomesticWeather 是否是国内天气结果
func (v *VerticalResult) IsDomesticWeather() bool {
	return v != nil && v.VrCategory == "weather" && strings.HasPrefix(v.Vrid, "702698")
}

// IsInternationalWeather 是否是国际天气结果
func (v *VerticalResult) IsInternationalWeather() bool {
	return v != nil && v.VrCategory == "weather" && strings.HasPrefix(v.Vrid, "702699")
}

// IsStock 是否是股票结果
func (v *VerticalResult) IsStock() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "702803")
}

// IsMedicalV1 .
func (v *VerticalResult) IsMedicalV1() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "110180")
}

// IsMedicalV2 .
func (v *VerticalResult) IsMedicalV2() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "110209")
}

// IsBaikeV1 .
func (v *VerticalResult) IsBaikeV1() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "300102")
}

// IsBaikeV2 .
func (v *VerticalResult) IsBaikeV2() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "300104")
}

// IsBaikeV3 .
func (v *VerticalResult) IsBaikeV3() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "300100")
}

// IsCalendar .
func (v *VerticalResult) IsCalendar() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "21343201")
}

// IsTrainV1 .
func (v *VerticalResult) IsTrainV1() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "703300")
}

// IsTrainV2 .
func (v *VerticalResult) IsTrainV2() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "700836")
}

// IsTrainV3 .
func (v *VerticalResult) IsTrainV3() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "703301")
}

// IsStarV1 .
func (v *VerticalResult) IsStarV1() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "700050")
}

// IsStarV2 .
func (v *VerticalResult) IsStarV2() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "700198")
}

// IsStarV3 .
func (v *VerticalResult) IsStarV3() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "701305")
}

// IsStarV4 .
func (v *VerticalResult) IsStarV4() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "700196")
}

// IsGoldV1 .
func (v *VerticalResult) IsGoldV1() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "700992")
}

// IsGoldV2 .
func (v *VerticalResult) IsGoldV2() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "702275")
}

// IsGoldV3 .
func (v *VerticalResult) IsGoldV3() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "702276")
}

// IsOil .
func (v *VerticalResult) IsOil() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "700224")
}

// IsPhone .
func (v *VerticalResult) IsPhone() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "700333")
}

// IsCarV1 .
func (v *VerticalResult) IsCarV1() bool {
	return v != nil && (strings.HasPrefix(v.Vrid, "701513") || strings.HasPrefix(v.Vrid, "703535"))
}

// IsCarV2 .
func (v *VerticalResult) IsCarV2() bool {
	return v != nil && (strings.HasPrefix(v.Vrid, "701775") || strings.HasPrefix(v.Vrid, "703536"))
}

// IsExchangerate .
func (v *VerticalResult) IsExchangerate() bool {
	return v != nil && strings.HasPrefix(v.Vrid, "702695")
}
