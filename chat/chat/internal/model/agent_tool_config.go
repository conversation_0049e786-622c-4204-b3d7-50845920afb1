package model

import (
	"database/sql"
	"time"
)

// AgentToolConfig 对应数据库表 agent_tool_config 的映射
type AgentToolConfig struct {
	ID              uint64         `db:"id"`               // 主键，自增
	Category        string         `db:"category"`         // 类别，如 “搜索”、“图片生成”
	PluginName      string         `db:"plugin_name"`      // 插件标识，如 “WebSearch”
	ToolName        string         `db:"tool_name"`        // 工具内部名称
	MappingTemplate string         `db:"mapping_template"` // 文案模板，如 “{工具名称} 正在搜索”
	DisplayType     string         `db:"display_type"`     // 展示类型
	DisplayParam    string         `db:"display_param"`    // 展示参数，如 “Query”
	Example         sql.NullString `db:"example"`          // 示例说明，多行文本，可为 NULL
	CreateTime      time.Time      `db:"create_time"`      // 创建时间
	UpdateTime      time.Time      `db:"update_time"`      // 更新时间，更新时自动修改
}
