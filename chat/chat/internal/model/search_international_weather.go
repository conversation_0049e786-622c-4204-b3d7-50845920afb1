package model

// InternationalWeather 表示国际天气信息的结构体
type InternationalWeather struct {
	Display    IWDisplayInfo `json:"display"`     // 展示相关的信息
	Vr         bool          `json:"vr"`          // 是否为垂直结果
	VrCategory string        `json:"vr_category"` // 垂直结果分类
	Vrid       string        `json:"vrid"`        // 垂直结果ID
}

// IWDisplayInfo 包含展示部分的详细信息
type IWDisplayInfo struct {
	ContentTitle interface{} `json:"ContentTitle"`  // 内容标题
	AbstractInfo interface{} `json:"abstract_info"` // 摘要信息
	Content      interface{} `json:"content"`       // 内容详情
	Date         string      `json:"date"`          // 日期
	Favicon      interface{} `json:"favicon"`       // 网站图标
	PcURL        interface{} `json:"pc_url"`        // PC版链接
	Subitem      SubitemInfo `json:"subitem"`       // 子项信息
	Title        string      `json:"title"`         // 标题
	URL          string      `json:"url"`           // URL链接
}

// SubitemInfo 包含关于子项的详细信息
type SubitemInfo struct {
	Key        string         `json:"key"`        // 子项的关键字
	Subdisplay SubdisplayInfo `json:"subdisplay"` // 子显示信息
}

// SubdisplayInfo 包含具体的日常天气展示信息
type SubdisplayInfo struct {
	Day []DayWeather `json:"day"` // 包含具体每日的天气信息
}

// DayWeather 表示每天的天气信息
type DayWeather struct {
	Date             string `json:"date"`             // 日期
	Daydescription   string `json:"daydescription"`   // 白天描述
	Days             string `json:"days"`             // 白天的日期
	Description      string `json:"description"`      // 总体天气描述
	High             string `json:"high"`             // 最高温度
	Low              string `json:"low"`              // 最低温度
	Month            string `json:"month"`            // 月份
	Nightdescription string `json:"nightdescription"` // 夜间描述
	Week             string `json:"week"`             // 星期几
	Wind             string `json:"wind"`             // 风速
	Wind1            string `json:"wind1"`            // 额外风速描述
}
