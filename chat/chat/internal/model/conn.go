package model

import (
	"regexp"
	"strings"
	"sync/atomic"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/apex/trpcapex"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
)

// ConnType 连接类型
type ConnType uint8

// 连接类型
const (
	ConnTypeSeat       ConnType = 1 // 已废弃
	ConnTypeVisitor    ConnType = 2 // 页面-发布端对话
	ConnTypeExperience ConnType = 3 // 页面-评测端对话
	ConnTypeAPIVisitor ConnType = 5 // API接入-默认都是走发布
	ConnTypeWorkflow   ConnType = 10
)

// ConnSourceMap 连接类型转消息来源
type ConnSourceMap map[ConnType]SourceType

// Conn2Source 连接类型转消息来源
var Conn2Source = ConnSourceMap{
	ConnTypeSeat:       SourceTypeSeat,
	ConnTypeVisitor:    SourceTypeVisitor,
	ConnTypeExperience: SourceTypeVisitor,
	ConnTypeAPIVisitor: SourceTypeAPIVisitor,
}

var labelNameReg = regexp.MustCompile(`^[a-zA-Z]([A-Za-z_]){0,29}$`)

// WsEvent 事件
type WsEvent interface {
	Name() string
}

// WsMessage 消息
type WsMessage struct {
	Event   string `json:"event"`
	Payload any    `json:"payload"`
}

// Label 标签
type Label struct {
	Name   string   `json:"name"`
	Values []string `json:"values"`
}

// IsValid 判断标签是否有效
func (l Label) IsValid() bool {
	if l.Name == "" || len([]rune(l.Name)) > config.GetLabelNameLength() {
		log.Errorf("[param invalid] label name is empty or length out of limit(%d), len:%d",
			config.GetLabelNameLength(), len([]rune(l.Name)))
		return false
	}
	if !labelNameReg.MatchString(l.Name) {
		log.Errorf("[param invalid] label name regex match string failed, name:%s", l.Name)
		return false
	}
	if len(l.Values) == 0 || len(l.Values) > config.GetLabelValueCount() {
		log.Errorf("[param invalid] label values is empty or number out of limit(%d), len:%d",
			config.GetLabelValueCount(), len(l.Values))
		return false
	}
	for _, v := range l.Values {
		if v == "" || len([]rune(v)) > config.GetLabelValueLength() {
			log.Errorf("[param invalid] label value is empty or length out of limit(%d), len:%d",
				config.GetLabelValueLength(), len([]rune(v)))
			return false
		}
	}
	return true
}

// ToVectorLabel 转为检索标签
func (l Label) ToVectorLabel() *knowledge.VectorLabel {
	return &knowledge.VectorLabel{Name: l.Name, Values: l.Values}
}

// Conn 长连接客户端信息 (包括 WS 和 SSE)
type Conn struct {
	Type           ConnType     `json:"type"`
	IsSSE          bool         `json:"is_sse"`
	ClientID       string       `json:"client_id"`
	SessionID      string       `json:"session_id"`
	CorpStaffID    uint64       `json:"corp_staff_id"`
	APIBotBizID    uint64       `json:"api_bot_biz_id"`
	CorpStaffBizID uint64       `json:"corp_staff_biz_id"`
	VisitorLabels  []Label      `json:"visitor_labels"`
	LoginUserType  uint32       `json:"login_user_type"` // 增加当前登录账户类型，0-正常用户；1-体验用户
	refCount       atomic.Int32 `json:"-"`
}

// Ref 增加引用计数
func (c *Conn) Ref() {
	c.refCount.Add(1)
	metrics.ReportSseClientRef()
}

// Deref 减少引用计数
func (c *Conn) Deref() {
	c.refCount.Add(-1)
	metrics.ReportSseClientDeref()
}

// HasRef 判断连接是否还被引用
func (c *Conn) HasRef() bool {
	return c.refCount.Load() > 0
}

// GetSourceType 根据连接类型获取消息来源类型
func (c *Conn) GetSourceType() SourceType {
	return Conn2Source.GetSourceType(c.Type, c.LoginUserType)
}

// IsVisitor 判断是否是访客连接
func (c *Conn) IsVisitor() bool {
	return c.Type == ConnTypeVisitor || c.Type == ConnTypeAPIVisitor
}

// IsClientNotFoundError 判断是否 WS 客户端未找到错误
func IsClientNotFoundError(err error) bool {
	return err != nil && (strings.Contains(errs.Msg(err), "client not found") ||
		// strings.Contains(errs.Msg(err), "connection refused") || // 兼容 Apex 缩容找不到接入节点的情况
		err == trpcapex.ErrClientNotFound)
}

// IsContextCanceled 判断是否取消
func IsContextCanceled(err error) bool {
	return err != nil && strings.Contains(errs.Msg(err), "context canceled")
}

// GetSourceType 获取SourceType
func (s ConnSourceMap) GetSourceType(typ ConnType, userType uint32) SourceType {
	if len(s) == 0 {
		return 0
	}
	// 如果当前登录的是体验用户访客，返回体验用户类型
	if typ == ConnTypeVisitor && userType == LoginUserExpType {
		return SourceTypeExpVisitor
	}
	return s[typ]
}
