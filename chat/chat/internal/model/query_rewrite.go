package model

// RewriteStatus 改写状态
type RewriteStatus int

// 改写状态
const (
	RewriteStatusError    RewriteStatus = -1 // 改写失败
	RewriteStatusSuccess  RewriteStatus = 0  // 改写成功
	RewriteStatusEmpty    RewriteStatus = 1  // 改写结果为空
	RewriteStatusNoChange RewriteStatus = 2  // 没有改写
)

// RewriteType 改写类型
type RewriteType int

// 改写类型
const (
	RewriteTypeNormal       RewriteType = 0 // 普通改写
	RewriteTypeSimpleImage  RewriteType = 1 // 简单图片改写
	RewriteTypeMultiModel   RewriteType = 2 // 多模态改写
	RewriteTypeComplexQuery RewriteType = 3 // 复杂query改写
)

// QueryRewriteReq 改写请求
type QueryRewriteReq struct {
	Model       *AppModel
	Histories   [][2]string
	RewriteType RewriteType
	OriQuery    string
}

// QueryRewriteRsp 改写响应
type QueryRewriteRsp struct {
	Status        RewriteStatus // 改写结果
	OriQuery      string        // 原始query
	RewriteQuery  string        // 改写的query
	ReviseQueries []string      // 复杂query改写后的子query
	Prompt        string        // 改写prompt
	RewriteType   RewriteType   // 改写类型
}
