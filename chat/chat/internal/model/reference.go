package model

import (
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
)

// 参考来源类型
const (
	ReferTypeQA           = 1
	ReferTypeSegment      = 2
	ReferTypeDoc          = 3
	ReferTypeSearchEngine = 4
)

// Reference 参考来源
type Reference struct {
	ID       uint64 `json:"id,string"`
	Type     uint32 `json:"type"`
	URL      string `json:"url"`
	Name     string `json:"name"`
	DocID    uint64 `json:"doc_id,string"`
	DocBizID uint64 `json:"doc_biz_id,string"` // 前端需要biz id用于反馈
	DocName  string `json:"doc_name"`
	QABizID  uint64 `json:"qa_biz_id,string"`
}

// ToPbReference 转为 pb 参考来源
func (r Reference) ToPbReference() *pb.MsgRecord_Reference {
	return &pb.MsgRecord_Reference{Id: r.ID, Type: r.Type, Url: r.URL, Name: r.Name, DocId: r.DocID}
}
