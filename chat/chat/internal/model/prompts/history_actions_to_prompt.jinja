{% for message in messages[-NUM_HISTORY_DIALOGS:] %}
[USER] {{ message.user_query }}
{%- if loop.index > messages|length - NUM_KEEP_LAST_N_ACTIONS %} {# 只保留最近几轮的思考过程 #}
    {%- if message.bot_actions %} {# 判断模型是否已经开始思考 #}
[AI thinking before response, INVISIBLE to USER]
        {%- for bot_action in message.bot_actions %}
            {%- set warning_tag = "" %}
            {%- if loop.index0 > 0 and bot_action.thought == message.bot_actions[loop.index0 - 1].thought and bot_action.action not in ["answer_directly", "ask_user", "task_completed"] %} {# 判断模型是否在重复思考(即当前轮的thought==上一轮的thought), 跳过特殊情况: 思考过程重复, 但当前轮模型选择直接回答 #}
                {%- set warning_tag = WARNING_FOR_REPEATING %}
            {%- endif %}
Thinking step {{ loop.index }}: {{ warning_tag }}Thought: {{ bot_action.thought }}; Action: {{ bot_action.action }}; Action Input: {{ bot_action.action_input }}; Action response: {{ bot_action.observation if bot_action.observation != 'error' else '工具无法调用, 请使用其他工具' }} {# 如果工具无法调用(注意:不是调用失败), 需要设置observation为"error", 并提示模型使用其他工具 #}
        {%- endfor %}
[/AI thinking before response, INVISIBLE to USER]
    {%- endif %}
{%- endif %}
{%- if message.bot_response %} {# 判断模型是否已经回答 #}
[AI] {{ message.bot_response }}
{%- endif %}
{%- endfor -%}