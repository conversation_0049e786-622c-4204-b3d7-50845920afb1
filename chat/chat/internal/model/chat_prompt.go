package model

import "time"

// ChatPrompt 对话中的Prompt
type ChatPrompt struct {
	ID             uint64    `db:"id"`
	BotBizID       uint64    `db:"bot_biz_id"`
	SessionID      string    `db:"session_id"`
	RecordID       string    `db:"record_id"`
	IntentPrompt   string    `db:"intent_prompt"`
	IntentResult   string    `db:"intent_result"`
	WorkflowPrompt string    `db:"workflow_prompt"`
	WorkflowResult string    `db:"workflow_result"`
	CreateTime     time.Time `db:"create_time"`
	UpdateTime     time.Time `db:"update_time"`
}
