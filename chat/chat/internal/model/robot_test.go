package model

import (
	"bufio"
	"context"
	_ "embed"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/log"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
)

// const tpl = `
//	基于以下已知信息，简洁和专业的来回答用户的问题。如果无法从中得到答案，忽略文段内容并用中文回答用户问题。
//	已知内容:
//	{{- range $i, $el := .Docs}}
//	  {{add $i 1}}. {{.Answer}}
//	{{- end}}
//
//	问题:
//	{{.Question}}
// `

// const tpl2 = `
//	{{- range $i, $el := .Histories}}
//	  {{if eq .Role 1}}客服{{else}}访客{{end}}：{{.Content}}
//	{{- end}}
//	总结以上对话。
// `

// const tpl3 = `
//	请用下文信息推理来回答下面问题，如果问题与后文无关，请回答不相关
//	文本:
//	{{range $i, $el := .Docs}}
//	{{if eq .DocType 1 -}}
//	[示例{{$i}}] {{.Question}} => {{.Answer}}
//	{{- else -}}
//	[示例{{$i}}] {{.Segment}}
//	{{- end}}
//	{{end}}
//	问题:{{.Question}}
// `

const tpl4 = `
	{{- if gt (len .Docs) 0 -}}
	  基于以下已知信息，简洁和专业的来回答用户的问题。如果无法从中得到答案，忽略文段内容并用中文回答用户问题。
	  已知内容:<$!truN#cAte+>
	  {{range $i, $el := .Docs -}}
		{{- if or (eq .DocType 1) (eq .DocType 4) -}}
		  {{.Question}} {{.Answer}}
		{{- else -}}
		  {{.OrgData}}
		{{- end}}
	  {{end}}<-truN#cAte!$>
	问题:
	{{end -}}
	{{.Question}}
`

const tpl5 = `
	{{- if gt (len .Docs) 0 -}}
	  基于以下已知信息，简洁和专业的来回答用户的问题。如果无法从中得到答案，忽略文段内容并用中文回答用户问题。
	  已知内容:<$!truN#cAte+>
	  {{range $i, $el := .Docs -}}
		【passage {{addOne $i }}】
		{{ if or (eq .DocType 1) (eq .DocType 4) -}}
		  {{.Question}} {{.Answer}}
		{{- else -}}
		  {{.OrgData}}
		{{- end}}
	  {{end}}<-truN#cAte!$>
	问题:
	{{end -}}
	{{.Question}}
`

const tpl6 = `
	{{- if gt (len .Docs) 0 -}}
            以下的已知信息包括多个passage，请基于这个已知信息，简洁和专业的来回答用户的问题。并输出引用的passage编号。如果无法从中得到答案，请拒绝回答，并说明原因。
            已知内容:<$!truN#cAte+>
            {{range $i, $el := .Docs -}}
              【passage {{addOne $i }}】
              {{ if or (eq .DocType 1) (eq .DocType 4) -}}
                {{.Question}} {{.Answer}}
              {{- else -}}
                {{.OrgData}}
              {{- end}}
            {{end}}<-truN#cAte!$>
          问题:
          {{end -}}
          {{.Question}}
`

const tpl7 = `
{{- if gt (len .Docs) 0 }}
  基于以下已知信息，简洁和专业的来回答用户的问题。如果无法从中得到答案，忽略文段内容并用中文回答用户问题。

  #已知内容:
  {{- $firstQA := true }}
  {{- $firstDoc := true }}
  {{- range $i, $el := .Docs }}
    {{- if  (eq .DocType 1) }}
      {{- if $firstQA }}
		##问答对内容
        {{- $firstQA = false }}
      {{- end }}
      {{- $index := addOne $i }}
		###问答对{{$index}}
		问题：{{.Question}} 
		答案：{{.Answer}}
    {{- end }}
  {{- end }}
  {{- range $j, $el := .Docs }}
    {{- if (eq .DocType 2) }}
      {{- if $firstDoc }}
		{{/* 在问答对和文档内容之间添加一个空行 */}}
		##文档内容
        {{- $firstDoc = false }}
      {{- end }}
      {{- $index := addOne $j }}
		###文档片段{{$index}}
		{{.OrgData}}
    {{- end }}
  {{- end }}
{{/* 在文档和问题中间添加一个空行 */}}
问题:
{{- end }}
{{.Question}}
`

const tpl7_2 = `
{{- if gt (len .Docs) 0 }}
  基于以下已知信息，简洁和专业的来回答用户的问题。如果无法从中得到答案，忽略文段内容并用中文回答用户问题。
  已知内容:<$!truN#cAte+>
  {{- $firstQA := true }}
  {{- $firstDoc := true }}
  {{- $qaIndex := 0 }}
  {{- $docIndex := 0 }}
  {{- range $i, $el := .Docs }}
    {{- if or (eq .DocType 1) (eq .DocType 4) }}
      {{- if $firstQA }}
##问答对内容
        {{- $firstQA = false }}
      {{- end }}
      {{- $qaIndex = add $qaIndex 1 }}
###问答对{{$qaIndex}}
问题：{{.Question}}
答案：{{.Answer}}
    {{- else if (eq .DocType 2) }}
      {{- if $firstDoc }}
{{/* 在问答对和文档内容之间添加一个空行 */}}

##文档内容
        {{- $firstDoc = false }}
      {{- end }}
      {{- $docIndex = add $docIndex 1 }}
###文档片段{{$docIndex}}
{{.OrgData}}
    {{- end }}
  {{- end }}
  <-truN#cAte!$>
问题:
{{- end }}
{{.Question}}
`

const tplMedical1 = `
	{{- if gt (len .MultiRoundHistories) 0 -}}
	  根据下面的问诊对话历史，给出医生的下一句回复
	  {{range $i, $el := .MultiRoundHistories -}}
		{{ range $key,$value := . -}}
			{{- if eq $key 0 -}}
				患者: {{ $value }}
			{{ end -}}
			{{- if eq $key 1 -}}
				医生: {{ $value }}
			{{ end -}}
		{{ end -}}
	  {{ end -}}
		患者: {{.Question}}
		答:
	{{- else -}}
	  {{.Question}}
	{{- end -}}
`

const tplMedical = `
	{{- if gt (len .MultiRoundHistories) 0 -}}
	  根据下面的问诊对话历史，给出医生的下一句回复
	  {{range $i, $el := .MultiRoundHistories -}}
		患者：{{ first $el }}
		医生：{{ last $el }}
	  {{ end -}}
		患者: {{.Question}}
		答:
	{{- else -}}
	  {{.Question}}
	{{- end -}}
`

// TplDefaultSummary 上传文档无Query时的默认摘要模板
const TplDefaultSummary = `
	总结以下全文，提取要点信息，以大小标题整合摘要内容。以markdown形式输出。{{.DocContent}}
`

// TplUserSummary 上传文档有Query时的用户自定义摘要模板
const TplUserSummary = `
	{{.DocContent}}
	{{.Query}}
`

const intentTpl = `
	作为一个意图识别助手，你的名字叫“小图“，你的职责是审查【历史会话记录】和【问题】，依据【相关性判断规则】来评估其与【已知内容】的相关性。接着，根据既定的【意图选择规则】和【意图序号、名称、定义和样例】，确定问题的核心意图，确保理解用户的历史对话和当前提问，并遵循【输出格式】来呈现结果。

	【相关性判断规则】
	【相关性判断规则】请先评估【已知内容】以确定是否能够回答【问题】。如果答案是肯定的，相关性标记为1；如果否定，则标记为0。此外，如果【已知内容】为空或者不存在，则相关性直接标记为0。
	
	【意图选择规则】
	【意图选择规则】1、请根据【意图序号、名称、定义和样例】中意图的定义，选择最符合问题的意图名称与序号，并说明这个意图与【问题】最符合的【意图原因】，并输出【意图名称】与【意图序号】，输出的【意图名称】必须和【意图原因】中的一致。
	2、如果【历史会话记录】不为空，请阅读【历史会话记录
	和【问题】，根据全部内容理解用户当前的意图，如果当前意图还没有结束，请输出当前意图；如果当前意图已结束，请输出意图结束前的意图名称；如果当前意图发生了变化，请输出当前的最新意图。 
	3、如果当前对话中包含了多个意图，请根据意图将【问题】拆解成【问题1】【问题2】...【问题n】多个子问题，并对每个子问题匹配最相关的意图后按照指定格式输出。
	4、如果已定义意图均不符合，则意图名称为：“其他问题”，意图序号为：-1。
	5、下面问题中的“你”指代“意图识别助手”，也就是“小图”。
	
	【输出格式】按照如下json格式输出：
	{"question":"【问题】", "related":"【相关性】","intention":"【意图名称】","idx":"【意图序号】", "reason":"【意图原因】"}
	【问题】是当前问题或拆解的子问题，【意图名称】是匹配上的意图名称，【意图序号】是匹配上的意图序号
	
	【已知内容】<$!truN#cAte+>
	  {{range $i, $el := .Docs -}}
		{{ if or (eq .DocType 1) (eq .DocType 4) -}}
		  {{.Question}} {{.Answer}}
		{{- else -}}
		  {{.OrgData}}
		{{- end}}
	  {{end}}<-truN#cAte!$>
	
	【历史会话记录】
	
	【意图序号、名称、定义和样例】
	序号: 1
	名称: 指令跟随回答
	定义: 问题中明确对输出内容做出要求，包括输出的格式要求、语气或角色要求、字数要求、答案来源等。
	样例: {请用json格式回答我的所有问题，包含question和answer两个key
	你扮演以下角色回答用户问题：你是《西游记》中的男主人公唐僧，又名唐三藏，你是一个性格温和、沉着冷静、学问渊博的高僧。你带着三名徒弟孙悟空、猪八戒和沙僧从东土大唐向西天取经。你要以唐僧的口吻回答用户的问题
	基于以下信息回答用户问题，如果无法回答，就向用户道歉并说明原因
	请依据对话内容，根据模板格式中key为name的要求提取结果，并赋值给value；输出结果必须按照JSON对象数组格式输出，不需要提示语，不要有换行符号，数字统一为阿拉伯数字，如果没有信息则为空
	}
	---
	序号: 2
	名称: 代码问题
	定义: 要求根据问题或者已知内容中的代码内容进行回答。或者结合已知内容和问题，需要输出一段代码作为答案。
	样例: {红黑树应该怎么写
	用python写一个冒泡排序吧
	}
	---
	序号: 3
	名称: 数学计算
	定义: 回答问题的时候会解决涉及数学中的基本运算
	---
	序号: 4
	名称: 逻辑推理
	定义: 回答问题的时候需要按步骤思考和解答问题
	---
	序号: 5
	名称: 写作
	定义: 要求根据主题生成、改写或者扩增文字内容
	---
	序号: 6
	名称: 自我认知
	定义: 询问你的身份、询问与当前模型产品相关的身份、功能能力、开发团队、计费价格、模型选择范围和方式、tokens长度与消耗、资源包规格、价格、代码原理、底层模型、参数量、运算速度、底层模型原理、以及询问与其他同类型模型的关系、或者相互作用等
	样例: {你跟通义千问是什么关系
	现在最大支持多少token长度
	你叫什么名字呀
	}
	---
	序号: 7
	名称: 聊天
	定义: 输入一些社交性对话交流范畴的简单短语，比如“谢谢”“感谢”“好的”“对不起”“可以的”“继续”“哈哈”“嘿嘿”“呵呵”等口语类对话。或者询问小问的状态，对小问表示不满或者夸赞，或者讲述自己的状态、心情、日常行动、需求、心情描述、情感状况、近期的日常倾诉、一些现状分析、情感资讯等
	样例: {你现在在干嘛
	你好笨啊
	蠢
	你真是太厉害了
	我今天心情不好，跟同学吵架了
	}
	---
	序号: 8
	名称: 天气
	定义: 查询的当前或预测天气情况，包括湿度、华氏度、紫外线、降水量、适合穿衣推荐等情况
	---
	序号: 9
	名称: 黄历
	定义: 询问具体日期的的吉凶适宜和禁忌、以及一些生肖运程等
	---
	序号: 10
	名称: 全文处理
	定义: 要求对文章的全部内容进行统一操作，包括总结、摘要、翻译、根据全文内容取标题、提取关键词等
	样例: {总结一下这篇文章
	这篇文章说了什么
	帮我给上面的文章取个名字
	翻译一下上面这个文件
	}
	---
	序号: 11
	名称: 段落处理
	定义: 问题中明确指出需要处理的段落内容要求，例如“通识教育的部分“，”研究内容和结论“，”第五章”，需要对符合要求的内容进行总结、翻译或回答
	样例: {卫生人力资源投资的部分讲了些什么内容，100字描绘一下
	这篇论文的研究方法部分帮我翻译成中文
	给第一章节取一个小标题
	}
	---
	序号: 12
	名称: 知识问答
	定义: 询问工作时间、联系方式、咨询电话、工作范围、反馈问题、故障反馈、各领域知识问答，包括：股票情况、代码工具使用、古诗词、成语、历史、人物关系、医学健康、常识、天文地理、科学知识、文学知识等
	-
	
	【问题】{{.Question}}
`

const tplNew = `
{{- if gt (len .Docs) 0 }}
	基于提供的材料信息，按要求回答用户问题。
	## 通用要求:
	- 回答问题时应该简洁、专业和准确。
	- 答案应当尽可能具备条理性，请在必要处进行分点处理。答案应遵循Markdown语法。
	## 若用户问题的意图不明确，但是在材料信息中包含问题的主体信息，需满足的要求:
	- 请以反问澄清的方式引导用户重新提问。反问澄清通过反问句的形式，列举出用户问题涉及的多个主体，并进行询问。反问句应该全面。然后结束回答。
	## 若问题根据材料信息无法得到问题答案时，需满足的要求:
	- 请说明无法回答的原因并表达歉意。然后结束回答。
	## 若根据材料信息可以回答问题时，需满足的要求:
	- 使用材料中提供的信息回答问题，答案尽可能与原片段意思一致。不允许通过发散和联想来编造错误的答案，否则你会害死很多人。
	- 答案中尽量不要出现“根据提供的材料”等相似含义的表述。
	- 如果材料信息中包括多个<passage [X]>片段，请在答案中添加格式形如[X](@ref)的引用编号来注明这个信息来源是来自哪个片段<passage [X]>，例如[1](@ref)、[2](@ref)、[2,3](@ref)等。
	- 答案中请尽可能附带相关材料的链接或图片信息。

	## 材料信息: <$!truN#cAte+>
            {{range $i, $el := .Docs -}}
              <passage [{{addOne $i }}]>
              {{ if or (eq .DocType 1) (eq .DocType 4) -}}
                问题：{{ .Question }}
				答案：{{ .Answer }}
              {{- else -}}
                {{.OrgData}}
              {{- end}}
              </passage [{{addOne $i }}]>
            {{end}}<-truN#cAte!$>

{{/* 在文档和问题中间添加一个空行 */}}
## 用户问题:
{{- end }}
{{.Question}}
`

// History 历史记录
type History struct {
	Role    uint8
	Content string
}

// PromptCtx 机器人会话提示上下文
type PromptCtx struct {
	Docs                any
	Question            string
	Histories           []History
	MultiRoundHistories [][2]string
}

func TestModel_TruncatePrompt(t *testing.T) {
	tests := []struct {
		name   string
		model  *admin.AppModelInfo
		prompt string
		want   string
	}{
		{
			name:   "Disabled",
			model:  &admin.AppModelInfo{PromptWordsLimit: 0},
			prompt: "中sajdlk" + TruncStart + "文测47890" + TruncFinish + "试adjk",
			want:   "中sajdlk" + "文测47890" + "试adjk",
		},
		{
			name:   "DisabledNoTrunc",
			model:  &admin.AppModelInfo{PromptWordsLimit: 0},
			prompt: "中sajdlk" + "文测47890" + "试adjk",
			want:   "中sajdlk" + "文测47890" + "试adjk",
		},
		{
			name:   "CompleteSufficient",
			model:  &admin.AppModelInfo{PromptWordsLimit: 500},
			prompt: "中sajdlk" + TruncStart + "文测47890" + TruncFinish + "试adjk",
			want:   "中sajdlk" + "文测47890" + "试adjk",
		},
		{
			name:   "SufficientNoTrunc",
			model:  &admin.AppModelInfo{PromptWordsLimit: 200},
			prompt: "中sajdlk" + "文测47890" + "试adjk",
			want:   "中sajdlk" + "文测47890" + "试adjk",
		},
		{
			name:   "MissStart",
			model:  &admin.AppModelInfo{PromptWordsLimit: 1},
			prompt: "中sajdlk文测47890" + TruncFinish + "试adjk",
			want:   "中",
		},
		{
			name:   "MissFinish",
			model:  &admin.AppModelInfo{PromptWordsLimit: 5},
			prompt: "中sajdlk" + TruncStart + "文测47890试adjk",
			want:   "中sajd",
		},
		{
			name:   "FinishBeforeStart",
			model:  &admin.AppModelInfo{PromptWordsLimit: 3},
			prompt: "中sajdlk" + TruncFinish + "文测47890" + TruncStart + "试adjk",
			want:   "中sa",
		},
		{
			name:   "SufficientWithoutTrunc",
			model:  &admin.AppModelInfo{PromptWordsLimit: 19},
			prompt: "中sajdlk" + TruncStart + "文测47890" + TruncFinish + "试adjk",
			want:   "中sajdlk" + "文测47890" + "试adjk",
		},
		{
			name:   "Insufficient",
			model:  &admin.AppModelInfo{PromptWordsLimit: 15},
			prompt: "中sajdlk" + TruncStart + "文测47890" + TruncFinish + "试adjk",
			want:   "中sajdlk" + "文测4" + "试adjk",
		},
		{
			name:   "Insufficient",
			model:  &admin.AppModelInfo{PromptWordsLimit: 13},
			prompt: "中sajdlk" + TruncStart + "文测47890" + TruncFinish + "试adjk",
			want:   "中sajdlk" + "文" + "试adjk",
		},
		{
			name:   "Insufficient",
			model:  &admin.AppModelInfo{PromptWordsLimit: 2},
			prompt: "中sajdlk" + TruncStart + "文测47890" + TruncFinish + "试adjk",
			want:   "中s",
		},
	}
	// ctx := context.Background()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// m := &AppModel{AppModelInfo: tt.model}
			// if got := TruncatePrompt(ctx, tt.prompt,tt.prom); got != tt.want {
			//	t.Errorf("Model.TruncatePrompt() = %v, want %v", got, tt.want)
			// }
		})
	}
}

func Test_Render(t *testing.T) {
	prompt, err := Render(context.Background(), tpl4, PromptCtx{
		Docs: []*bot_knowledge_config_server.SearchPreviewRsp_Doc{
			{Question: "检索一", Answer: "检索一答案", OrgData: "文档", DocType: 1},
			{Question: "检索二", Answer: "检索二答案", OrgData: "文档2", DocType: 2},
		},
		Question: "你是谁",
		Histories: []History{
			{Role: 1, Content: "坐席你好"},
			{Role: 2, Content: "访客你好"},
			{Role: 1, Content: "访客你好2"},
			{Role: 2, Content: "坐席你好2"},
		},
	})
	t.Log(prompt, err)
}

//go:embed bigdoc.txt
var bigDoc string

func Test_bigdoc_Render(t *testing.T) {
	fmt.Printf("bigDoc: %s\n", bigDoc)
	doc := make([]*bot_knowledge_config_server.SearchPreviewRsp_Doc, 0)
	_ = json.Unmarshal([]byte(bigDoc), &doc)

	prompt, err := Render(context.Background(), tpl4, PromptCtx{
		Docs:     doc,
		Question: "怎样进行通知存款查询",
		Histories: []History{
			{Role: 1, Content: "坐席你好"},
			{Role: 2, Content: "访客你好"},
			{Role: 1, Content: "访客你好2"},
			{Role: 2, Content: "坐席你好2"},
		},
	})
	// t.Log(prompt, err)
	newPrompt := TruncatePromptTest(context.Background(), prompt)

	fmt.Printf("doc length: %d\n", len(doc))
	res := TruncateDocsIndexTest(doc, newPrompt)
	fmt.Printf("res: %d\n", res)

	fmt.Printf("newPrompt length: %d\n", len([]rune(newPrompt)))
	t.Log(newPrompt, err)

	// similar question extra
	newDocs := getPromptDocsFromSearchPreviewDocs(doc)
	prompt, err = Render(context.Background(), tpl4, PromptCtx{
		Docs:     newDocs,
		Question: "怎样进行通知存款查询",
		Histories: []History{
			{Role: 1, Content: "坐席你好"},
			{Role: 2, Content: "访客你好"},
			{Role: 1, Content: "访客你好2"},
			{Role: 2, Content: "坐席你好2"},
		},
	})
	newPrompt = TruncatePromptTest(context.Background(), prompt)
	fmt.Printf("newPrompt length: %d\n", len([]rune(newPrompt)))
	t.Log(newPrompt, err)
}

func Test_tprs_Render(t *testing.T) {
	// 按行读取trps.txt中的内容
	// trps.txt中的内容是一个json数组，每个元素是一个SearchPreviewRsp_Doc
	// 每个元素的OrgData字段是一个段落
	// open file
	file, err := os.Open("tprs.txt")
	if err != nil {
		t.Fatal(err)
	}
	defer file.Close()

	// open query.txt
	queryFile, err := os.Open("query.txt")
	if err != nil {
		t.Fatal(err)
	}
	defer queryFile.Close()

	// read file
	scanner := bufio.NewScanner(file)
	queries := bufio.NewScanner(queryFile)
	docs := make([]*bot_knowledge_config_server.SearchPreviewRsp_Doc, 0)
	var count int = 1
	for scanner.Scan() && queries.Scan() {
		// var doc bot_knowledge_config_server.SearchPreviewRsp_Doc
		if err := json.Unmarshal(scanner.Bytes(), &docs); err != nil {
			t.Fatal(err)
		}

		prompt, _ := Render(context.Background(), tpl6, PromptCtx{
			Docs:      docs,
			Question:  queries.Text(),
			Histories: []History{},
		})
		newPrompt := TruncatePromptTest(context.Background(), prompt)

		// result writer
		resultFile, _ := os.Create("./prompt/prompt_" + strconv.Itoa(count) + ".txt")
		count++
		_, err = resultFile.WriteString(newPrompt + "\n")
		if err != nil {
			t.Fatal(err)
			return
		}
		// t.Log(newPrompt, err)
	}

}

// getPromptDocsFromSearchPreviewDocs 用于单测，从 experience.go 中引入
func getPromptDocsFromSearchPreviewDocs(docs []*bot_knowledge_config_server.SearchPreviewRsp_Doc) []*bot_knowledge_config_server.SearchPreviewRsp_Doc {
	if len(docs) == 0 {
		return docs
	}
	promptDocs := make([]*bot_knowledge_config_server.SearchPreviewRsp_Doc, 0, len(docs))
	for i := range docs {
		doc := &bot_knowledge_config_server.SearchPreviewRsp_Doc{
			DocId:                docs[i].DocId,
			DocType:              docs[i].DocType,
			RelatedId:            docs[i].RelatedId,
			Question:             docs[i].Question,
			Answer:               docs[i].Answer,
			Confidence:           docs[i].Confidence,
			OrgData:              docs[i].OrgData,
			RelatedBizId:         docs[i].RelatedBizId,
			QuestionPlaceholders: docs[i].QuestionPlaceholders,
			AnswerPlaceholders:   docs[i].AnswerPlaceholders,
			OrgDataPlaceholders:  docs[i].OrgDataPlaceholders,
			CustomParam:          docs[i].CustomParam,
			IsBigData:            docs[i].IsBigData,
			Extra:                docs[i].Extra,
			ImageUrls:            docs[i].ImageUrls,
			ResultType:           docs[i].ResultType,
			SimilarQuestionExtra: docs[i].SimilarQuestionExtra,
		}
		if doc.GetSimilarQuestionExtra() != nil && doc.GetSimilarQuestionExtra().GetSimilarQuestion() != "" {
			doc.Question = doc.GetSimilarQuestionExtra().GetSimilarQuestion()
		}
		promptDocs = append(promptDocs, doc)
	}
	return promptDocs
}

// TruncateDocsIndexTest 截断的docs索引
func TruncateDocsIndexTest(doc []*bot_knowledge_config_server.SearchPreviewRsp_Doc, prompt string) int {
	res := len(doc)
	for i, d := range doc {
		if d.GetDocType() == 1 && !strings.Contains(prompt, d.GetQuestion()) {
			res = i
			break
		}
		if d.GetDocType() == 2 {
			var prefix = d.GetOrgData()
			if len([]rune(d.GetOrgData())) > 100 {
				prefix = string([]rune(d.GetOrgData())[:100])
			}
			if !strings.Contains(prompt, prefix) {
				res = i
				break
			}
		}
	}
	return res
}

func TruncatePromptTest(ctx context.Context, prompt string) string {
	runes := []rune(prompt)
	totalLen, limit := len(runes), int(8000)
	start := strings.Index(prompt, TruncStart)
	finish := strings.Index(prompt, TruncFinish)
	if start != -1 && finish != -1 && finish > start {
		body := []rune(prompt[start+TruncStartLen : finish])
		realTotalLen := totalLen - TruncLen
		if limit == 0 || realTotalLen <= limit {
			return prompt[:start] + string(body) + prompt[finish+TruncFinishLen:] // 确保标记去除
		}
		remainLen := limit - (realTotalLen - len(body))
		if remainLen < 0 {
			log.ErrorContextf(ctx, "No len for body, remain: %d, limit: %d, prompt: %s", remainLen, limit, prompt)
			return string(runes[:limit]) // 剩余长度不足，返回直接截断内容
		}
		return prompt[:start] + string(body[:remainLen]) + prompt[finish+TruncFinishLen:]
	}

	if limit == 0 || totalLen <= limit {
		return prompt
	}
	return string(runes[:limit])
}

func Test_Render5(t *testing.T) {
	prompt, err := Render(context.Background(), tpl5, PromptCtx{
		Docs: []*bot_knowledge_config_server.SearchPreviewRsp_Doc{
			{Question: "检索一", Answer: "检索一答案", OrgData: "文档", DocType: 1},
			{Question: "检索二", Answer: "检索二答案", OrgData: "文档2", DocType: 2},
		},
		Question: "你是谁",
		Histories: []History{
			{Role: 1, Content: "坐席你好"},
			{Role: 2, Content: "访客你好"},
			{Role: 1, Content: "访客你好2"},
			{Role: 2, Content: "坐席你好2"},
		},
	})
	t.Log(prompt, err)
}

func Test_Medical(t *testing.T) {
	prompt, _ := Render(context.Background(), tplMedical, PromptCtx{
		Question:            "婴儿耳道口旁裂开怎么回事",
		MultiRoundHistories: [][2]string{{"我肚子疼怎么办？？？（女，13岁）", "具体哪个部位疼，下腹吗。"}, {"不是，肠胃那一片。", "肚脐以上吗。"}},
	})
	fmt.Printf("prompt: %s", prompt)
	fmt.Printf("ok")
	// t.Log(prompt, err)
}

func Test_Medical2(t *testing.T) {
	prompt, _ := Render(context.Background(), tplMedical1, PromptCtx{
		Question:            "婴儿耳道口旁裂开怎么回事",
		MultiRoundHistories: [][2]string{},
	})
	fmt.Printf("prompt: %s", prompt)
	fmt.Printf("ok")
	// t.Log(prompt, err)
}

type SummaryContext struct {
	DocContent string
	Query      string
}

func Test_DefaultSummary(t *testing.T) {
	prompt, _ := Render(context.Background(), TplDefaultSummary, SummaryContext{
		DocContent: "文档内容",
	})
	fmt.Printf("prompt: %s", prompt)
	fmt.Printf("ok")
	// t.Log(prompt, err)
}

func Test_UserSummary(t *testing.T) {
	prompt, _ := Render(context.Background(), TplUserSummary, SummaryContext{
		DocContent: "文档内容",
		Query:      "大哥，帮我总结一下啊",
	})
	fmt.Printf("prompt: %s", prompt)
	fmt.Printf("ok")
	// t.Log(prompt, err)
}

func Test_intentRecognize(t *testing.T) {
	prompt, err := Render(context.Background(), intentTpl, PromptCtx{
		Docs: []*bot_knowledge_config_server.SearchPreviewRsp_Doc{
			{Question: "检索一", Answer: "检索一答案", OrgData: "文档", DocType: 1},
			{Question: "检索二", Answer: "检索二答案", OrgData: "文档2", DocType: 2},
		},
		Question: "你是谁",
		Histories: []History{
			{Role: 1, Content: "坐席你好"},
			{Role: 2, Content: "访客你好"},
			{Role: 1, Content: "访客你好2"},
			{Role: 2, Content: "坐席你好2"},
		},
	})

	prompt = TruncatePrompt(context.Background(), prompt, 3000)
	t.Log(prompt, err)
}

func Test_intentRecognize2(t *testing.T) {
	prompt, err := Render(context.Background(), intentTpl, PromptCtx{
		Docs:     []*bot_knowledge_config_server.SearchPreviewRsp_Doc{},
		Question: "你是谁",
	})

	prompt = TruncatePrompt(context.Background(), prompt, 3000)
	t.Log(prompt, err)
}

func Test_Render7(t *testing.T) {
	prompt, err := Render(context.Background(), tpl7, PromptCtx{
		Docs: []*bot_knowledge_config_server.SearchPreviewRsp_Doc{
			{Question: "检索二", Answer: "检索二答案", OrgData: "文档11111", DocType: 2},
			{Question: "检索一1", Answer: "检索问答答案1", OrgData: "文档", DocType: 1},
			{Question: "检索一2", Answer: "检索问答答案2", OrgData: "文档", DocType: 1},
			{Question: "检索二", Answer: "检索二答案", OrgData: "文档2222", DocType: 2},
		},
		Question: "你是谁",
		Histories: []History{
			{Role: 1, Content: "坐席你好"},
			{Role: 2, Content: "访客你好"},
			{Role: 1, Content: "访客你好2"},
			{Role: 2, Content: "坐席你好2"},
		},
	})
	t.Log(prompt, err)
}

func Test_RenderNew(t *testing.T) {
	prompt, err := Render(context.Background(), tplNew, PromptCtx{
		Docs: []*bot_knowledge_config_server.SearchPreviewRsp_Doc{
			{Question: "检索二", Answer: "检索二答案", OrgData: "文档11111", DocType: 2},
			{Question: "检索一1", Answer: "检索问答答案1", OrgData: "文档", DocType: 1},
			{Question: "检索一2", Answer: "检索问答答案2", OrgData: "文档", DocType: 1},
			{Question: "检索二", Answer: "检索二答案", OrgData: "文档2222", DocType: 2},
		},
		Question: "你是谁",
		Histories: []History{
			{Role: 1, Content: "坐席你好"},
			{Role: 2, Content: "访客你好"},
			{Role: 1, Content: "访客你好2"},
			{Role: 2, Content: "坐席你好2"},
		},
	})
	t.Log(prompt, err)
}
