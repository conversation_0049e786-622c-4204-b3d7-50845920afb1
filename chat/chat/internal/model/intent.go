package model

const (
	// DocTypeQA QA
	DocTypeQA = 1
	// DocTypeSegment 文档段
	DocTypeSegment = 2
	// DocTypeRejectedQuestion 拒答问题
	DocTypeRejectedQuestion = 3
	// DocTypeSearchEngine 搜索引擎检索
	DocTypeSearchEngine = 4
)

const (
	// SearchEngineIntent 搜索引擎类
	SearchEngineIntent = "search_engine"
	// SelfAwarenessIntent 自我认知类
	SelfAwarenessIntent = "self_awareness"
	// ModelChatIntent 模型聊天类
	ModelChatIntent = "model_chat"
	// KnowledgeQAIntent 知识问答类
	KnowledgeQAIntent = "knowledge_qa"
	// DocSummaryIntent 文档摘要类，单文档摘要走拒答
	DocSummaryIntent = "doc_summary"
	// MathPOTIntent 数学计算使用
	MathPOTIntent = "数学计算"
	// MathPOTIntentCategory 数学计算分类
	MathPOTIntentCategory = "mathematics_calculate"
	// IntentTypeQAPriority 问答优先
	IntentTypeQAPriority = "qa_priority"
	// IntentTypeCaption 图片识别
	IntentTypeCaption = "image_caption"
	// IntentTypeImageQA 图片问答
	IntentTypeImageQA = "image_qa"
	// IntentTypeMLLM 多模态阅读理解
	IntentTypeMLLM = "mllm_cognition"
)

// 系统意图序号
const (
	// SysIntentDocSummaryIdx 文档摘要序号
	SysIntentDocSummaryIdx = 8
	// SysIntentKnowledgeQAIdx 知识问答序号
	SysIntentKnowledgeQAIdx = 9
)

// 达成方式 Method of achievement
const (
	// IntentTypeDoc 文档问答
	IntentTypeDoc = "文档问答"
	// IntentTypeFAQ FAQ
	IntentTypeFAQ = "FAQ"
	// IntentTypeWorkflow 工作流
	IntentTypeWorkflow = "工作流"
	// IntentTypeCustom 自定义
	IntentTypeCustom = "自定义"
)

var sysIntent = map[string]string{
	SearchEngineIntent:    "系统意图",
	SelfAwarenessIntent:   "系统意图",
	ModelChatIntent:       "系统意图",
	MathPOTIntentCategory: "系统意图",
	DocSummaryIntent:      "系统意图",
	KnowledgeQAIntent:     "系统意图",
}

// CandidateIntent 候选意图
type CandidateIntent struct {
	Index      int
	Name       string
	Def        string
	Example    string
	Type       string
	WorkflowID string // for workflow
	DocID      uint64 // for FAQ
}

// MultiIntentResult 多意图返回结果
type MultiIntentResult struct {
	ResultList  []int    `json:"result_list"` // 模型返回的结果
	FinalList   []int    // 最终意图序号
	OptionCards []string // 多意图下发的选项卡信息
}

// IntentAchieveMethod 意图达成方式
type IntentAchieveMethod struct {
	IntentCate string // 达成方式
	Priority   int    // 优先级
	Index      int    // 意图序号
}

// IntentRsp 意图返回结果
type IntentRsp struct {
	LLMRspStr                string            // 模型返回的结果字符串
	MultiIntent              MultiIntentResult // 模型返回结果结构化
	IntentCate               string            // 意图达成方式
	IntentName               string            // 意图名称
	IsDocPriorityByRecognize bool              // 意图识别结果中是否文档最高优
	CandidateIntent          CandidateIntent   // 命中的意图
	Related                  bool              // 是否文档相关
}

// IsKnowledgeQAIntent 是否是知识问答类意图
func IsKnowledgeQAIntent(intentCate string) bool {
	return intentCate == KnowledgeQAIntent
}

// IsSelfAwarenessIntent 是否是自我认知类意图
func IsSelfAwarenessIntent(intentCate string) bool {
	return intentCate == SelfAwarenessIntent
}

// IsModelChatIntent 是否是模型聊天类意图
func IsModelChatIntent(intentCate string) bool {
	return intentCate == ModelChatIntent
}

// GetRealCategory 获取实际的意图类别
func GetRealCategory(intentCate string) string {
	if cate, ok := sysIntent[intentCate]; ok {
		return cate
	}
	return intentCate
}
