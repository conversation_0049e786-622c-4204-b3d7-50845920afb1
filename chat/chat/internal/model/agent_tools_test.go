package model

import (
	"testing"

	"git.woa.com/dialogue-platform/proto/pb-stub/openapi"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	jsoniter "github.com/json-iterator/go"
	"github.com/stretchr/testify/require"
)

func TestGetDefaultTools(t *testing.T) {
	// 按照【图1】【图2】这种格式提取图片描述
	t.Run("test get default tools", func(t *testing.T) {
		res := GetDefaultTools()
		t.Logf("GetAskUserTool tools is:  %s\n", helper.Object2String(res[0]))
		require.EqualValues(t, &openapi.Tool{
			Type: "function",
			Function: &openapi.Function{
				Name:        "ask_user",
				Description: "Ask user for missing information",
				Parameters: &openapi.Definition{
					Type:        "object",
					Description: "",
					Enum:        nil,
					Properties: map[string]*openapi.Definition{
						"question": &openapi.Definition{
							Type: "string",
							Description: "the question or missing information you want to ask the user, " +
								"DO NOT ask the same question as the user's question",
						},
					},
					Required: []string{"question"},
				},
			},
		}, res[0])
		t.Logf("GetAnswerDirectlyTool tools is:  %s\n", helper.Object2String(res[1]))
		t.Logf("GetTaskCompletedTool tools is:  %s\n", helper.Object2String(res[2]))
	})
}

func TestToolRegexp(t *testing.T) {

	askUserCases := []struct {
		input    string
		expected string
	}{
		{
			input:    "{\"question\": \"你想问的是腾讯公司CEO？\"}",
			expected: "你想问的是腾讯公司CEO？",
		},
	}

	t.Run("test MatchAskUserReply regexp", func(t *testing.T) {
		for _, testCase := range askUserCases {
			// 匹配 question 的内容
			matchQuestion := MatchAskUserReply(testCase.input)
			t.Logf("matchQuestion is:  %s\n", matchQuestion)
			require.EqualValues(t, testCase.expected, matchQuestion)
		}
	})

	answerDirectlyCases := []struct {
		input    string
		expected string
	}{
		{
			input:    "{\"answer\": \"腾讯公司CEO是麻花腾\"}",
			expected: "腾讯公司CEO是麻花腾",
		},
	}

	t.Run("test MatchAnswerDirectlyReply regexp", func(t *testing.T) {
		for _, testCase := range answerDirectlyCases {
			// 匹配 answer 的内容
			matchAnswer := MatchAnswerDirectlyReply(testCase.input)
			t.Logf("matchAnswer is:  %s\n", matchAnswer)
			require.EqualValues(t, testCase.expected, matchAnswer)
		}
	})

	taskCompletedReplyCases := []struct {
		input    string
		expected string
	}{
		{
			input:    "{\n\"final_answer\": \"阿里巴巴CEO是吴妈\"\n}",
			expected: "阿里巴巴CEO是吴妈",
		},
		{
			input:    "{\n\"final_answer\": \"阿里巴巴",
			expected: "阿里巴巴",
		},
		{
			input:    "{\n\"final_answer\": \"阿里巴巴CEO是\\\"吴妈\\\"吴永明\"\n}",
			expected: "阿里巴巴CEO是\\\"吴妈\\\"吴永明",
		},
		{
			input:    "{\"final_answer\": \"1. 杭州未来一周的天气预报如下：\\n- 2025-01-21：晴（白天），气温12.67°C，湿度47%，AQI 92，最高气温17°C，最低气温8.33°C，风速6.98m/s\\n- 2025-01-22：多云（白天），气温13.06°C，湿度42%，AQI 74，最高气温19°C，最低气温7°C，风速5.97m/s\\n- 2025-01-23：阴，气温13.28°C，湿度54%，AQI 102，最高气温18°C，最低气温8°C，风速8.36m/s\\n- 2025-01-24：阴，气温12.41°C，湿度80%，AQI 67，最高气温16°C，最低气温10.55°C，风速14.47m/s\\n- 2025-01-25：小雨，气温10.03°C，湿度76%，AQI 48，最高气温14°C，最低气温8.36°C，风速13.13m/s\\n- 2025-01-26：小雨，气温6.08°C，湿度78%，AQI 42，最高气温10°C，最低气温1.8°C，风速15.05m/s\\n- 2025-01-27：阴，气温2.89°C，湿度39%，AQI 48，最高气温6°C，最低气温0°C，风速14.76m/s\\n\\n2. 杭州未来一周的气温折线图如下：\\n![](https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/code-interpreter/testing/file/image_2a26c436-d70",
			expected: "1. 杭州未来一周的天气预报如下：\\n- 2025-01-21：晴（白天），气温12.67°C，湿度47%，AQI 92，最高气温17°C，最低气温8.33°C，风速6.98m/s\\n- 2025-01-22：多云（白天），气温13.06°C，湿度42%，AQI 74，最高气温19°C，最低气温7°C，风速5.97m/s\\n- 2025-01-23：阴，气温13.28°C，湿度54%，AQI 102，最高气温18°C，最低气温8°C，风速8.36m/s\\n- 2025-01-24：阴，气温12.41°C，湿度80%，AQI 67，最高气温16°C，最低气温10.55°C，风速14.47m/s\\n- 2025-01-25：小雨，气温10.03°C，湿度76%，AQI 48，最高气温14°C，最低气温8.36°C，风速13.13m/s\\n- 2025-01-26：小雨，气温6.08°C，湿度78%，AQI 42，最高气温10°C，最低气温1.8°C，风速15.05m/s\\n- 2025-01-27：阴，气温2.89°C，湿度39%，AQI 48，最高气温6°C，最低气温0°C，风速14.76m/s\\n\\n2. 杭州未来一周的气温折线图如下：\\n![](https://lke-realtime-test-1251316161.cos.ap-guangzhou.myqcloud.com/code-interpreter/testing/file/image_2a26c436-d70",
		},
		{
			input:    "{\"final_answer\": \"以下是用柱状图展示的中国历届奥运会奖牌数数据：\\n图中展示了从1984年到2021年历届奥运会中国获得的金牌、银牌和铜牌数量。每个年份的柱状图分为三层，分别代表金牌、银牌和铜牌的数量。希望这个图表对您有所帮助！如果您有其他问题或需要进一步的帮助，请随时告诉我。\" }",
			expected: "以下是用柱状图展示的中国历届奥运会奖牌数数据：\\n图中展示了从1984年到2021年历届奥运会中国获得的金牌、银牌和铜牌数量。每个年份的柱状图分为三层，分别代表金牌、银牌和铜牌的数量。希望这个图表对您有所帮助！如果您有其他问题或需要进一步的帮助，请随时告诉我。",
		},
		{
			input:    "{\"final_answer\":\"根据上海未来五天的天气预报，以下是每天的天气状况和建议：\n\n- **4月1日（周二）**：多云，最高温16.18℃，最低7.59℃，湿度45%，风速8.23，AQI 37。天气不错，但温度稍低，适合户外活动，但需注意保暖。\n- **4月2日（周三）**：晴，最高温22.49℃，最低7.49℃，湿度50%，风速13.09，AQI 38。白天晴朗，温度较高，但风速较大，可能对户外活动有些影响。\n- **4月3日（周四）**：晴，最高温18.19℃，最低8.49℃，湿度49%，风速8.19，AQI 33。天气晴朗，温度适中，风速较低，空气质量好，**最适合出游**。\n- **4月4日（周五）**：多云，最高温19.99℃，最低7.99℃，湿度51%，风速12.74，AQI 33。多云天气，温度适宜，风速稍高，但整体适合出行。\n- **4月5日（周六）**：阴，最高温24.79℃，最低11.89℃，湿度48%，风速12.08，AQI 38。阴天，温度较高，但空气质量稍差，可能不太理想。\n\n综合来看，**4月3日**和**4月4日**天气较为适宜，尤其是**4月3日**，晴朗且风速低，空气质量最佳，是最佳的出游日期。以下是未来五天最高温和最低温的变化折线图：\n\n希望这些信息对您的出游计划有所帮助！\"}",
			expected: "根据上海未来五天的天气预报，以下是每天的天气状况和建议：\n\n- **4月1日（周二）**：多云，最高温16.18℃，最低7.59℃，湿度45%，风速8.23，AQI 37。天气不错，但温度稍低，适合户外活动，但需注意保暖。\n- **4月2日（周三）**：晴，最高温22.49℃，最低7.49℃，湿度50%，风速13.09，AQI 38。白天晴朗，温度较高，但风速较大，可能对户外活动有些影响。\n- **4月3日（周四）**：晴，最高温18.19℃，最低8.49℃，湿度49%，风速8.19，AQI 33。天气晴朗，温度适中，风速较低，空气质量好，**最适合出游**。\n- **4月4日（周五）**：多云，最高温19.99℃，最低7.99℃，湿度51%，风速12.74，AQI 33。多云天气，温度适宜，风速稍高，但整体适合出行。\n- **4月5日（周六）**：阴，最高温24.79℃，最低11.89℃，湿度48%，风速12.08，AQI 38。阴天，温度较高，但空气质量稍差，可能不太理想。\n\n综合来看，**4月3日**和**4月4日**天气较为适宜，尤其是**4月3日**，晴朗且风速低，空气质量最佳，是最佳的出游日期。以下是未来五天最高温和最低温的变化折线图：\n\n希望这些信息对您的出游计划有所帮助！",
		},
	}

	t.Run("test MatchTaskCompletedReply regexp", func(t *testing.T) {
		for _, testCase := range taskCompletedReplyCases {
			// 匹配 final_answer 的内容
			matchFinalAnswer := MatchTaskCompletedReply(testCase.input)
			t.Logf("matchFinalAnswer is:  %s\n", helper.Object2StringEscapeHTML(matchFinalAnswer))
			require.EqualValues(t, testCase.expected, matchFinalAnswer)
		}
	})

	responseToUserReplyCases := []struct {
		input    string
		expected string
	}{
		{
			input:    "{\"content\":\"好的，已为您成功挂了北京中医药大学东直门医院 营养科 3月9日的号源，稍后也会短信发送到您的来电号码，感谢您的来电，再见。\",\"response_type\":\"本医院挂号成功\"}",
			expected: "好的，已为您成功挂了北京中医药大学东直门医院 营养科 3月9日的号源，稍后也会短信发送到您的来电号码，感谢您的来电，再见。",
		},
		{
			input:    "{\"content\":\"好的，已为您成功挂了北京中医药大学东直门医院 营养科 3月9日的号源，稍后也会短信",
			expected: "好的，已为您成功挂了北京中医药大学东直门医院 营养科 3月9日的号源，稍后也会短信",
		},
		{
			input:    "{\"content\":\"好的，已为您成功挂了北京中医药大学东直门医院 营养科 3月9日的号源，稍后也会短信发送到您的来电号码，感谢您的来电，再见。\"}",
			expected: "好的，已为您成功挂了北京中医药大学东直门医院 营养科 3月9日的号源，稍后也会短信发送到您的来电号码，感谢您的来电，再见。",
		},
		{
			input:    "{\"content\": \"33330\", \"response_type\": \"回复1\"}",
			expected: "33330",
		},
		{
			input:    "{\"content\":\"33330是\\\"黑鹰\\\"的代号\",\"response_type\":\"回复1\"}",
			expected: "33330是\\\"黑鹰\\\"的代号",
		},
	}

	t.Run("test tool regexp", func(t *testing.T) {
		for _, testCase := range responseToUserReplyCases {
			// 匹配 content 的内容
			matchFinalAnswer := MatchResponseToUserReply(testCase.input)
			t.Logf("matchFinalAnswer is:  %s\n", matchFinalAnswer)
			require.EqualValues(t, testCase.expected, matchFinalAnswer)
		}
	})
}

var knowledgeResponse = "{\"Code\":0,\"Msg\":\"success\",\"Data\":{\"Answer\":\"打开车窗的方法如下：\\n\\n1. 按下对应的车窗开关。\\n2. 在一键升降系统中，按下对应的一键下降按钮。\\n\\n请注意，在寒冷潮湿的环境里，电动车窗可能会由于冻结而不能正常工作。此外，为延长保险丝的使用寿命，预防电动车窗系统受损，请勿同时操作两个及以上的车窗。\\n\\n\",\"References\":[{\"DocID\":3168,\"ID\":1874411483876521024,\"Name\":\"UNI-T使用说明书.docx\",\"Type\":2,\"Url\":\"\",\"DocBizID\":1874407935210124352,\"DocName\":\"UNI-T使用说明书.docx\",\"QABizID\":0}]}}"

func TestSpecialDisplayTools(t *testing.T) {
	t.Run("test special display tools", func(t *testing.T) {
		response := &KnowledgeResponse{}
		err := jsoniter.Unmarshal([]byte(knowledgeResponse), response)
		t.Logf("response is:  %s\n", helper.Object2String(response))
		require.NoError(t, err)
	})
}

func TestGetQbTools(t *testing.T) {
	t.Run("test get qb tools", func(t *testing.T) {
		res := GetQbTools()
		t.Logf("res count is:  %d\n", len(res))
		t.Logf("GetQbTools tools is:  %s\n", helper.Object2String(res[0]))
		// require.EqualValues(t, &openapi.Tool{
		//	Type: "function",
		//	Function: &openapi.Function{
		//		Name:        "qb_tool",
		//		Description: "QQ浏览器工具",
		//		Parameters: &openapi.Definition{
		//			Type:        "object",
		//			Description: "",
		//			Enum:        nil,
		//			Properties: map[string]*openapi.Definition{
		//				"question": &openapi.Definition{
		//					Type:        "string",
		//					Description: "问题描述",
		//				},
		//			},
		//			Required: []string{"question"},
		//		},
		//	},
		// }, res[0])
	})
}
