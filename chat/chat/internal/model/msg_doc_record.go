package model

import "time"

// MsgDocRecord 消息文档记录
type MsgDocRecord struct {
	ID         uint64    `db:"id"`
	BotBizID   uint64    `db:"bot_biz_id"`
	RecordID   string    `db:"record_id"`
	SessionID  string    `db:"session_id"`
	FileURL    string    `db:"file_url"`
	DocID      string    `db:"doc_id"`
	CosID      uint64    `db:"cos_id"`
	DocName    string    `db:"doc_name"`
	ModelName  string    `db:"model_name"`
	Summary    string    `db:"summary"`
	IsDeleted  bool      `db:"is_deleted"`
	CreateTime time.Time `db:"create_time"`
	UpdateTime time.Time `db:"update_time"`
}
