package model

// SearchProRequestBody 搜索引擎请求体
type SearchProRequestBody struct {
	Query string `json:"Query"`
	Mode  int    `json:"Mode"`
}

// SearchProResponseBody .
type SearchProResponseBody struct {
	SearchProResponse `json:"Response"`
}

// SearchProResponse .
type SearchProResponse struct {
	Pages []string `json:"Pages"`
}

// SearchResultPage .
type SearchResultPage struct {
	Passage string  `json:"passage"`
	Score   float64 `json:"score"`
	URL     string  `json:"url"`
	Title   string  `json:"title"`
	Date    string  `json:"date"`
}

// SearchProSummaryPromptCtx .
type SearchProSummaryPromptCtx struct {
	CurQuestion  string
	SearchResult []RefDoc
	RepQuestion  string
	CurDate      string
}

// RefDoc .
type RefDoc struct {
	Num    int
	Title  string
	Date   string
	RefDoc string
}
