// chat
//
// @(#)prompt_summary_test.go  Monday, March 04, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package model

import (
	"context"
	"testing"

	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

const s1 = `根据下面对话，进行摘要
{{.SummaryRequireCommand}}
{{.Question}}
`

// nil
func TestSummaryPrompt1(t *testing.T) {
	var p botsession.PromptCtx
	r, err := model.Render(context.Background(), s1, p)
	t.Log(r, err)
}

// empty
func TestSummaryPrompt2(t *testing.T) {
	p := botsession.PromptCtx{}
	r, err := model.Render(context.Background(), s1, p)
	t.Log(r, err)
}

// 1 行数据
func TestSummaryPrompt3(t *testing.T) {
	p := botsession.PromptCtx{
		Question: "用户输入和摘要内容",
	}
	r, err := model.Render(context.Background(), s1, p)
	t.Log(r, err)
}

// 2 行数据
func TestSummaryPrompt4(t *testing.T) {
	p := botsession.PromptCtx{
		Question:              "用户输入和摘要内容",
		SummaryRequireCommand: "开发环境现在可以用不",
	}
	r, err := model.Render(context.Background(), s1, p)
	t.Log(r, err)
}

// 多行数据
func TestSummaryPrompt5(t *testing.T) {

}
