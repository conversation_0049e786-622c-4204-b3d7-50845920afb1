// chat
//
// @(#)prompt_tag_test.go  Monday, March 04, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package model

import (
	"context"
	"testing"

	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// 你是一个人工智能专家，尤其擅长进行多轮对话分析。分析"对话内容"，提取对话中的标签信息。
// # 待抽取标签及标签说明：
// 1. 标签1名称
// 1.1 标签描述:xxx（可选）
// 1.2 标签取值范围：XXX,XXX(可选)
// 2. 标签2名称
// 2.1 标签描述:xxx（可选）
// 2.2 标签取值范围：XXX,XXX(可选)
// ...
//
// 分析结果以json格式返回，结果格式示例:{"标签1名称":"xxx","标签n名称":"xxx",....}, 未提及的标签无需返回
//
// # 任务示例(可选)：
// 1. 对话内容：XXXX
// 2. 抽取结果：{"xxx":"xxx","xxx":"xxx"}
//
// # 对话内容
// XXXX
// XXXX
//
// # 答案:

const te1 = `你是一个人工智能专家，尤其擅长进行多轮对话分析。分析"对话内容"，提取对话中的标签信息。
# 待抽取标签及标签说明：
{{range $i1, $e1 := .Tags -}}
{{add1 $i1}}. {{$e1.Name}}
  {{add1 $i1}}.1 标签描述: {{$e1.Description}}
  {{add1 $i1}}.2 标签取值范围: {{join "," $e1.Values}}
{{end}}

分析结果以json格式返回，结果格式示例:{
{{- range $i, $e := .Tags -}}
  "{{.Name}}":["{{join "\",\"" .Values}}"]
  {{- if ne (add1 $i) (len $.Tags)}},{{end -}}
{{- end -}}
}, 未提及的标签无需返回

# 任务示例
用户：帮我查一下11月份的信用卡账单是多少
客服：11月份信用卡账单总金额是7890元。
用户：最迟还款时间是什么时候？
客服：11月份信用卡账单最迟还款时间是11月10日。
用户：可以分期么？
客服：是的，11月份的信用卡账单可以分期。根据信用卡账单分期的信息，11月份的账单总金额为7890元，最低还款金额为789元。您可以选择3期、6期或12期的分期付款方式。
用户：好的
客服：请问您还有是否还要咨询其他问题呢？
用户：没有，可以了

业务类型：信用卡
语境业务：账单分期
客户情绪：未知

# 对话内容
{{.Question}}

# 答案:`

// nil
func TestTagExtractionPrompt1(t *testing.T) {
	p := botsession.PromptCtx{
		Question: "对话内容对话内容对话内容对话内容对话内容",
	}
	r, err := model.Render(context.Background(), te1, p)
	t.Log(r, err)
}

// empty
func TestTagExtractionPrompt2(t *testing.T) {
	p := botsession.PromptCtx{
		Question: "QuestionQuestionQuestionQuestionQuestion",
	}
	tags := []*admin.ClassifyLabel{}
	p.Tags = tags
	r, err := model.Render(context.Background(), te1, p)
	t.Log(r, err)
}

// 1 行数据
func TestTagExtractionPrompt3(t *testing.T) {
	p := botsession.PromptCtx{}
	tags := []*admin.ClassifyLabel{
		{Name: "城市", Description: "城市名称", Values: []string{"深圳", "北京", "东京", "南京"}},
	}
	p.Tags = tags
	r, err := model.Render(context.Background(), te1, p)
	t.Log(r, err)
}

// 2 行数据
func TestTagExtractionPrompt4(t *testing.T) {
	p := botsession.PromptCtx{
		Question: "QuestionQuestionQuestionQuestionQuestion",
	}
	tags := []*admin.ClassifyLabel{
		{Name: "城市", Description: "城市名称", Values: []string{"深圳", "北京", "东京", "南京"}},
		{Name: "作家", Description: "知名人物", Values: []string{"金庸", "黄易", "鲁迅", "刘慈欣"}},
	}
	p.Tags = tags
	r, err := model.Render(context.Background(), te1, p)
	t.Log(r, err)
}

// 多行数据
func TestTagExtractionPrompt5(t *testing.T) {
	p := botsession.PromptCtx{}
	tags := []*admin.ClassifyLabel{
		{Name: "城市", Description: "城市名称", Values: []string{"深圳", "北京", "东京", "南京"}},
		{Name: "作家", Description: "知名人物", Values: []string{"金庸", "黄易", "鲁迅", "刘慈欣"}},
		{Name: "生活用品", Values: []string{"水杯"}},
		{Name: "乐器", Description: "吹拉弹唱"},
		{Name: "毛绒玩具"},
	}
	p.Tags = tags
	r, err := model.Render(context.Background(), te1, p)
	t.Log(r, err)
}
