// chat
//
// @(#)prompt_clarify_test.go  Sunday, April 28, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package model

import (
	"context"
	"testing"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// 正常
const p1 = `{{- if .UseQuestionClarify}}
	根据以下已知信息，回答用户问题。要求：
	{{if gt (len .QuestionClarifyKeywords) 0 -}}	
		1.如果用户问题不明确，涉及多个
		{{- range $i, $e := .QuestionClarifyKeywords -}}
		  {{$e}}{{- if ne (add1 $i) (len $.QuestionClarifyKeywords)}}、{{end -}}
		{{- end -}}
		，且已知内容中包含这些
		{{- range $i, $e := .QuestionClarifyKeywords -}}
		  {{$e}}{{- if ne (add1 $i) (len $.QuestionClarifyKeywords)}}、{{end -}}
		{{- end -}}
		的信息，需要反问澄清。反问澄清通过反问句的形式，列举出用户问题涉及的多个
		{{- range $i, $e := .QuestionClarifyKeywords -}}
		  {{$e}}{{- if ne (add1 $i) (len $.QuestionClarifyKeywords)}}、{{end -}}
		{{- end -}}
		，并进行询问。反问句应该全面。
		2.如果用户问题有明确的
		{{- range $i, $e := .QuestionClarifyKeywords -}}
		  {{$e}}{{- if ne (add1 $i) (len $.QuestionClarifyKeywords)}}、{{end -}}
		{{- end -}}
		信息，需要基于已知内容，简洁和专业地回答用户的问题。
		3.如果无法从已知内容中得到答案，忽略已知内容并用中文回答用户问题。
	{{else}}
		1.如果用户问题不明确，涉及多个主体，且已知内容中包含这些主体的信息，需要反问澄清。反问澄清通过反问句的形式，列举出用户问题涉及的多个主体，并进行询问。反问句应该全面。
		2.如果用户问题明确是对某一主体进行询问，需要基于已知内容，简洁和专业地回答用户的问题。
		3.如果无法从已知内容中得到答案，忽略已知内容并用中文回答用户问题。
	{{end}}
{{- else}}
	{{- if and .Docs (gt (len .Docs) 0) -}}
	基于以下已知信息，简洁和专业的来回答用户的问题。如果无法从中得到答案，忽略文段内容并用中文回答用户问题。
	{{end -}}
{{end}}
{{- if and .Docs (gt (len .Docs) 0) -}}
	已知内容:<$!truN#cAte+>
	{{range $i, $el := .Docs -}}
	  {{- if or (eq .DocType 1) (eq .DocType 4) -}}
		{{.Question}} {{.Answer}}
	  {{- else -}}
		{{.OrgData}}
	  {{- end}}
	{{end}}<-truN#cAte!$>
	问题:
{{else -}}
	{{- if .UseQuestionClarify}}
		问题:
	{{end -}}
{{end -}}
{{.Question}}`

// 拒答
const p2 = `{{- if .UseQuestionClarify -}}
	根据以下已知信息，回答用户问题。要求：
	{{if gt (len .QuestionClarifyKeywords) 0 -}}
		1.如果用户问题不明确，涉及多个
		{{- range $i, $e := .QuestionClarifyKeywords -}}
		  {{$e}}{{- if ne (add1 $i) (len $.QuestionClarifyKeywords)}}、{{end -}}
		{{- end -}}
		，且已知内容中包含这些
		{{- range $i, $e := .QuestionClarifyKeywords -}}
		  {{$e}}{{- if ne (add1 $i) (len $.QuestionClarifyKeywords)}}、{{end -}}
		{{- end -}}
		的信息，需要反问澄清。反问澄清通过反问句的形式，列举出用户问题涉及的多个
		{{- range $i, $e := .QuestionClarifyKeywords -}}
		  {{$e}}{{- if ne (add1 $i) (len $.QuestionClarifyKeywords)}}、{{end -}}
		{{- end -}}
		，并进行询问。反问句应该全面。
		2.如果用户问题有明确的
		{{- range $i, $e := .QuestionClarifyKeywords -}}
		  {{$e}}{{- if ne (add1 $i) (len $.QuestionClarifyKeywords)}}、{{end -}}
		{{- end -}}
		信息，需要基于已知内容，简洁和专业地回答用户的问题。
	{{else}}
		1.如果用户问题不明确，涉及多个主体，且已知内容中包含这些主体的信息，需要反问澄清。反问澄清通过反问句的形式，列举出用户问题涉及的多个主体，并进行询问。反问句应该全面。
		2.如果用户问题明确是对某一主体进行询问，需要基于已知内容，简洁和专业地回答用户的问题。
	{{end -}}
	3.如果无法从已知内容中得到答案，则直接回复：”对不起，已知信息和问题无关“，并说明原因。
{{else}}
	{{- if and .Docs (gt (len .Docs) 0) -}}
	基于以下已知信息，简洁和专业的来回答用户的问题。如果无法从中得到答案，则直接回复对不起已知信息和问题无关，并说明原因。
	{{- end}}
{{- end}}     
{{- if and .Docs (gt (len .Docs) 0) -}}
	已知内容:<$!truN#cAte+>
	{{range $i, $el := .Docs -}}
	  {{- if or (eq .DocType 1) (eq .DocType 4) -}}
	    {{.Question}} {{.Answer}}
	  {{- else -}}
	    {{.OrgData}}
	  {{- end}}
	{{end}}<-truN#cAte!$>
	问题:
{{else -}}
	{{- if .UseQuestionClarify}}
		问题:
	{{end -}}
{{end -}}
{{.Question}}`

func TestClarifyP11(t *testing.T) {
	p := botsession.PromptCtx{}
	r, err := model.Render(context.Background(), p1, p)
	t.Log(r, err)
}

func TestClarifyP12(t *testing.T) {
	p := botsession.PromptCtx{
		Docs:                    []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc{},
		QuestionClarifyKeywords: []string{},
	}
	r, err := model.Render(context.Background(), p1, p)
	t.Log(r, err)
}

func TestClarifyP13(t *testing.T) {
	p := botsession.PromptCtx{
		Docs:                    []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc{},
		QuestionClarifyKeywords: []string{"北京"},
	}
	r, err := model.Render(context.Background(), p1, p)
	t.Log(r, err)
}

func TestClarifyP14(t *testing.T) {
	p := botsession.PromptCtx{
		Docs:                    []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc{},
		UseQuestionClarify:      true,
		QuestionClarifyKeywords: []string{"深圳", "北京", "东京", "南京"},
		Question:                "这是一个问题",
	}
	r, err := model.Render(context.Background(), p1, p)
	t.Log(r, err)
}

func TestClarifyP21(t *testing.T) {
	p := botsession.BotSession{}
	r, err := model.Render(context.Background(), p2, p)
	t.Log(r, err)
}

func TestClarifyP22(t *testing.T) {
	p := botsession.PromptCtx{
		Docs:                    []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc{},
		QuestionClarifyKeywords: []string{},
	}
	r, err := model.Render(context.Background(), p2, p)
	t.Log(r, err)
}

func TestClarifyP23(t *testing.T) {
	p := botsession.PromptCtx{
		Docs:                    []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc{},
		QuestionClarifyKeywords: []string{"北京"},
	}
	r, err := model.Render(context.Background(), p2, p)
	t.Log(r, err)
}

func TestClarifyP24(t *testing.T) {
	p := botsession.PromptCtx{
		// Docs:                    []event.Knowledge{&bot_knowledge_config_server.SearchPreviewRsp_Doc{}},
		//	UseQuestionClarify:      true,
		QuestionClarifyKeywords: []string{"深圳", "北京", "东京", "南京"},
		Question:                "这是一个问题",
	}
	r, err := model.Render(context.Background(), p2, p)
	t.Log(r, err)
}
