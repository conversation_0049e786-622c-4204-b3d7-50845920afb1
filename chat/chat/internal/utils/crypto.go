package utils

import (
	"bytes"
	"crypto/aes"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"fmt"
	"net/url"
)

// HmacSha1Base64Encode returns the hmac-sha1 url encode base64 string of data.
func HmacSha1Base64Encode(key, data string) string {
	return url.QueryEscape(HmacSha1Base64(key, data))
}

// HmacSha1Base64 returns the hmac-sha1 base64 string of data.
func HmacSha1Base64(key, data string) string {
	return base64.StdEncoding.EncodeToString(HmacSha1(key, data))
}

// HmacSha1 returns the hmac-sha1 bytes of data.
func HmacSha1(key, data string) []byte {
	harsher := hmac.New(sha1.New, []byte(key))
	harsher.Write([]byte(data))
	return harsher.Sum(nil)
}

// AESECBEncrypt AES加密函数
func AESECBEncrypt(data string, key []byte) (string, error) {
	// 创建AES加密器
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建AES加密器失败: %v", err)
	}

	// PKCS7填充
	plaintext := pkcs7Padding([]byte(data), block.BlockSize())

	// ECB模式加密
	ciphertext := make([]byte, len(plaintext))
	for bs, be := 0, block.BlockSize(); bs < len(plaintext); bs, be = bs+block.BlockSize(), be+block.BlockSize() {
		block.Encrypt(ciphertext[bs:be], plaintext[bs:be])
	}

	// 返回Base64编码结果
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// pkcs7Padding PKCS7填充
func pkcs7Padding(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(src, padText...)
}
