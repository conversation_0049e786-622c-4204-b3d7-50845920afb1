// Package utils TODO
package utils

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
)

// GetPlaceHolders 获取占位符
func GetPlaceHolders(docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) map[string]string {
	placeHolders := make(map[string]string)
	for _, doc := range docs {
		for _, ph := range doc.GetQuestionPlaceholders() {
			placeHolders[ph.Key] = ph.Value
		}
		for _, ph := range doc.GetAnswerPlaceholders() {
			placeHolders[ph.Key] = ph.Value
		}
		for _, ph := range doc.GetOrgDataPlaceholders() {
			placeHolders[ph.Key] = ph.Value
		}
	}
	return placeHolders
}

// GetPromptDocsFromSearchPreviewDocs 相似问需求，下游针对相似问返回的是主问信息，相似问信息在 SimilarQuestionExtra，
// 这里需要将相似问的 question 赋值给 doc 的 question，请求到大模型,这里需要做一定程度的深拷贝，避免影响 knowledge 里的 doc 的字段
func GetPromptDocsFromSearchPreviewDocs(
	docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc {
	if len(docs) == 0 {
		return docs
	}
	promptDocs := make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0, len(docs))
	for i := range docs {
		doc := &knowledge.SearchKnowledgeRsp_SearchRsp_Doc{
			DocId:                docs[i].DocId,
			DocType:              docs[i].DocType,
			RelatedId:            docs[i].RelatedId,
			Question:             docs[i].Question,
			Answer:               docs[i].Answer,
			Confidence:           docs[i].Confidence,
			OrgData:              docs[i].OrgData,
			RelatedBizId:         docs[i].RelatedBizId,
			QuestionPlaceholders: docs[i].QuestionPlaceholders,
			AnswerPlaceholders:   docs[i].AnswerPlaceholders,
			OrgDataPlaceholders:  docs[i].OrgDataPlaceholders,
			CustomParam:          docs[i].CustomParam,
			IsBigData:            docs[i].IsBigData,
			Extra:                docs[i].Extra,
			ImageUrls:            docs[i].ImageUrls,
			ResultType:           docs[i].ResultType,
			SimilarQuestionExtra: docs[i].SimilarQuestionExtra,
		}
		if doc.GetSimilarQuestionExtra() != nil && doc.GetSimilarQuestionExtra().GetSimilarQuestion() != "" {
			doc.Question = doc.GetSimilarQuestionExtra().GetSimilarQuestion()
		}
		promptDocs = append(promptDocs, doc)
	}
	return promptDocs
}

// GetCustomParams 获取自定义参数
func GetCustomParams(docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) []string {
	customParams := make([]string, 0)
	for _, doc := range docs {
		if len(doc.GetCustomParam()) == 0 {
			continue
		}
		log.DebugContextf(context.Background(), "doc: %+v", doc)
		customParams = append(customParams, doc.GetCustomParam())
	}
	return customParams
}
