package metrics

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
)

const (
	agentKeyThoughtFirstToken = "agent_thought_first_token"
	agentKeyContentFirstToken = "agent_content_first_token"
	agentKeyToolCost          = "agent_tool_cost"
)

// ReportThoughtFirstToken 上报思考事件首包耗时
func ReportThoughtFirstToken(ctx context.Context, ms int64) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		nil,
		[]*metrics.Metrics{metrics.NewMetrics(agentKeyThoughtFirstToken, float64(ms), metrics.PolicyHistogram)},
	)
	if err != nil {
		log.WarnContextf(ctx, "ReportThoughtFirstToken error: %v", err)
		return
	}
}

// ReportContentFirstToken 上报回复事件首包耗时
func ReportContentFirstToken(ctx context.Context, ms int64) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		nil,
		[]*metrics.Metrics{metrics.NewMetrics(agentKeyContentFirstToken, float64(ms), metrics.PolicyHistogram)},
	)
	if err != nil {
		log.WarnContextf(ctx, "ReportContentFirstToken error: %v", err)
		return
	}
}

// ReportToolCost 上报回复事件首包耗时
func ReportToolCost(ctx context.Context, toolName string, ms int64) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		[]*metrics.Dimension{{Name: "tool", Value: toolName}},
		[]*metrics.Metrics{metrics.NewMetrics(agentKeyToolCost, float64(ms), metrics.PolicyHistogram)},
	)
	if err != nil {
		log.WarnContextf(ctx, "ReportToolCost error: %v", err)
		return
	}
}
