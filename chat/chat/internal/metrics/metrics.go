// Package metrics 定义 Prometheus 上报所用的指标和方法
package metrics

import (
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
)

func fillLabel(labels, labelValues []string) []*metrics.Dimension {
	if len(labels) != len(labelValues) {
		panic(fmt.Sprintf("label %d cannot match values %d", len(labels), len(labelValues)))
	}
	d := make([]*metrics.Dimension, 0, len(labels))
	for i := range labels {
		d = append(d, &metrics.Dimension{
			Name:  labels[i],
			Value: labelValues[i],
		})
	}
	return d
}

// ReportLLMLength 大模型输出长度
func ReportLLMLength(value float64, labelValues ...string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		fillLabel([]string{"index", "first", "final"}, labelValues),
		[]*metrics.Metrics{
			metrics.NewMetrics("chat_svr_llm_length_metrics", value, metrics.PolicyHistogram),
		},
	)
	if err != nil {
		return
	}
}

// ReportTime 上报耗时
func ReportTime(name string, idx int, ms int64) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		[]*metrics.Dimension{{Name: "index", Value: fmt.Sprintf("%d", idx)}},
		[]*metrics.Metrics{metrics.NewMetrics(name, float64(ms), metrics.PolicyHistogram)},
	)
	if err != nil {
		return
	}
}

// ReportLLMLatency 大模型延迟
func ReportLLMLatency(value float64, labelValues ...string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		fillLabel([]string{"index", "first", "final"}, labelValues),
		[]*metrics.Metrics{
			metrics.NewMetrics("chat_svr_llm_latency_metrics", value, metrics.PolicyHistogram),
		},
	)
	if err != nil {
		return
	}
}

// ReportEventProcess 事件处理
func ReportEventProcess(value float64, labelValues ...string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		fillLabel([]string{"type", "success"}, labelValues),
		[]*metrics.Metrics{
			metrics.NewMetrics("chat_svr_event_process_metrics", value, metrics.PolicyHistogram),
		},
	)
	if err != nil {
		return
	}
}

func addMetric(name string) []*metrics.Metrics {
	return []*metrics.Metrics{
		metrics.NewMetrics(name, 1, metrics.PolicySUM),
	}
}

// ReportSendEvent 发出事件
func ReportSendEvent(labelValues ...string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		fillLabel([]string{"type", "success"}, labelValues),
		addMetric("chat_svr_send_event_metrics"),
	)
	if err != nil {
		return
	}
}

// ReportRecvEvent 收到事件
func ReportRecvEvent(labelValues ...string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		fillLabel([]string{"type"}, labelValues),
		addMetric("chat_svr_receive_event_metrics"),
	)
	if err != nil {
		return
	}
}

// ReportClientConnect 客户端连接
func ReportClientConnect(labelValues ...string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		fillLabel([]string{"success"}, labelValues),
		addMetric("chat_svr_client_connect_metrics"),
	)
	if err != nil {
		return
	}
}

// ReportClientDisconnect 客户端断开连接
func ReportClientDisconnect(labelValues ...string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		fillLabel([]string{"success"}, labelValues),
		addMetric("chat_svr_client_disconnect_metrics"),
	)
	if err != nil {
		return
	}
}

// ReportSseClientConnect  SSE 客户端连接
func ReportSseClientConnect(labelValues ...string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		fillLabel([]string{"success"}, labelValues),
		addMetric("chat_svr_sse_client_connect_metrics"),
	)
	if err != nil {
		return
	}
}

// ReportSseClientDisconnect  SSE 客户端断开连接
func ReportSseClientDisconnect(labelValues ...string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		fillLabel([]string{"success"}, labelValues),
		addMetric("chat_svr_sse_client_disconnect_metrics"),
	)
	if err != nil {
		return
	}
}

// ReportSseClientRef  SSE 客户端增加引用
func ReportSseClientRef() {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		nil,
		addMetric("chat_svr_sse_client_ref_metrics"),
	)
	if err != nil {
		return
	}
}

// ReportSseClientDeref  SSE 客户端减少引用
func ReportSseClientDeref() {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		nil,
		addMetric("chat_svr_sse_client_deref_metrics"),
	)
	if err != nil {
		return
	}
}

// ReportClientNotFound  客户端不存在
func ReportClientNotFound() {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		nil,
		addMetric("chat_svr_client_not_found_metrics"),
	)
	if err != nil {
		return
	}
}

// ReportLimit limit上报
func ReportLimit(labelValues ...string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		fillLabel([]string{"lock", "holder"}, labelValues),
		addMetric("concurrency_limiter_block_metrics"),
	)
	if err != nil {
		return
	}
}

// ReportConcurrencyNum 上报用户实时并发数
func ReportConcurrencyNum(value float64, labelValues ...string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		fillLabel([]string{"uin", "app_id", "main_model"}, labelValues),
		[]*metrics.Metrics{
			metrics.NewMetrics("chat_svr_concurrency_num", value, metrics.PolicyHistogram),
		},
	)
	if err != nil {
		return
	}
}

// ReportClientReconnectRecovery  客户端重连恢复情况
func ReportClientReconnectRecovery(labelValues ...string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		fillLabel([]string{"success"}, labelValues),
		addMetric("reconnect_recovery_metrics"),
	)
	if err != nil {
		return
	}
}
