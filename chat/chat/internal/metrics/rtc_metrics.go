package metrics

import (
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
)

const (
	rtcKeyStart  = "rtc_start_metrics"
	rtcKeyStop   = "rtc_stop_metrics"
	rtcKeyError  = "rtc_error_metrics"
	rtcKeyMetric = "rtc_metric_"
)

func buildDimensions(labelMap map[string]string) []*metrics.Dimension {
	d := make([]*metrics.Dimension, 0, len(labelMap))
	for name, value := range labelMap {
		d = append(d, &metrics.Dimension{
			Name:  name,
			Value: value,
		})
	}
	return d
}

// ReportRTCStart 任务开始
func ReportRTCStart(labelMap map[string]string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		buildDimensions(labelMap),
		addMetric(rtcKeyStart),
	)
	if err != nil {
		return
	}
}

// ReportRTCStop 任务结束
func ReportRTCStop(labelMap map[string]string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		buildDimensions(labelMap),
		addMetric(rtcKeyStop),
	)
	if err != nil {
		return
	}
}

// ReportRTCError 任务错误
func ReportRTCError(labelMap map[string]string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		buildDimensions(labelMap),
		addMetric(rtcKeyError),
	)
	if err != nil {
		return
	}
}

// ReportRTCHistogramMetric 任务指标
func ReportRTCHistogramMetric(metric string, value int64, labelMap map[string]string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		buildDimensions(labelMap),
		[]*metrics.Metrics{metrics.NewMetrics(rtcKeyMetric+metric, float64(value), metrics.PolicyHistogram)},
	)
	if err != nil {
		return
	}
}

// ReportRTCSumMetric 任务指标
func ReportRTCSumMetric(metric string, labelMap map[string]string) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		buildDimensions(labelMap),
		addMetric(rtcKeyMetric+metric),
	)
	if err != nil {
		return
	}
}
