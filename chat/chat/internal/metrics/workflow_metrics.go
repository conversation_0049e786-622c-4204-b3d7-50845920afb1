package metrics

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
)

const (
	workflowKeyThoughtFirstToken = "workflow_thought_first_token"
	workflowKeyContentFirstToken = "workflow_content_first_token"
	workflowKeyFirstToken        = "workflow_first_token"
)

// WorkflowThoughtFirstToken 上报思考事件首包耗时
func WorkflowThoughtFirstToken(ctx context.Context, ms int64) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		nil,
		[]*metrics.Metrics{metrics.NewMetrics(workflowKeyThoughtFirstToken, float64(ms), metrics.PolicyHistogram)},
	)
	if err != nil {
		log.WarnContextf(ctx, "WorkflowThoughtFirstToken error: %v", err)
		return
	}
}

// WorkflowContentFirstToken 上报回复事件首包耗时
func WorkflowContentFirstToken(ctx context.Context, ms int64) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		nil,
		[]*metrics.Metrics{metrics.NewMetrics(workflowKeyContentFirstToken, float64(ms), metrics.PolicyHistogram)},
	)
	if err != nil {
		log.WarnContextf(ctx, "WorkflowContentFirstToken error: %v", err)
		return
	}
}

// WorkflowFirstToken 工作流首包耗时
func WorkflowFirstToken(ctx context.Context, ms int64) {
	err := metrics.ReportMultiDimensionMetricsX(
		trpc.GlobalConfig().Server.Server,
		nil,
		[]*metrics.Metrics{metrics.NewMetrics(workflowKeyFirstToken, float64(ms), metrics.PolicyHistogram)},
	)
	if err != nil {
		log.WarnContextf(ctx, "WorkflowFirstToken error: %v", err)
		return
	}
}
