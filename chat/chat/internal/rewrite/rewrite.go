// Package rewrite TODO
package rewrite

import (
	"context"

	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/memory"
)

// QueryRewriteAPI TODO
type QueryRewriteAPI interface {
	Rewrite(ctx context.Context, bs *botsession.BotSession) (string, string, error)
	SimpleImageRewrite(ctx context.Context, bs *botsession.BotSession) (string, string, error)
	MultiModalRewrite(ctx context.Context, bs *botsession.BotSession) (string, string, error)
	ComplexQueryRewrite(ctx context.Context, bs *botsession.BotSession) error
	HunYuanImageRewrite(ctx context.Context, bs *botsession.BotSession) (string, error)
}

// QueryRewrite Query多轮改写
type QueryRewrite struct {
	dao    dao.Dao
	memory memory.ChatMemoryAPI
}

var (
	rewrite QueryRewriteAPI
)

func init() {
	rewrite = &QueryRewrite{
		dao:    dao.New(),
		memory: memory.New(),
	}
}

// New .
func New() QueryRewriteAPI {
	return rewrite
}
