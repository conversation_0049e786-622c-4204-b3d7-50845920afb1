package eventbus

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	handlers[event.EventReconnect] = &ReconnectEventHandler{}
}

// ReconnectEventHandler 推荐问事件处理器
type ReconnectEventHandler struct{}

// NewRequest 创建事件请求
func (r *ReconnectEventHandler) NewRequest(ctx context.Context, cli *model.Conn, bs []byte) (any, error) {
	log.InfoContextf(ctx, "Invoke ReconnectEventHandler NewRequest cli: %s, req: %s",
		helper.Object2String(cli), string(bs))
	req := event.ReconnectEvent{}
	if err := jsoniter.Unmarshal(bs, &req); err != nil {
		log.ErrorContextf(ctx, "Unmarshal event req error: %+v, data: %s", err, bs)
		return nil, err
	}
	// todo 参数校验
	// 框架未携带, 则使用业务数据
	pkg.WithSessionID(ctx, req.SessionID)
	pkg.WithRequestID(ctx, req.RequestID)
	pkg.WithTraceID(ctx, req.RequestID)
	return req, nil
}

// Process 处理事件请求
func (r *ReconnectEventHandler) Process(ctx context.Context, cli *model.Conn, ev any) error {
	log.InfoContextf(ctx, "ReconnectEventHandler Process cli: %s, req: %+v",
		helper.Object2String(cli), helper.Object2String(ev))
	success := false
	defer func() {
		metrics.ReportClientReconnectRecovery(helper.When(success == true, "1", "0"))
		log.InfoContextf(ctx, "ReconnectEventHandler end, success: %+v", success)
	}()
	req := ev.(event.ReconnectEvent)
	if req.RelatedRecordID != "" { // 如果有related_record_id, 则说明是首包就中断了，需要获取下record_id
		recordID, err := bus.dao.GetMsgRecordIDByRelatedRecordID(ctx, req.RelatedRecordID)
		if err != nil {
			log.ErrorContextf(ctx, "ReconnectEventHandler getRecord error: %+v, params: %+v", err, req)
			return err
		}
		req.RecordID = recordID
	}
	if req.RecordID == "" {
		log.ErrorContextf(ctx, "ReconnectEventHandler recordID is empty, params: %+v", req)
		return errors.New("recordID is empty")
	}
	if r.isExceedMaxTimes(ctx, req.RecordID) {
		return errors.New("reconnect exceed max times")
	}
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	timer := time.NewTimer(time.Duration(config.App().Reconnect.RecoveryMaxTime) * time.Second) // 支持续传的最长时间
	defer timer.Stop()
	ch := make(chan *model.WsMessage, 10)
	errCh := make(chan error, 1)
	go func() { // 从redis读数据，模拟流式下发
		defer errors.PanicHandler()
		for {
			data, err := bus.dao.GetReconnectData(ctx, req.RecordID)
			if err != nil {
				errCh <- err
				return
			}
			ch <- data
			if r.isFinalReply(ctx, data) { // 判断是否已经是尾包,退出循环
				log.InfoContextf(ctx, "ReconnectEventHandler get final rsp")
				return
			}
			time.Sleep(time.Duration(config.App().Reconnect.OutputRate) * time.Millisecond) // 配置输出频率
		}
	}()
	lastReply := event.ReplyEvent{}
	lastThought := event.AgentThoughtEvent{}
	for {
		select {
		case <-ctx.Done(): // 上游断开
			log.WarnContextf(ctx, "ReconnectEventHandler  ctx done")
			return nil
		case <-timer.C: // 超过最大时间终端
			log.WarnContextf(ctx, "ReconnectEventHandler timer done")
			return nil
		case err := <-errCh: // 从redis读数据失败
			log.WarnContextf(ctx, "ReconnectEventHandler get data err: %+v", err)
			return err
		case rsp := <-ch: // 重连恢复下发
			if rsp.Event == event.EventReply {
				b, _ := jsoniter.Marshal(rsp.Payload)
				reply := event.ReplyEvent{}
				if err := jsoniter.Unmarshal(b, &reply); err != nil {
					log.ErrorContextf(ctx, "ReconnectEventHandler reply unmarshal error: %+v", err)
					return err
				}
				if reply.Content == lastReply.Content && !reply.IsFinal { // 如果内容相同，则不下发
					log.InfoContextf(ctx, "ReconnectEventHandler reply same continue")
					continue
				}
				bus.dao.DoEmitWsClient(ctx, cli.ClientID, &reply, cancel)
				if reply.IsFinal {
					log.InfoContextf(ctx, "ReconnectEventHandler reply final: %+v", reply)
					success = true
					return nil
				}
				lastReply = reply
			} else if rsp.Event == event.EventThought {
				b, _ := jsoniter.Marshal(rsp.Payload)
				thought := event.AgentThoughtEvent{}
				if err := jsoniter.Unmarshal(b, &thought); err != nil {
					log.ErrorContextf(ctx, "ReconnectEventHandler thought unmarshal error: %+v", err)
					return err
				}
				if len(lastThought.Procedures) > 0 && thought.Procedures[len(thought.Procedures)-1].Debugging.Content ==
					lastThought.Procedures[len(lastThought.Procedures)-1].Debugging.Content { // 如果内容相同，则不下发
					log.InfoContextf(ctx, "ReconnectEventHandler thought same continue")
					continue
				}
				bus.dao.DoEmitWsClient(ctx, cli.ClientID, &thought, cancel)
				lastThought = thought
			} else if rsp.Event == event.EventTokenStat {
				b, _ := jsoniter.Marshal(rsp.Payload)
				tokenStat := event.TokenStatEvent{}
				if err := jsoniter.Unmarshal(b, &tokenStat); err != nil {
					log.ErrorContextf(ctx, "ReconnectEventHandler tokenStat unmarshal error: %+v", err)
					return err
				}
				bus.dao.DoEmitWsClient(ctx, cli.ClientID, &tokenStat, cancel)
			} else if rsp.Event == event.EventReference {
				b, _ := jsoniter.Marshal(rsp.Payload)
				refer := event.ReferenceEvent{}
				if err := jsoniter.Unmarshal(b, &refer); err != nil {
					log.ErrorContextf(ctx, "ReconnectEventHandler reference unmarshal error: %+v", err)
					return err
				}
				bus.dao.DoEmitWsClient(ctx, cli.ClientID, &refer, cancel)
			}
		}
	}
}

func (r *ReconnectEventHandler) isFinalReply(ctx context.Context, msg *model.WsMessage) bool {
	if msg.Event != event.EventReply {
		return false
	}
	b, _ := jsoniter.Marshal(msg.Payload)
	reply := event.ReplyEvent{}
	if err := jsoniter.Unmarshal(b, &reply); err != nil {
		log.ErrorContextf(ctx, "isFinalReply reply unmarshal error: %+v", err)
		return true
	}
	return reply.IsFinal
}

func (r *ReconnectEventHandler) isExceedMaxTimes(ctx context.Context, recordID string) bool {
	times := bus.dao.GetReconnectTimes(ctx, recordID)
	if times >= config.App().Reconnect.MaxTimes {
		log.WarnContextf(ctx, "reconnect times is over, times: %d, recordID: %+v", times, recordID)
		return true
	}
	bus.dao.SetReconnectTimes(ctx, recordID, times+1)
	return false
}
