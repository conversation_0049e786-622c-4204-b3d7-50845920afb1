package eventbus

import (
	"context"
	"errors"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	errors2 "git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/common/v3/utils"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	jsoniter "github.com/json-iterator/go"
)

// getSession 获取会话
func (d *DialogEventHandler) getSession(ctx context.Context, bs *botsession.BotSession) (err error) {
	session := &model.Session{
		SessionID:    bs.SessionID,
		BotBizID:     bs.To.APIBotBizID,
		VisitorID:    bs.To.CorpStaffID,
		VisitorBizID: bs.To.CorpStaffBizID,
	}
	if bs.To.Type != model.ConnTypeAPIVisitor {
		if session, err = bus.dao.GetSession(ctx, bs.SessionType, bs.SessionID); err != nil {
			return err
		}
		if session == nil {
			return pkg.ErrSessionNotFound
		}
	} else {
		// 如果是api接入，正常是取不到session，但是trtc的场景是有创建session，所以能取到就用，取不到不影响
		if data, err := bus.dao.GetSession(ctx, bs.SessionType, bs.SessionID); err == nil && data != nil {
			session = data
		}
	}
	log.InfoContextf(ctx, "Process.botsession:%s", utils.Any2String(session))
	bs.Session = session
	return nil
}

// CreateQueryMsgRecord 创建查询消息记录
func CreateQueryMsgRecord(ctx context.Context, bs *botsession.BotSession) error {
	pf.StartElapsed(ctx, bs.EventSource+".createQueryMsgRecord")
	fileInfos, _ := jsoniter.MarshalToString(bs.FileInfos)
	msg := model.MsgRecord{
		BotBizID:     bs.App.GetAppBizId(),
		SessionID:    bs.SessionID,
		RecordID:     bs.RecordID,
		Type:         bs.Type,
		ToID:         bs.App.GetId(),
		ToType:       model.SourceTypeRobot,
		FromID:       bs.To.CorpStaffID,
		FromType:     bs.To.GetSourceType(),
		Content:      bs.OriginContent,
		FileInfos:    fileInfos,
		CreateTime:   time.Now(),
		CfgVersionID: bs.App.GetConfigVersionId(),
		TraceID:      model.TraceID(ctx),
	}
	msgID, err := bus.dao.CreateMsgRecord(ctx, msg, nil)                  // for query
	go bus.dao.CreateChatPrompt(trpc.CloneContext(ctx), model.ChatPrompt{ // 初始化一下ChatPrompt
		BotBizID:   msg.BotBizID,
		SessionID:  msg.SessionID,
		RecordID:   msg.RecordID,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	})
	pf.AppendSpanElapsed(ctx, bs.EventSource+".createQueryMsgRecord")
	pkg.WithRecordID(ctx, bs.RecordID) // query record_id
	clues.AddTrackData(ctx, "query:record:id", msg.RecordID)
	bs.RelatedRecordID = bs.RecordID // 写消息记录后，设置RelatedRecordID
	bs.Msg = &msg
	if err != nil {
		return err
	}
	msg.ID = uint64(msgID)
	return err
}

// replyFromSelf 首包回复自身信息
func (d *DialogEventHandler) replyFromSelf(ctx context.Context, bs *botsession.BotSession) {
	ctx, cancel := context.WithCancel(ctx)
	// 如果IsEvil为true，是否有必要设置ReplyMethod。这里异步执行即可。
	bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, &event.ReplyEvent{
		IsFinal:    true,
		IsFromSelf: true,
		IsEvil:     bs.Msg.ResultCode == ispkg.ResultEvil,
		RequestID:  bs.RequestID,
		FromName:   bs.CorpStaff.NickName,
		FromAvatar: bs.CorpStaff.Avatar,
		Content:    bs.OriginContent,
		RecordID:   bs.RelatedRecordID,
		SessionID:  bs.SessionID,
		Timestamp:  bs.StartTime.Unix(),
		FileInfos:  bs.FileInfos,
	}, cancel)
	return
}

// specialQueryProcess 处理不包含文本Query的消息
func (d *DialogEventHandler) specialQueryProcess(ctx context.Context, bs *botsession.BotSession) (err error) {
	bs.Images = helper.GetAllImageURLs(bs.OriginContent)
	if len(bs.FileInfos) == 0 && helper.IsQueryOnlyImage(bs.OriginContent) { // 只有图片的情况，返回图片Caption
		return QueryWithImage(ctx, bs) // 走不到这里
	} else if len(bs.FileInfos) != 0 && helper.IsQueryOnlyImage(bs.OriginContent) { // Query中只有图片，且同时有文件的情况
		// 并行调用图片和文件的处理
		return QueryWithImageAndFile(ctx, bs)
	} else if len(bs.FileInfos) != 0 && helper.IsQueryContainsImage(bs.OriginContent) { // Query中包含图片、Query和文件的情况
		return QueryWithTextImageFileByMLLMComprehension(ctx, bs)
	} else if len(bs.OriginContent) < 1 && len(bs.FileInfos) != 0 { // 仅仅包含文件的情况
		_, err = processDocSummary(ctx, bs, true)
		return err
	}
	return pkg.ErrBadRequest
}

// processImageAndFileCount 处理图片和文件数量
func (d *DialogEventHandler) processImageAndFileCount(ctx context.Context, bs *botsession.BotSession) {
	if bus.dao.IsInFileKnowledge(ctx, bs.SessionID, bs.App.GetHistoryLimit()) && len(bs.FileInfos) == 0 {
		_ = bus.dao.SetFileKnowledgeCount(ctx, bs.SessionID) // 对话轮数+1
	}
	if bus.dao.IsInMultiModal(ctx, bs.SessionID, bs.App.GetHistoryLimit()) &&
		!helper.IsQueryContainsImage(bs.OriginContent) {
		go func() {
			defer errors2.PanicHandler()
			err := bus.dao.SetMultiModalCount(ctx, bs.SessionID)
			if err != nil {
				log.ErrorContextf(ctx, "processImageAndFileCount SetMultiModalCount error: %v", err)
			}
		}() // 多模态次数+1
	}
}

// FaqReply FAQ回复
func (d *DialogEventHandler) FaqReply(ctx context.Context,
	bs *botsession.BotSession, hitIntent model.CandidateIntent) (err error) {
	// 找到FAQ
	doc, err := d.FindFaqAnswer(ctx, bs.Knowledge, hitIntent.DocID)
	if err != nil {
		return err
	}
	// 返回FAQ答案
	bs.Flags.IsFaqReply = true
	bs.Knowledge = []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc{doc}
	log.DebugContextf(ctx, "FaqReply intent: %s intentCate: %s", bs.Intent, bs.IntentCate)
	reply, err := botDirectReply(ctx, bs) // 这里要直接返回
	if err != nil {
		return err
	}
	matchRecommended(ctx, bs, reply, bs.Placeholders)
	matchRefer(ctx, bs, reply, bs.Placeholders)
	return nil
}

// FindFaqAnswer 查找FAQ答案
func (d *DialogEventHandler) FindFaqAnswer(ctx context.Context,
	docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc,
	docID uint64) (doc *knowledge.SearchKnowledgeRsp_SearchRsp_Doc, err error) {
	for _, d := range docs {
		if d.DocType != 1 {
			continue
		}
		if d.GetRelatedId() == docID {
			doc = d
			break
		}
	}
	if doc == nil {
		log.ErrorContextf(ctx, "FindFaqAnswer doc not found, docID: %d", docID)
		return nil, errors.New("doc not found")
	}
	log.DebugContextf(ctx, "FindFaqAnswer doc is: %s", helper.Object2StringEscapeHTML(doc))
	return doc, nil
}

// AddHitSkills 增加命中的技能
func (d *DialogEventHandler) AddHitSkills(hitIntent model.CandidateIntent) (content string) {
	content = "## 意图\n## 意图1：" + hitIntent.Name + "\n## 意图描述：" +
		hitIntent.Def + "\n## 意图实现：" + hitIntent.Example + "\n"
	return content
}

// KnowledgeReply 知识库回复
func (d *DialogEventHandler) KnowledgeReply(ctx context.Context,
	bs *botsession.BotSession, hitIntent model.CandidateIntent) (err error) {
	if hitIntent.Type == model.IntentTypeCustom { // 在满足相关性且命中自定义意图时，把自定义意图拼上。
		bs.SystemRole = bs.SystemRole + "\n" + d.AddHitSkills(hitIntent)
	}
	bs.Flags.IsKnowledgeQAIntent = model.IsKnowledgeQAIntent(bs.IntentCate)
	filterQuestions, filterErr := getFilterClarifyConfirm(ctx, bs)
	if filterErr == nil && bs.IsClarifyConfirm(model.FilterKeyPreviewQuestion, filterQuestions) {
		bs.Flags.IsClarifyConfirm = true
		_, err = clarifyConfirmReply(ctx, bs, filterQuestions)
		return err
	}
	_, _, output, err := botReply(ctx, bs)
	if !bs.Flags.IsRealTimeDocument {
		matchRefer(ctx, bs, output, bs.Placeholders)
	}
	return err
}

// KnowledgeReplyV2 文档阅读理解回复
func (d *DialogEventHandler) KnowledgeReplyV2(ctx context.Context, bs *botsession.BotSession) (err error) {
	bs.Flags.IsKnowledgeQAIntent = model.IsKnowledgeQAIntent(bs.IntentCate)
	filterQuestions, filterErr := getFilterClarifyConfirm(ctx, bs)
	if filterErr == nil && bs.IsClarifyConfirm(model.FilterKeyPreviewQuestion, filterQuestions) {
		bs.Flags.IsClarifyConfirm = true
		_, err = clarifyConfirmReply(ctx, bs, filterQuestions)
		return err
	}
	reply := ""
	useMultiModalQA := docsContainsImage(bs.Knowledge) && bs.App.GetKnowledgeQa().GetMultiModalGenerationImage()
	if useMultiModalQA { // 开启多模态出图&召回的文档包含图片
		reply, err = docMultiModelReply(ctx, bs)
	} else {
		reply, err = docReply(ctx, bs)
	}
	if err != nil {
		log.ErrorContextf(ctx, "docReply error: %v", err)
		return err
	}
	if !bs.Flags.IsRealTimeDocument && reply != "" { // 出参考来源
		matchRefer(ctx, bs, reply, bs.Placeholders)
	}
	if !bs.Flags.IsDocSegmentReject { // 出推荐问流程
		matchRecommended(ctx, bs, "", bs.Placeholders)
	}
	return nil
}

// GetQuestionAnswersForTaskflow 获取任务流程的问题答案
func (d *DialogEventHandler) GetQuestionAnswersForTaskflow(ctx context.Context, bs *botsession.BotSession,
	docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) (similarQuestions []string) {
	for _, doc := range docs {
		question := doc.GetQuestion()
		if doc.GetSimilarQuestionExtra() != nil && doc.GetSimilarQuestionExtra().GetSimilarQuestion() != "" {
			question = doc.GetSimilarQuestionExtra().GetSimilarQuestion()
		}

		similarQuestions = append(similarQuestions, question)
		bs.QuestionAnswers = append(bs.QuestionAnswers, [2]string{question, doc.GetAnswer()})
	}
	return similarQuestions
}

// NewFaqReply 新的FAQ回复 V2.6.1
// 1. 带角色指令；2. 不带历史记录；3. 先请求模型拒答；4. 详细ppl流程：
func (d *DialogEventHandler) NewFaqReply(ctx context.Context,
	bs *botsession.BotSession, hitIntent model.CandidateIntent) (err error) {
	faq, _ := d.FindFaqAnswer(ctx, bs.Knowledge, hitIntent.DocID) // 只输入命中的那条FAQ
	if err == nil {
		// 处理相似问逻辑
		if faq.GetSimilarQuestionExtra() != nil && faq.GetSimilarQuestionExtra().GetSimilarQuestion() != "" {
			faq.Question = faq.GetSimilarQuestionExtra().GetSimilarQuestion()
		}
		bs.PromptCtx.Docs = []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc{faq}
	}
	bs.ModelType = model.ModelTypeMessageNonGeneralKnowledge // 必须拒答
	m := getAppModel(ctx, bs)
	if bs.App.IsDeepSeekModel(ctx) { // ds场景，faq拒答模型还是用神农模型
		m.ModelName = config.App().IntentModel.DsFAQRejectModel
	}
	p := m.Prompt // m是个指针，会修改原数据，所以先保存一下。用完改回去。
	m1 := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeFaqReject)
	log.DebugContextf(ctx, "get faq_reject_prompt, finally model_name:%s, prompt:%s", m1.GetModelName(), m1.GetPrompt())
	m.Prompt = m1.GetPrompt()
	systemPrompt := d.getUserSystemPrompt(ctx, bs)
	if systemPrompt == "" { // 优先使用用户自定义的system prompt，如果为空，则使用默认的sysRole
		systemPrompt = m.GetSysPrompt(true, false, bs.SystemRole)
	}
	prompt, err := bus.dao.TextTruncateTemp(ctx, m, bs.PromptCtx) // 生成Prompt
	bs.Flags.IsJudgeModelReject = true                            // 校验模型拒绝
	m.Prompt = p
	message := m.WrapMessages(systemPrompt, nil, prompt)
	inferParams := m.CreateModelParamsObject(bs.EnableRandomSeed)
	req := m.NewLLMRequestWithModelParams(bs.LLMRequestID, message, inferParams)
	clues.AddTrackData(ctx, "generalReply.NewLLMRequest", req)
	log.InfoContextf(ctx, "R|generalReply|NewFaqReply|%s,req: %s", bs.EventSource, helper.Object2String(req))

	var isTimeout bool
	cfg := config.App().Bot
	ch := make(chan *llmm.Response, cfg.ResponseChannelSize)
	g, gctx := errgroupx.WithContext(ctx)
	llmctx, cancel := context.WithCancel(ctx)
	var st time.Time
	var last *llmm.Response
	var lastEvil *infosec.CheckRsp
	var isModelRejected, directReply bool
	// 开始计时
	pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.LLMReply)
	g.Go(func() error {
		if err := bus.dao.Chat(llmctx, req, ch, bs.StartTime, nil); !errors.Is(err, context.Canceled) {
			return err
		}
		return nil
	})
	g.Go(func() error {
		last, st, lastEvil, isTimeout, isModelRejected, directReply =
			faqStreamReply(gctx, cancel, bs, ch, req, event.ProcedureLLM, model.ReplyMethodModel)
		return helper.When(isTimeout, pkg.ErrLLMTimeout, nil)
	})
	if err = g.Wait(); err != nil {
		return err
	}

	if isModelRejected {
		return d.docReply(ctx, bs)
	}
	var reply string
	if directReply {
		doc, err := d.FindFaqAnswer(ctx, bs.Knowledge, hitIntent.DocID) // 需要直接回复
		if err != nil {
			return err
		}
		bs.Flags.IsFaqReply = true // 返回FAQ答案
		bs.Knowledge = []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc{doc}
		log.DebugContextf(ctx, "FaqReply intent: %s intentCate: %s", bs.Intent, bs.IntentCate)
		reply, err = botDirectReply(ctx, bs) // 这里要直接返回
		if err != nil {
			return err
		}
		matchRecommended(ctx, bs, reply, bs.Placeholders)
	} else {
		// 走到这里，说明是润色后回复
		if last != nil && last.Message != nil {
			reply = last.Message.Content
			_, _ = checkLastAndCreateRecord(ctx, bs, model.ReplyMethodModel, last, lastEvil, req, st)
		}
	}
	matchRefer(ctx, bs, reply, bs.Placeholders)
	return nil
}

// getUserSystemPrompt 获取客户自定义的system prompt
func (d *DialogEventHandler) getUserSystemPrompt(ctx context.Context, bs *botsession.BotSession) string {
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeFaqSystem)
	log.DebugContextf(ctx, "get faq_system_prompt, finally model_name:%s, prompt:%s|len:%d", m.GetModelName(),
		m.GetPrompt(), len(m.GetPrompt()))
	if m.GetPrompt() != model.DefaultFaqSystemPrompt {
		return m.GetPrompt()
	}
	return ""
}

// findAllFaqAnswers 找到全部FAQ
func (d *DialogEventHandler) findAllFaqAnswers(docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) (
	faqs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) {
	for _, doc := range docs {
		if doc.DocType == 1 {
			faqs = append(faqs, doc)
		}
	}
	return faqs
}

// faqStreamReply 向前端流式输出
func faqStreamReply(ctx context.Context, cancel context.CancelFunc, bs *botsession.BotSession,
	ch chan *llmm.Response, req *llmm.Request, eventPrName string, replyMethod model.ReplyMethod,
) (last *llmm.Response, t time.Time, lastEvil *infosec.CheckRsp, isTimeout bool, isModelRejected, directReply bool) {
	throttleCheck, throttleStreaming, _, ticker, timeout, index := initStreamVars(ctx, bs, 0)
	defer ticker.Stop()
	defer timeout.Stop()
	intentionCategory := bs.IntentCate
	mainModel := ""
	if req != nil {
		mainModel = req.ModelName
	}
	statistics := pkg.GetStatistics(ctx)
	if statistics != nil {
		statistics.MainModel = mainModel
		statistics.IntentionCategory = intentionCategory
		defer pkg.WithStatistics(ctx, statistics) // 更新后将统计信息放入context
	}
	for {
		select {
		case <-timeout.C:
			cancel()
			log.ErrorContext(ctx, "faqStreamReply llm timeout")
			bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(eventPrName))
			SendTokenStat(ctx, bs, bs.TokenStat)
			return last, t, lastEvil, true, false, directReply
		case <-ticker.C:
			if ok := middleStop(ctx, cancel, bs, last, eventPrName); ok {
				return last, t, lastEvil, false, false, directReply
			}
		case rsp, ok := <-ch:
			timeout.Stop()
			if !ok {
				return last, t, lastEvil, false, false, directReply
			}
			if last == nil {
				t = time.Now()
			}
			last = rsp
			if discardMiddleResult(ctx, bs, rsp) {
				continue
			}
			if rsp.GetFinished() {
				if rsp.Message.Content == "" {
					rsp.Message.Content = config.App().Bot.EmptyReply
				}
				sendFinishTokenStat(ctx, bs, req, nil, rsp, eventPrName)
			}
			rsp.Message.Content = replacePlaceholders(rsp.GetMessage().GetContent(), bs.Placeholders)
			if needReject(ctx, bs, rsp) {
				if isHitRejectAnswer(rsp.GetMessage().GetContent()) {
					pf.AddNode(ctx, pf.PipelineNode{Key: "stream.HitReject"})
					bus.dao.UpdateFaqReject(ctx, bs.RelatedRecordID)
					log.InfoContextf(ctx, "reply: %s isHitRejectAnswer", rsp.GetMessage().GetContent())
					cancel()
					sendFinishTokenStat(ctx, bs, req, nil,
						&llmm.Response{StatisticInfo: &llmm.StatisticInfo{}}, eventPrName)
					return last, t, lastEvil, false, true, directReply
				}
				if !rsp.GetFinished() {
					continue
				}
			}
			// 能到这里 说明没有拒答。
			if bs.NeedCheck && throttleCheck.Hit(len([]rune(rsp.GetMessage().GetContent())), rsp.GetFinished()) {
				_, _, v, signal := handleEvilCheck(ctx, cancel, bs, t,
					rsp.GetMessage().GetContent(), rsp, req, nil, eventPrName, last)
				if signal {
					return last, t, v, false, false, directReply
				}
			}
			if index == 0 {
				answerMetrics(ctx, metricNameFirstLLMReply, mainModel, intentionCategory, statistics, pkg.Uin(ctx))
			} else if rsp.GetFinished() { // 大模型回复阶段耗时统计，记录第二个 span 表示尾包包耗时
				answerMetrics(ctx, metricNameFinalLLMReply, mainModel, intentionCategory, statistics, pkg.Uin(ctx))
			}
			if directReply = faqThrottleFinish(ctx, bs, len([]rune(rsp.GetMessage().GetContent())),
				throttleStreaming, rsp, t, replyMethod, index, cancel); directReply {
				cancel()
				//  FAQ直接回复
				return last, t, lastEvil, false, false, directReply
			}
			index++
		}
	}
}

func faqThrottleFinish(ctx context.Context, bs *botsession.BotSession, l int, throttleStreaming helper.Throttle,
	rsp *llmm.Response, t time.Time, replyMethod model.ReplyMethod,
	index int, cancel context.CancelFunc) (directReply bool) {

	if bs.App.IsQAPriority() {
		log.DebugContextf(ctx, "faqThrottleFinish|QAPriority")
		return true
	}

	if throttleStreaming.Hit(l, rsp.GetFinished()) {
		rEvent := bs.NewReplyEvent(ctx, rsp, false, replyMethod, t, []string{})
		if rsp.GetFinished() {
			_, positions, indexes := helper.MatchSearchResults(ctx, rsp.GetMessage().GetContent())
			bs.QuoteInfos = helper.Object2String(bs.GetQuoteInfos(rsp, positions, indexes))
			bs.ReferIndex = indexes
		}
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, rEvent, cancel)
	}
	if index == 0 || rsp.GetFinished() {
		key := bs.EventSource
		if bs.To.IsSSE {
			key = "SSE." + bs.EventSource
		}
		pf.AppendSpanElapsed(ctx, key)
		pf.AppendSpanElapsed(ctx, key+".queryRewrite_firstPacket")
		// 大模型回复阶段耗时统计，记录第一个 span 表示首包耗时
	}
	if rsp.GetFinished() { // 处理大模型引用
		bs.ReferIndex = append(bs.ReferIndex,
			helper.GetAllSubMatch(bs.App.GetAppBizId(), rsp.GetMessage().GetContent())...)
	}
	return false
}

// docReply TODO
// processFaqReply 处理FAQ回复
func (d *DialogEventHandler) docReply(ctx context.Context, bs *botsession.BotSession) (err error) {
	// 拒答以后 重新走文档问答的逻辑
	bs.ModelType = bs.App.GetMessageModelType() // 拒答以后要改回去
	// 文档问答 找到全部doc
	docs := make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0)
	for _, doc := range bs.Knowledge {
		if doc.DocType == 2 {
			docs = append(docs, doc)
		}
	}
	bs.IntentCate = model.KnowledgeQAIntent
	bs.Knowledge = docs
	bs.PromptCtx.Docs = docs
	_, _, _, _ = botReply(ctx, bs)
	return nil
}
