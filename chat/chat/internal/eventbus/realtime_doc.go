package eventbus

import (
	"context"
	"errors"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/utils"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
)

// 文档问答ppl

// RealtimeDocProcess 实时文档问答ppl
func RealtimeDocProcess(ctx context.Context, bs *botsession.BotSession) (isReject bool, err error) {
	defer func() {
		log.InfoContextf(ctx, "RealtimeDocProcess isReject: %v,err: %+v", isReject, err)
	}()
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessageNonGeneralKnowledge)
	m1 := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeRealTimeDoc)
	m.Prompt = m1.GetPrompt()
	log.DebugContextf(ctx, "get real_time_doc, prompt:%s", m1.GetPrompt())
	m.PromptLimit = getPromptLimit(ctx, bs, m.GetModelName())
	isSingleDoc := isRealtimeSingleDoc(bs)
	isTruncated := false // 文档是否被截断
	if isSingleDoc {     // 单文档
		var file model.FileInfo
		if len(bs.FileInfos) == 1 {
			file = *bs.FileInfos[0]
		} else if len(bs.FileQueue) > 0 {
			file.DocID = bs.FileQueue[len(bs.FileQueue)-1].DocID
		}
		if file.DocID == "" {
			log.Warn(ctx, "file empty")
			return false, errors.New("file empty")
		}
		// 先获取单文档全文
		fileInfo, err := bus.retrieval.GetDocFullText(ctx, bs, file, int64(m.PromptLimit))
		if err != nil {
			return false, err
		}
		// 全文送阅读理解
		isReject, err = singleDocFullTextReply(ctx, bs, m, fileInfo)
		if err != nil {
			return false, err
		}
		isTruncated = fileInfo.IsTruncated
	}
	if !isSingleDoc || (isReject && isTruncated) { // 多文档 或者 单文档被拒答且文档有截断，需要重新检索再走阅读理解
		isReject, err = docSearchReply(ctx, bs)
		if err != nil {
			return false, err
		}
	}
	return isReject, nil
}

// singleDocFullTextReply 单文档全文阅读理解
func singleDocFullTextReply(ctx context.Context,
	bs *botsession.BotSession, m *model.AppModel, file *model.FileInfo) (bool, error) {
	sysRole := m.GetSysPrompt(true, false, bs.SystemRole)
	docs := make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0)
	docs = append(docs, &knowledge.SearchKnowledgeRsp_SearchRsp_Doc{
		DocType: 2,
		OrgData: fmt.Sprintf("文档名：%s\n文档片段：%s", file.FileName, file.FullText),
	})
	promptCtx := bs.PromptCtx
	promptCtx.Docs = docs
	prompt, err := bus.dao.TextTruncate(ctx, m, promptCtx) // 生成Prompt
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedureFile))
	SendTokenStat(ctx, bs, bs.TokenStat)
	if err != nil {
		return false, err
	}
	bs.Flags.IsJudgeModelReject = true // 需要用拒答prompt
	message := m.WrapMessages(sysRole, bs.LLMHisMessages, prompt)
	inferParams := m.CreateModelParamsObject(bs.EnableRandomSeed)
	req := m.NewLLMRequestWithModelParams(bs.LLMRequestID, message, inferParams)
	log.InfoContextf(ctx, "singleDocFullTextReply|NewLLMRequest req: %s", utils.Any2String(req))
	startTime, last, lastEvil, isModelRejected, err := streamReply(ctx, bs, req,
		event.ProcedureFile, model.ReplyMethodFile, bs.LLMHisMessages)
	if err != nil {
		return false, err
	}
	if !isModelRejected && last != nil && last.Message != nil {
		checkLastAndCreateRecord(ctx, bs, model.ReplyMethodFile, last, lastEvil, req, startTime)
	}
	return isModelRejected, nil
}

// docSearchReply 文档检索回复
func docSearchReply(ctx context.Context, bs *botsession.BotSession) (bool, error) {
	docs, err := bus.retrieval.SearchMultiDoc(ctx, bs, bs.FileInfos)
	if err != nil {
		return false, err
	}
	bs.Knowledge = docs
	reply, err := docReply(ctx, bs)
	if err != nil {
		return false, err
	}
	if reply == "" {
		return true, nil
	}
	return false, nil
}

// isRealtimeSingleDoc 是否单文档
func isRealtimeSingleDoc(bs *botsession.BotSession) bool {
	if !bs.Flags.IsRealTimeDocument {
		return false
	}
	if len(bs.FileInfos) == 1 {
		return true
	}
	// 是不是在实时文档对话中，且是单文档
	if len(bs.FileQueue) > 0 {
		// 取最近一轮的文件，看是否是单文档
		round := bs.FileQueue[len(bs.FileQueue)-1].Round
		fileCount := 0
		for _, file := range bs.FileQueue {
			if file.Round == round {
				fileCount++
			}
		}
		if fileCount == 1 {
			return true
		}
	}
	return false
}
