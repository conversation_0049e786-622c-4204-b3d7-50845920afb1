package eventbus

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	pb "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/utils"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"github.com/google/uuid"
)

// GetAnswerFromKnowledgeImpl 获取知识库答案
func GetAnswerFromKnowledgeImpl(ctx context.Context, request *pb.GetAnswerFromKnowledgeRequest,
	stopSignal chan struct{}) (chan *pb.GetAnswerFromKnowledgeReply, error) {
	log.DebugContextf(ctx, "GetAnswerFromKnowledgeImpl, req: %s", helper.Object2String(request))
	chReply := make(chan *pb.GetAnswerFromKnowledgeReply, 10)

	// step 1: 查询APP
	scene := dao.AppTestScene
	if request.FilterKey == pb.Filter_FILTER_PRODUCTION {
		scene = dao.AppReleaseScene
	}
	app, err := bus.dao.GetAppByBizID(ctx, scene, request.RobotID)
	if err != nil || app == nil {
		log.ErrorContextf(ctx, "GetAppByBizID failed, error: %v", err)
		return nil, pkg.ErrRobotNotExist
	}

	// step 2: 检索
	docs, placeholders, err := SearchKnowledge(ctx, request, app)
	if err != nil {
		log.ErrorContextf(ctx, "SearchDocs failed, error: %v", err)
		return nil, err
	}
	if len(docs) == 0 {
		reply := &pb.GetAnswerFromKnowledgeReply{
			Message: &pb.LLMMessage{
				Role:    pb.LLMRole_NONE,
				Content: "知识库中未检索到相关结果",
			},
			Finished: true,
		}
		chReply <- reply
		defer close(chReply)
		return chReply, nil
	}
	// step 3: 生成Prompt
	promptCtx := &botsession.PromptCtx{}
	promptCtx.Docs = docs
	promptCtx.Question = request.Question
	m := app.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessage)
	m.ModelName = request.GetModelName()
	m.PromptLimit = bus.dao.GetModelPromptLimit(ctx, m.GetModelName()) - len([]rune(app.RoleDescription()))
	prompt, err := bus.dao.TextTruncate(ctx, m, promptCtx) // 生成Prompt
	log.InfoContextf(ctx, "RenderPrompt, prompt: %s", prompt)
	if err != nil {
		return nil, err
	}
	//  step 3: 构造请求
	requestID := model.RequestID(ctx, request.SessionID, encode.GenerateUUID()) // 非流式调用
	message := m.WrapMessages("", nil, prompt)
	reqLLM := m.NewLLMRequest(requestID, message)
	// step 4: 请求LLM
	go bus.dao.UpdateWorkflowPrompt(ctx, request.RobotID,
		request.SessionID, helper.Object2StringEscapeHTML(reqLLM)) // 更新工作流prompt
	GetAnswerFromKnowledgeLLM(ctx, reqLLM, chReply, request, docs, placeholders, stopSignal)

	return chReply, nil
}

// SearchKnowledge 检索知识库
func SearchKnowledge(ctx context.Context, req *pb.GetAnswerFromKnowledgeRequest, app *model.App) (
	[]*bot_knowledge_config_server.MatchReferReq_Doc, map[string]string, error) {
	docs := make([]*bot_knowledge_config_server.MatchReferReq_Doc, 0)
	log.DebugContextf(ctx, "SearchKnowledge, req: %s", helper.Object2String(req))
	bs := &botsession.BotSession{}
	var filters []*knowledge.WorkflowSearchExtraParam_Filter
	for _, filter := range req.GetWorkflowSearchExtraParam().GetFilters() {
		filters = append(filters, &knowledge.WorkflowSearchExtraParam_Filter{
			DocType:    filter.GetDocType(),
			Confidence: filter.GetConfidence(),
			TopN:       filter.GetTopN(),
		})
	}
	workflowSearchStrategy := req.GetWorkflowSearchExtraParam().GetSearchStrategy()
	searchStrategy := &knowledge.SearchStrategy{
		StrategyType:     knowledge.SearchStrategyTypeEnum(workflowSearchStrategy.GetStrategyType()),
		TableEnhancement: workflowSearchStrategy.GetTableEnhancement(),
	}

	bs.WorkflowSearchExtraParam = &knowledge.WorkflowSearchExtraParam{
		Filters:          filters,
		TopN:             req.GetWorkflowSearchExtraParam().GetTopN(),
		LabelLogicOpr:    knowledge.LogicOpr(req.GetWorkflowSearchExtraParam().GetLabelLogicOpr()),
		IsLabelOrGeneral: req.GetWorkflowSearchExtraParam().GetIsLabelOrGeneral(),
		SearchStrategy:   searchStrategy,
	}

	bs.Knowledge = make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0) // 存储结果
	var labels []*knowledge.VectorLabel
	for _, label := range req.GetLabels() {
		labels = append(labels, &knowledge.VectorLabel{
			Name:   label.GetName(),
			Values: label.GetValues(),
		})
	}
	reqWorkflow := &knowledge.SearchKnowledgeReq_SearchReq{
		BotBizId:                 app.GetAppBizId(),
		Question:                 req.GetQuestion(),
		UsePlaceholder:           app.CanUsePlaceholder(),
		ImageUrls:                bs.Images,
		WorkflowSearchExtraParam: bs.WorkflowSearchExtraParam,
		Labels:                   labels,
	}
	scene := knowledge.SceneType_PROD
	if req.FilterKey == pb.Filter_FILTER_SANDBOX {
		scene = knowledge.SceneType_TEST
	}

	rsp, _ := bus.dao.SearchWorkflow(ctx, reqWorkflow, scene)
	bs.Knowledge = rsp

	for _, doc := range rsp {
		similarQuestionParams := &bot_knowledge_config_server.SimilarQuestionExtra{
			SimilarId:       doc.GetSimilarQuestionExtra().GetSimilarId(),
			SimilarQuestion: doc.GetSimilarQuestionExtra().GetSimilarQuestion(),
		}
		docs = append(docs, &bot_knowledge_config_server.MatchReferReq_Doc{
			DocId:                doc.GetDocId(),
			DocType:              doc.GetDocType(),
			RelatedId:            doc.GetRelatedId(),
			Question:             doc.GetQuestion(),
			Answer:               doc.GetAnswer(),
			OrgData:              doc.GetOrgData(),
			IsBigData:            doc.GetIsBigData(),
			SimilarQuestionExtra: similarQuestionParams,
			SheetInfo:            doc.GetSheetInfo(),
		})
	}

	placeholders := utils.GetPlaceHolders(bs.Knowledge)
	log.DebugContextf(ctx, "SearchDocs, req: %s, docs: %s", req, docs)
	return docs, placeholders, nil
}

// GetAnswerFromKnowledgeLLM 请求LLM
func GetAnswerFromKnowledgeLLM(ctx context.Context, reqLLM *llmm.Request,
	chReply chan *pb.GetAnswerFromKnowledgeReply, request *pb.GetAnswerFromKnowledgeRequest,
	docs []*bot_knowledge_config_server.MatchReferReq_Doc, placeholders map[string]string, stopSignal chan struct{}) {
	ch := make(chan *llmm.Response, 100)
	llmCtx, cancel := context.WithCancel(ctx)
	go func() {
		defer errors.PanicHandler()
		err := bus.dao.Chat(llmCtx, reqLLM, ch, time.Now(), nil) // todo: 耗时统计
		if err != nil {
			log.ErrorContextf(llmCtx, "Chat error: %v", err)
		}
		log.DebugContextf(llmCtx, "Chat finish")
	}()
	var reply *pb.GetAnswerFromKnowledgeReply
	go func() {
		defer errors.PanicHandler()
		defer func() {
			chReply <- reply
			close(chReply)
		}()
		for {
			reply = &pb.GetAnswerFromKnowledgeReply{}
			select {
			case <-ctx.Done():
				log.InfoContextf(ctx, "GetAnswerFromKnowledgeLLM, ctx done")
				reply.Finished = true
				return
			case <-stopSignal:
				log.InfoContextf(ctx, "GetAnswerFromKnowledgeLLM|stopSignal|return")
				reply.Code = -1
				reply.ErrMsg = "canceled"
				reply.Finished = true
				cancel()
				return
			case llmRsp, ok := <-ch:
				if !ok || llmRsp == nil {
					log.DebugContextf(ctx, "GetAnswerFromKnowledgeLLM, llmRsp is nil")
					reply.Code = -1
					reply.ErrMsg = "llmRsp error"
					reply.Finished = true
					return
				}
				reply.Code = llmRsp.Code
				reply.ErrMsg = llmRsp.ErrMsg
				reply.RequestID = llmRsp.GetRequestId()
				llmRsp.Message.Content = replacePlaceholders(llmRsp.Message.Content, placeholders)

				content := helper.RemoveReference(request.RobotID, llmRsp.Message.Content)
				content, _, _ = helper.MatchSearchResults(ctx, content) // 去掉知识库问答引用
				reply.Message = &pb.LLMMessage{
					Role:    pb.LLMRole(llmRsp.Message.Role),
					Content: content,
					Thought: llmRsp.GetMessage().GetReasoningContent(),
				}
				reply.Finished = llmRsp.Finished
				if llmRsp.StatisticInfo != nil {
					reply.StatisticInfo = &pb.StatisticInfo{
						FirstTokenCost: llmRsp.StatisticInfo.FirstTokenCost,
						TotalCost:      llmRsp.StatisticInfo.TotalCost,
						InputTokens:    llmRsp.StatisticInfo.InputTokens,
						OutputTokens:   llmRsp.StatisticInfo.OutputTokens,
						TotalTokens:    llmRsp.StatisticInfo.TotalTokens,
					}
				} else {
					log.WarnContextf(ctx, "llmRsp.StatisticInfo is empty, llmRsp: %v", llmRsp)
				}
				if reply.Finished {
					reply.References = processDMReferenceToWorkFlow(ctx, reply.Message.GetContent(), docs, request)
					log.InfoContextf(ctx, "MatchRefer finish. Final reply: %s", helper.Object2String(reply))
					return
				}
				chReply <- reply // 避免2次final
			}
		}
	}()
}

func processDMReferenceToWorkFlow(ctx context.Context, reply string,
	docs []*bot_knowledge_config_server.MatchReferReq_Doc,
	request *pb.GetAnswerFromKnowledgeRequest) (refer []*pb.Reference) {
	if len(docs) < 1 {
		log.InfoContextf(ctx, "SearchDocs finish, docs is empty")
		return refer
	}

	referIndex := helper.GetAllSubMatch(request.RobotID, reply)
	mDocs := make([]*bot_knowledge_config_server.MatchReferReq_Doc, 0)
	disableMatch := false
	if len(referIndex) > 0 {
		disableMatch = true
		for _, index := range referIndex {
			if index > len(docs) {
				log.WarnContextf(ctx, "index out of range, index: %d, knowledge: %v", index, docs)
				continue
			}
			doc := docs[index-1] // index从1开始
			mDocs = append(mDocs, &bot_knowledge_config_server.MatchReferReq_Doc{
				DocId:     doc.GetDocId(),
				DocType:   doc.GetDocType(),
				RelatedId: doc.GetRelatedId(),
				Question:  doc.GetQuestion(),
				Answer:    doc.GetAnswer(),
				OrgData:   doc.GetOrgData(),
				IsBigData: doc.GetIsBigData(),
				SheetInfo: doc.GetSheetInfo(),
			})
		}
	} else {
		mDocs = docs
	}
	// MatchRefer 匹配参考来源
	refers, err := bus.dao.MatchRefer(ctx, &bot_knowledge_config_server.MatchReferReq{
		BotBizId:              request.RobotID,
		Docs:                  mDocs,
		Answer:                reply,
		IsRelease:             request.FilterKey == pb.Filter_FILTER_PRODUCTION,
		MsgId:                 uuid.NewString(),
		Question:              request.Question,
		IgnoreConfidenceScore: disableMatch,
	})
	if err != nil {
		log.ErrorContextf(ctx, "MatchRefer failed, error: %v", err)
	} else {
		refer = ToReplyReference(refers)
	}
	return refer
}
