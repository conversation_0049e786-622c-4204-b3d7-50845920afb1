package eventbus

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	parse "git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
)

// processDocSummary 处理文档摘要
func processDocSummary(ctx context.Context, bs *botsession.BotSession, useStream bool) (docSummary string, err error) {
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedureFile))
	SendTokenStat(ctx, bs, bs.TokenStat)
	cfg := config.App().Bot
	ch := make(chan *parse.GetDocSummaryRsp, cfg.ResponseChannelSize)
	g, gctx := errgroupx.WithContext(ctx)
	parseCtc, cancel := context.WithCancel(ctx)
	var lastEvil *infosec.CheckRsp
	var last *parse.GetDocSummaryRsp
	var isTimeout bool
	// 构造请求体
	parseReq := wrapSummaryRequest(ctx, bs)
	pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.LLMReply)
	g.Go(func() error {
		if err := bus.dao.GetDocSummary(parseCtc, parseReq, ch); !errors.Is(err, context.Canceled) {
			return err
		}
		return nil
	})
	g.Go(func() error {
		if useStream {
			bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedureLLM))
			SendTokenStat(ctx, bs, bs.TokenStat)
			last, lastEvil, isTimeout = streamSummaryReply(gctx, cancel, bs, ch)
			log.DebugContextf(ctx, "I|DocSummaryEventHandler|Process|lastEvil: %+v", lastEvil)
			isEvil := lastEvil.GetResultCode() == ispkg.ResultEvil
			if last != nil && (!last.GetIsFinal() || isEvil) { // 最后一个包含敏感词也需要兜底结束
				last.IsFinal = true // 发送回复
				_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, NewReplyEvent(*bs, last, isEvil, model.ReplyMethodFile), cancel)
			}
			reply := last.GetDocSummary()
			method := helper.When(isEvil, model.ReplyMethodEvil, model.ReplyMethodFile)
			newReq := &llmm.Request{} // prompt
			m0 := bs.NewBotRecord(ctx, reply, newReq, method, lastEvil, time.Now())
			newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
			_, err = bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
			clues.AddTrackDataWithError(ctx, "dao.CreateMsgRecord", m0, err)
			if !isEvil {
				processRealTimeFileHistory(ctx, bs)
			}
		} else {
			last, lastEvil, isTimeout = getSummaryReply(gctx, cancel, ch)
			if bs.NeedCheck { // 安全审核
				checkCode, checkType := bus.dao.CheckTextEvil(ctx, bs.App.GetAppBizId(), bs.App.CorpId, bs.RecordID,
					last.GetDocSummary(),
					bs.App.GetInfosecBizType())
				if checkCode == ispkg.ResultEvil {
					lastEvil = &infosec.CheckRsp{
						ResultCode: checkCode,
						ResultType: checkType,
					}
				} else {
					processRealTimeFileHistory(ctx, bs) // 非流式输出的场景，是文件和图片同时传入的情况下，在这里处理历史记录，外面不再处理。
				}
			}
		}
		return helper.When(isTimeout, pkg.ErrLLMTimeout, nil)
	})
	if err := g.Wait(); err != nil {
		log.ErrorContextf(ctx, "processSummary: GetDocSummary error: %v", err)
		bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(event.ProcedureFile))
		SendTokenStat(ctx, bs, bs.TokenStat)
		return "", err
	}
	d := event.ProcedureDebugging{} // @halelv 调试信息 2.4.0不涉及
	p := event.NewSuccessTSProcedure(event.ProcedureFile, convertToLLMStatisticInfo(last.GetStatisticInfos()), d, nil)
	bs.TokenStat.UpdateSuccessProcedure(p)
	SendTokenStat(ctx, bs, bs.TokenStat)
	return last.GetDocSummary(), nil
}

// wrapSummaryRequest 包装摘要请求
func wrapSummaryRequest(ctx context.Context, bs *botsession.BotSession) *parse.GetDocSummaryReq {
	fileInfos := make([]*parse.FileInfo, 0)

	for _, fileInfo := range bs.FileInfos { // 有文件，用当前文件
		if fileInfo == nil {
			continue
		}
		fileInfos = append(fileInfos, &parse.FileInfo{
			DocId:    helper.GetUint64FromString(fileInfo.DocID),
			FileName: fileInfo.FileName,
		})
	}
	if len(fileInfos) == 0 { // 无文件 查历史记录。
		for _, fileInfo := range bs.FileQueue {
			if fileInfo == nil {
				continue
			}
			fileInfos = append(fileInfos, &parse.FileInfo{
				DocId:    helper.GetUint64FromString(fileInfo.DocID),
				FileName: fileInfo.FileName,
			})
		}
	}
	fileNamesInQuery := helper.GetFileName(bs.PromptCtx.Question)
	if len(fileNamesInQuery) > 0 { // 这里Query改写以后带有文件名，需要过滤下
		tmp := make([]*parse.FileInfo, 0)
		for _, fileInfo := range fileInfos {
			if fileInfo == nil {
				continue
			}
			if helper.Contains(fileNamesInQuery, fileInfo.FileName) {
				tmp = append(tmp, fileInfo)
			}
		}
		if len(tmp) > 0 {
			fileInfos = tmp // 覆盖
		}
	}

	parseReq := &parse.GetDocSummaryReq{
		BotBizId:    bs.App.AppBizId,
		RequestId:   bs.RequestID,
		SessionId:   bs.Session.SessionID,
		FileInfos:   fileInfos,
		ModelName:   bs.App.GetModelName(),
		PromptLimit: uint32(bus.dao.GetModelPromptLimit(ctx, bs.App.GetModelName())),
		Query:       bs.PromptCtx.Question,
	}
	return parseReq
}

// getSummaryReply 获取摘要
func getSummaryReply(ctx context.Context, cancel context.CancelFunc,
	ch chan *parse.GetDocSummaryRsp) (last *parse.GetDocSummaryRsp, lastEvil *infosec.CheckRsp, isTimeout bool) {

	cfg := config.App().Bot
	ticker := time.NewTicker(time.Duration(cfg.StopGeneration.CheckInterval) * time.Millisecond)
	timeout := time.NewTimer(time.Duration(cfg.Timeout) * time.Second)
	clues.AddTrackData(ctx, "stream().vars", clues.M{"StopGeneration": cfg.StopGeneration, "Timeout": cfg.Timeout})
	defer ticker.Stop()
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			cancel()
			log.ErrorContext(ctx, "doc summary timeout")
			return last, lastEvil, true
		case rsp, ok := <-ch:
			timeout.Stop()
			if !ok || rsp == nil {
				return last, lastEvil, false
			}
			last = rsp
			if last.GetIsFinal() {
				return last, lastEvil, false
			}
		}
	}
}

// streamSummaryReply 流式处理文档摘要回复
func streamSummaryReply(ctx context.Context, cancel context.CancelFunc, bs *botsession.BotSession,
	ch chan *parse.GetDocSummaryRsp) (last *parse.GetDocSummaryRsp, lastEvil *infosec.CheckRsp, isTimeout bool) {
	cfg := config.App().Bot
	ticker := time.NewTicker(time.Duration(cfg.StopGeneration.CheckInterval) * time.Millisecond)
	timeout := time.NewTimer(time.Duration(cfg.Timeout) * time.Second)
	clues.AddTrackData(ctx, "stream().vars", clues.M{"StopGeneration": cfg.StopGeneration, "Timeout": cfg.Timeout})
	defer ticker.Stop()
	defer timeout.Stop()
	firstThought := false
	finalThought := false
	uin := pkg.Uin(ctx)
	mainModelName := bs.App.GetModelName()
	intentionCategory := bs.IntentCate
	statistics := pkg.GetStatistics(ctx)
	throttles := cfg.Throttles
	throttleCheck := helper.NewThrottle(throttles.Check)
	throttleThought := helper.NewThrottle(helper.When(bs.StreamingThrottle > 0,
		bs.StreamingThrottle, throttles.Streaming))
	throttleStreaming := helper.NewThrottle(helper.When(bs.StreamingThrottle > 0,
		bs.StreamingThrottle, throttles.Streaming))
	if statistics != nil {
		statistics.MainModel = mainModelName
		statistics.IntentionCategory = intentionCategory
	}
	for {
		select {
		case <-timeout.C:
			cancel()
			log.ErrorContext(ctx, "doc summary timeout")
			bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(event.ProcedureFile))
			SendTokenStat(ctx, bs, bs.TokenStat)
			return last, lastEvil, true
		case <-ticker.C:
			if ok, _ := bus.dao.IsGenerationStopped(ctx, bs.To.CorpStaffID, bs.TokenStat.RecordID); ok {
				last.IsFinal = true
				re := NewReplyEvent(*bs, last, false, model.ReplyMethodFile)
				_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
				return last, lastEvil, false
			}
		case rsp, ok := <-ch:
			timeout.Stop()
			if !ok || rsp == nil {
				d := event.ProcedureDebugging{} // @halelv 调试信息 2.4.0不涉及
				p := event.NewSuccessTSProcedure(event.ProcedureFile, nil, d, nil)
				bs.TokenStat.UpdateSuccessProcedure(p)
				SendTokenStat(ctx, bs, bs.TokenStat)
				return last, lastEvil, false
			}
			last = rsp
			l := len([]rune(last.GetDocSummary()))
			isFirstReply := throttleCheck.IsFirstReply()
			if bs.NeedCheck && throttleCheck.Hit(l, rsp.GetIsFinal()) { // 安全审核
				checkCode, checkType := bus.dao.CheckTextEvil(ctx, bs.App.GetAppBizId(),
					bs.App.CorpId, bs.RecordID, last.DocSummary, bs.App.GetInfosecBizType())
				if checkCode == ispkg.ResultEvil {
					lastEvil = &infosec.CheckRsp{
						ResultCode: checkCode,
						ResultType: checkType,
					}
					last.IsFinal = true
					return last, lastEvil, false
				}
			}
			if !firstThought && rsp.GetReasoningContent() != "" {
				log.InfoContextf(ctx, "doc summary thought first")
				addThoughtProcedure(ctx, bs)
				firstThought = true
			}
			var isEvil bool
			if rsp.GetDocSummary() == "" && rsp.GetReasoningContent() != "" { // 需要输出思考过程
				lastEvil, isEvil = thoughtEventReply(ctx, bs, throttleCheck, throttleThought, rsp.GetReasoningContent(),
					time.Now(), func() {}) // 如果传入cancel，会给前端返回一个error事件
				if isEvil {
					last.IsFinal = true
					last.DocSummary = config.App().Bot.EvilReply // evil返回时需要DocSummary不为空
					return last, lastEvil, false
				}
				continue
			}
			if !finalThought && rsp.GetReasoningContent() != "" { // 思维链最后一包
				log.InfoContextf(ctx, "doc summary thought final")
				lastEvil, isEvil = thoughtEventFinalReply(ctx, time.Now(), bs, rsp.GetReasoningContent(), func() {})
				if isEvil {
					last.IsFinal = true
					last.DocSummary = config.App().Bot.EvilReply // evil返回时需要DocSummary不为空
					return last, lastEvil, false
				}
				finalThought = true
			}
			if throttleStreaming.Hit(len([]rune(rsp.GetDocSummary())), rsp.GetIsFinal()) {
				// 发送回复
				re := NewReplyEvent(*bs, rsp, false, model.ReplyMethodFile)
				// 统计两次，分别是首包耗时和尾包耗时
				if isFirstReply {
					answerMetrics(ctx, metricNameFirstLLMReply, mainModelName, intentionCategory, nil, uin)
				} else if last.GetIsFinal() {
					answerMetrics(ctx, metricNameFinalLLMReply, mainModelName, intentionCategory, nil, uin)
				}
				_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
			}
		}
	}
}

// convertToLLMStatisticInfo 转换为LLM统计信息
func convertToLLMStatisticInfo(origin []*parse.StatisticInfo) (res *llmm.StatisticInfo) {
	res = &llmm.StatisticInfo{}
	for _, v := range origin {
		res.FirstTokenCost += v.GetFirstTokenCost() // 这里应该加和还是取max？
		res.TotalCost += v.GetTotalCost()
		res.InputTokens += v.InputTokens
		res.OutputTokens += v.OutputTokens
		res.TotalTokens += v.TotalTokens
	}
	return
}

// NewReplyEvent 生成回复事件
func NewReplyEvent(
	bs botsession.BotSession, rsp *parse.GetDocSummaryRsp, isEvil bool, replyMethod model.ReplyMethod,
) *event.ReplyEvent {
	reply := rsp.GetDocSummary()
	replyMethod = helper.When(isEvil, model.ReplyMethodEvil, replyMethod)
	r := &event.ReplyEvent{
		RequestID:       bs.RequestID,
		SessionID:       bs.Session.SessionID,
		Content:         helper.When(isEvil, config.App().Bot.EvilReply, reply),
		FromName:        bs.App.GetBaseConfig().GetName(),
		FromAvatar:      bs.App.GetBaseConfig().GetAvatar(),
		RecordID:        bs.RecordID,
		RelatedRecordID: bs.RelatedRecordID,
		Timestamp:       time.Now().Unix(),
		IsFinal:         rsp.GetIsFinal(),
		IsFromSelf:      false,
		CanRating:       false,
		CanFeedback:     false,
		IsEvil:          isEvil,
		IsLLMGenerated:  true,
		ReplyMethod:     replyMethod,
		IntentCategory:  "文档摘要",
		FileInfos:       bs.FileInfos,
	}
	return r
}

// processRealTimeFileHistory 处理实时文档历史
func processRealTimeFileHistory(ctx context.Context, bs *botsession.BotSession) {

	newFileQueues := bus.memory.GetNewRealTimeFileQueues(bs.FileInfos)
	// 读取历史文件记录
	history, err := bus.dao.GetRealTimeFileHistory(ctx, bs.Session.SessionID)
	if err != nil || len(history) == 0 {
		b, _ := json.Marshal(newFileQueues)
		_ = bus.dao.SetRealTimeFileHistory(ctx, bs.Session.SessionID, string(b))
		return

	}
	oldFileQueues := make([]*model.FileQueue, 0) // 读取历史记录
	_ = json.Unmarshal([]byte(history), &oldFileQueues)
	finalFileQueues := make([]*model.FileQueue, 0) // 合并得到最终的记录
	// Round+1 并保存,去掉超过上限的记录
	for _, file := range oldFileQueues {
		file.Round++
		if file.Round <= int(config.App().MultiModal.HistoryLimit) {
			finalFileQueues = append(finalFileQueues, file)
		}
	}

	finalFileQueues = append(finalFileQueues, newFileQueues...)
	b, _ := json.Marshal(finalFileQueues)
	_ = bus.dao.SetRealTimeFileHistory(ctx, bs.Session.SessionID, string(b))
}
