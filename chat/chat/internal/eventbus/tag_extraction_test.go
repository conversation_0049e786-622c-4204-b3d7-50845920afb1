// chat
//
// @(#)tag_extraction_test.go  Wednesday, March 13, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package eventbus

import (
	"context"
	"fmt"
	"testing"

	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

func TestSplitPromptCtx(t *testing.T) {
	tags := []*admin.ClassifyLabel{
		{
			Name:        "城市",
			Description: "地区名字",
			Values: []string{
				"西宁市", "海东市", "海北藏族自治州", "黄南藏族自治州", "海南藏族自治州", "果洛藏族自治州",
				"玉树藏族自治州", "海西蒙古族藏族自治州",
			},
		},
		{
			Name:        "年龄, 请说出你的出生年月, 4位数字, 公元纪年法",
			Description: "出生年月",
			Values:      []string{"2000", "2001", "2002", "2003", "2000", "2001", "2002", "2003"},
		},
		{
			Name:        "国家",
			Description: "出生年月",
			Values:      []string{"中国", "英"},
		},
		{
			Name:        "城市",
			Description: "地区名字",
			Values: []string{
				"兰州", "嘉峪关市",
				"临夏回族自治州", "甘南藏族自治州",
			},
		},
		{
			Name:        "国家",
			Description: "出月",
			Values:      []string{"a", "中国", "英国"},
		},
		{
			Name:        "英文",
			Description: "字母",
			Values:      []string{"a", "b", ""},
		},
	}
	r := splitPromptCtx(context.Background(), "this is a query string", tags, 40)
	fmt.Println("len", len(r))
	for i := range r {
		fmt.Printf("%d, %+v\n", i, r[i].Tags)
	}
}
