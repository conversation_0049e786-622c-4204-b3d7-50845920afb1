package eventbus

import (
	"context"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	knowledgepb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
)

// initTokenStat 初始化token统计
func initTokenStat(ctx context.Context, bs *botsession.BotSession) error {
	ts := &event.TokenStatEvent{
		SessionID:         bs.Session.SessionID,
		RequestID:         bs.RequestID,
		RecordID:          bs.RecordID, // 这个用于answer的recordID
		StartTime:         time.Now(),
		EventSource:       bs.EventSource,
		FinanceSubBizType: bs.GetFinanceSubBizType(),
	}
	ts.MainModelName = bs.App.GetMainModelName()
	ts.OrderCount = 1
	// ts.InitTokenBalance(ctx, bus.dao, bs.App.GetCorpId(), "KnowledgeQA", bs.App.GetMainModelName())
	// clues.AddTrackData(ctx, "answer:record:id", ts.RecordID)
	bs.TokenStat = ts
	// if ts.OrderCount == 0 {
	//	return pkg.ErrNoBalance
	// }
	return nil
}

// needSpecialProcess 特殊处理
func needSpecialProcess(ctx context.Context, bs *botsession.BotSession) (needReturn bool) {
	defer func() {
		log.InfoContextf(ctx, "needReturn:%v, IsMultiModal:%v, IsRealTimeDocument:%v", needReturn,
			bs.Flags.IsMultiModal,
			bs.Flags.IsRealTimeDocument)
	}()
	query := bs.OriginContent
	log.InfoContextf(ctx, "query: %s", query)
	if len(bs.FileInfos) > 0 {
		_ = bus.dao.SetFileKnowledgeStatus(ctx, bs.Session.SessionID, true)
		bs.Flags.IsRealTimeDocument = true // 通过变量保存一下，不要每次都去查缓存
	} else if bus.dao.IsInFileKnowledge(ctx, bs.Session.SessionID, bs.App.GetHistoryLimit()) &&
		bs.App.GetKnowledgeQa().GetRealtimeDocContext() { // 在上下文轮次里面且开启了实时文档上下文影响
		bs.Flags.IsRealTimeDocument = true
	}
	if helper.IsQueryContainsImage(query) {
		_ = bus.dao.SetMultiModalStatus(ctx, bs.Session.SessionID, true)
		bs.Flags.IsMultiModal = true
	} else if bus.dao.IsInMultiModal(ctx, bs.Session.SessionID, bs.App.GetHistoryLimit()) {
		bs.Flags.IsMultiModal = true
	}
	// 只有图片和文档
	if len(bs.FileInfos) != 0 && helper.IsQueryOnlyImage(bs.OriginContent) {
		needReturn = true
		return needReturn
	}
	// 图片+文本+文档
	if len(bs.FileInfos) > 0 && helper.IsQueryContainsImage(query) {
		if bs.App.GetKnowledgeQa().GetImageTextRetrieval() { // 开启图文检索时【才走特殊逻辑】
			return true
		} else { // 没有开启图文检索，直接丢弃图片，只用文本和文档
			log.InfoContextf(ctx, "image and text, drop image")
			for _, image := range helper.GetAllImage(bs.OriginContent) { // 丢弃query中的图片
				query = strings.Replace(query, image, "", 1)
			}
			bs.Images = nil
		}
	}
	// 纯文档
	if len(query) == 0 {
		needReturn = true
	}
	return needReturn
}

// canUseTaskFlow 判断是否可以使用任务流 env : 0 为测试环境 1 为正式环境
func canUseTaskFlow(ctx context.Context, bs *botsession.BotSession) {
	if bs.App.GetKnowledgeQa().GetWorkflow().GetIsEnabled() {
		return // 新版工作流开启，不走该流程
	}
	// 开启任务流程 && 不包含文件 && 不包含图片
	useTask := bs.App.GetKnowledgeQa().GetCanUseTaskFlow() && len(bs.FileInfos) < 1 &&
		!helper.IsQueryContainsImage(bs.OriginContent)
	if useTask {
		useTask, _ = bus.dao.IsTaskRelease(ctx, bs.App.AppBizId, uint32(bs.Env)) // 检查是否发布
	}
	canUseTask := map[string]any{
		"AppBizId":       bs.App.AppBizId,
		"CanUseTaskFlow": bs.App.KnowledgeQa.CanUseTaskFlow,
		"useTask":        useTask,
	}
	clues.AddTrackData(ctx, "exec.useTask", canUseTask)
	bs.Flags.CanUseTask = useTask
}

func matchRefer(ctx context.Context, bs *botsession.BotSession,
	output string, placeholders map[string]string) {
	if len(output) < 1 || len(bs.Knowledge) == 0 {
		return // 文档为空，或者Output为空（走搜索引擎等）等场景，不做参考来源计算
	}
	mDocs := make([]*knowledgepb.MatchReferReq_Doc, 0)
	disableMatch := false
	if len(bs.ReferIndex) > 0 {
		disableMatch = true
		for _, index := range bs.ReferIndex {
			if index > len(bs.Knowledge) || index <= 0 {
				log.WarnContextf(ctx, "index out of range, index: %d, knowledge: %v", index, bs.Knowledge)
				continue
			}
			doc := bs.Knowledge[index-1] // index从1开始
			mDocs = append(mDocs, &knowledgepb.MatchReferReq_Doc{
				DocId:     doc.GetDocId(),
				DocType:   doc.GetDocType(),
				RelatedId: doc.GetRelatedId(),
				Question:  doc.GetQuestion(),
				Answer:    doc.GetAnswer(),
				OrgData:   doc.GetOrgData(),
				IsBigData: doc.GetIsBigData(),
				SheetInfo: doc.GetSheetInfo(),
			})
		}
	} else {
		if bs.KnowledgeTruncatedIndex > 0 && bs.KnowledgeTruncatedIndex <= len(bs.Knowledge) {
			bs.Knowledge = bs.Knowledge[:bs.KnowledgeTruncatedIndex]
		}
		mDocs = helper.Map(bs.Knowledge, toMatchReferDoc)
	}
	ev := event.ReferToEvent{
		Answer:       output,
		Question:     bs.PromptCtx.Question,
		RecordID:     bs.RecordID,
		BotBizID:     bs.App.GetAppBizId(),
		Docs:         mDocs,
		Placeholders: placeholders,
		DisableMatch: disableMatch, // 不计算相似度，直接获取Reference相关信息
		IsRelease:    bs.Type == model.RecordTypeMessage,
	}
	if bs.Type == model.RecordTypeOPDebug {
		ev.IsRelease = bs.Scene == dao.AppReleaseScene
	}
	err := Push(ctx, bs.To, event.EventReferTo, ev)
	if err != nil {
		log.ErrorContextf(ctx, "match refer fail, err: %v", err)
	}
}

func toMatchReferDoc(doc *knowledge.SearchKnowledgeRsp_SearchRsp_Doc) *knowledgepb.MatchReferReq_Doc {
	return &knowledgepb.MatchReferReq_Doc{
		DocId:     doc.GetDocId(),
		DocType:   doc.GetDocType(),
		Answer:    doc.GetAnswer(),
		Question:  doc.GetQuestion(),
		OrgData:   doc.GetOrgData(),
		RelatedId: doc.GetRelatedId(),
		IsBigData: doc.GetIsBigData(),
		SheetInfo: doc.GetSheetInfo(),
	}
}

func matchRecommended(ctx context.Context, bs *botsession.BotSession, output string, placeholders map[string]string) {
	if !bs.App.GetKnowledgeQa().GetOutput().GetUseRecommended() {
		return
	}
	err := Push(ctx, bs.To, event.EventRecommendedTo, event.RecommendedToEvent{
		Docs:     helper.Map(bs.Knowledge, toRecommendDoc),
		Question: bs.PromptCtx.Question,
		Answer:   output,
		RecordID: bs.RecordID,
		Session:  bs.Session,
		BotBizID: bs.App.GetAppBizId(),
		Type:     bs.Type,
	})
	if err != nil {
		log.InfoContextf(ctx, "match recommended fail, err: %v", err)
	}
}

func toRecommendDoc(doc *knowledge.SearchKnowledgeRsp_SearchRsp_Doc) model.RecommendedDoc {
	return model.RecommendedDoc{
		Question: doc.GetQuestion(),
		Answer:   doc.GetAnswer(),
		DocType:  doc.GetDocType(),
		OrgData:  doc.GetOrgData(),
	}
}

// TruncateDocsIndex 截断的docs索引,该索引对应文档不在Prompt
func TruncateDocsIndex(doc []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, prompt string) int {
	res := len(doc)
	for i, d := range doc {
		if d.GetDocType() == 1 && !strings.Contains(prompt, d.GetQuestion()) {
			res = i
			break
		}
		if d.GetDocType() == 2 {
			var prefix = d.GetOrgData()
			if len([]rune(d.GetOrgData())) > 100 {
				prefix = string([]rune(d.GetOrgData())[:100])
			}
			if !strings.Contains(prompt, prefix) {
				res = i
				break
			}
		}
	}
	return res
}

// SendTokenStat 发送 token 统计事件到前端
func SendTokenStat(ctx context.Context, bs *botsession.BotSession, tse *event.TokenStatEvent) {
	if bs.TokenStat == nil {
		return
	}
	ctx, cancel := context.WithCancel(ctx)
	err := bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, bs.TokenStat, cancel)
	tse0 := *tse
	tse0.Procedures = append([]event.Procedure{}, tse.Procedures...)
	clues.AddTrackDataWithError(ctx, tse.EventSource+".TokenStatEvent", tse0, err)
}

// dosageMergeReport 合并上报
func dosageMergeReport(ctx context.Context, e *event.TokenStatEvent, app *model.App, d dao.Dao) {
	if e == nil || len(e.Procedures) == 0 {
		return
	}
	var input, output, searchEngine int
	var needMultipleReports bool
	for i := range e.Procedures {
		if config.App().WorkflowConfig.CloseTokenReport && e.Procedures[i].Name == event.ProcedureWorkflow {
			continue // 工作流不上报
		}
		if e.Procedures[i].Status != event.ProcedureStatusSuccess {
			continue
		}
		if e.Procedures[i].Name == event.ProcedureSE {
			searchEngine++
		}
		if len(e.Procedures[i].TokenUsageDetails) > 1 {
			needMultipleReports = true
			continue
		}
		input += int(e.Procedures[i].InputCount)
		output += int(e.Procedures[i].OutputCount)
	}
	dosage := dao.TokenDosage{
		AppID:        app.GetAppBizId(),
		AppType:      app.GetAppType(),
		ModelName:    e.MainModelName,
		RecordID:     e.RecordID,
		StartTime:    e.StartTime,
		EndTime:      time.Now(),
		SearchEngine: searchEngine,
	}
	if input > 0 {
		dosage.InputDosages = []int{int(input)}
	}
	if output > 0 {
		dosage.OutputDosages = []int{output}
	}
	if len(dosage.InputDosages) > 0 || len(dosage.OutputDosages) > 0 {
		_ = d.ReportTokenDosage2(ctx, app.GetCorpId(), dosage, e.FinanceSubBizType)
	}
	if needMultipleReports { // 工作流使用多个大模型的场景，需要多次上报用量
		multipleReports(ctx, e, app, d)
	}
}

// multipleReports 多次上报
func multipleReports(ctx context.Context, e *event.TokenStatEvent, app *model.App, d dao.Dao) {
	if e == nil || len(e.Procedures) == 0 {
		return
	}
	for i := range e.Procedures {
		if e.Procedures[i].Status != event.ProcedureStatusSuccess {
			continue
		}
		if len(e.Procedures[i].TokenUsageDetails) <= 1 {
			continue
		}
		for j, item := range e.Procedures[i].TokenUsageDetails {
			modelName := item.ModelName
			if modelName == config.App().WorkflowConfig.ModelName {
				modelName = app.GetMainModelName()
			}
			dosage := dao.TokenDosage{
				AppID:        app.GetAppBizId(),
				AppType:      app.GetAppType(),
				ModelName:    modelName,
				RecordID:     e.RecordID + "-" + strconv.Itoa(j),
				StartTime:    e.StartTime,
				EndTime:      time.Now(),
				SearchEngine: 0,
			}
			if e.Procedures[i].TokenUsageDetails[j].InputTokens > 0 {
				dosage.InputDosages = []int{int(e.Procedures[i].TokenUsageDetails[j].InputTokens)}
			}
			if e.Procedures[i].TokenUsageDetails[j].OutputTokens > 0 {
				dosage.OutputDosages = []int{int(e.Procedures[i].TokenUsageDetails[j].OutputTokens)}
			}
			if len(dosage.InputDosages) > 0 || len(dosage.OutputDosages) > 0 {
				_ = d.ReportTokenDosage2(ctx, app.GetCorpId(), dosage, e.FinanceSubBizType)
			}
		}
	}
}

// concurrencyDosageReport 上报并发次数
func concurrencyDosageReport(ctx context.Context, e *event.TokenStatEvent, app *model.App, d dao.Dao) {
	if e == nil {
		return
	}
	if !app.IsExclusive {
		log.InfoContextf(ctx, "app is not exclusive, skip concurrency dosage report")
		return
	}
	dosage := dao.ConcurrencyDosage{
		AppID:     app.GetAppBizId(),
		AppType:   app.GetAppType(),
		ModelName: e.MainModelName,
		RecordID:  e.RecordID,
		StartTime: e.StartTime,
		EndTime:   time.Now(),
		Dosage:    1,
	}
	_ = d.ReportConcurrencyDosage(ctx, app.GetCorpId(), dosage)
}

// initTokenBalance 初始化 token 余量
func initTokenBalance(ctx context.Context, e *event.TokenStatEvent, dao dao.Dao, corpID uint64, modelName string) {

	e.MainModelName = modelName
	// 检查账户状态
	st := dao.GetModelStatus(ctx, corpID, modelName)
	clues.AddT(ctx, "Balance.cloud.ModelStatus", clues.M{"ModelStatus": st, "corpID": corpID, "modelName": modelName})
	// 明确账户不可用, 返回余量小于0
	if st == 1 {
		e.UsedCount = 0
		e.FreeCount = 0
		e.OrderCount = 0
		return
	}

	e.OrderCount = 1
}
