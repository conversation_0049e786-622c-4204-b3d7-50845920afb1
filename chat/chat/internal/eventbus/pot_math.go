// chat
//
// @(#)pot_math.go  Friday, May 24, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package eventbus

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	"golang.org/x/exp/rand"
)

// mathPOTReply 数学计算
func mathPOTReply(ctx context.Context, cli *model.Conn, bs *botsession.BotSession) (bool, bool, string, error) {

	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, bs.ModelType)
	clues.AddT(ctx, "App.GetModel", clues.M{"model": m, "appType": model.AppTypeKnowledgeQA, "type": bs.ModelType})
	m.ModelName = "pot-70b"
	if config.App().POT.Math.ModelName != "" {
		m.ModelName = config.App().POT.Math.ModelName
	}

	// 获取历史消息
	// histories, useRole, err := getHistoryAndRole(ctx, bs, m.GetHistoryLimit(), m.GetHistoryWordsLimit())
	// clues.AddTrackE(ctx, "mathPOTReply.getHistoryAndRole()", clues.M{"histories": histories, "useRole": useRole}, err)
	// if err != nil {
	//	return false, false, "", err
	// }

	bs.NeedCheck = false // 是否需要安全检查
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedurePOTMath))
	SendTokenStat(ctx, bs, bs.TokenStat)

	// 渲染Prompt
	var prompt = bs.PromptCtx.Question
	if config.App().POT.Math.Prompt != "" {
		m.Prompt = config.App().POT.Math.Prompt
		m.PromptLimit = bus.dao.GetModelPromptLimit(ctx, m.ModelName) // 获取Prompt限制
		prompt, _ = bus.dao.TextTruncate(ctx, m, bs.PromptCtx)        // 生成Prompt
	}

	bs.SystemRole = m.GetSysPrompt(true, model.IsSelfAwarenessIntent(bs.IntentCate), bs.SystemRole)
	inferParams := m.CreateModelParamsObject(bs.EnableRandomSeed)
	if bs.EnableRandomSeed {
		inferParams = inferParams.WithSeed(rand.Intn(10000) + 1).WithStopWordsList([]string{"→"})
	} else {
		inferParams = inferParams.WithStopWordsList([]string{"→"})
	}
	req := m.NewPOTMathRequest(ctx, bs.Session, bs.RecordID, prompt, bs.SystemRole, inferParams)
	clues.AddTrackData(ctx, "mathPOTReply.NewPOTMathRequest", req)
	log.InfoContextf(ctx, "R|botReply|NewPOTMathRequest|%s %+v", bs.EventSource, req)
	st, last, lastEvil, isModelRejected, err := streamReply(ctx, bs, req,
		event.ProcedurePOTMath, model.ReplyMethodModel, nil)
	if err != nil {
		return false, false, "", err
	}
	if last == nil { // 没有输出
		return false, isModelRejected, "", nil
	}

	isEvil := lastEvil.GetResultCode() == ispkg.ResultEvil
	if last != nil && (!last.GetFinished() || isEvil) { // 最后一个包含敏感词也需要兜底结束
		last.Finished = true
		ctx, cancel := context.WithCancel(ctx)
		_ = bus.dao.DoEmitWsClient(ctx, cli.ClientID,
			bs.NewReplyEvent(ctx, last, isEvil, model.ReplyMethodModel, st, []string{}), cancel,
		)
	}
	reply := last.GetMessage().GetContent()
	method := helper.When(isEvil, model.ReplyMethodEvil, model.ReplyMethodModel)
	m0 := bs.NewBotRecord(ctx, reply, req, method, lastEvil, st)
	newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
	_, err = bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
	clues.AddTrackDataWithError(ctx, "mathPOTReply:dao.CreateMsgRecord", m0, err)
	if err != nil {
		return false, false, "", err
	}

	return isEvil, isModelRejected, "", nil // 通过Output为空，不去找参考来源
}
