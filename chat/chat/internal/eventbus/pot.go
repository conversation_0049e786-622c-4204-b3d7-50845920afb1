// chat
//
// @(#)pot.go  Tuesday, May 21, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package eventbus

import (
	"encoding/json"
	"fmt"
	"regexp"
)

// POT 后期训练优化工具套件, Post Optimization Toolkit

var calcRegexp = regexp.MustCompile(`\[Calculator\((.*?)\)→`)

// CalcText 计算器文本
type CalcText struct {
	Original    string // 原文本
	OriginalLen int    // 原文本长度
	StartIndex  int    // 开始位置
	IgnoreText  string // 不处理的字符串
	SubText     string // 处理的子串
	BeforeExpr  string // 表达式前的文本
	Expr        string // 表达式, 不包含特殊字符: [Calculator( )→
	AfterExpr   string // 表达式后面文本
	extracted   string // 提取的字符
}

// ExistsCalc 是否存在文本
func ExistsCalc(text string, startIndex int) (CalcText, bool) {
	oLen := len(text)
	if startIndex >= oLen {
		return CalcText{Original: text, OriginalLen: oLen, StartIndex: startIndex}, false
	}
	subText := text[startIndex:]
	loc := calcRegexp.FindStringSubmatchIndex(subText)
	if len(loc) != 4 {
		return CalcText{Original: text, OriginalLen: oLen, StartIndex: startIndex}, false
	}
	fmt.Println(loc)
	return CalcText{
		Original:    text,
		OriginalLen: oLen,
		StartIndex:  startIndex,
		IgnoreText:  text[:startIndex],
		SubText:     subText,
		BeforeExpr:  text[:startIndex] + subText[:loc[0]],
		Expr:        subText[loc[2]:loc[3]],
		AfterExpr:   subText[loc[3]+4:],
		extracted:   subText[loc[0] : loc[3]+4],
	}, true
}

// String to string
func (c CalcText) String() string {
	d, _ := json.MarshalIndent(c, "", "")
	return string(d)
}

// Substituted 替换后的文本, 以及下次替换的起始位置
func (c CalcText) Substituted(text string) (string, int) {
	s := fmt.Sprintf("%s%s%s]%s%s", c.BeforeExpr, c.extracted, text, text, c.AfterExpr)
	l := len(s)
	if l > c.StartIndex {
		return s, l
	}
	return s, c.StartIndex
}

var calcTextRegexp = regexp.MustCompile(`\[Calculator\(.*?\)→.*?]`)

// RemoveCalcText 去掉计算器文本内容, 主要用于前端展示
func RemoveCalcText(text string) string {
	return calcTextRegexp.ReplaceAllString(text, "")
}
