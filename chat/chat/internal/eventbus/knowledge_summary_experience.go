// chat
//
// @(#)knowledge_summary_experience.go  Tuesday, February 27, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package eventbus

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
	"golang.org/x/exp/slices"
)

func init() {
	handlers[event.EventKnowledgeSummaryExperience] = &KnowledgeSummaryExperienceEventHandler{}
}

// KnowledgeSummaryExperienceEventHandler 知识摘要体验事件处理器
type KnowledgeSummaryExperienceEventHandler struct{}

// NewRequest TODO
func (e *KnowledgeSummaryExperienceEventHandler) NewRequest(ctx context.Context, conn *model.Conn, data []byte) (any,
	error) {
	log.InfoContextf(ctx, "I|KnowledgeSummaryExperienceEventHandler|NewRequest %+v %s", conn, string(data))
	req := event.KnowledgeSummaryExperienceEvent{}
	if err := jsoniter.Unmarshal(data, &req); err != nil {
		log.ErrorContextf(ctx, "Unmarshal event req error: %+v, data: %s", err, data)
		return nil, err
	}
	if conn.Type != model.ConnTypeExperience {
		return nil, pkg.ErrBadRequest
	}
	if !req.IsValid() {
		return nil, pkg.ErrBadRequest
	}
	return req, nil
}

// Process 处理事件请求
func (e *KnowledgeSummaryExperienceEventHandler) Process(ctx context.Context, cli *model.Conn, req any) error {
	// TODO: 临时解决 针对Apex 链接未建立成功首次进入的请求 先sleep下再执行逻辑 确保Apex链接建立后再执行 后面Apex修复后删除
	if cli.IsSSE && config.App().SSE.EnableDeferClientClose {
		log.DebugContextf(ctx, "clientID: %s SSE.DeferClientCloseTime: %+v",
			cli.ClientID, config.App().SSE.DeferClientCloseTime)
		time.Sleep(config.App().SSE.DeferClientCloseTime)
	}
	log.InfoContextf(ctx, "I|KnowledgeSummaryExperienceEventHandler|Process %+v %+v", cli, req)
	ev := req.(event.KnowledgeSummaryExperienceEvent)
	// 框架未携带, 则使用业务数据
	pkg.WithSessionID(ctx, ev.SessionID)
	pkg.WithRequestID(ctx, ev.RequestID)
	pkg.WithTraceID(ctx, ev.RequestID)
	pkg.WithLoginUserType(ctx, cli.LoginUserType)
	var err error
	session := &model.Session{
		SessionID: ev.SessionID,
		BotBizID:  cli.APIBotBizID, VisitorID: cli.CorpStaffID, VisitorBizID: cli.CorpStaffBizID,
	}
	if cli.Type != model.ConnTypeAPIVisitor {
		if session, err = bus.dao.GetSession(ctx, model.SessionTypeExperience, ev.SessionID); err != nil {
			return err
		}
		if session == nil {
			return pkg.ErrSessionNotFound
		}
	}
	log.InfoContextf(ctx, "R|KnowledgeSummaryExperienceEventHandler|botsession %+v", session)
	app, err := getValidApp(ctx, session.BotBizID, dao.AppTestScene)
	if err != nil {
		return err
	}
	if !app.IsAppTypeSummary() {
		return pkg.ErrAppTypeNotSupported
	}

	// 如果用户有传入相关参数, 则按用户的做一次覆盖
	ev = e.useUserInput(ctx, ev, app)
	if err = e.checkChatWordsLimit(ctx, app, ev); err != nil {
		return err
	}
	from := &model.CorpStaff{}
	if cli.Type != model.ConnTypeAPIVisitor {
		if from, err = bus.dao.GetCorpStaffByBizID(ctx, cli.CorpStaffBizID); err != nil {
			return err
		}
		if from == nil {
			return pkg.ErrVisitorNotExist
		}
	}
	msg, err := e.CreateMsgRecord(ctx, cli, app, ev)
	if err != nil {
		return err
	}
	ctx, cancel := context.WithCancel(ctx)
	_ = bus.dao.DoEmitWsClient(ctx, cli.ClientID, &event.ReplyEvent{
		IsFinal:    true,
		IsFromSelf: true,
		SessionID:  ev.SessionID,
		RequestID:  ev.RequestID,
		FromName:   from.NickName,
		FromAvatar: from.Avatar,
		Content:    ev.Content,
		RecordID:   msg.RecordID,
		Timestamp:  msg.CreateTime.Unix(),
	}, cancel)
	if err := limit(ctx, app, msg.RecordID, func() error {
		return e.exec(ctx, cli, session, app, ev, &msg)
	}); err != pkg.ErrConcurrenceExceeded {
		return err
	}
	return botBusyReply(ctx,
		botsession.NewBusySession(cli, session, app, model.RecordTypeExperience, ev.RequestID, msg.RecordID),
	)
}

// CreateMsgRecord 创建消息记录
func (e *KnowledgeSummaryExperienceEventHandler) CreateMsgRecord(ctx context.Context,
	cli *model.Conn, app *model.App, ev event.KnowledgeSummaryExperienceEvent) (model.MsgRecord, error) {
	msg := model.MsgRecord{
		BotBizID:     app.GetAppBizId(),
		SessionID:    ev.SessionID,
		RecordID:     uuid.NewString(),
		Type:         model.RecordTypeExperience,
		ToID:         app.GetId(),
		ToType:       model.SourceTypeRobot,
		FromID:       cli.CorpStaffID,
		FromType:     cli.GetSourceType(),
		Content:      ev.Content,
		CreateTime:   time.Now(),
		CfgVersionID: app.GetConfigVersionId(),
		TraceID:      model.TraceID(ctx),
	}
	msgID, err := bus.dao.CreateMsgRecord(ctx, msg, nil) // for query
	if err != nil {
		msg.ID = uint64(msgID)
	}
	return msg, err
}

func (e *KnowledgeSummaryExperienceEventHandler) useUserInput(ctx context.Context,
	ev event.KnowledgeSummaryExperienceEvent, app *model.App) event.KnowledgeSummaryExperienceEvent {
	if app.GetSummary() == nil {
		return ev
	}
	if len(ev.ModelName) > 0 {
		if v, ok := app.GetSummary().GetModuleList()[ev.ModelName]; ok {
			if app.GetSummary().GetModel() == nil {
				app.Summary.Model = make(map[string]*admin.AppModelInfo)
			}
			app.Summary.Model[string(model.ModelTypeSummaryRecognize)] = v
		}
	}
	if app.GetSummary().GetOutput() != nil {
		s := app.GetSummary().GetOutput()
		if slices.Contains(config.App().App.KnowledgeSummary.AllowReplaceMethod, ev.Method) {
			s.Method = ev.Method
		}
		if slices.Contains(config.App().App.KnowledgeSummary.AllowReplaceRequirement, ev.Requirement) {
			s.Requirement = ev.Requirement
		}
		if len(ev.RequireCommand) > 0 {
			s.RequireCommand = ev.RequireCommand
		}
		app.Summary.Output = s
	}
	log.InfoContextf(ctx, "REPLACED|summary %+v", app.GetSummary())
	return ev
}

// exec 执行消息
func (e *KnowledgeSummaryExperienceEventHandler) exec(
	ctx context.Context, cli *model.Conn, session *model.Session,
	app *model.App, ev event.KnowledgeSummaryExperienceEvent, msg *model.MsgRecord,
) (err error) {
	replyID := uuid.NewString()
	botSession := botsession.BotSession{
		Session:         session,
		Type:            model.RecordTypeExperience,
		RecordID:        replyID,
		RelatedRecordID: msg.RecordID,
		RequestID:       ev.RequestID,
		App:             app,
		To:              cli,
		PromptCtx: botsession.PromptCtx{
			Question: ev.Content,
			SummaryRequireCommand: func() string {
				if app != nil && app.GetSummary() != nil && app.GetSummary().GetOutput() != nil {
					if app.GetSummary().GetOutput().GetRequirement() == 2 {
						return app.GetSummary().GetOutput().GetRequireCommand()
					}
				}
				return ""
			}(),
		},
		StreamingThrottle: ev.StreamingThrottle,
		ModelType:         app.GetSummaryModelType(),
		EventSource:       ev.Name(),
		NeedCheck:         true,
		TokenStat: &event.TokenStatEvent{
			SessionID:   ev.SessionID,
			RequestID:   ev.RequestID,
			RecordID:    replyID,
			StartTime:   time.Now(),
			EventSource: ev.Name(),
		},
	}
	initTokenBalance(ctx, botSession.TokenStat, bus.dao, app.GetCorpId(), app.GetMainModelName())
	_, _, _, err = botReply(ctx, &botSession)
	// isDirectReply, isModelRejected, output, err := botReply(ctx, botSession)
	if err != nil {
		return err
	}
	return nil
}

// checkChatWordsLimit 用户输入长度限制
func (e *KnowledgeSummaryExperienceEventHandler) checkChatWordsLimit(ctx context.Context, app *model.App,
	ev event.KnowledgeSummaryExperienceEvent) error {
	if ev.Content == "" {
		log.ErrorContextf(ctx, "R|KnowledgeSummaryExperienceEventHandler|checkChatWordsLimit empty content")
		return pkg.ErrBadRequest
	}
	// 长度限制: 一个汉字按一个字符计算
	contentLen := len([]rune(ev.Content))
	m := app.GetModel(ctx, model.AppTypeSummary, app.GetSummaryModelType())
	chatWordsLimit := m.GetChatWordsLimit()
	if chatWordsLimit > 0 {
		if uint32(contentLen) > chatWordsLimit {
			log.ErrorContextf(ctx, "R|KnowledgeSummaryExperienceEventHandler|checkChatWordsLimit "+
				"over model chat words limit:%d contentLen:%d", chatWordsLimit, contentLen)
			return pkg.ErrBadRequest
		}
		return nil
	}
	defaultQueryMaxLen := config.App().App.KnowledgeSummary.DefaultQueryMaxLen
	if defaultQueryMaxLen > 0 && len([]rune(ev.Content)) > defaultQueryMaxLen {
		log.ErrorContextf(ctx, "R|KnowledgeSummaryExperienceEventHandler|checkChatWordsLimit "+
			"over summary default chat words limit:%d contentLen:%d", defaultQueryMaxLen, contentLen)
		return pkg.ErrBadRequest
	}
	return nil
}
