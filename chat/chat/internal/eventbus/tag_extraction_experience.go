// chat
//
// @(#)tag_extraction_experience.go  Tuesday, February 27, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package eventbus

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	handlers[event.EventTagExtractionExperience] = &TagExtractionExperienceEventHandler{}
}

// TagExtractionExperienceEventHandler 标签提取体验事件处理器
type TagExtractionExperienceEventHandler struct{}

// NewRequest 处理请求
func (e *TagExtractionExperienceEventHandler) NewRequest(ctx context.Context, conn *model.Conn, data []byte) (any,
	error) {
	log.InfoContextf(ctx, "I|TagExtractionExperienceEventHandler|NewRequest %+v %s", conn, string(data))
	req := event.TagExtractionExperienceEvent{}
	if err := jsoniter.Unmarshal(data, &req); err != nil {
		log.ErrorContextf(ctx, "Unmarshal event req error: %+v, data: %s", err, data)
		return nil, err
	}
	if conn.Type != model.ConnTypeExperience {
		return nil, pkg.ErrBadRequest
	}
	if !req.IsValid() {
		return nil, pkg.ErrBadRequest
	}
	return req, nil
}

// Process 处理事件请求
func (e *TagExtractionExperienceEventHandler) Process(ctx context.Context, cli *model.Conn, req any) error {
	// TODO: 临时解决 针对Apex 链接未建立成功首次进入的请求 先sleep下再执行逻辑 确保Apex链接建立后再执行 后面Apex修复后删除
	if cli.IsSSE && config.App().SSE.EnableDeferClientClose {
		log.DebugContextf(ctx, "clientID: %s SSE.DeferClientCloseTime: %+v",
			cli.ClientID, config.App().SSE.DeferClientCloseTime)
		time.Sleep(config.App().SSE.DeferClientCloseTime)
	}
	log.InfoContextf(ctx, "I|TagExtractionExperienceEventHandler|Process %+v %+v", cli, req)
	ev := req.(event.TagExtractionExperienceEvent)
	// 框架未携带, 则使用业务数据
	pkg.WithSessionID(ctx, ev.SessionID)
	pkg.WithRequestID(ctx, ev.RequestID)
	pkg.WithTraceID(ctx, ev.RequestID)
	pkg.WithLoginUserType(ctx, cli.LoginUserType)
	var err error
	session := &model.Session{
		SessionID: ev.SessionID,
		BotBizID:  cli.APIBotBizID, VisitorID: cli.CorpStaffID, VisitorBizID: cli.CorpStaffBizID,
	}
	if cli.Type != model.ConnTypeAPIVisitor {
		if session, err = bus.dao.GetSession(ctx, model.SessionTypeExperience, ev.SessionID); err != nil {
			return err
		}
		if session == nil {
			return pkg.ErrSessionNotFound
		}
	}
	log.InfoContextf(ctx, "R|TagExtractionExperienceEventHandler|botsession %+v", session)
	app, err := getValidApp(ctx, session.BotBizID, dao.AppTestScene)
	if err != nil {
		return err
	}
	if !app.IsAppTypeClassify() {
		return pkg.ErrAppTypeNotSupported
	}

	// 如果用户有传入相关参数, 则按用户的做一次覆盖
	ev = e.useUserInput(ctx, ev, app)
	if err = e.checkChatWordsLimit(ctx, app, ev); err != nil {
		return err
	}
	from := &model.CorpStaff{}
	if cli.Type != model.ConnTypeAPIVisitor {
		if from, err = bus.dao.GetCorpStaffByBizID(ctx, cli.CorpStaffBizID); err != nil {
			return err
		}
		if from == nil {
			return pkg.ErrVisitorNotExist
		}
	}

	msg, err := e.CreateMsgRecord(ctx, cli, app, ev)
	if err != nil {
		return err
	}
	ctx, cancel := context.WithCancel(ctx)
	_ = bus.dao.DoEmitWsClient(ctx, cli.ClientID, &event.ReplyEvent{
		IsFinal:    true,
		IsFromSelf: true,
		SessionID:  ev.SessionID,
		RequestID:  ev.RequestID,
		FromName:   from.NickName,
		FromAvatar: from.Avatar,
		Content:    ev.Content,
		RecordID:   msg.RecordID,
		Timestamp:  msg.CreateTime.Unix(),
	}, cancel)
	if err := limit(ctx, app, msg.RecordID, func() error {
		return e.exec(ctx, cli, session, app, ev, &msg)
	}); err != pkg.ErrConcurrenceExceeded {
		return err
	}
	return botBusyReply(ctx,
		botsession.NewBusySession(cli, session, app, model.RecordTypeExperience, ev.RequestID, msg.RecordID),
	)
}

// CreateMsgRecord 创建消息记录
func (e *TagExtractionExperienceEventHandler) CreateMsgRecord(ctx context.Context,
	cli *model.Conn, app *model.App, ev event.TagExtractionExperienceEvent) (model.MsgRecord, error) {
	msg := model.MsgRecord{
		BotBizID:     app.GetAppBizId(),
		SessionID:    ev.SessionID,
		RecordID:     uuid.NewString(),
		Type:         model.RecordTypeExperience,
		ToID:         app.GetId(),
		ToType:       model.SourceTypeRobot,
		FromID:       cli.CorpStaffID,
		FromType:     cli.GetSourceType(),
		Content:      ev.Content,
		CreateTime:   time.Now(),
		CfgVersionID: app.GetConfigVersionId(),
		TraceID:      model.TraceID(ctx),
	}
	msgID, err := bus.dao.CreateMsgRecord(ctx, msg, nil) // for query
	if err != nil {
		msg.ID = uint64(msgID)
	}
	return msg, err
}

func (e *TagExtractionExperienceEventHandler) useUserInput(ctx context.Context,
	ev event.TagExtractionExperienceEvent, app *model.App) event.TagExtractionExperienceEvent {
	if app.GetClassify() == nil {
		return ev
	}
	if len(ev.ModelName) > 0 {
		if v, ok := app.GetClassify().GetModuleList()[ev.ModelName]; ok {
			if app.GetClassify().GetModel() == nil {
				app.Classify.Model = make(map[string]*admin.AppModelInfo)
			}
			app.Classify.Model[string(model.ModelTypeClassifyExtract)] = v
		}
	}
	if len(ev.Tags) > 0 {
		var labels []*admin.ClassifyLabel
		for _, t := range ev.Tags {
			labels = append(labels, &admin.ClassifyLabel{
				Name:        t.Name,
				Description: t.Description,
				Values:      t.Values,
			})
		}
		app.Classify.Labels = labels
	}
	log.InfoContextf(ctx, "REPLACED|classify %+v", app.GetClassify())
	return ev
}

// exec 执行消息
func (e *TagExtractionExperienceEventHandler) exec(
	ctx context.Context, cli *model.Conn, session *model.Session,
	app *model.App, ev event.TagExtractionExperienceEvent, msg *model.MsgRecord,
) (err error) {
	replyID := uuid.NewString()
	botSession := botsession.BotSession{
		Session:         session,
		Type:            model.RecordTypeMessage,
		RecordID:        replyID,
		RelatedRecordID: msg.RecordID,
		RequestID:       ev.RequestID,
		App:             app,
		To:              cli,
		PromptCtx: botsession.PromptCtx{
			Question: ev.Content,
			Tags: func() []*admin.ClassifyLabel {
				if app != nil && app.GetClassify() != nil {
					return app.GetClassify().GetLabels()
				}
				return nil
			}(),
		},
		StreamingThrottle: ev.StreamingThrottle,
		ModelType:         app.GetClassifyModelType(),
		EventSource:       ev.Name(),
		NeedCheck:         true,
		TokenStat: &event.TokenStatEvent{
			SessionID:   ev.SessionID,
			RequestID:   ev.RequestID,
			RecordID:    replyID,
			StartTime:   time.Now(),
			EventSource: ev.Name(),
		},
	}
	initTokenBalance(ctx, botSession.TokenStat, bus.dao, app.GetCorpId(), app.GetMainModelName())
	_, _, _, err = tagReply(ctx, &botSession)
	if err != nil {
		return err
	}
	return nil
}

// checkChatWordsLimit 用户输入长度限制
func (e *TagExtractionExperienceEventHandler) checkChatWordsLimit(ctx context.Context, app *model.App,
	ev event.TagExtractionExperienceEvent) error {
	if ev.Content == "" {
		log.ErrorContextf(ctx, "R|TagExtractionExperienceEventHandler|checkChatWordsLimit empty content")
		return pkg.ErrBadRequest
	}
	defaultAllowMaxTags := config.App().App.TagExtraction.AllowMaxTags
	if len(ev.Tags) > defaultAllowMaxTags { // api 调用时可能传入
		log.ErrorContextf(ctx, "R|TagExtractionExperienceEventHandler|checkChatWordsLimit "+
			"over classify max tags limit:%d len(ev.tags):%d", defaultAllowMaxTags, len(ev.Tags))
		return pkg.ErrBadRequest
	}
	// 长度限制: 一个汉字按一个字符计算
	contentLen := len([]rune(ev.Content))
	m := app.GetModel(ctx, model.AppTypeClassify, app.GetClassifyModelType())
	chatWordsLimit := m.GetChatWordsLimit()
	if chatWordsLimit > 0 {
		if uint32(contentLen) > chatWordsLimit {
			log.ErrorContextf(ctx, "R|TagExtractionExperienceEventHandler|checkChatWordsLimit "+
				"over model chat words limit:%d contentLen:%d", chatWordsLimit, contentLen)
			return pkg.ErrBadRequest
		}
		return nil
	}
	defaultQueryMaxLen := config.App().App.TagExtraction.DefaultQueryMaxLen
	if defaultQueryMaxLen > 0 && len([]rune(ev.Content)) > defaultQueryMaxLen {
		log.ErrorContextf(ctx, "R|TagExtractionExperienceEventHandler|checkChatWordsLimit "+
			"over classify default chat words limit:%d contentLen:%d", defaultQueryMaxLen, contentLen)
		return pkg.ErrBadRequest
	}
	return nil
}
