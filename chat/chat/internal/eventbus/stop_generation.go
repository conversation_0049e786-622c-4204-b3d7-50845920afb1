package eventbus

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	handlers[event.EventStopGeneration] = &StopGenerationEventHandler{}
}

// StopGenerationEventHandler 设置已读事件处理器
type StopGenerationEventHandler struct{}

// NewRequest 创建事件请求
func (e *StopGenerationEventHandler) NewRequest(ctx context.Context, cli *model.Conn, bs []byte) (any, error) {
	req := event.StopGenerationEvent{}
	if err := jsoniter.Unmarshal(bs, &req); err != nil {
		log.ErrorContextf(ctx, "Unmarshal event req error: %+v, data: %s", err, bs)
		return nil, err
	}
	if !req.IsValid() {
		return nil, pkg.ErrBadRequest
	}
	return req, nil
}

// Process 处理事件请求
func (e *StopGenerationEventHandler) Process(ctx context.Context, cli *model.Conn, req any) error {
	ev := req.(event.StopGenerationEvent)
	return bus.dao.StopGeneration(ctx, cli.CorpStaffID, ev.RecordID)
}
