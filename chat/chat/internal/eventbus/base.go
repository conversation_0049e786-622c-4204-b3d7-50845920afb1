// Package eventbus 事件总线
package eventbus

import (
	"context"
	"reflect"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/intent"
	"git.woa.com/ivy/qbot/qbot/chat/internal/memory"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/internal/retrieval"
	"git.woa.com/ivy/qbot/qbot/chat/internal/rewrite"
	"git.woa.com/ivy/qbot/qbot/chat/internal/workflow"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
)

var bus *Bus

type handler interface {
	Process(context.Context, *model.Conn, any) error
	NewRequest(context.Context, *model.Conn, []byte) (any, error)
}

type eventWrapper struct {
	ctx       context.Context
	handler   handler
	conn      *model.Conn
	typ       string
	req       any
	requestID string
}

func (ev *eventWrapper) process(ctx context.Context) (err error) {
	defer func() {
		if ev.conn.IsSSE && !ev.conn.HasRef() {
			err := bus.dao.DelSseClient(ctx, ev.conn.ClientID)
			// go bus.dao.StopSilenceDetect(ctx, ev.conn.SessionID)
			metrics.ReportSseClientDisconnect(helper.When(err == nil, "1", "0"))
		}
	}()

	start := time.Now()
	defer func() {
		success := helper.When(err == nil, "1", "0")
		metrics.ReportEventProcess(time.Since(start).Seconds(), ev.typ, success)
	}()

	defer func() {
		ev.conn.Deref()
	}()
	if err := ev.handler.Process(ctx, ev.conn, ev.req); err != nil {
		if flag, ok := config.App().EventDowngradeConf[ev.typ]; ok && flag { // 降级不发送错误，只输出告警日志
			log.WarnContextf(ctx, "%s Event handler process error: %+v", ev.typ, err)
			return nil
		} else if config.ErrorDowngrade(err.Error()) {
			log.WarnContextf(ctx, "%s Event handler process error: %+v", ev.typ, err)
		} else {
			log.ErrorContextf(ctx, "%s Event handler process error: %+v", ev.typ, err)
		}
		ctx, cancel := context.WithCancel(ctx)
		_ = bus.dao.DoEmitWsClient(ctx, ev.conn.ClientID, event.NewErrorEvent(ev.requestID, err), cancel)
		return err
	}
	return nil
}

var handlers = map[string]handler{}

// Bus 事件总线
type Bus struct {
	dao       dao.Dao
	intent    intent.ChatIntentAPI       // 意图
	retrieval retrieval.ChatRetrievalAPI // 检索
	workflow  workflow.ChatWorkflowAPI   // 工作流
	memory    memory.ChatMemoryAPI       // 历史记录
	rewrite   rewrite.QueryRewriteAPI    // 改写
	events    chan eventWrapper
}

// Run 启动事件总线
func Run() {
	cfg := config.App().EventBus
	bus = &Bus{
		dao:       dao.New(),
		intent:    intent.New(),
		retrieval: retrieval.New(),
		workflow:  workflow.New(),
		memory:    memory.New(),
		rewrite:   rewrite.New(),
		events:    make(chan eventWrapper, cfg.EventPoolSize),
	}
	for i := 0; i < cfg.WorkerNum; i++ {
		go func() {
			defer errors.PanicHandler()
			for ev := range bus.events {
				_ = ev.process(ev.ctx)
			}
		}()
	}
}

// Push 向事件总线推送事件
func Push(ctx context.Context, conn *model.Conn, typ string, req any) error {
	if bus == nil {
		return nil
	}

	h := handlers[typ]
	if h == nil {
		log.WarnContextf(ctx, "Event handler not exist, type: %s", typ)
		return pkg.ErrEventHandlerNotExist
	}

	var err error
	if bs, ok := req.([]byte); ok {
		req, err = h.NewRequest(ctx, conn, bs)
		if err != nil {
			return err
		}
	}

	conn.Ref()
	metrics.ReportRecvEvent(typ)
	log.DebugContextf(ctx, "bus.events len/cap: %d/%d", len(bus.events), cap(bus.events))
	bus.events <- eventWrapper{trpc.CloneContext(ctx), h, conn, typ, req, getRequestID(req)}
	return nil
}

// getRequestID 获取事件中的请求 ID
func getRequestID(req any) string {
	t := reflect.Indirect(reflect.ValueOf(req)).FieldByName("RequestID")
	if t.IsValid() && t.Kind() == reflect.String {
		return t.String()
	}
	return ""
}
