package eventbus

import (
	"context"
	"strings"
	"testing"

	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

/**
    @author：cooper
    @date：2025/2/17
    @note：
**/

func Test_searchProImpl(t *testing.T) {
	type args struct {
		ctx   context.Context
		query string
	}
	tests := []struct {
		name    string
		args    args
		want    []*model.SearchResultPage
		wantErr string
	}{
		{
			name: "test",
			args: args{
				ctx:   context.Background(),
				query: "根据如下剧情介绍：  \n乔言心深爱顾怀之多年，做他的助理一心一意为他，因顾怀之一场醉酒导致乔言心怀孕，二人秘密结婚。乔言心以为嫁给幸福，可这是噩梦的开始。顾怀之满心满眼都是白月光，甚至为白月光数次伤害妻儿，妻儿彻底清醒，远离顾怀之并永不原谅。  \n  \n起一个能让中老年人有点击欲望的推广标题吗？需要突出剧情中的冲突环节，限30个字以内",
			},
			wantErr: "AuthFailure.SignatureFailure",
		},
		{
			name: "testerr",
			args: args{
				ctx:   context.Background(),
				query: "\n abc \t \n",
			},
			wantErr: "AuthFailure.SignatureFailure",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := searchProImpl(tt.args.ctx, tt.args.query)
			if err != nil && !strings.Contains(err.Error(), tt.wantErr) {
				t.Errorf("searchProImpl() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
