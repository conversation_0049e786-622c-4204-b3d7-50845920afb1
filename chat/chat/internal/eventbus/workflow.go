package eventbus

// // workflowReply 工作流回复
// func workflowReply(ctx context.Context, bs *botsession.BotSession,
//	hitIntent intent.CandidateIntent, isDebug bool) (err error) {
//	if !bs.App.GetKnowledgeQa().GetWorkflow().GetIsEnabled() {
//		return pkg.ErrWorkflowDisable
//	}
//	// 更新气泡信息
//	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedureTaskFlow))
//	SendTokenStat(ctx, bs, bs.TokenStat)
//	// 封装请求
//	req := NewWorkflowRequest(ctx, bs, hitIntent.WorkflowID, isDebug)
//	clues.AddTrackData(ctx, "taskReply.GetSemantic.NewTaskRequest", req)
//	// 发起请求
//	cfg := config.App().Bot
//	ch := make(chan *KEP_WF_DM.RunWorkflowReply, cfg.ResponseChannelSize)
//	g, gCtx := errgroupx.WithContext(ctx)
//	dmCtx, cancel := context.WithCancel(ctx)
//	signal := make(chan int, 10)
//	g.Go(func() error {
//		return bus.dao.RunWorkflow(dmCtx, req, ch, signal) // 调用DM
//	})
//	g.Go(func() error {
//		_, isTimeout := workflowStreamDisplay(gCtx, cancel, bs, req, ch)
//		return utils.When(isTimeout, pkg.ErrLLMTimeout, nil)
//	})
//	if err = g.Wait(); err != nil {
//		return err
//	}
//	//if last == nil {
//	//	return nil
//	//}
//	return nil
// }
//
// // NewWorkflowRequest 构造请求，请求工作流
// func NewWorkflowRequest(ctx context.Context,
//	bs *botsession.BotSession, workflowID string, isDebug bool) *KEP_WF_DM.RunWorkflowRequest {
//	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, bs.ModelType)
//	clues.AddTrackData(ctx, "workflow.App.GetModel", map[string]any{
//		"appType": model.AppTypeKnowledgeQA, "ModelType": bs.ModelType, "m": m,
//	})
//
//	histories, _ := bus.memory.MakeMultiRoundHistories(ctx, bs, uint(config.App().Bot.HistoryLimit), false)
//	log.DebugContextf(ctx, "histories :%v", histories)
//	// 组装历史消息
//	var messages []*KEP_WF_DM.Message
//	for _, pair := range model.TruncateHistories(histories, int(m.GetPromptWordsLimit())) { // todo 限制交给DM去做？
//		messages = append(messages,
//			&KEP_WF_DM.Message{Role: KEP_WF_DM.Role_USER, Content: pair[0].Content, RecordID: pair[0].RecordID},
//			&KEP_WF_DM.Message{Role: KEP_WF_DM.Role_ASSISTANT, Content: pair[1].Content, RecordID: pair[1].RecordID},
//		)
//	}
//	// 生成请求体
//	log.DebugContextf(ctx, "DM request history: %+v", messages)
//	systemVariables := make(map[string]*KEP_WF_DM.Variable, 0)
//	for k, v := range bs.SystemInfo {
//		systemVariables[k] = &KEP_WF_DM.Variable{Value: v}
//	}
//	cv := make(map[string]*KEP_WF_DM.Variable, len(bs.CustomVariables)*2)
//	for k, v := range bs.CustomVariables {
//		cv[k] = &KEP_WF_DM.Variable{Value: v}
//	}
//
//	runEnv := KEP_WF_DM.RunEnvType_SANDBOX
//	if bs.EventSource == event.EventSend {
//		runEnv = KEP_WF_DM.RunEnvType_PRODUCT
//	}
//
//	return &KEP_WF_DM.RunWorkflowRequest{
//		SessionID:       bs.SessionID,
//		RunEnv:          runEnv,
//		RequestType:     0,
//		AppID:           strconv.FormatUint(bs.App.GetAppBizId(), 10),
//		WorkflowID:      workflowID,
//		Query:           bs.OriginContent,
//		QueryHistory:    messages,
//		CustomVariables: cv,
//		RelatedRecordID: bs.RelatedRecordID,
//		RecordID:        bs.RecordID,
//		IsDebug:         isDebug,
//	}
// }
//
// // workflowStreamDisplay 工作流结果向前端流式输出
// func workflowStreamDisplay(ctx context.Context, cancel context.CancelFunc, bs *botsession.BotSession,
//	req *KEP_WF_DM.RunWorkflowRequest, ch chan *KEP_WF_DM.RunWorkflowReply,
// ) (last *KEP_WF_DM.RunWorkflowReply, isTimeout bool) {
//	cfg := config.App().Bot
//	throttles := cfg.Throttles
//	//throttleCheck := NewThrottle(throttles.Check)
//	//throttleStreaming := NewThrottle(utils.When(bs.StreamingThrottle > 0, bs.StreamingThrottle, throttles.Streaming))
//	ticker := time.NewTicker(time.Duration(cfg.StopGeneration.CheckInterval) * time.Millisecond)
//	timeout := time.NewTimer(time.Duration(cfg.Timeout) * time.Second)
//	clues.AddTrackData(ctx, "streamDisplay().vars", map[string]any{
//		"throttles": throttles, "ts.StreamingThrottle": bs.StreamingThrottle,
//		"StopGeneration": cfg.StopGeneration, "Timeout": cfg.Timeout,
//	})
//	defer ticker.Stop()
//	defer timeout.Stop()
//	for {
//		select {
//		case <-timeout.C:
//			cancel()
//			bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(event.ProcedureTaskFlow))
//			SendTokenStat(ctx, bs, bs.TokenStat)
//			return last, true
//		case <-ticker.C:
//			if ok, _ := bus.dao.IsGenerationStopped(ctx, bs.To.CorpStaffID, bs.TokenStat.RecordID); ok {
//				cancel()
//				sendWorkflowFinishToken(ctx, bs, req, last)
//				return last, false
//			}
//		case rsp, ok := <-ch:
//			timeout.Stop()
//			if !ok {
//				return last, false
//			}
//			last = rsp
//			if isEvil := processWorkflowResponse(ctx, bs, last); isEvil {
//				cancel()
//				return last, false
//			}
//			if rsp.IsFinal {
//				sendWorkflowFinishToken(ctx, bs, req, last) // token 使用量统计
//				return last, false
//			}
//		}
//	}
// }
//
// // 处理工作流的返回
// func processWorkflowResponse(ctx context.Context, bs *botsession.BotSession,
//	rsp *KEP_WF_DM.RunWorkflowReply) (isEvil bool) {
//	throttles := config.App().Bot.Throttles
//	throttleCheck := helper.NewThrottle(throttles.Check)
//	throttleStreaming := helper.NewThrottle(utils.When(bs.StreamingThrottle > 0,
//	bs.StreamingThrottle, throttles.Streaming))
//	debugInfo := &event.TaskFlowDebugInfo{}
//	replyMethod := model.ReplyMethodTaskFlow
//
//	l := len([]rune(rsp.GetRespond().GetContent()))
//	if bs.NeedCheck && throttleCheck.Hit(l, rsp.IsFinal) {
//		checkCode, checkType := bus.dao.CheckTextEvil(ctx, bs.App.GetAppBizId(),
//			bs.App.GetCorpId(), bs.RecordID, rsp.GetRespond().GetContent(), bs.App.GetInfosecBizType())
//		bs.Msg.ResultCode = checkCode
//		bs.Msg.ResultType = checkType
//		isEvil = checkCode == ispkg.ResultEvil
//		if isEvil {
//			ctx, cancel := context.WithCancel(ctx)
//			rsp.IsFinal = true
//			re := bs.NewWorkflowReplyEvent(rsp, isEvil, model.ReplyMethodEvil, debugInfo)
//			_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
//			return isEvil
//		}
//	}
//	if throttleStreaming.Hit(l, rsp.IsFinal) {
//		ctx, cancel := context.WithCancel(ctx)
//		if rsp.GetRespond().GetWorkflowStatus() == KEP_WF_DM.WorkflowStatus_SUCCESS {
//			replyMethod = model.ReplyMethodTaskAnswer
//		}
//		re := bs.NewWorkflowReplyEvent(rsp, isEvil, replyMethod, debugInfo)
//		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
//	}
//	//processReference(ctx, out, ts, rsp) todo 加协议字段
//	return isEvil
// }
//
// func sendWorkflowFinishToken(ctx context.Context, bs *botsession.BotSession,
//	req *KEP_WF_DM.RunWorkflowRequest, reply *KEP_WF_DM.RunWorkflowReply) {
//	if reply == nil {
//		p := event.NewSuccessTSProcedure(event.ProcedureTaskFlow, nil,
//			event.ProcedureDebugging{Content: req.Query})
//		bs.TokenStat.UpdateSuccessProcedure(ctx, p, bs.App, bus.dao)
//		SendTokenStat(ctx, bs, bs.TokenStat)
//		return
//	}
//	// @boyucao @halelv 调试信息
//	d := event.ProcedureDebugging{Content: req.Query}
//	llmmToken := &llmm.StatisticInfo{}
//	// LLM统计信息。
//	for i := 0; i < len(reply.GetLLMStatisticInfos()); i++ {
//		llmmToken.InputTokens += reply.GetLLMStatisticInfos()[i].InputTokens
//		llmmToken.OutputTokens += reply.GetLLMStatisticInfos()[i].OutputTokens
//		llmmToken.TotalTokens += reply.GetLLMStatisticInfos()[i].TotalTokens
//	}
//	d.Workflow = event.WorkflowSummary{
//		IntentName: reply.Respond.WorkflowName,
//		RunNodes:   reply.Respond.RunNodes,
//	}
//
//	p := event.NewSuccessTSProcedure(event.ProcedureTaskFlow, llmmToken, d)
//	bs.TokenStat.UpdateSuccessProcedure(ctx, p, bs.App, bus.dao)
//	SendTokenStat(ctx, bs, bs.TokenStat)
// }
