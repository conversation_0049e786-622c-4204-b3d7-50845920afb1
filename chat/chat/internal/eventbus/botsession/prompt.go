package botsession

import admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"

// PromptCtx 机器人会话提示上下文
type PromptCtx struct {
	Docs                any
	Question            string
	MultiRoundHistories [][2]string

	// 【知识引擎2.2.1】【知识库问答】精准澄清--3
	// https://tapd.woa.com/project_qrobot/prong/stories/view/1070080800116546674
	// 应用到模型如下:
	// model.ModelTypeMessage, model.ModelTypeMessageNonGeneralKnowledge
	UseQuestionClarify      bool                   // 是否打开问题澄清
	IsDeepSeek              bool                   // 是否ds模型，以防需要针对ds单独调整prompt，预留口子
	QuestionClarifyKeywords []string               // 问题澄清关键词列表, 只有 KnowledgeQaOutput.UseQuestionClarify 打开时, 才填充内容
	Tags                    []*admin.ClassifyLabel // 标签提取
	SummaryRequireCommand   string                 // 自定义要求指令
}
