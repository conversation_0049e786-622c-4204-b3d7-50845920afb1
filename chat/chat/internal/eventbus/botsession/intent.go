package botsession

import (
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
)

// ChatIntentCtx 意图上下文
type ChatIntentCtx struct {
	Docs            any
	Question        string
	Intents         []model.CandidateIntent
	Histories       []ChatHistory
	UseSingleIntent bool // 是否使用单意图prompt
}

// // CandidateIntent 候选意图
// type CandidateIntent struct {
//	Index      int
//	Name       string
//	Def        string
//	Example    string
//	Type       string
//	WorkflowID string // for workflow
//	DocID      uint64 // for FAQ
// }

// ChatHistory 历史记录
type ChatHistory struct {
	User      string
	Assistant string
	Intention string
}

// TruncateIntentHistories 调整历史记录上线 for 意图识别
func TruncateIntentHistories(histories []ChatHistory, limit int) []ChatHistory {
	length := len(histories)
	totalLength := 0
	singleMessageLimit := int(config.App().Bot.SingleMessageLimit)
	for i := length; i > 0; i-- { // 限制单条150，总数2000
		if len([]rune(histories[i-1].User)) > singleMessageLimit {
			histories[i-1].User = model.TruncateSentence([]rune(histories[i-1].User)[:singleMessageLimit])
		}
		if len([]rune(histories[i-1].Assistant)) > singleMessageLimit {
			histories[i-1].Assistant = model.TruncateSentence([]rune(histories[i-1].Assistant)[:singleMessageLimit])
		}
		totalLength += len([]rune(histories[i-1].User)) + len([]rune(histories[i-1].Assistant))
		if totalLength > limit {
			return histories[i:length]
		}
	}
	return histories
}
