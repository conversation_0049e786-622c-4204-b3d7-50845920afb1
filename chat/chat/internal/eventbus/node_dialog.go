package eventbus

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	errors2 "git.woa.com/dialogue-platform/common/v3/errors"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/metrics"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/internal/workflow"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	handlers[event.EventNodeDialog] = &NodeDialogEventHandler{}
}

// NodeDialogEventHandler 工作流对话节点事件处理器
type NodeDialogEventHandler struct{}

// NewRequest 创建事件请求，参数校验等。
func (n *NodeDialogEventHandler) NewRequest(ctx context.Context, cli *model.Conn, bs []byte) (any, error) {
	log.InfoContextf(ctx, "Invoke NodeDialogEventHandler NewRequest cli: %s, req: %s",
		helper.Object2String(cli), string(bs))
	req := event.NodeDialogDebugEvent{}
	if err := jsoniter.Unmarshal(bs, &req); err != nil {
		log.WarnContextf(ctx, "Unmarshal event req error: %+v, data: %s", err, bs)
		return nil, pkg.ErrBadRequest
	}
	if err := req.IsValid(); err != nil {
		return nil, pkg.ErrBadRequest
	}
	if len([]rune(req.Content)) > config.GetContentLimit() {
		log.ErrorContextf(ctx, "Content too long: %d > cfg.Limit:%d", len([]rune(req.Content)),
			config.GetContentLimit())
		return nil, pkg.ErrContentTooLong
	}
	// 框架未携带, 则使用业务数据
	pkg.WithSessionID(ctx, req.SessionID)
	pkg.WithRequestID(ctx, req.RequestID)
	pkg.WithTraceID(ctx, req.RequestID)
	return req, nil
}

// Process 处理事件请求
func (n *NodeDialogEventHandler) Process(ctx context.Context, cli *model.Conn, req any) (err error) {
	log.InfoContextf(ctx, "Invoke NodeDialogEventHandler Process cli: %s, req: %+v",
		helper.Object2String(cli), helper.Object2String(req))
	// TODO: 临时解决 针对Apex 链接未建立成功首次进入的请求 先sleep下再执行逻辑 确保Apex链接建立后再执行 后面Apex修复后删除
	if cli.IsSSE && config.App().SSE.EnableDeferClientClose {
		log.DebugContextf(ctx, "clientID: %s SSE.DeferClientCloseTime: %+v",
			cli.ClientID, config.App().SSE.DeferClientCloseTime)
		time.Sleep(config.App().SSE.DeferClientCloseTime)
	}
	ctx = pf.NewPipelineFlowContext(ctx)
	defer func(ctx *context.Context) { pf.Persist(*ctx) }(&ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddTrackData(ctx, "Conn", cli)
	clues.AddTrackData(ctx, "NodeDialog", req)
	ev := req.(event.NodeDialogDebugEvent)
	clues.AddTrackData(ctx, "query", ev.Content)
	bs := n.initBotSession(ctx, cli, ev)

	if err = n.prepare(ctx, bs); err != nil {
		return err
	}
	return n.doProcess(ctx, bs, ev)
}

// initBotSession 初始化botSession
func (n *NodeDialogEventHandler) initBotSession(ctx context.Context, cli *model.Conn,
	ev event.NodeDialogDebugEvent) *botsession.BotSession {
	app, _ := bus.dao.GetAppByAppKey(ctx, dao.AppTestScene, ev.BotAppKey)
	bs := &botsession.BotSession{
		SessionID:       ev.SessionID,
		RecordID:        encode.GenerateSessionID(),
		RelatedRecordID: "",
		RequestID:       ev.RequestID,
		OriginContent:   ev.Content,
		CustomVariables: ev.CustomVariables,
		WorkflowID:      ev.WorkflowID,
		CorpStaff:       &model.CorpStaff{},
		Msg:             &model.MsgRecord{},
		WorkflowInput:   ev.WorkflowInput,
		Type:            model.RecordTypeMessage,
		SessionType:     model.SessionTypeWorkflow,
		Scene:           dao.AppReleaseScene,
		Env:             KEP_DM.RunEnvType_Product,
		To:              cli,
		Intent:          "",
		IntentCate:      "",
		PromptCtx:       botsession.PromptCtx{Question: ev.Content},
		ReferIndex:      []int{},
		Flags:           botsession.Flags{},
		QuestionAnswers: make([][2]string, 0),
		ToolsInfo:       make(map[string]model.AgentTool),
		WorkflowDebug:   len(ev.WorkflowID) > 0,
		Thought: &event.AgentThoughtEvent{
			SessionID:  ev.SessionID,
			RequestID:  ev.RequestID,
			StartTime:  time.Now(),
			Procedures: make([]event.AgentProcedure, 0),
		},
		App: app,
		// ChatHistoriesV2: ev.ChatHistory,
	}

	bs.Labels = helper.Map(bs.To.VisitorLabels, model.Label.ToVectorLabel)
	for k, v := range bs.CustomVariables {
		bs.CustomVariablesForDisplay = append(bs.CustomVariablesForDisplay, fmt.Sprintf("%s:%s", k, v))
	}
	log.InfoContextf(ctx, "NodeDialogEventHandler init BotSession: %s", helper.Object2String(bs))
	return bs
}

// prepare 预处理
func (n *NodeDialogEventHandler) prepare(ctx context.Context, bs *botsession.BotSession) (err error) {
	// 获取session
	if err = bus.workflow.GetSession(ctx, bs); err != nil {
		return err
	}
	bs.ModelType = bs.App.GetMessageModelType()

	pkg.WithLoginUserType(ctx, bs.To.LoginUserType)
	// 获取from
	from := &model.CorpStaff{}
	if bs.To.Type != model.ConnTypeAPIVisitor {
		if from, err = bus.dao.GetCorpStaffByBizID(ctx, bs.To.CorpStaffBizID); err != nil {
			return err
		}
		if from == nil {
			return pkg.ErrVisitorNotExist
		}
		bs.CorpStaff = from
	}
	// 这里先统一获取下用户历史轮, todo 后续改写，意图，工作流，阅读理解只需要做处理，不用再获取
	bs.ChatHistoriesV2, _ = bus.memory.GetChatHistories(ctx, bs)
	// 问题写消息记录  // for query;
	if err := bus.workflow.CreateQueryMsgRecord(ctx, bs); err != nil {
		log.WarnContextf(ctx, "createQueryMsgRecord error: %v", err)
		return nil
	}
	log.InfoContextf(ctx, "time cost before limit: %+v", time.Since(bs.StartTime).Milliseconds())
	return nil
}

// doProcess TODO
// pipeline 对话ppl
func (n *NodeDialogEventHandler) doProcess(ctx context.Context, bs *botsession.BotSession,
	ev event.NodeDialogDebugEvent) (err error) {
	bs.RecordID = encode.GenerateSessionID() // 在这里重置RecordID
	ppl := func() error {
		log.InfoContextf(ctx, "botsession is:%s", bs.String())
		err0 := n.dialogPipeline(ctx, bs, ev) // 核心流程在这里
		if bs.DebugMessage != nil {
			log.InfoContextf(ctx, "%s botsession is debug request, dosn't need to report", bs.SessionID)
			return err0
		}
		concurrencyDosageReport(ctx, bs.TokenStat, bs.App, bus.dao)
		dosageMergeReport(ctx, bs.TokenStat, bs.App, bus.dao)
		dosageMergeReport(ctx, bs.TokenStat2, bs.App, bus.dao)
		return err0
	}

	err = limit(ctx, bs.App, bs.RelatedRecordID, ppl)
	if err == nil { // todo: 先留着
		go bus.workflow.ProcessImageAndFileCount(ctx, bs)
		return
	}
	if !errors.Is(err, pkg.ErrConcurrenceExceeded) {
		return err
	}
	// 上报超并发
	_ = bus.dao.ReportOverConcurrencyDosage(ctx, bs.App, fillOverConcurrencyDosage(
		bs.App.GetAppBizId(), bs.App.GetMainModelName(), bs.OriginContent))

	return err
}

// dialogPipeline 对话ppl
// todo 1、历史任务型待梳理清楚后，再做修改，也需要放到并行调用里面
func (n *NodeDialogEventHandler) dialogPipeline(ctx context.Context, bs *botsession.BotSession,
	ev event.NodeDialogDebugEvent) (err error) {
	log.InfoContextf(ctx, "time cost before dialogPipeline:%v", time.Since(bs.StartTime).Milliseconds())
	if err = initTokenStat(ctx, bs); err != nil { // 初始化token统计
		return err
	}
	start := time.Now()
	isEvil := false // isEvil 命中敏感，需要直接返回
	var wg sync.WaitGroup
	wg.Add(1)
	go func() { // 安全检测
		defer errors2.PanicHandler()
		defer pf.AppendSpanElapsed(ctx, config.App().StageTaskName.CheckEvil)
		defer wg.Done()
		// 安全审核阶段耗时统计
		pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.CheckEvil)
		isEvil = n.checkEvil(ctx, bs)
		bus.workflow.ReplyFromSelf(ctx, bs) // 首包回复
	}()
	wg.Add(1)
	go func() { // 改写 + 同义词替换
		defer errors2.PanicHandler()
		defer pf.AppendSpanElapsed(ctx, config.App().StageTaskName.QueryRewrite)
		defer wg.Done()
		// 改写 + 同义词替换阶段耗时统计
		pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.QueryRewrite)
		bs.PromptCtx.Question = queryRewriteAndNER(ctx, bs)
	}()
	wg.Wait()
	log.InfoContextf(ctx, "time cost after rewrite %v", time.Since(start).Milliseconds())
	if isEvil {
		return err
	}
	log.InfoContextf(ctx, "core process begin.")
	return n.coreProcess(ctx, bs, ev)
}

// coreProcess 核心处理流程
func (n *NodeDialogEventHandler) coreProcess(ctx context.Context,
	bs *botsession.BotSession, ev event.NodeDialogDebugEvent) (err error) {

	bs.IntentCate = model.IntentTypeWorkflow

	req := n.NewWorkflowRequest(ctx, bs, ev)

	// 发起请求
	cfg := config.App().Bot
	ch := make(chan *KEP_WF_DM.DebugWorkflowNodeDialogReply, cfg.ResponseChannelSize)
	g, gCtx := errgroupx.WithContext(ctx)
	dmCtx, cancel := context.WithCancel(ctx)
	signal := make(chan int, 10)
	pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.Workflow)
	g.Go(func() error {
		return bus.dao.NodeDialogWorkflow(dmCtx, req, ch, signal) // 调用DM
	})
	g.Go(func() error {
		last, isTimeout, err := n.WorkflowStreamDisplay(gCtx, cancel, bs, ch)
		if last != nil && !last.GetIsFinal() {
			last.IsFinal = true
			if workflow.NeedReplyThoughtEvent(ctx, bs) { // 下发停止思考事件
				bus.workflow.ThoughtEventStopReply(ctx, bs, cancel)
			}
			re := n.NewWorkflowReplyEvent(last, false, model.ReplyMethodWorkflowAnswer, nil, bs)
			_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
		}
		return helper.When(isTimeout, pkg.ErrLLMTimeout, err)
	})
	if err = g.Wait(); err != nil {
		return err
	}

	// 端到端尾包耗时统计
	statistics := pkg.GetStatistics(ctx)
	if statistics != nil {
		pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.DialogPPL, statistics.MainModel,
			statistics.IntentionCategory, pkg.Uin(ctx), -1)
	}
	return err
}

// NewWorkflowRequest 构造请求，请求工作流
func (n *NodeDialogEventHandler) NewWorkflowRequest(ctx context.Context,
	bs *botsession.BotSession, ev event.NodeDialogDebugEvent) *KEP_WF_DM.DebugWorkflowNodeDialogRequest {

	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessage)
	limit := int(m.GetHistoryLimit())
	num := helper.When(limit > len(bs.ChatHistoriesV2.ChatStack), len(bs.ChatHistoriesV2.ChatStack), limit)
	start := len(bs.ChatHistoriesV2.ChatStack) - num // 指定截取的起始位置
	histories := make([]*KEP_WF_DM.Message, 0, len(bs.ChatHistoriesV2.ChatStack))
	for i := start; i < len(bs.ChatHistoriesV2.ChatStack); i++ {
		item := bs.ChatHistoriesV2.ChatStack[i]
		histories = append(histories, &KEP_WF_DM.Message{
			Role:     KEP_WF_DM.Role_USER,
			Content:  helper.When(item.RewriteQuery != "", item.RewriteQuery, item.OriginQuery),
			RecordID: item.RecordID,
		})
		histories = append(histories, &KEP_WF_DM.Message{
			Role:     KEP_WF_DM.Role_ASSISTANT,
			Content:  item.GetAssistantContent(),
			RecordID: item.RecordID,
		})
	}
	request := &KEP_WF_DM.DebugWorkflowNodeDialogRequest{
		AppID:             strconv.FormatUint(bs.App.GetAppBizId(), 10),
		NodeJSON:          ev.NodeInfo,
		Inputs:            bs.WorkflowInput,
		SessionID:         bs.SessionID,
		Query:             bs.OriginContent,
		ConfiguredHistory: ev.ChatHistory,
		QueryHistory:      histories,
		RelatedRecordID:   bs.RelatedRecordID,
		RecordID:          bs.RecordID,
		MainModelName:     bs.App.GetMainModelName(),
		RewriteQuery:      bs.PromptCtx.Question,
	}
	return request
}

// WorkflowStreamDisplay 工作流结果向前端流式输出
func (n *NodeDialogEventHandler) WorkflowStreamDisplay(ctx context.Context, cancel context.CancelFunc,
	bs *botsession.BotSession,
	ch chan *KEP_WF_DM.DebugWorkflowNodeDialogReply,
) (last *KEP_WF_DM.DebugWorkflowNodeDialogReply, isTimeout bool, err error) {
	cfg := config.App().Bot
	throttles := cfg.Throttles
	ticker := time.NewTicker(time.Duration(cfg.StopGeneration.CheckInterval) * time.Millisecond)
	timeout := time.NewTimer(time.Duration(cfg.Timeout) * time.Second)
	throttleCheck, thoughtStreaming := helper.NewThrottle(throttles.Check), helper.NewThrottleList()
	streamingStep := helper.When(bs.StreamingThrottle > 0, bs.StreamingThrottle, throttles.Streaming)
	throttleStreaming := helper.NewThrottle(streamingStep)
	clues.AddTrackData(ctx, "streamDisplay().vars", map[string]any{
		"throttles": throttles, "ts.StreamingThrottle": bs.StreamingThrottle,
		"StopGeneration": cfg.StopGeneration, "Timeout": cfg.Timeout,
	})
	defer ticker.Stop()
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			cancel()
			bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(event.ProcedureWorkflow))
			bus.workflow.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
			return last, true, nil
		case <-ticker.C:
			ok, err := bus.dao.IsGenerationStopped(ctx, bs.To.CorpStaffID, bs.TokenStat.RecordID)
			if ok {
				log.DebugContextf(ctx, "workflowStreamDisplay: generation stopped")
				n.sendWorkflowToken(ctx, bs, last, true) // todo 后面优化一下这里。
				cancel()
				return last, false, nil
			}
			if err != nil && (errors.Is(err, context.Canceled) ||
				strings.Contains(errs.Msg(err), "context canceled")) {
				return last, false, err
			}
		case rsp, ok := <-ch:
			timeout.Stop()
			if !ok {
				return last, false, pkg.ErrWorkflowRunError
			}
			last = rsp
			if len(rsp.GetRespond().GetThoughtList()) > len(bs.Thought.Procedures) {
				workflow.AddThoughtProcedure(ctx, bs, rsp.GetRespond().GetThoughtList(), &thoughtStreaming, streamingStep)
			}
			if workflow.NeedReplyThoughtEvent(ctx, bs) {
				bus.workflow.ThoughtEventReply(ctx, bs, thoughtStreaming, rsp.GetRespond().GetThoughtList(), cancel)
			}
			if n.discardWorkflowMiddleResult(ctx, *bs, rsp) {
				continue
			}
			if isEvil := n.processWorkflowResponse(ctx, bs, last, throttleCheck, throttleStreaming); isEvil {
				cancel()
				return last, false, nil
			}
			if rsp.IsFinal {
				return last, false, nil
			}
		}
	}
}

// NewWorkflowReplyEvent 生成回复事件
func (n *NodeDialogEventHandler) NewWorkflowReplyEvent(rsp *KEP_WF_DM.DebugWorkflowNodeDialogReply,
	isEvil bool, replyMethod model.ReplyMethod, debugInfo *event.WorkflowDebugInfo, bs *botsession.BotSession,
) model.WsEvent {
	reply := rsp.GetRespond().GetContent()
	replyMethod = helper.When(isEvil, model.ReplyMethodEvil, replyMethod)
	if bs.Type == model.RecordTypeMessage ||
		bs.Type == model.RecordTypeExperience ||
		bs.Type == model.RecordTypeSearch ||
		bs.Type == model.RecordTypeOPDebug {
		reply, _, _ = helper.MatchSearchResults(context.Background(), reply)
		re := &event.ReplyEvent{
			RequestID:       bs.RequestID,
			SessionID:       bs.Session.SessionID,
			Content:         helper.When(isEvil, config.App().Bot.EvilReply, reply),
			FromName:        bs.App.BaseConfig.GetName(),
			FromAvatar:      bs.App.BaseConfig.GetAvatar(),
			RecordID:        bs.RecordID,
			RelatedRecordID: bs.RelatedRecordID,
			Timestamp:       bs.StartTime.Unix(),
			IsFinal:         rsp.IsFinal,
			IsFromSelf:      false,
			CanRating:       model.CanMsgRating(bs.Type, replyMethod, bs.App.GetAppType()),
			CanFeedback:     bs.EventSource == event.EventExperience, // model.CanMsgFeedback(ts.Type, replyMethod),
			IsEvil:          isEvil,
			IsLLMGenerated:  bs.IsLLMGenerated(replyMethod),
			ReplyMethod:     replyMethod,
			Workflow:        debugInfo,
			OptionCards:     rsp.GetRespond().GetOptionCards(),
		}
		return re
	}
	return nil
}

func (n *NodeDialogEventHandler) sendWorkflowToken(ctx context.Context,
	bs *botsession.BotSession, reply *KEP_WF_DM.DebugWorkflowNodeDialogReply, stop bool) error {
	if reply == nil {
		p := event.NewSuccessTSProcedure(event.ProcedureWorkflow, nil,
			event.ProcedureDebugging{Content: bs.OriginContent, CustomVariables: bs.CustomVariablesForDisplay}, nil)
		bs.TokenStat.UpdateSuccessProcedure(p)
		bus.workflow.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
		return nil
	}
	// @boyucao @halelv 调试信息
	d := event.ProcedureDebugging{Content: bs.OriginContent, CustomVariables: bs.CustomVariablesForDisplay}
	llmmToken := &llmm.StatisticInfo{}
	tokenUsage := make([]*event.TokenUsage, 0)
	// LLM统计信息。
	for i := 0; i < len(reply.GetStatisticInfos()); i++ {
		llmmToken.InputTokens += reply.GetStatisticInfos()[i].InputTokens
		llmmToken.OutputTokens += reply.GetStatisticInfos()[i].OutputTokens
		llmmToken.TotalTokens += reply.GetStatisticInfos()[i].TotalTokens

		tokenUsage = append(tokenUsage, &event.TokenUsage{
			TotalTokens:  reply.GetStatisticInfos()[i].TotalTokens,
			InputTokens:  reply.GetStatisticInfos()[i].InputTokens,
			OutputTokens: reply.GetStatisticInfos()[i].OutputTokens,
			ModelName:    reply.GetStatisticInfos()[i].ModelName,
		})
	}
	pre, err := bus.dao.GetWorkflowRunNodes(ctx, bs.SessionID, reply.GetRespond().GetWorkflowRunID())
	if errors.Is(err, context.Canceled) || strings.Contains(errs.Msg(err), "context canceled") {
		return err
	}
	runNodes, needOutput := workflow.MergeRunNodeInfos(ctx, pre, reply.GetRespond().GetRunNodes())
	if !reply.GetIsFinal() && !stop && !needOutput {
		return nil
	}
	d.Workflow = n.createWorkflowSummary(ctx, bs, runNodes, reply)

	_ = bus.dao.SetWorkflowRunNodes(ctx, bs.SessionID, reply.GetRespond().GetWorkflowRunID(), d.Workflow.RunNodes)
	if reply.IsFinal || stop {
		p := event.NewSuccessTSProcedure(event.ProcedureWorkflow, llmmToken, d, tokenUsage)
		bs.TokenStat.UpdateSuccessProcedure(p)
	} else {
		p := event.NewProcessingTSProcedureWithDebug(event.ProcedureWorkflow, llmmToken, d, tokenUsage)
		bs.TokenStat.UpdateProcedure(p)
	}
	bus.workflow.SendTokenStat(ctx, bs.To.ClientID, bs.TokenStat)
	log.DebugContextf(ctx, "sendWorkflowFinishToken: %v", helper.Object2String(bs.TokenStat))
	return nil
}

// createWorkflowSummary 构建工作流的摘要信息
func (n *NodeDialogEventHandler) createWorkflowSummary(ctx context.Context, bs *botsession.BotSession,
	runNodes []*KEP_WF_DM.RunNodeInfo,
	reply *KEP_WF_DM.DebugWorkflowNodeDialogReply) event.WorkflowSummary {
	ws := event.WorkflowSummary{
		WorkflowID:    reply.GetRespond().GetWorkflowID(),
		WorkflowName:  reply.GetRespond().GetWorkflowName(),
		RunNodes:      runNodes,
		WorkflowRunID: reply.GetRespond().GetWorkflowRunID(),
	}
	hitOption := reply.GetRespond().HitOptionCardIndex
	if hitOption <= 0 {
		return ws
	}
	chatStack := bs.ChatHistoriesV2.ChatStack
	for i := len(chatStack) - 1; i >= 0; i-- {
		if chatStack[i].HasOptionCards {
			ws.OptionCardIndex = event.OptionCardIndex{
				RecordID: chatStack[i].RecordID,
				Index:    hitOption,
			}
			break
		}
	}
	return ws
}

// discardWorkflowMiddleResult 是否仅回尾包
func (n *NodeDialogEventHandler) discardWorkflowMiddleResult(ctx context.Context, bs botsession.BotSession,
	resp *KEP_WF_DM.DebugWorkflowNodeDialogReply) bool {
	var discard bool
	if !bs.IsWorkflowOutputStream() && !resp.GetIsFinal() { // 非流式 且 不是最后一个输出，丢弃
		discard = true
	}
	// 流式,非尾包为空，丢弃（大模型首包可能出现内容为空）
	if bs.IsWorkflowOutputStream() && !resp.GetIsFinal() &&
		resp.GetRespond().GetContent() == "" && len(resp.GetRespond().GetRunNodes()) == 0 {
		discard = true
	}
	if !discard {
		// 占位符输出不完整的时候，丢弃
		reply := resp.GetRespond().GetContent()
		discard = helper.IsPlaceholderEnd(reply) || helper.IsEndWithCalc(reply) || helper.IsEndWithPicture(reply) ||
			helper.EndsWithPlaceholderPrefix(reply, "[Calculator") ||
			helper.EndsWithPlaceholderPrefix(reply, "[Picture")
	}
	log.DebugContextf(ctx, "R|discardMiddleResult|%s %t", bs.EventSource, discard)
	return discard
}

// processWorkflowResponse 处理工作流的返回
func (n *NodeDialogEventHandler) processWorkflowResponse(ctx context.Context, bs *botsession.BotSession,
	rsp *KEP_WF_DM.DebugWorkflowNodeDialogReply, throttleCheck, throttleStreaming helper.Throttle) (isEvil bool) {

	// 1. 构造调试信息
	debugInfo := n.createWorkflowDebugInfo(ctx, bs, rsp)
	replyMethod := model.ReplyMethodWorkflow

	l := len([]rune(rsp.GetRespond().GetContent()))
	isFirstReply := throttleCheck.IsFirstReply()
	if bs.NeedCheck && throttleCheck.Hit(l, rsp.IsFinal) {
		checkCode, checkType := bus.dao.CheckTextEvil(ctx, bs.App.GetAppBizId(),
			bs.App.GetCorpId(), bs.RecordID, rsp.GetRespond().GetContent(), bs.App.GetInfosecBizType())
		bs.Msg.ResultCode = checkCode
		bs.Msg.ResultType = checkType
		isEvil = checkCode == ispkg.ResultEvil
		if isEvil {
			ctx, cancel := context.WithCancel(ctx)
			rsp.IsFinal = true
			re := n.NewWorkflowReplyEvent(rsp, isEvil, model.ReplyMethodEvil, debugInfo, bs)
			_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
		}
	}
	if !isEvil && throttleStreaming.Hit(l, rsp.IsFinal) {
		ctx, cancel := context.WithCancel(ctx)
		if rsp.GetRespond().GetWorkflowStatus() == KEP_WF_DM.WorkflowStatus_SUCCESS {
			replyMethod = model.ReplyMethodWorkflowAnswer
		}
		if rsp.GetRespond().GetWorkflowStatus() != KEP_WF_DM.WorkflowStatus_RUNNING &&
			config.IsWorkflowUnchanged(bs.App.GetAppBizId()) {
			bus.dao.SetWorkflowUnchanged(ctx, bs.App.GetAppBizId(), bs.SessionID, "")
		}
		re := n.NewWorkflowReplyEvent(rsp, isEvil, replyMethod, debugInfo, bs)
		// 记录首包、尾包耗时
		uin := pkg.Uin(ctx)
		mainModelName := bs.IntentCate
		if isFirstReply {
			metrics.WorkflowContentFirstToken(ctx, time.Since(bs.StartTime).Milliseconds())
			metrics.WorkflowFirstToken(ctx, time.Since(bs.StartTime).Milliseconds())
			pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.Workflow, mainModelName, "", uin, -1)
			// 端到端首包耗时
			pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.DialogPPL, mainModelName, "", uin, -1)
		} else if rsp.GetIsFinal() {
			pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.Workflow, mainModelName, "", uin, -1)
		}
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, re, cancel)
	}
	if err := n.sendWorkflowToken(ctx, bs, rsp, false); err != nil { // token 使用量统计
		return isEvil
	}

	if rsp.IsFinal {
		if !isEvil {
			n.workflowReference(ctx, bs, rsp)
		}
		// 结果写DB
		lastEvil := &infosec.CheckRsp{
			ResultCode: bs.Msg.ResultCode,
			ResultType: bs.Msg.ResultType,
		}
		refs := workflow.GetReferences(rsp.GetRespond().GetReferences())
		record := bs.NewWorkflowRecord(ctx, rsp.GetRespond().Content, replyMethod,
			lastEvil, bs.TokenStat, rsp.GetRespond().GetOptionCards(), refs)
		log.DebugContextf(ctx, "Token Stat: %v", helper.Object2String(bs.TokenStat))
		log.DebugContextf(ctx, "record.tokenStat: %v", record.TokenStat)
		newRecord, newStat := event.GetMsgRecordAndTokenStat(ctx, record)
		newStat.AgentThought, _ = jsoniter.MarshalToString(bs.Thought)
		_, _ = bus.dao.CreateMsgRecord(ctx, newRecord, newStat) // for answer
	}
	return isEvil
}

// createWorkflowDebugInfo 提取调试信息的构造逻辑
func (n *NodeDialogEventHandler) createWorkflowDebugInfo(ctx context.Context, bs *botsession.BotSession,
	rsp *KEP_WF_DM.DebugWorkflowNodeDialogReply) *event.WorkflowDebugInfo {
	respond := rsp.GetRespond()
	debugInfo := &event.WorkflowDebugInfo{
		WorkflowName:        respond.GetWorkflowName(),
		WorkflowID:          respond.GetWorkflowID(),
		WorkflowRunID:       respond.GetWorkflowRunID(),
		OptionCards:         respond.GetOptionCards(),
		Outputs:             respond.GetContentList(),
		WorkflowReleaseTime: helper.ParseRFC3339ToUnix(respond.GetWorkflowReleaseTime()),
	}
	if len(respond.GetRunNodes()) != 0 {
		debugInfo.CurrentNode = respond.GetRunNodes()[len(respond.GetRunNodes())-1]
	}
	return debugInfo
}

func (n *NodeDialogEventHandler) workflowReference(ctx context.Context,
	bs *botsession.BotSession, rsp *KEP_WF_DM.DebugWorkflowNodeDialogReply) {
	if len(rsp.GetRespond().GetReferences()) > 0 {
		refs := workflow.GetReferences(rsp.GetRespond().GetReferences())
		reEvent := &event.ReferenceEvent{
			RecordID:   bs.TokenStat.RecordID,
			References: refs,
		}
		ctx, cancel := context.WithCancel(ctx)
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, reEvent, cancel)
		clues.AddTrackData(ctx, "processDisplay.EmitWsUser", map[string]any{
			"To.CorpStaffID": bs.To.CorpStaffID, "To.Type": bs.To.Type, "ReferenceEvent": reEvent,
		})
	}
}

// checkEvil 安全审核
func (n *NodeDialogEventHandler) checkEvil(ctx context.Context, bs *botsession.BotSession) bool {
	checkCode, checkType := checkEvil(ctx, bs.App.GetAppBizId(), bs.App.CorpId, bs.RelatedRecordID, bs.OriginContent,
		bs.App.GetInfosecBizType())
	bs.Msg.ResultCode = checkCode
	bs.Msg.ResultType = checkType
	if checkCode == ispkg.ResultEvil {
		if err := bus.dao.UpdateMsgRecordCheckResult(ctx, bs.RelatedRecordID, checkCode, checkType,
			bs.App.GetAppBizId()); err != nil {
			log.WarnContextf(ctx, "UpdateMsgRecordCheckResult err: %v", err)
		}
		return true
	}
	return false
}
