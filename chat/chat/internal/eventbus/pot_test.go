// chat
//
// @(#)pot_test.go  Tuesday, May 21, 2024
// Copyright(c) 2024, leyton@Tencent. All rights reserved.

package eventbus

import "testing"

var text1 = `你支持使用用户选择的多种语言流利地进行交流并解答用户的问题。`
var text2 = `[Calculator(数学表达式)→结果]]`
var text3 = `你可以使用API工具来协助计算纯数学表达式结果，比如：要获得数学表达式的结果，使用API并获得结果格式为：[Calculator(数学表达式)→结果]。`
var text4 = `老奶奶一共卖了年糕10元，红豆粥50元，甜瓜50元，加上卖掉玉米得到的100元，所以老奶奶一共卖了10+50+50+100= [Calculator(10+50+50+100)→210] 210 元。`
var text5 = `老奶奶一共卖了年糕10元，红豆粥50元，甜瓜50元，加上卖掉玉米得到的100元，所以老奶奶一共卖了10+50+50+100= [Calculator(10+50[Calculator(10+50+50+100)→2220]+50+100)→210] 210 元。`
var text6 = `玉米得到的100元，所以老奶奶一共卖了10+50+50+100= [Calculator(10+50[Calculator(10+50+50+100)→`
var text7 = `玉米得到的100元，所以老奶奶一共卖了10+50+50+100= [Calculator(10*50)→`
var text8 = `首先，我们分析题目给出的信息：甲车和乙车同时从A和B两地相向而行，4小时后相遇。这意味着在相遇时，甲车和乙车一共行驶了A、B两地之间的全部距离。由于甲车在相遇后还需要3小时才能到达B地，我们可以推断出甲车在相遇时已经行驶了A到相遇点的距离，而乙车则行驶了B到相遇点的距离。

由于甲车每小时比乙车多行驶20公里，那么在4小时内，甲车比乙车多行驶的距离是20公里/小时 × 4小时 = [Calculator(20*4)→80]80`

var text9 = "近期总容量为7360人+8325人+10138人+4200人+4910人+5220人+925人+4500人= [Calculator(7360+8325+10138+4200+4910+5220+925+4500)→45578]45578人，中期总容量为18780人+13250人+16768人+7900人+6620人+9690人+1775人+7125人= [Calculator(18780+13250+16768+7900+6620+9690+1775+7125)→81908]81908人，远期总容量为22680人+17375人+19893人+9750人+9315人+11690人+2225人+10450人= [Calculator(22680+17375+19893+9750+9315+11690+2225+10450)→103378]103378人。"

func TestExistsCalc1(t *testing.T) {
	var ct CalcText
	var ok bool
	ct, ok = ExistsCalc(text1, 0)
	t.Log(ct, ok)
	if ok {
		t.Log(ct.Substituted("HELLO"))
	}
	ct, ok = ExistsCalc(text2, 0)
	t.Log(ct, ok)
	if ok {
		t.Log(ct.Substituted("HELLO"))
	}
	ct, ok = ExistsCalc(text3, 100)
	t.Log(ct, ok)
	if ok {
		t.Log(ct.Substituted("HELLO"))
	}
	ct, ok = ExistsCalc(text4, 20)
	t.Log(ct, ok)
	if ok {
		t.Log(ct.Substituted("HELLO"))
	}
	ct, ok = ExistsCalc(text5, 60)
	t.Log(ct, ok)
	if ok {
		t.Log(ct.Substituted("HELLO"))
	}
	ct, ok = ExistsCalc(text6, 3)
	t.Log(ct, ok)
	if ok {
		t.Log(ct.Substituted("HELLO"))
	}
	ct, ok = ExistsCalc(text7, 5)
	t.Log(ct, ok)
	if ok {
		t.Log(ct.Substituted("HELLO"))
	}
}

func TestExistsCalc2(t *testing.T) {
	t.Log(len("→"))
}

func TestExistsCalc3(t *testing.T) {
	var ct CalcText
	var ok bool
	ct, ok = ExistsCalc(text8, 554)
	t.Log(ct, ok)
	if ok {
		t.Log(ct.Substituted("HELLO"))
	}
}

func TestRemoveCalcText1(t *testing.T) {
	t.Log(text1)
	t.Log(RemoveCalcText(text1))
	t.Log(text2)
	t.Log(RemoveCalcText(text2))
	t.Log(text3)
	t.Log(RemoveCalcText(text3))
	t.Log(text4)
	t.Log(RemoveCalcText(text4))
	t.Log(text9)
	t.Log(RemoveCalcText(text9))
}
