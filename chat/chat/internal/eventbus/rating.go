package eventbus

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	handlers[event.EventRating] = &RatingEventHandler{}
}

// RatingEventHandler 评分事件处理器
type RatingEventHandler struct{}

// NewRequest 创建事件请求
func (e *RatingEventHandler) NewRequest(ctx context.Context, cli *model.Conn, bs []byte) (any, error) {
	req := event.RatingEvent{}
	log.InfoContextf(ctx, "Invoke RatingEventHandler NewRequest cli: %s, req: %s",
		helper.Object2String(cli), string(bs))
	if err := jsoniter.Unmarshal(bs, &req); err != nil {
		log.WarnContextf(ctx, "Unmarshal event req error: %+v, data: %s", err, bs)
		return nil, pkg.ErrBadRequest
	}
	if !req.IsValid() {
		return nil, pkg.ErrBadRequest
	}
	return req, nil
}

// Process 处理事件请求
func (e *RatingEventHandler) Process(ctx context.Context, cli *model.Conn, req any) error {
	ctx = clues.NewTrackContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	clues.AddTrackData(ctx, "Conn", cli)
	clues.AddTrackData(ctx, "RatingEvent", req)
	pkg.WithLoginUserType(ctx, cli.LoginUserType)
	ev := req.(event.RatingEvent)
	r, err := bus.dao.GetMsgRecordByRecordID(ctx, ev.RecordID, cli.APIBotBizID)
	clues.AddTrackE(ctx, "dao.GetMsgRecordByRecordID", r, err)
	if err != nil {
		return err
	}
	if r == nil {
		return pkg.ErrInvalidMsgRecord
	}

	session := &model.Session{BotBizID: cli.APIBotBizID, VisitorID: cli.CorpStaffID, VisitorBizID: cli.CorpStaffBizID}
	if cli.Type != model.ConnTypeAPIVisitor {
		if session, err = bus.dao.GetSession(ctx, model.SessionTypeAny, r.SessionID); err != nil {
			return err
		}
		if session == nil {
			return pkg.ErrSessionNotFound
		}
	}

	err = e.checkBeforeRating(ctx, session, cli, r, ev)
	clues.AddTrackE(ctx, "e.checkBeforeRating", session, err)
	if err != nil {
		return err
	}

	if cli.IsVisitor() && ev.Score == model.ScoreDownvote {
		err := e.addUnsatisfiedReply(ctx, cli, session, r, ev)
		clues.AddTrackE(ctx, "e.addUnsatisfiedReply", r, err)
		if err != nil {
			return err
		}
	}

	err = bus.dao.Rating(ctx, r.ID, ev.Score, ev.Reasons, cli.APIBotBizID)
	clues.AddTrackE(ctx, "dao.Rating", r.ID, err)
	if err != nil {
		return err
	}
	ctx, cancel := context.WithCancel(ctx)
	_ = bus.dao.DoEmitWsClient(ctx, cli.ClientID, ev, cancel)
	if cli.Type == model.ConnTypeVisitor && ev.Score == model.ScoreDownvote {
		return e.tryTransfer(ctx, cli, session)
	}

	return nil
}

func (e *RatingEventHandler) checkBeforeRating(
	ctx context.Context, session *model.Session, cli *model.Conn, r *model.MsgRecord, ev event.RatingEvent) error {
	if len(ev.Reasons) == 0 && len(r.GetReasons()) > 0 {
		return pkg.ErrBadRequest
	}
	app, err := bus.dao.GetAppByBizID(ctx, dao.AppReleaseScene, session.BotBizID)
	if err != nil {
		return err
	}
	if app == nil {
		return pkg.ErrRobotNotExist
	}

	if !(r.IsTo(cli.GetSourceType(), cli.CorpStaffID) || r.IsToAPIVisitor()) || !r.GetCanRating(ctx, app.GetAppType()) {
		log.InfoContextf(ctx, "r.IsTo:%+v", r.IsTo(cli.GetSourceType(), cli.CorpStaffID))
		log.InfoContextf(ctx, "r.IsToAPIVisitor:%+v", r.IsToAPIVisitor())
		log.InfoContextf(ctx, "r.GetCanRating:%+v", r.GetCanRating(ctx, app.GetAppType()))
		return pkg.ErrInvalidMsgRecord
	}

	if r.Score == model.ScoreUpvote || (r.Score != model.ScoreNone && ev.Score != r.Score) {
		return pkg.ErrInvalidMsgRecord
	}
	return nil
}

// tryTransfer 尝试转人工
func (e *RatingEventHandler) tryTransfer(ctx context.Context, cli *model.Conn, session *model.Session) error {
	if session.IsTransfered {
		return nil
	}

	return nil
}

// addUnsatisfiedReply 增加不满意回复
func (e *RatingEventHandler) addUnsatisfiedReply(
	ctx context.Context, cli *model.Conn, session *model.Session, r *model.MsgRecord, ev event.RatingEvent,
) error {
	related, err := bus.dao.GetMsgRecordByRecordID(ctx, r.RelatedRecordID, r.BotBizID)
	clues.AddTrackE(ctx, "dao.GetMsgRecordByRecordID",
		clues.M{"r.RelatedRecordID": r.RelatedRecordID, "related": related}, err)
	if err != nil {
		return err
	}
	if related == nil { // 关联消息不存在
		return pkg.ErrInvalidMsgRecord
	}

	ctxs, err := e.getContexts(ctx, *r)
	if err != nil {
		return err
	}

	botBizID := cli.APIBotBizID
	if session != nil {
		botBizID = session.BotBizID
	}

	req := &bot_knowledge_config_server.AddUnsatisfiedReplyReq{
		BotBizId: botBizID,
		RecordId: ev.RecordID,
		Question: related.Content,
		Answer:   r.GetSafeContent(),
		Context:  ctxs,
		Reasons:  ev.Reasons,
	}
	if req.Question == "" && len(related.FileInfos) != 0 && related.FileInfos != "[]" {
		req.Question = related.FileInfos
	}
	if req.Question == "" {
		req.Question = config.App().MultiModal.GetCaptionPrompt
	}

	err = bus.dao.AddUnsatisfiedReply(ctx, req)
	clues.AddTrackE(ctx, "dao.AddUnsatisfiedReply", req, err)
	return err
}

// getContexts 获取访客端会话上下文
func (e *RatingEventHandler) getContexts(
	ctx context.Context, r model.MsgRecord,
) ([]*bot_knowledge_config_server.UnsatisfiedReplyContext, error) {
	// 欢迎语可以默认加上
	// 目前仅限访客端点踩，并且效果评测不下发欢迎语，因此不会影响效果评测和坐席搜索点踩
	cfg := config.App().UnsatisfiedReply.ContextLength
	above, err := bus.dao.GetMsgRecord(ctx, model.GetMsgRecordParam{
		LastIDCreateTime: r.CreateTime,
		Count:            cfg.Above,
		Types:            []model.RecordType{r.Type, model.RecordTypeGreeting},
		SessionID:        r.SessionID,
		IncludeBotEvil:   true,
	})
	clues.AddTrackE(ctx, "getContexts.above", above, err)
	if err != nil {
		return nil, err
	}

	below, err := bus.dao.GetMsgRecord(ctx, model.GetMsgRecordParam{
		FirstIDCreateTime: r.CreateTime,
		Count:             cfg.Below,
		Types:             []model.RecordType{r.Type, model.RecordTypeGreeting},
		SessionID:         r.SessionID,
		IncludeBotEvil:    true,
	})
	clues.AddTrackE(ctx, "getContexts.below", below, err)
	if err != nil {
		return nil, err
	}

	all := append(above, r)
	all = append(all, below...)
	clues.AddT(ctx, "getContexts.all", all)
	return helper.Map(all, model.MsgRecord.ToUnsatisfiedReplyContext), nil
}
