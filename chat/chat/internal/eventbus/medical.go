package eventbus

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
)

// medicalReply 医疗大模型回复，纯hard coding
func medicalReply(ctx context.Context, bs *botsession.BotSession) (bool, bool, string, error) {
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, bs.ModelType)
	// 获取历史会话
	reWriteModel := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeQueryRewrite)
	var historyLimit uint = 3 // 医疗建议默认是3，不想大于5，先给他hardcoding
	if m.HistoryLimit < 3 {   // 演示的时候，要用单轮的效果，
		historyLimit = uint(reWriteModel.HistoryLimit)
	}
	var histories [][2]model.HisMessage
	if bs.ChatHistoryFromAPI != nil {
		histories = bs.MakeChatAPIHistory()
		log.InfoContextf(ctx, "use api histories:%s", helper.Object2String(histories))
	} else {
		histories, _ = makeMultiRoundHistories(ctx, bs.App.AppBizId, bs.Session,
			historyLimit, []model.RecordType{bs.Type}, true)
	}
	var his [][2]string
	for i := range histories {
		his = append(his, [2]string{histories[i][0].Content, histories[i][1].Content})
	}
	bs.PromptCtx.MultiRoundHistories = his
	log.InfoContextf(ctx, "R|medicalReply|MultiRoundHistories|%v", his)

	log.InfoContextf(ctx, "R|medicalReply|GetModel type: %v, model: %v", bs.ModelType, m)
	prompt, err := m.RenderMedicalPrompt(ctx, bs.PromptCtx)
	log.InfoContextf(ctx, "R|medicalReply|prompt:%s, ERR: %v", prompt, err)
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedureKnowledge))
	SendTokenStat(ctx, bs, bs.TokenStat)
	req := m.NewMedicalRequest(ctx, bs.Session, bs.RecordID, prompt, bs.SystemRole)
	log.InfoContextf(ctx, "R|medicalReply|NewMedicalRequest|%s %+v", bs.EventSource, req)
	st, last, lastEvil, isModelRejected, err := streamReply(ctx, bs, req,
		event.ProcedureKnowledge, model.ReplyMethodModel, histories)
	if err != nil {
		return false, false, "", err
	}
	if last == nil { // 没有输出
		return false, isModelRejected, "", nil
	}

	isEvil := lastEvil.GetResultCode() == ispkg.ResultEvil
	if !last.GetFinished() || isEvil { // 最后一个包含敏感词也需要兜底结束
		last.Finished = true
		ctx, cancel := context.WithCancel(ctx)
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID,
			bs.NewReplyEvent(ctx, last, isEvil, model.ReplyMethodModel, st, []string{}), cancel,
		)
	}
	reply := last.GetMessage().GetContent()
	method := helper.When(isEvil, model.ReplyMethodEvil, model.ReplyMethodModel)
	m0 := bs.NewBotRecord(ctx, reply, req, method, lastEvil, st)
	newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
	_, err = bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
	clues.AddTrackDataWithError(ctx, "medicalReply:dao.CreateMsgRecord", m0, err)
	if err != nil {
		return false, false, "", err
	}

	return isEvil, isModelRejected, "", nil // 通过Output为空，不去找参考来源
}
