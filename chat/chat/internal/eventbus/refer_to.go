package eventbus

import (
	"context"
	"path/filepath"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/ivy/qbot/qbot/chat/internal/dao"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	handlers[event.EventReferTo] = &ReferToEventHandler{}
}

// ReferToEventHandler 请求参考来源事件处理器
type ReferToEventHandler struct{}

// NewRequest 创建事件请求
func (e *ReferToEventHandler) NewRequest(ctx context.Context, cli *model.Conn, bs []byte) (any, error) {
	req := event.ReferToEvent{}
	if err := jsoniter.Unmarshal(bs, &req); err != nil {
		log.ErrorContextf(ctx, "Unmarshal event req error: %+v, data: %s", err, bs)
		return nil, err
	}
	return req, nil
}

// Process 处理事件请求
func (e *ReferToEventHandler) Process(ctx context.Context, cli *model.Conn, req any) error {
	ctx = clues.NewTrackContext(ctx)
	defer func(ctx *context.Context) { clues.Flush(*ctx) }(&ctx)
	ev := req.(event.ReferToEvent)
	clues.AddTrackData(ctx, "Conn", cli)
	clues.AddTrackData(ctx, "ReferToEvent", req)
	scene := dao.AppTestScene
	if ev.IsRelease {
		scene = dao.AppReleaseScene
	}
	app, err := bus.dao.GetAppByBizID(ctx, scene, ev.BotBizID)
	clues.AddTrackDataWithError(ctx, ev.Name()+":dao.GetAppByBizID", map[string]any{
		"scene": scene, "BotBizID": ev.BotBizID, "app": app}, err)
	if err != nil {
		return err
	}
	if app == nil {
		return pkg.ErrRobotNotExist
	}
	for i := range ev.Docs {
		ev.Docs[i].Answer = replacePlaceholders(ev.Docs[i].Answer, ev.Placeholders)
		ev.Docs[i].OrgData = replacePlaceholders(ev.Docs[i].OrgData, ev.Placeholders)
	}
	mr := &bot_knowledge_config_server.MatchReferReq{
		BotBizId:              app.GetAppBizId(),
		Docs:                  ev.Docs,
		Answer:                ev.Answer,
		IsRelease:             ev.IsRelease,
		MsgId:                 ev.RecordID,
		Question:              ev.Question,
		IgnoreConfidenceScore: ev.DisableMatch,
	}
	t0 := time.Now()
	refers, err := bus.dao.MatchRefer(ctx, mr)
	clues.AddTrack4RPC(ctx, ev.Name()+":dao.MatchRefer", mr, refers, err, t0)
	if err != nil {
		return err
	}
	if len(refers) == 0 {
		return nil
	}
	refs := make([]model.Reference, 0, len(refers))
	for _, v := range refers {
		name := v.GetName()
		if v.GetReferType() == model.ReferTypeDoc ||
			v.GetReferType() == model.ReferTypeSegment {
			name = removeExt(name)
		}
		refs = append(refs, model.Reference{
			ID:       v.GetReferId(),
			Type:     v.GetReferType(),
			URL:      v.GetUrl(),
			DocID:    v.GetDocId(),
			Name:     name,
			DocBizID: v.GetDocBizId(),
			DocName:  v.GetDocName(),
			QABizID:  v.GetQaBizId(),
		})
	}
	clues.AddTrackData(ctx, ev.Name()+":refs", refs)
	err = bus.dao.UpdateMsgRecordRef(ctx, ev.RecordID, refs, ev.BotBizID)
	clues.AddTrackE(ctx, "dao.UpdateMsgRecordRef", ev.RecordID, err)
	if err != nil {
		return err
	}
	ctx, cancel := context.WithCancel(ctx)
	_ = bus.dao.DoEmitWsClient(ctx, cli.ClientID, &event.ReferenceEvent{
		RecordID:   ev.RecordID,
		References: refs,
	}, cancel)
	return nil
}

func removeExt(name string) string {
	return name[:len(name)-len(filepath.Ext(name))]
}
