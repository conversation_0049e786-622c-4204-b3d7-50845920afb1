global:
  env_name: dev # 服务运行环境

server: # 服务端配置
  app: qbot.qbot # 业务的应用名，格式：`业务领域.功能模块`
  server: chat # 进程服务名
  admin: # Admin HTTP server
    ip: 0.0.0.0
    port: 8081
  filter:
    - recovery
    - validation
  service: # 业务服务提供的service，可以有多个
    - name: qbot.qbot.chat.Chat.trpc # service的路由名称，格式：`业务领域.功能模块.项目名.Pb定义的服务名`
      ip: 0.0.0.0 # 除了本地，其他环境都需要写成 nic: eth1
      port: 9090 # 服务监听端口
      network: tcp # 网络监听类型 tcp/udp
      protocol: trpc # 应用层协议 trpc/restful
      timeout: 3000 # 请求最长处理时间，单位毫秒

plugins: # 插件配置
  config:
    file:
      providers:
        - name: rainbow
          path: ./config
  log:
    default:
      - writer: console # 控制台标准输出，非开发环境建议输出到 file
        level: debug
        caller_skip: 3 # 控制log函数嵌套深度
