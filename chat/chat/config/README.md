本地 `trpc_go.template.yaml` 和 `application.template.yaml` 是项目配置模板，项目可以将最新的配置记录存放在 `template` 文件中（切记不要存放 IP、密码等信息，只存在配置项结构），方便本地开发调试，以及在远程配置中心配置的时候进行参考。

如果要本地调试，拷贝 `trpc_go.template.yaml` 到同级目录，命名为 `trpc_go.yaml`，但它仅用于 **`本地开发自测`**，不能提交到 git 上进行托管，项目的 `.gitignore` 中默认忽略 `trpc_go.yaml` 这个文件，防止把带有账号、密码等信息的配置文件错误提交了。

> **本地配置 `不应该包含密码、数据库地址等敏感信息`，禁止将这些敏感信息提交到 git 上。**
>
> 其他各环境的配置应该通过各自的发布平台或配置中心来管理。
>
> 配置使用详情见：https://iwiki.woa.com/pages/viewpage.action?pageId=441983120
