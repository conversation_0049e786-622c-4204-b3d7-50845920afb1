[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "Cha<PERSON>", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "qbot.qbot.chat.Chat", "MethodName": "GetSharePageWsToken", "Func": "/qbot.qbot.chat.Chat/GetSharePageWsToken", "ReqBody": "qbot.qbot.chat.GetSharePageWsTokenReq", "RspBody": "qbot.qbot.chat.GetSharePageWsTokenRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"share_code": "ncRCRF"}, "CheckList": [{"JsonPath": "", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "222111333xxxxx222111"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]