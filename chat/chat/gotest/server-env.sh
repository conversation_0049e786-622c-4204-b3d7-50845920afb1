#!/bin/bash

# trpc-cli 相关资料
# https://iwiki.woa.com/p/194215409

TRPC_CLI="$HOME/go/bin/trpc-cli"
TRPC_CLI="$HOME/gosrc0/trpc-cli"

PROTO_FILE="./chat.proto"

#TARGET="ip://************:8000"
#TARGET="ip://************:8000"
TARGET="ip://**********:9090"

echo "--------------------------------------------------------------"
echo "TRPC_CLI=$TRPC_CLI"
echo "PROTO_FILE=$PROTO_FILE"
echo "TARGET=$TARGET"
echo "--------------------------------------------------------------"



#$TRPC_CLI -protofile="$PROTO_FILE" -interfacelist

#$TRPC_CLI -protofile="$PROTO_FILE" -interfacename=GetTokenUsage -outputjson=GetTokenUsage.json
# $TRPC_CLI -protofile="$PROTO_FILE" -interfacename=SendDataSyncTaskEvent -outputjson=SendDataSyncTaskEvent.json
# $TRPC_CLI -protofile="$PROTO_FILE" -interfacename=GetPipelineDebugging -outputjson=GetPipelineDebugging.json


