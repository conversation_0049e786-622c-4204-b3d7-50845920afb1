syntax = "proto3";

package qbot.qbot.chat;

option go_package = "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat";

import "validate.proto";

service Chat {
  // 获取机器人token用量信息
  rpc GetTokenUsage(TokenUsageReq) returns (TokenUsageRsp);
  // 获取 分享页面WS Token
  rpc GetSharePageWsToken(GetSharePageWsTokenReq) returns (GetSharePageWsTokenRsp);

  // pipeline debug 信息
  rpc GetPipelineDebugging(GetPipelineDebuggingReq) returns (GetPipelineDebuggingRsp);

}

message  TokenUsageReq {
  repeated uint64 bot_biz_id = 1;
}

message TokenUsageRsp {
  repeated TokenUsage usage = 1;
}

message TokenUsage {
  uint64 bot_biz_id = 1;
  uint64 usage = 2;
}

message GetSharePageWsTokenReq {
  string share_code = 2; // 分享码
}

message GetSharePageWsTokenRsp {
  string token = 1;
  string app_type = 2; //应用类型
  string name = 3; //应用名称
  string avatar = 4; //应用头像
  string greeting = 5; //应用问候语
  uint64 bot_biz_id = 6; //机器人ID
  uint64 seat_biz_id = 7; //坐席ID
  repeated string recommend_questions = 8; // 推荐问题
  repeated ExperienceRecommendQuestion experience_recommend_question = 9; // 体验中心推荐问题
}

message ExperienceRecommendQuestion {
  string question = 1; //推荐问题
  string content = 2; //快捷按钮内容
  uint32 show_type = 3; //显示类型
}



message GetPipelineDebuggingReq {
  uint64 app_biz_id   = 1; // 应用 ID
  string record_id    = 2 [(validate.rules).string = { min_len: 1 }]; // 一问一答, 对应<问>的记录 ID
}

message GetPipelineDebuggingRsp {
  message PipelineDebugging {
    string      key         = 1;  // 步骤关键字
    string      input       = 2;  // 输入参数
    string      output      = 3;  // 输出参数
    uint32      status      = 4;  // 状态值
    string      fail_msg    = 5;  // 失败消息
    string      extra_data  = 6;  // 附加信息
    int64       start_time  = 7;  // 开始时间, ms
    int64       end_time    = 8;  // 结束时间, ms
  }
  repeated PipelineDebugging Logs = 1; // pipeline 过程信息
}