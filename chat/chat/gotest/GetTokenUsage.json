[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "Cha<PERSON>", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "qbot.qbot.chat.Chat", "MethodName": "GetTokenUsage", "Func": "/qbot.qbot.chat.Chat/GetTokenUsage", "ReqBody": "qbot.qbot.chat.TokenUsageReq", "RspBody": "qbot.qbot.chat.TokenUsageRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"bot_biz_id": [1728995369063141376]}, "CheckList": [{"JsonPath": "usage", "OP": "EQ", "TARGET": []}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": []}]