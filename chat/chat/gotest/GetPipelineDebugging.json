[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "Cha<PERSON>", "CaseGenMode": "easy-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "qbot.qbot.chat.Chat", "MethodName": "GetPipelineDebugging", "Func": "/qbot.qbot.chat.Chat/GetPipelineDebugging", "ReqBody": "qbot.qbot.chat.GetPipelineDebuggingReq", "RspBody": "qbot.qbot.chat.GetPipelineDebuggingRsp", "Protocol": "trpc", "Setup": "", "Teardown": "", "RequestJson": {"app_biz_id": "1784484525112295424", "record_id": "teA_20240528_201941_052_4wyNhdVj"}, "CheckList": [{"JsonPath": "Logs", "OP": "EQ", "TARGET": []}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": [{"TransInfoKey": "request_id", "TransInfoValue": "4x44"}, {"TransInfoKey": "uin", "TransInfoValue": "************"}, {"TransInfoKey": "sub_account_uin", "TransInfoValue": "************"}]}]