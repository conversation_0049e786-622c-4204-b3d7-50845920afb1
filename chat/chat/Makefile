.PHONY: build build-all clean vet fmt run

GOCMD=GO111MODULE=on go
BINARY=bin/`basename $(CURDIR)`

# Build info
GO_VERSION=`go version | sed 's/go version //g'`
BUILD_GIT_HASH=`git rev-parse --short HEAD`
BUILD_TIME=`date '+%Y-%m-%d %H:%M:%S'`
BUILD_LDFLAGS=-X '${main}.BuildGoVersion=${GO_VERSION}' -X '${main}.BuildGitHash=${BUILD_GIT_HASH}' -X '${main}.BuildTime=${BUILD_TIME}'

build:
	@echo "build go binary..."
	${GOCMD} build -ldflags="${BUILD_LDFLAGS}" -o ${BINARY} ${GOARGS} cmd/main.go
	@echo "build go binary done"

build-linux:
	@echo "build go binary for linux..."
	GOOS=linux GOARCH=amd64 ${GOCMD} build -ldflags="${BUILD_LDFLAGS}" -o ${BINARY} ${GOARGS} cmd/main.go
	@echo "build go binary for linux done"

build-all:
	@echo "build all go files..."
	${GOCMD} build ${GOARGS} ./...
	@echo "build all go files done"

clean:
	@echo "clean go binary..."
	@if [ -f ${BINARY} ] ; then rm ${BINARY} ; fi
	@echo "clean go binary done"

vet:
	@echo "vet go files..."
	${GOCMD} vet ${GOARGS} ./...
	@echo "vet go files done"

fmt:
	@echo "fmt go files..."
	${GOCMD} fmt ${GOARGS} ./...
	@echo "fmt go files done"

run:
	@echo "run server..."
	${GOCMD} run ${GOARGS} cmd/main.go -conf config/trpc_go.yaml || true
lint:
	@echo "lint..."
	find . -name '*.go' -exec $(GOPATH)/bin/goimports -local git.code.oa.com,git.woa.com,git.tencent.com -w -l {} \;
	staticcheck ./...
	golangci-lint run

DATE=$(shell date +%Y%m%d-%H%M%S)
GIT_COMMIT=$(shell git rev-parse --short HEAD)
GIT_BRANCH=$(shell git rev-parse --abbrev-ref HEAD)
IMAGE_REPO  ?=  csighub.tencentyun.com/gaussguan
IMAGE_TAG   ?=  v1.7.0-$(GIT_COMMIT)-$(DATE)
TARGET = chat_gaussguan

docker_build: build  ## Build docker images.
	echo ${IMAGE_REPO}/${TARGET}:${IMAGE_TAG}
	cd bin; docker build -t ${IMAGE_REPO}/${TARGET}:${IMAGE_TAG} .
	docker push ${IMAGE_REPO}/${TARGET}:${IMAGE_TAG}


# upload to TKEx-CSIG
upload2tkex:
	@./build/rtools-patch.sh 10

# shortcut for upload2tkex
ut: upload2tkex

ut9:
	./build/rtools-patch.sh 9

ut10:
	./build/rtools-patch.sh 10

ut11:
	./build/rtools-patch.sh 11

# 创建标准 TAG
# make tag: 从 master 打 TAG
# make tag FROM_BRANCH=true: 从当前分支打 TAG
tag:
	@../git-tag.sh $(FROM_BRANCH)