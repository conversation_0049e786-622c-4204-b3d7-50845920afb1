ALTER TABLE
    `t_chat_prompt`
    ADD COLUMN `workflow_result` text NULL COMMENT '工作流检索结果'

-- online ddl
ALTER TABLE `t_msg_record_token_stat`
    ADD COLUMN `agent_thought` LONGTEXT NULL COMMENT 'Agent思考过程' After `procedures`;
UPDATE `t_msg_record_token_stat`
SET `agent_thought` = ''
WHERE `agent_thought` IS NULL;

-- 上线后 可将字段更新为 NOT NULL
ALTER TABLE `t_msg_record_token_stat`
    MODIFY COLUMN `agent_thought` LONGTEXT NOT NULL COMMENT 'Agent思考过程';