CREATE TABLE `t_reject_answer`
(
    `id`          bigint unsigned                                       NOT NULL AUTO_INCREMENT,
    `pattern`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回复方式',
    `create_time` datetime                                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted`  tinyint(1)                                            NOT NULL DEFAULT '0' COMMENT '是否逻辑删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='拒答回复方式';

INSERT INTO t_reject_answer (pattern) VALUES ('根据提供的信息，无法确定');
INSERT INTO t_reject_answer (pattern) VALUES ('对不起，我无法提供');
INSERT INTO t_reject_answer (pattern) VALUES ('尚未确定');
INSERT INTO t_reject_answer (pattern) VALUES ('根据提供的信息，无法找到');
INSERT INTO t_reject_answer (pattern) VALUES ('对不起，已知信息和问题无关');
INSERT INTO t_reject_answer (pattern) VALUES ('对不起已知信息和问题无关');
INSERT INTO t_reject_answer (pattern) VALUES ('对不起，我不理解您的问题');
INSERT INTO t_reject_answer (pattern) VALUES ('对不起，根据图片内容');
INSERT INTO t_reject_answer (pattern) VALUES ('很抱歉');
INSERT INTO t_reject_answer (pattern) VALUES ('抱歉，根据提供的信息');
INSERT INTO t_reject_answer (pattern) VALUES ('对不起，根据提供的已知信息');
INSERT INTO t_reject_answer (pattern) VALUES ('对不起，您提供的信息');
INSERT INTO t_reject_answer (pattern) VALUES ('对不起，根据提供的信息');
INSERT INTO t_reject_answer (pattern) VALUES ('抱歉，根据提供的已知信息');
INSERT INTO t_reject_answer (pattern) VALUES ('根据您提供的信息，无法确定');
INSERT INTO t_reject_answer (pattern) VALUES ('根据提供的信息，我无法');
INSERT INTO t_reject_answer (pattern) VALUES ('对不起，我无法提供');
INSERT INTO t_reject_answer (pattern) VALUES ('基于这些信息，我无法');
INSERT INTO t_reject_answer (pattern) VALUES ('我无法根据这些信息回答');
INSERT INTO t_reject_answer (pattern) VALUES ('根据提供的已知信息，我无法');
INSERT INTO t_reject_answer (pattern) VALUES ('抱歉，您提供的信息');
INSERT INTO t_reject_answer (pattern) VALUES ('抱歉，由于提供的信息');
INSERT INTO t_reject_answer (pattern) VALUES ('对不起，您提供的已知信息');
INSERT INTO t_reject_answer (pattern) VALUES ('抱歉，我无法提供');
INSERT INTO t_reject_answer (pattern) VALUES ('对不起，您的问题');
INSERT INTO t_reject_answer (pattern) VALUES ('无法从已知信息中确定');
INSERT INTO t_reject_answer (pattern) VALUES ('根据您提供的已知信息，无法确定');
INSERT INTO t_reject_answer (pattern) VALUES ('对不起，我不能回答这个问题');
INSERT INTO t_reject_answer (pattern) VALUES ('对不起，我不能提供');

alter table t_msg_doc_record
    add column `record_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '记录 ID' after `session_id`;
alter table t_msg_doc_record
    add column model_name varchar(128) DEFAULT ''  COMMENT '模型名称' after doc_id;
alter table t_msg_doc_record
    add column summary text NOT NULL COMMENT '文档摘要' after model_name;


ALTER TABLE `t_msg_record`
    Modify COLUMN has_read tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读';

ALTER TABLE t_msg_record
    add COLUMN quote_infos varchar(512) DEFAULT '' COMMENT '引用信息';

CREATE TABLE `t_system_intent`
(
    `id`              bigint unsigned NOT NULL AUTO_INCREMENT,
    `intent_idx`      varchar(255)    NOT NULL DEFAULT '' COMMENT '意图索引',
    `intent_category` varchar(255)    NOT NULL DEFAULT '' COMMENT '意图类别',
    `create_time`     datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted`      tinyint(1)      NOT NULL DEFAULT '0' COMMENT '是否逻辑删除',
    PRIMARY KEY (t_system_intent.`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='系统意图';

INSERT INTO
    `t_intent` (
    `policy_id`,
    `category`,
    `name`,
    `is_deleted`,
    `is_used`,
    `operator`
)
VALUES
    (1, 'knowledge_qa', '指令跟随回答', 0, 1, 'gaussguan'),
    (1, 'knowledge_qa', '段落处理', 0, 1, 'gaussguan'),
    (1, 'doc_summary', '全文处理', 0, 1, 'gaussguan'),
    (1, 'self_awareness', '自我认知', 0, 1, 'gaussguan');

update t_intent set name = '代码问题' where name = '代码';


CREATE TABLE IF NOT EXISTS `t_robot_keywords` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `robot_id` bigint NOT NULL COMMENT '应用ID',
    `keywords` varchar(200) NOT NULL DEFAULT '' COMMENT '关键词',
    `similarwords` varchar(200) NOT NULL DEFAULT '' COMMENT '相似词',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否逻辑删除',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_robot_id` (`robot_id`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='应用专业词设置';


CREATE TABLE `t_prompt_version` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `model_type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模型类型',
  `model_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模型名称',
  `version` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模型使用的prompt版本',
  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '0：未删除，1已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_type_name_version` (`model_type`,`model_name`,`version`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

INSERT INTO t_prompt_version(`model_type`,`model_name`,`version`,`is_deleted`,`create_time`,`update_time`) VALUES 
('normal_message','hunyuan-13B','hunyuan',0,NOW(),NOW()),
('normal_message','hunyuan','hunyuan',0,NOW(),NOW()),
('normal_message','hunyuan-standard-256K','hunyuan',0,NOW(),NOW()),
('normal_message_non_general_knowledge','hunyuan-13B','hunyuan',0,NOW(),NOW()),
('normal_message_non_general_knowledge','hunyuan','hunyuan',0,NOW(),NOW()),
('normal_message_non_general_knowledge','hunyuan-standard-256K','hunyuan',0,NOW(),NOW());
