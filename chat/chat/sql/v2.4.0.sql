-- 消息记录调试信息表
CREATE TABLE `t_msg_record_token_stat` (
    `id`                   bigint unsigned NOT NULL AUTO_INCREMENT,
    `record_id`            varchar(64)     NOT NULL DEFAULT '' COMMENT '记录 ID',
    `trace_id`             varchar(64)     NOT NULL DEFAULT '' COMMENT 'trace ID',
    `used_count`           int unsigned    NOT NULL DEFAULT 0 COMMENT 'token 已使用数',
    `free_count`           int unsigned    NOT NULL DEFAULT 0 COMMENT '免费 token 数',
    `order_count`          int unsigned    NOT NULL DEFAULT 0 COMMENT '订单总 token 数',
    `status_summary`       varchar(64)     NOT NULL DEFAULT '' COMMENT '当前执行状态汇总',
    `status_summary_title` varchar(64)     NOT NULL DEFAULT '' COMMENT '当前执行状态汇总后中文展示',
    `elapsed`              int unsigned    NOT NULL DEFAULT 0 COMMENT '当前请求执行时间, 单位 ms',
    `token_count`          int unsigned    NOT NULL DEFAULT 0 COMMENT '当前请求消耗 token 数',
    `procedures`           longtext        NOT NULL COMMENT '调试过程信息',
    `is_deleted`           tinyint(1)      NOT NULL DEFAULT 0 COMMENT '是否逻辑删除:0否，1是',
    `create_time`          datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_record_id` (`record_id`),
    KEY `idx_trace_id` (`trace_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '消息记录统计信息表';