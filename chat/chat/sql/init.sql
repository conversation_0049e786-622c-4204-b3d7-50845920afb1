CREATE TABLE `t_msg_record`
(
    `id`                bigint unsigned                                                NOT NULL AUTO_INCREMENT,
    `type`              tinyint                                                        NOT NULL COMMENT '记录类型',
    `bot_biz_id`        bigint unsigned                                                NOT NULL DEFAULT '0' COMMENT '机器人ID',
    `session_id`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '会话 ID',
    `record_id`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '记录 ID',
    `related_record_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '关联记录 ID',
    `to_id`             bigint unsigned                                                NOT NULL COMMENT '接收者 ID',
    `to_type`           tinyint                                                        NOT NULL COMMENT '记录类型',
    `from_id`           bigint unsigned                                                NOT NULL COMMENT '发送者 ID',
    `from_type`         tinyint                                                        NOT NULL COMMENT '记录类型',
    `content`           longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL COMMENT '消息内容',
    `image_url`         varchar(512) COLLATE utf8mb4_general_ci                        NOT NULL DEFAULT '' COMMENT '图片URL',
    `caption`           text COLLATE utf8mb4_general_ci                                NOT NULL COMMENT '图片识别内容',
    `intent`            varchar(255) COLLATE utf8mb4_general_ci                        NOT NULL DEFAULT '' COMMENT '意图',
    `labels`            text COLLATE utf8mb4_general_ci                                NOT NULL COMMENT '标签',
    `intent_category`   varchar(50) COLLATE utf8mb4_general_ci                         NOT NULL DEFAULT '' COMMENT '意图类型',
    `rewrote_content`   longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL COMMENT '改写的消息内容',
    `has_read`          tinyint(1)                                                     NOT NULL COMMENT '是否已读',
    `score`             tinyint(1)                                                     NOT NULL COMMENT '评价',
    `reason`            varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '评价原因',
    `reference`         longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL COMMENT '参考来源',
    `prompt`            longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL COMMENT '大模型提示词',
    `statistic_info`    varchar(1024) COLLATE utf8mb4_general_ci                       NOT NULL DEFAULT '' COMMENT '模型响应统计数据',
    `static_info`       varchar(1024) COLLATE utf8mb4_general_ci                       NOT NULL DEFAULT '' COMMENT '模型响应统计数据',
    `rewrite_prompt`    longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL COMMENT '改写大模型提示词',
    `result_type`       int unsigned                                                   NOT NULL DEFAULT '0' COMMENT '恶意类型',
    `result_code`       int unsigned                                                   NOT NULL DEFAULT '0' COMMENT '恶意操作码',
    `knowledge`         longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL COMMENT '检索到的知识',
    `rejected_question` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL COMMENT '检索到的拒答问题',
    `reply_method`      tinyint                                                        NOT NULL COMMENT '回复方式',
    `is_deleted`        tinyint(1)                                                     NOT NULL COMMENT '是否逻辑删除',
    `rating_time`       datetime                                                       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '首次评分时间',
    `create_time`       datetime                                                       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime                                                       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `cfg_version_id`    bigint unsigned                                                NOT NULL DEFAULT '0' COMMENT '机器人配置版本ID',
    `trace_id`          varchar(64) COLLATE utf8mb4_general_ci                         NOT NULL DEFAULT '' COMMENT 'Trace ID',
    `token_stat`        text COLLATE utf8mb4_general_ci COMMENT 'TokenStatEvent 数据, JSON 格式文本',
    `option_cards`      varchar(1024) COLLATE utf8mb4_general_ci                       NOT NULL DEFAULT '' COMMENT '选项卡',
    `task_flow`         text COLLATE utf8mb4_general_ci COMMENT '任务流程信息',
    `files`             varchar(4096) COLLATE utf8mb4_general_ci                       NOT NULL DEFAULT '' COMMENT '上传的文件信息,JSON格式文本',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_record_id` (`record_id`) USING BTREE,
    KEY `idx_from_id_from_type` (`from_id`, `from_type`) USING BTREE,
    KEY `idx_to_id_to_type` (`to_id`, `to_type`) USING BTREE,
    KEY `idx_related_record_id` (`related_record_id`) USING BTREE,
    KEY `idx_type_from_type_result_code` (`type`, `from_type`, `result_code`) USING BTREE,
    KEY `idx_session_id_record_id` (`session_id`, `record_id`) USING BTREE,
    KEY `idx_session_id_rating_time_score` (`session_id`, `rating_time`, `score`) USING BTREE,
    KEY `idx_robot_id` (`bot_biz_id`) USING BTREE,
    KEY `idx_create_time` (`create_time`),
    FULLTEXT KEY `idx_content` (`content`),
    FULLTEXT KEY `idx_prompt` (`prompt`),
    FULLTEXT KEY `idx_rewrite_prompt` (`rewrite_prompt`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1157467
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '消息记录'