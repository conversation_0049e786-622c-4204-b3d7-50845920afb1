ALTER TABLE
    `t_msg_record`
ADD
    COLUMN file_infos varchar(4096) NOT NULL DEFAULT '' COMMENT '文件信息'
After
    `caption`;


CREATE TABLE `t_msg_doc_record`
(
    `id`          bigint unsigned                                              NOT NULL AUTO_INCREMENT,
    `bot_biz_id`  bigint unsigned                                              NOT NULL DEFAULT '0' COMMENT '机器人ID',
    `session_id`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会话 ID',
    `file_url`    varchar(512) COLLATE utf8mb4_general_ci                      NOT NULL DEFAULT '' COMMENT '文件URL',
    `doc_id`      varchar(64) COLLATE utf8mb4_general_ci                       NOT NULL DEFAULT '' COMMENT '文档 ID',
    `cos_id`      bigint unsigned                                              NOT NULL DEFAULT '0' COMMENT 'cos ID',
    `doc_name`    varchar(256) COLLATE utf8mb4_general_ci                      NOT NULL DEFAULT '' COMMENT '文档名称',
    `is_deleted`  tinyint(1)                                                   NOT NULL DEFAULT '0' COMMENT '是否逻辑删除',
    `create_time` datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_session_id` (`session_id`) USING BTREE,
    KEY `idx_robot_id` (`bot_biz_id`) USING BTREE,
    KEY `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '消息文件映射表';

update  `t_intent` set category = 'mathematics_calculate' where name = '数学计算';