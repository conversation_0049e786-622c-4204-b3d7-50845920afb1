CREATE TABLE IF NOT EXISTS `t_chat_prompt`
(
    `id`              bigint unsigned                                              NOT NULL AUTO_INCREMENT,
    `bot_biz_id`      bigint unsigned                                              NOT NULL DEFAULT '0' COMMENT '机器人ID',
    `session_id`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会话 ID',
    `record_id`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '问题记录 ID',
    `intent_prompt`   text                                                         NOT NULL COMMENT '意图Prompt',
    `intent_result`   varchar(256)                                                 NOT NULL DEFAULT '' COMMENT '意图结果',
    `workflow_prompt` longtext                                                     NOT NULL COMMENT '工作流知识问答Prompt',
    `is_deleted`      tinyint(1)                                                   NOT NULL DEFAULT '0' COMMENT '是否逻辑删除',
    `create_time`     datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_record_id` (`record_id`) USING BTREE,
    KEY `idx_session_id` (`session_id`) USING BTREE,
    KEY `idx_robot_id` (`bot_biz_id`) USING BTREE,
    KEY `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '对话prompt';