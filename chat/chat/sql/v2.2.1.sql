ALTER TABLE `t_msg_record`
    ADD COLUMN image_url varchar(512) NOT NULL DEFAULT '' COMMENT '图片URL' After `content`;
ALTER TABLE `t_msg_record`
    ADD COLUMN caption text NOT NULL COMMENT '图片识别内容' After `image_url`;

ALTER TABLE `t_session`
    ADD COLUMN reset_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '重置时间' After `is_deleted`;

ALTER TABLE `t_msg_record`
    ADD COLUMN image_url  varchar(512) NOT NULL DEFAULT '' COMMENT '图片URL' After `content`,
    ADD COLUMN caption    text COMMENT '图片识别内容' After `image_url`,
    ADD COLUMN token_stat text COMMENT 'TokenStatEvent 数据, JSON 格式';

ALTER TABLE `t_msg_record`
    ADD COLUMN cfg_version_id bigint unsigned NOT NULL DEFAULT '0' COMMENT '机器人配置版本ID';

ALTER TABLE `t_msg_record`
    ADD COLUMN trace_id varchar(64) NOT NULL DEFAULT '' COMMENT 'Trace ID';

#ALTER TABLE `t_msg_record` Modify COLUMN caption text NOT NULL  COMMENT '图片识别内容' ;

ALTER TABLE `t_msg_record`
    ADD COLUMN token_stat text COMMENT 'token 统计信息';