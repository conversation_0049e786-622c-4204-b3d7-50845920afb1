CREATE TABLE `agent_tool_config`
(
    `id`               INT UNSIGNED      NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
    `category`         VARCHAR(64)   NOT NULL DEFAULT '' COMMENT '类别，如 “搜索”、“图片生成”',
    `plugin_name`      VARCHAR(128)  NOT NULL DEFAULT '' COMMENT '插件标识，如 “WebSearch”、“TextToImage”',
    `tool_name`        VARCHAR(128)  NOT NULL DEFAULT '' COMMENT '工具内部名称，如 “WebSearch”',
    `mapping_template` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '映射文案模板，如 “{工具名称} 正在搜索”',
    `display_type`     VARCHAR(50)   NOT NULL DEFAULT '' COMMENT '展示类型',
    `display_param`    VARCHAR(100)  NOT NULL DEFAULT '' COMMENT '展示参数，展示对应字段，如 “Query”对应的值',
    `example`          VARCHAR(1024) NOT NULL DEFAULT '' COMMENT '示例说明，多行文本',
    `create_time`      DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_plugin_tool` (`plugin_name`,`tool_name`),
    KEY                `idx_category` (`category`),
    KEY                `idx_plugin_name` (`plugin_name`)
) ENGINE=InnoDB
  DEFAULT CHARSET = utf8mb4
  COMMENT = 'Agent工具静态配置表';



INSERT INTO `agent_tool_config`
(`category`
,`plugin_name`
,`tool_name`
,`mapping_template`
,`display_type`
,`display_param`
,`example`)
VALUES
    -- code 代码生成
    ('code', '代码解释器',              'CodeInterpreter',    '{工具名称} 代码生成中', '', '', ''),
    ('code', '代码解释器',              'GenerateCharts',     '{工具名称} 代码生成中', '', '', ''),
    ('code', '腾讯云 EdgeOne Pages',    'deploy-html',        '{工具名称} 代码生成中', '', '', '腾讯云 EdgeOne Pages/deploy-html 代码生成中'),

    -- image 图片生成
    ('image','图片生成',                'TextToImage',        '{工具名称} 图片生成中', '', 'Prompt',   '图片生成/TextToImage 图片生成中 {盛开的粉色樱花树, 花瓣随风飘落, 阳光透过花瓣形成柔和光晕, 背景是蓝天和远山, 营造浪漫唯美春日氛围}'),
    ('image','脑图生成',                'MindMapGenerate',    '{工具名称} 图片生成中', '', 'Markdown', '脑图生成/MindMapGenerate 图片生成中 [\"Markdown\":\"## 深度学习\\n# 基础概念\\n- 神经网络\\n- 激活函数\\n- 损失函数\\n\\n# 优化算法\\n常见\"]'),
    ('image','商品背景生成',            'ReplaceBackground',  '{工具名称} 图片生成中', '', '', ''),

    -- search 搜索
    ('search','混元搜索',               'HunyuanSearchSummary','{工具名称} 正在搜索',   '', 'Query',   '混元搜索/HunyuanSearchSummary 李白介绍'),
    ('search','DeepSeek 搜索',          'DeepSeekV3Search',   '{工具名称} 正在搜索',   '', 'Query',   'DeepSeek搜索/DeepSeekV3Search 腾讯2025年5月最新新闻'),
    ('search','DeepSeek 搜索',          'DeepSeekR1Search',   '{工具名称} 正在搜索',   '', 'Query',   'DeepSeek搜索/DeepSeekR1Search 腾讯2025年5月最新新闻'),
    ('search','搜狗搜索',               'SougouWebSearch',    '{工具名称} 正在搜索',   '', 'Query',   '搜狗搜索/SougouWebSearch 腾讯2025年5月最新新闻'),
    ('search','网页搜索',               'WebSearch',          '{工具名称} 正在搜索',   '', 'Query',   ''),
    ('search','Tavily',                'tavily-search',      '{工具名称} 正在搜索',   '', 'Query',   ''),
    ('search','Airbnb',                'airbnb_search',      '{工具名称} 正在搜索',   '', 'location',''),
    ('search','有声电台（喜马拉雅）',   'AlbumSearch',        '{工具名称} 正在搜索',   '', '',        ''),
    ('search','有声电台（喜马拉雅）',   'TrackSearch',        '{工具名称} 正在搜索',   '', '',        ''),
    ('search','百科',                  'GetBaikeInfo',       '{工具名称} 正在查询',   '', 'Keyword', '百科/GetBaikeInfo 正在查询 [舟山]'),
    ('search','腾讯医典',              'MedicalSearch',      '{工具名称} 正在搜索',   '', 'Query',   ''),

    -- knowledge 知识库问答
    ('knowledge','知识库问答',        'KnowledgeRetrievalAnswer','{工具名称} 正在检索','', 'Query','知识库问答/KnowledgeRetrievalAnswer 正在查询 积水潭挂号多少钱'),

    -- calculation 科学计算
    ('calculation','科学计算',        'Calculator',         '{工具名称} 正在计算',   '', 'Expression','科学计算/Calculator 正在计算 1+1=2'),

    -- parse 网页解析
    ('parse','网页解析',             'WebPageParse',       '{工具名称} 正在解析',   '', 'PageUrl', ''),
    ('parse','网页解析',             'WebpageParsingQA',   '{工具名称} 正在回复',   '', 'Query',   ''),
    -- 浏览器操作（x5_use）
    ('x5_use', 'lke sandbox',               'browser_go_to_url',               '{工具名称} 正在浏览网页',           '', 'url',   'QQ浏览器沙箱环境/go_to_url 正在浏览网页：http://stic.sz.gov.cn/'),
    ('x5_use', 'lke sandbox',          'browser_click_element',          '{工具名称} 正在点击元素',           '', 'index', ''),
    ('x5_use', 'lke sandbox',            'browser_input_text',            '{工具名称} 正在输入',               '', 'text',  ''),
    ('x5_use', 'lke sandbox',           'browser_scroll_down',           '{工具名称} 正在向下滑动',           '', '',      ''),
    ('x5_use', 'lke sandbox',             'browser_scroll_up',             '{工具名称} 正在向上滑动',           '', '',      ''),
    ('x5_use', 'lke sandbox',                  'browser_done',                  '{工具名称} 已完成前动作',           '', '',      ''),
    ('x5_use', 'lke sandbox',  'browser_get_dropdown_options',  '{工具名称} 正在获取所有选项',       '', '',      ''),
    ('x5_use', 'lke sandbox','browser_select_dropdown_option','{工具名称} 正在进行选择',           '', '',      ''),
    ('x5_use', 'lke sandbox',               'browser_go_back',               '{工具名称} 正在返回',               '', '',      ''),
    ('x5_use', 'lke sandbox',                  'browser_wait',                  '{工具名称} 正在等待中',             '', '',      ''),
    ('x5_use', 'lke sandbox',        'browser_scroll_to_text',        '{工具名称} 正在滑动至指定内容',     '', '',      ''),
    ('x5_use', 'lke sandbox',      'browser_scroll_to_bottom',      '{工具名称} 正在滑动至指定按钮',     '', '',      ''),
    ('x5_use', 'lke sandbox',         'browser_scroll_to_top',         '{工具名称} 正在滑动至最上方',       '', '',      ''),
    ('x5_use', 'lke sandbox',         'browser_download_file',         '{工具名称} 正在进行文件下载',       '', '',      ''),

    -- Shell 命令
    ('shell', 'lke sandbox',               'shell_exec',               '{工具名称} 正在执行命令',       '', '', ''),
    ('shell', 'lke sandbox',               'shell_view',               '{工具名称} 正在查看进程',       '', '', ''),
    ('shell', 'lke sandbox',               'shell_wait',               '{工具名称} 正在等待进程',       '', '', ''),
    ('shell', 'lke sandbox',   'shell_write_to_process',   '{工具名称} 正在向进程写入数据', '', '', ''),
    ('shell', 'lke sandbox',       'shell_kill_process',       '{工具名称} 正在终止进程',       '', '', ''),

    -- 文件系统操作
    ('filesystem', 'lke sandbox',            'create_directory',            '{工具名称} 正在创建目录',                '', 'Path', ''),
    ('filesystem', 'lke sandbox',              'list_directory',              '{工具名称} 正在列举目录',                '', '',      ''),
    ('filesystem', 'lke sandbox',              'directory_tree',              '{工具名称} 正在获取目录树',              '', '',      ''),
    ('filesystem', 'lke sandbox',                   'move_file',                   '{工具名称} 正在移动文件',                '', '',      ''),
    ('filesystem', 'lke sandbox',                'search_files',                '{工具名称} 正在搜索文件',                '', '',      ''),
    ('filesystem', 'lke sandbox',               'get_file_info',               '{工具名称} 正在获取文件信息',            '', '',      ''),
    ('filesystem', 'lke sandbox',    'list_allowed_directories',    '{工具名称} 正在列举允许访问的目录',      '', '',      ''),
    ('filesystem', 'lke sandbox',                   'read_file',                   '{工具名称} 正在读取文件',                '', '',      ''),
    ('filesystem', 'lke sandbox',          'read_multiple_file',          '{工具名称} 正在多文件',                  '', '',      ''),
    ('filesystem', 'lke sandbox',                  'write_file',                  '{工具名称} 正在写入文件',                '', '',      ''),
    ('filesystem', 'lke sandbox',                 'deploy_file',                 '{工具名称} 正在部署文件',                '', '',      ''),
    ('filesystem', 'lke sandbox',            'file_str_replace',            '{工具名称} 正在替换文件内容',            '', '',      ''),
    ('filesystem', 'lke sandbox',        'file_find_in_content',        '{工具名称} 正在查找文件内容',            '', '',      ''),
    -- 发布
    ('deploy',     'deploy_expose',               'deploy_expose',               '{工具名称} 正在部署中',                   '', '',      ''),
    -- 通用搜索
    ('search',     'search',                      'search',                      '{工具名称} 执行搜索，搜索参数',           '', 'query', '');