CREATE TABLE `t_user_dialog_config` (
    `business_id` bigint NOT NULL COMMENT '用户对话配置ID',
    `bot_biz_id` bigint NOT NULL COMMENT '应用ID',
    `user_biz_id` bigint NOT NULL COMMENT '用户业务ID',
    `user_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户类型，experience: C端体验用户，staff：腾讯云企业用户',
    `search_network` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '是否联网(disable不联网 enable联网）',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`business_id`, `bot_biz_id`),
    UNIQUE KEY `idx_bot_app_user` (`bot_biz_id`, `user_biz_id`, `user_type`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'C端用户的应用配置' shardkey = bot_biz_id;