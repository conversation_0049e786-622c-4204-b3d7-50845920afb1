# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
qbot-chat-server

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Editor files
.vscode/
.idea/

# Logs
*.log

# Temp files
.DS_Store
*.temp
*.tmp

# Vendor
vendor/

# Project's binary
/cmd/cmd
/bin/chat

# TRPC configuration
config/*.yaml
!config/*.template.yaml
qbot-chat-server.tgz

# for private
!bin/version.txt
