## v0.0.0

1. init project

## v1.7

增加任务型相关逻辑

## v1.8

角色扮演相关

## v2.0

配置修改：

- 增加任务型配置端地址，请求任务流是否发布；
- 增加Bot并发控制白名单
- 增加用于混元大模型搜索的名称配置
- 根据模型名称判断是否使用占位符替换

DB修改：

- 增加statistic_info
- 增加bot_biz_id字段

pipeline修改：

- 任务型和知识型并行调用转为串行调用
- 增加会话摘要和标签提取
- 取消转人工
- 请求安全审核修改一个字段、加一个字段

接口修改：

- 从Robot维度到App维度
- 增加接口统计
- 支持标签位替换
- 云API新加入的接口(GetSharePageWsToken 的接口)

## 敏捷迭代 2024.3.25日

- for 贵州政法委需求，增加用户自定义参数
- for 行业知识包，增加金融行业FAQ；使用原始Query检索；
  - 增加两个配置 financial_bot_biz_id: and disable_financial_knowledge:
- 对话测试，去掉参考来源；

## pipeline调整

- [最新pipeline](https://iwiki.woa.com/p/4009182057#%E7%9F%A5%E8%AF%86%E5%BA%93%E9%97%AE%E7%AD%94)
- 注意pipeline的修改，对效果的影响
- 注意pipeline的修改，对标签提取、知识摘要的影响
- 拒答问题、全局知识库、直接回复的问答、行业知识库按照输出配置，流式或者非流式输出
- 配置修改：stream_output_batch_size 模拟流式输出的批次大小，支持通过StreamingThrottle参数控制
- lke域名替换

## 2.1调整

- 新需求（从文档中找答案、运行时API增加字段），与DM共同调整接口协议
- 任务型白名单取消
- 增加七彩石配置，上报调用数据 by boyucao
- 医疗行业知识包，增加医疗行业FAQ；使用原始Query检索；
  - 增加两个配置 medical_bot_biz_id: and disable_medical_knowledge:

## 敏捷需求 2024.4.8

- [支持按照企业维度区分审核策略](https://tapd.woa.com/project_qrobot/prong/stories/view/1070080800116834376)
- [阅读理解支持history_words_limit限制](https://tapd.woa.com/project_qrobot/prong/tasks/view/1070080800074963867)
- [Chat模块修改调用指向新服务](https://tapd.woa.com/project_qrobot/prong/tasks/view/1070080800074973805)
  - 七彩石添加配置

## V2.2.1 标品迭代

- [多模态问答接入-依赖算法](https://tapd.woa.com/project_qrobot/prong/stories/view/1070080800116624506)

## V2.2.2 标品迭代

- 回合master代码，包括V2.2.1的改动和hotfix内容
- 修改PB，支持Knowledge调试参数落库，供OP查询展示

## V2.3 标品迭代

### 需求

- [知识库问答类型升级-c侧文档输入ppl及对话窗口调整](https://tapd.woa.com/project_qrobot/prong/stories/view/1070080800117123776)
- [ppl增加多模态阅读理解模块](https://tapd.woa.com/project_qrobot/prong/stories/view/1070080800117427487)

### 技术方案

- [文档输入ppl及对话窗口调整](https://iwiki.woa.com/p/4010642020)

### 接口

- Apex增加SSE接口，用于文档解析DocParse接口，***域名跨域配置修改***

```json
{
  "bot_app_key": "HRujGVhu",
  "session_id": "aklsj-d2o34-22s2-s2dd",
  "request_id": "jdl2k-jsd-kf22-al232",
  "cos_bucket": "qidian-qbot-test-1251316161",
  "file_name": "test.txt",
  "cos_url": "/corp/1746858698981900288/1778345918658510848/doc/FdWzzqmVEVWbRTsVgbho-1795290182912573440.txt",
  "file_type": "txt",
  "e_tag": "\"0943bd8f4ef50630fa7971bebed02580\"",
  "cos_hash": "839623329326696060",
  "size": 6023
}
```

- 云APIGetMsgRecord接口

  修改数据结构 FileInfo、 MsgRecord
- chat的PB修改

  对应云API的修改

### 配置

- 多模态配置修改

包括：
七彩石多模态model_name修改
Prompt控制在8000字以内
多模态大模型增加占位符配置
多模态大模型增加白名单，根据botID，指定模型版本

- 请求knowledge配置修改

chat请求knowledge，超时时间拉大
添加target: polaris://trpc.KEP.bot-knowledge-config-server.Api

- 大模型超时修改

bot:
timeout: 60

### DB修改

见SQL2.3

## V2.3后续临时需求

### 需求

- [【企点】参考来源错误bug转需求：增加召回片段编号+模型输出参考来源文段](https://tapd.woa.com/project_qrobot/prong/stories/view/1070080800117639799)

### 配置

1. 任务型白名单，配置删除
2. NonSearchEngineReject配置代码删除
3. 删除hunyuan_model_name配置
4. 删除cos配置
5. 添加新的模型相关配置

## V2.4

### 需求

- [【知识引擎2.4】-多模态问答，支持结合文档作答-新增图文混合输入+多模态检索](https://tapd.woa.com/project_qrobot/prong/stories/view/1070080800117726715)

### 配置

- 增加定时清理文档

1. trpc-go中添加service
2. client中添加redis下游服务

- 修改MLLM 阅读理解Prompt

## 广义MoE 敏捷需求

### 需求

- [【知识引擎2.4+】广义MOE-demo版本](https://tapd.woa.com/project_qrobot/prong/stories/view/1070080800118351050)

## V2.5

### 需求

- [【【知识引擎2.5】对话ppl增加多模态多轮改写-依赖算法、请算法关注并拆解子任务【需要评审】】](https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800118640702)
- [【【知识引擎2.5】ppl改动-意图识别模块改动-待确认】](https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800118992126)

### 配置

IntentModel配置修改，增加意图识别白名单

### DB

见v2.5.0.sql文件

## V2.6

### 配置

添加Workflow下游服务
procedure添加配置：
workflow: 调用工作流

增加多模态阅读理解配置（模型名称和白名单）

增加配置：ppl_threshold: 10000

修改admin的意图Prompt、model_name
prompt:
https://doc.weixin.qq.com/doc/w3_AKoAwQZ9AD067AIFanmQ7qwgMWoY6?scode=AJEAIQdfAAoZhqgd2fAKoAwQZ9AD0

意图三个白名单

全文处理，映射为doc_summary

## V2.6.1

### 配置

intent_model:
下面全部内容check下
mock:
enable_llm: false
enable_search: false
enable_workflow: false

## V2.7

todo： 合并2.6.1的内容 和 2.7 第一批的内容

### 协议
云API接口修改

模型底座：https://git.woa.com/dialogue-platform/proto/-/merge_requests/238
Agent的Prompt：https://git.woa.com/ianxxu/Agent_prompt_templates


DB修改

procedure: 下面添加
agent: 调用智能体
添加七彩石配置

need_search_global_knowledge:
- 1868500601205424128
debug_message:
  salt: a99a52254a1d58231ebe07b2a7f28534
- agent_config:
  thought_icon: https://lke-realtime-1251316161.cos.ap-guangzhou.myqcloud.com/icon%2Fthinking.png
  max_iterations: 6
  is_debug: true
 
client.yaml下面添加插件配置和运行时服务、CodeExec服务。去掉opentelemetry插件

trpc_go.yaml,添加agent日志插件，去掉opentelemetry插件