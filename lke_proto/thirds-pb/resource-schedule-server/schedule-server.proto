syntax = "proto3";

package trpc.KEP.resource_schedule_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/resource_schedule_server";

import "trpc.proto";

service ScheduleServer {
  // Echo 方法用于测试服务是否部署成功
  // @alias=/Echo
  rpc Echo (EchoReq) returns (EchoRsp) {}

  // SchedulePod 调度pod资源
  // @alias=/SchedulePod
  rpc SchedulePod(SchedulePodReq) returns (SchedulePodRsp) {}

  // CancelPod 释放pod资源
  // @alias=/CancelPod
  rpc CancelPod(CancelPodReq) returns (CancelPodRsp) {}

  // GetScheduledPod 获取已调度的pod资源
  // @alias=/GetScheduledPod
  rpc GetScheduledPod(GetScheduledPodReq) returns (GetScheduledPodRsp) {}
}

message EchoReq {}

message EchoRsp {}

// Server 类型
enum ServerTypeEnum {
  UNKNOWN = 0; // UNKNOWN
  SANDBOX_MCP = 1; // 沙箱MCP
  SANDBOX_VNC = 2; // 沙箱VNC
  SANDBOX_CODE = 3; // 沙箱CODE
}

// Pod 信息
message PodInfo {
  message ServerInfo {
    ServerTypeEnum Type = 1; // Server 类型
    int32 Port = 2; // Server Port
    string URL = 3; // Server URL eg: http://IP:Port/runtime_mcp/sse
  }

  string IP = 1; // Pod IP
  bool IsFirstUse = 2; // 是否首次使用
  repeated ServerInfo Servers = 3; // Pod servers
}

// 调度pod资源请求
message SchedulePodReq {
  string Uin = 1; // Uin
  string AppBizId = 2;  // 应用ID
  string SessionId = 3; // SessionID
}

// 调度pod资源返回
message SchedulePodRsp {
  PodInfo PodInfo = 1; // Pod 信息
}

// 释放pod资源请求
message CancelPodReq {
  string Uin = 1; // Uin
  string AppBizId = 2;  // 应用ID
  string SessionId = 3; // SessionID
}

// 释放pod资源返回
message CancelPodRsp {
  bool Success = 1;   // 是否成功
  string Message = 2; // 执行信息
}

// 获取已调度的pod资源请求
message GetScheduledPodReq {
  string Uin = 1; // Uin
  string AppBizId = 2;  // 应用ID
  string SessionId = 3; // SessionID
}

// 获取已调度的pod资源返回
message GetScheduledPodRsp {
  PodInfo PodInfo = 1; // Pod 信息
}