syntax = "proto3";

package trpc.KEP.channel_token_svr;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/channel_token_svr";

//import "validate.proto";
// 资源管理服务
service ChannelTokenService {

  // GetChannelToken 获取渠道token接口
  rpc GetChannelToken(GetChannelTokenReq) returns (GetChannelTokenRsp) {}
  // GetChannelToken 获取渠道token接口
  rpc GetWechatPlatformInfo(GetWechatPlatformInfoReq) returns (GetWechatPlatformInfoRsp) {}
  // RefreshChannelToken 刷新渠道token接口
  rpc RefreshChannelToken(RefreshChannelTokenReq) returns (RefreshChannelTokenRsp) {}

}
enum ChannelType //资源类型
{
  Channel_Type_UNKNOW = 0;
  Channel_Type_WX_PUB = 10000;
  Channel_Type_WX_APP = 10001;
  Channel_Type_Wecom = 10002;
}

message GetChannelTokenReq
{
  //渠道类型
  uint64 channel_type = 1;
  // 渠道应用id
  string channel_app_id = 2;
  // 渠道企业id
  string channel_corp_id = 3;
  // 应用ID
  uint64 app_biz_id = 4;
}

message GetChannelTokenRsp
{
  // 企业ID
  uint64 corp_biz_id = 1;
  // 应用ID
  uint64 app_biz_id = 2;
  // token
  string access_token = 3;
  // 过期时间
  uint64 expire_time = 4;
}

message RefreshChannelTokenReq
{
  // 渠道类型
  uint64 channel_type = 1;
  // 企业ID
  uint64 corp_biz_id = 2;
  // 应用ID
  uint64 app_biz_id = 3;
  WechatInfo wechat_info = 4;
  WecomInfo wecom_info = 5;

}

message WechatInfo {
  string refresh_token = 1; // 刷新token
  string app_id = 2; // 公众号id
}


message WecomInfo {
  string corp_id = 1;  // 企业id
  uint64 agent_id = 2;  // 企微应用id
  string agent_secret = 3; // 企业微信应用secret
  string callback_token = 4; // 企微回调token
  string callback_aes_key = 5; // 企微回调secret
}


message RefreshChannelTokenRsp
{

}

message GetWechatPlatformInfoReq
{

}
message GetWechatPlatformInfoRsp
{
  // token
  string access_token = 1;
  // 过期时间
  uint64 expire_time = 2;
  // 微信平台id
  string component_app_id = 3;
  string component_token = 4;
  string encoding_aes_key = 5;
}