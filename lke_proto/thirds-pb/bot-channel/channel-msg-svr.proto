syntax = "proto3";

package trpc.KEP.channel_msg_svr;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/channel_msg_svr";


//import "validate.proto";
service ChannelMsgService {
  // 获取公众号授权url
  rpc GetWxAuthorizeUrl (GetWxAuthorizeUrlReq) returns (GetWxAuthorizeUrlRsp);
}

message GetWxAuthorizeUrlReq
{
  string  app_biz_id   =1 ;
  string  wx_appid     =2 ; //公众号appid
  string  comment      =3 ; // 备注
}

message GetWxAuthorizeUrlRsp
{
  string  redirect_url   =1 ;
}
