syntax = "proto3";

package trpc.KEP.sandbox_proxy_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/sandbox_proxy_server";


service ProxyServer {
  // Echo 方法用于测试服务是否部署成功
  // @alias=/Echo
  rpc Echo (EchoReq) returns (EchoRsp) {}

  // @alias=/GetSandboxViewAddress 获取链接
  rpc GetSandboxViewAddress(GetSandboxViewAddressReq) returns (GetSandboxViewAddressRsp) {}
}

message EchoReq {}

message EchoRsp {}

message GetSandboxViewAddressReq {
  string AppBizId = 1;
  string SessionId = 2;
  string Uin = 3;
}


message GetSandboxViewAddressRsp {
  string ViewAddress = 1;
  bool IsNewAddress = 2;
}