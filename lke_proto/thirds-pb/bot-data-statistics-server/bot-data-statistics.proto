syntax = "proto3";

package trpc.KEP.bot_data_statistics_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_data_statistics_server";

import "validate.proto";

// Api接口
service Api {
  // Demo
  rpc Demo(DemoReq) returns (DemoRsp);

  // 创建历史任务
  rpc CreateHistoryTask(CreateHistoryTaskReq) returns (CreateHistoryTaskRsp);

  // 重置历史任务至 Pending 状态以重跑任务
  rpc ResetHistoryTask(ResetHistoryTaskReq) returns (ResetHistoryTaskRsp);
}

message DemoReq {}

message DemoRsp {}

message CreateHistoryTaskReq {
  string task_type = 1 ; // 任务类型
  string start_time = 2; // 起始时间
  string end_time = 3 ; // 结束时间
  bool remove_exist_task = 4 ;
}

message CreateHistoryTaskRsp {
  string msg = 1;
}


message ResetHistoryTaskReq {
  string task_type = 1 ; // 任务类型
  string task_state = 2; // 任务状态
  string start_time = 3; // 起始时间
  string end_time = 4 ; // 结束时间
  repeated string task_ids = 5; // 任务ID列表;
  
}

message ResetHistoryTaskRsp {
  string msg = 1;
}