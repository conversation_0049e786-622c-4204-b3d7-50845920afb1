syntax = "proto3";

package trpc.KEP.knowledge;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge";

import "validate.proto";
import "knowledge-common.proto";


// 共享知识库创建请求
message CreateSharedKnowledgeReq {
  // 共享知识库名称, 非空
  string knowledge_name = 2;
  // 共享知识库描述
  string knowledge_description = 3;
  // Embedding模型
  string embedding_model = 4;
  // string qa_extract_model = 5;
}

// 共享知识库创建响应
message CreateSharedKnowledgeRsp {
  // 共享知识库业务ID
  uint64 knowledge_biz_id = 1;
}


// 共享知识库删除请求
message DeleteSharedKnowledgeReq {
  // 共享知识库业务ID
  uint64 knowledge_biz_id = 1;
}

// 共享知识库删除响应
message DeleteSharedKnowledgeRsp {
  // 共享知识库业务ID
  uint64 knowledge_biz_id = 1;
}


// 共享知识库列举请求
message ListSharedKnowledgeReq {
  // 分页序号, 编码从1开始
  uint32 page_number = 1;
  // 分页大小, 有效范围[1,200]
  uint32 page_size = 2;
  // 搜索关键字: 知识库名称名称/修改人
  string keyword = 3;
}

// 共享知识库查询响应
message KnowledgeBaseInfo {
  // 共享知识库业务ID
  uint64 knowledge_biz_id = 1;
  // 共享知识库名称
  string knowledge_name = 2;
  // 共享知识库描述
  string knowledge_description = 3;
  // Embedding模型
  string embedding_model = 4;
  // QaExtract模型
  string qa_extract_model = 5;
  // 更新时间
  int64 update_time = 6;
}

// 应用基础信息
message AppBaseInfo {
  // 应用ID
  uint64 app_biz_id = 1;
  // 应用名称
  string app_name = 2;
}

// 用户基础信息
message UserBaseInfo {
  // 用户ID
  uint64 user_biz_id = 1;
  // 用户名称
  string user_name = 2 ;
}

// 共享知识库详情信息
message KnowledgeDetailInfo {
  KnowledgeBaseInfo knowledge = 1;
  repeated AppBaseInfo app_list = 2;
  UserBaseInfo user = 3;
}

// 共享知识库列举响应
message ListSharedKnowledgeRsp {
  repeated KnowledgeDetailInfo knowledge_list = 1;
  uint32 total = 2;
}


// 共享知识库查询请求
message DescribeSharedKnowledgeReq {
  // 共享知识库业务ID
  uint64 knowledge_biz_id = 1;
}

// 共享知识库查询响应
message DescribeSharedKnowledgeRsp {
  KnowledgeDetailInfo info = 1;
}


enum KnowledgeUpdateField {
  KnowledgeInvalidField = 0;
  KnowledgeNameField = 1;
  KnowledgeDescriptionField = 2;
  KnowledgeEmbeddingModelField = 3;
  KnowledgeQAExtractModelField = 4;
}

message KnowledgeUpdateInfo {
  // 共享知识库名称, 非空
  string knowledge_name = 2;
  string knowledge_description = 3;
  string embedding_model = 4;
  string qa_extract_model = 5;
}

// 共享知识库更新请求
message UpdateSharedKnowledgeReq {
  // 共享知识库业务ID
  uint64 knowledge_biz_id = 1;
  KnowledgeUpdateInfo info = 2;
  // repeated KnowledgeUpdateField field_list = 3;
}

// 共享知识库更新响应
message UpdateSharedKnowledgeRsp {
  // 共享知识库业务ID
  uint64 knowledge_biz_id = 1;
}


// 引用共享知识库请求
message ReferSharedKnowledgeReq {
  uint64 app_biz_id = 1;   // 应用业务ID
  repeated uint64 knowledge_biz_id = 2; // 共享知识库业务ID
}

// 引用共享知识库响应
message ReferSharedKnowledgeRsp {

}


// 查看引用共享知识库列表请求
message ListReferSharedKnowledgeReq {
  uint64 app_biz_id = 1;   // 应用业务ID
}

// 查看引用共享知识库列表响应
message ListReferSharedKnowledgeRsp {
  repeated KnowledgeBaseInfo list = 1; // 共享知识库业务ID
}


// 共享知识库批量获取请求
message BatchGetSharedKnowledgeReq {
  // 企业业务ID
  uint64 corp_biz_id = 1;
  // 共享知识库业务ID列表
  repeated uint64 knowledge_biz_id_list = 2;
}

// 共享知识库批量获取响应
message BatchGetSharedKnowledgeRsp {
  // 共享知识库基本信息列表
  repeated KnowledgeBaseInfo info_list = 1;
}