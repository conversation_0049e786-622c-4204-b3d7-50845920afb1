// ############################################################################
//
// PB 来源 -> https://git.woa.com/dialogue-platform/bot-dm/bot-dm-server.git
//
// ############################################################################

// KEP.bot-dm-server
//
// @(#)bot-dm.proto  December 07, 2023
// Copyright(c) 2023, boyucao@Tencent. All rights reserved.


// ######################################################################################### //
//                                                                                           //
//     PB 文件 [bot-dm.proto] 请移步到                                                       //
//                                                                                           //
//     https://git.woa.com/raven/pb-stub/blob/master/thirds-pb/bot-dm-server/bot-dm.proto    //
//                                                                                           //
// ######################################################################################### //


syntax = "proto3";

package trpc.KEP.bot_workflow_dm_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM";

import "trpc.proto";
import "bot-task-config-server/workflow.proto";

// 服务全名: KEP.bot-workflow-dm-server.bot-dm
//
// 北极星传送门 | Development
// http://polaris.oa.com/#/services/info/instance/Development/trpc.KEP.bot-dm-server.bot-dm
//
// 北极星传送门 | Test
// http://polaris.oa.com/#/services/info/instance/Test/trpc.KEP.bot-dm-server.bot-dm
//
// 北极星传送门 | Pre-release
// http://polaris.oa.com/#/services/info/instance/Pre-release/trpc.KEP.bot-dm-server.bot-dm
//
// 北极星传送门 | Production
// http://polaris.oa.com/#/services/info/instance/Production/trpc.KEP.bot-dm-server.bot-dm
service WorkflowDm {

  // Echo 方法用于测试服务是否部署成功
  // rpc Echo (EchoReq) returns (EchoResp) {}

  // 检索topN工作流，依赖做意图判断
  rpc RetrieveWorkflows(RetrieveWorkflowsRequest) returns (RetrieveWorkflowsReply){}

  // 运行工作流
  rpc RunWorkflow(stream RunWorkflowRequest) returns (stream RunWorkflowReply){}
  // 清理Session
  rpc ClearSession(ClearSessionRequest) returns (ClearSessionReply){}

  // 更新应用到Sandbox。
  rpc UpsertAppToSandbox(UpsertAppToSandboxRequest) returns (UpsertAppToSandboxReply){}
  // 发布对话树到Sandbox。（"保存到测试环境"按钮）
  rpc UpsertWorkflowToSandbox(UpsertWorkflowToSandboxRequest) returns (UpsertWorkflowToSandboxReply){}
  // 删除对话树到Sandbox。（删除对话树，影响沙箱环境）
  rpc DeleteWorkflowsInSandbox(DeleteWorkflowsInSandboxRequest) returns (DeleteWorkflowsInSandboxReply){}

  // 发布应用到正式环境。（正式发布)
  rpc ReleaseWorkflowApp(ReleaseWorkflowAppRequest) returns (ReleaseWorkflowAppReply){}

  // 删除应用。（影响沙箱环境、正式环境)
  rpc DeleteWorkflowApp(DeleteWorkflowAppRequest) returns (DeleteWorkflowAppReply){}

  // 保存参数到Sandbox
  rpc UpsertParametersToSandbox(UpsertParametersToSandboxRequest) returns (UpsertParametersToSandboxReply){}
  // 删除参数
  rpc DeleteParametersInSandbox(DeleteParametersInSandboxRequest) returns (DeleteParametersInSandboxReply){}

  // 保存自定义参数到Sandbox
  rpc UpsertVariablesToSandbox(UpsertVariablesToSandboxRequest) returns (UpsertVariablesToSandboxReply){}
  // 删除自定义参数
  rpc DeleteVariablesInSandbox(DeleteVariablesInSandboxRequest) returns (DeleteVariablesInSandboxReply){}

  // 获取应用的所有工作流
  rpc DescribeWorkflows(DescribeWorkflowsRequest) returns (DescribeWorkflowsReply){}
  // 获取应用的所有参数
  rpc DescribeParameters(DescribeParametersRequest) returns (DescribeParametersReply){}

  // 非对话类的节点调试
  rpc DebugWorkflowNode(DebugWorkflowNodeRequest) returns (DebugWorkflowNodeReply){}
  // 对话类的节点调试
  rpc DebugWorkflowNodeDialog(stream DebugWorkflowNodeDialogRequest) returns (stream DebugWorkflowNodeDialogReply){}

  // 开始工作流运行实例
  rpc StartWorkflowRun(StartWorkflowRunRequest) returns (StartWorkflowRunReply) {}
  // 停止工作流运行实例
  rpc StopWorkflowRun(StopWorkflowRunRequest) returns (StopWorkflowRunReply) {}
  // 查看工作流运行实例数量
  rpc DescribeWorkflowRunNum(DescribeWorkflowRunNumRequest) returns (DescribeWorkflowRunNumReply) {}
}

message RunWorkflowRequest {
  // 应用下的会话ID。 是应用下唯一，不是全局唯一的
  string SessionID = 1 [(trpc.go_tag) = 'valid:"required~会话ID不能为空"'];
  // 运行环境
  RunEnvType RunEnv = 2;
  // 请求类型，包含运行、停止
  RequestType RequestType = 3;
  // 应用ID
  string AppID = 4 [(trpc.go_tag) = 'valid:"required~应用ID不能为空"'];
  // 指定工作流
  string WorkflowID = 5;
  // 当前的query。
  string Query = 6;
  // 对话历史原始的内容
  repeated Message QueryHistory = 7;
  // 变量信息，传用户ID等变量信息
  map<string, Variable> CustomVariables = 8;   // 自定义参数
  string RelatedRecordID = 9;         // query对应的recordID
  string RecordID = 10;  // query的回复对应的recordID
  // 是否为调试
  bool IsDebug = 11;
  // 主模型名称
  string MainModelName = 12;
  // 输入变量的值。 key：字段的名称； value：字段的值，可以为各种类型： int/float/string/图片url/视频url等
  map<string, string> Inputs = 13;
  // 改写后的query
  string RewriteQuery = 14;
  // 返回的信息类型
  VerboseModeType VerboseMode = 15;
  // // 上一个query命中的workflowID，用来判断对话是否连续。（如选项卡就需要用来判断是否需要重新提问） // 咱不需要，使用历史对话判断即可
  // string LastQueryWorkflowID = 15;
  // 计费子业务项（上报大模型用量需要，DM对子业务项无感知）
  string FinanceSubBizType = 16;
}

enum RunEnvType {
  SANDBOX = 0;  // 沙箱环境
  PRODUCT = 1;  // 正式环境
}

enum RequestType {
  RUN = 0;  // 运行
  STOP = 1; // 停止
}

// 消息的角色
enum Role {
  USER = 0;      // 用户说的内容
  ASSISTANT = 1; // 大模型回复的额内容
  SYSTEM = 2;    // 一般作为系统Prompt用
  NONE = 3;      // 无角色（与LLM交互时将不自动补齐角色名）
}

enum VerboseModeType {
  ALL = 0;              // 返回每次对话信息、运行信息和报错信息。
  RECORD_AND_ERROR = 1; // 返回每次对话信息和报错信息，
  RECORD_ONLY = 2;      // 只返回每次对话信息
}

// 一次对话消息
message Message {
  Role Role = 1;       // 角色
  string Content = 2;  // 内容
  string RecordID = 3; // record的ID
}

message Variable {
  string Name = 1;   // 关键字
  string Value = 2;  // 值
  //  ValueType ValueType = 3;        // 数值类型。如： string, int, float, bool
}

message RunWorkflowReply {
  int32 Code = 1;                   // 0表示成功。 只要服务端的stream回复过一条消息后，error就没有作用了，只能通过code/message返回错误原因
  string Message = 2;               // 错误原因
  string SessionID = 3;             // 会话ID
  bool IsFinal = 4;                 // 当前消息是否最后一条
  Respond Respond = 5;              // 正常非空。

  // LLM统计信息。
  repeated StatisticInfo StatisticInfos = 100;
}

message Respond {
  RespondType Type = 1;   // 回复类型
  string Content = 2;     // 回复内容（合并后的）
  repeated Reference References = 3;  // 文档数据
  string WorkflowRunID = 4;       // 工作流运行ID
  string WorkflowID = 5;          // 工作流ID
  string WorkflowName = 6;        // 工作流名称
  WorkflowStatus WorkflowStatus = 7;  // 工作流状态
  repeated RunNodeInfo RunNodes = 8;  // 节点的信息
  repeated string OptionCards = 9;    // 选项卡。
  repeated string ContentList = 10;   // 回复内容的数组。
  repeated ThoughtInfo ThoughtList = 11;   // 思考过程的数组。
  int32 HitOptionCardIndex = 12;      // 命中了哪个选项卡
  map<string, string> AnswerOutput = 13; // 回复节点的自定义输出
  string WorkflowReleaseTime = 14;       // 工作流的发布时间（RFC3339格式，如： 2025-05-08T15:04:49+08:00）
}

message ThoughtInfo {
  string Content = 1;   // 思考内容
  int64 StartTime = 2; // 开始时间（毫秒时间戳）
  int64 EndTime = 3;   // 结束时间（毫秒时间戳）
  string NodeName = 4; // 节点名称
  uint32 ReplyIndex = 5; // 所在的回复的下标，下标值可能比回复数组长度大，因为思考可能对应空回复
}

enum RespondType {
  RT_UNKNOWN = 0; // 未知
  RT_LLM = 1;     // LLM
  RT_CUSTOM = 2;  // 自定义
}

message Reference {
  uint64 DocID = 1;  // 文档ID
  uint64 ID = 2;     // QAID/SegmentID/RejectID
  string Name = 3;   // 显示名称
  uint32 Type = 4;   // 来源类型。 QA = 1;  SEGMENT = 2;  REJECT = 3;
  string Url = 5;    // 关联链接
  uint64 DocBizID = 6;
  string DocName = 7;
  uint64 QABizID = 8;
}

message RunNodeInfo {
  enum StatusType {
    INIT = 0;     // 初始状态
    RUNNING = 1;  // 运行中
    SUCCESS = 2;  // 运行成功
    FAILED = 3;   // 运行失败
    CANCELED = 4; // 已取消
  }

  string NodeID = 1;          // 节点ID
  trpc.KEP.bot_task_config_wf_server.NodeType NodeType = 2;  // 节点类型
  string NodeName = 3;        // 节点名称
  StatusType Status = 4;      // 状态
  string Input = 5;           // 节点的输入。json字符串（含普通字符串）
  string Output = 6;          // 节点的最终输出。json字符串（含普通字符串）
  //  string TaskInput = 7;       // 任务的输入。（如API的完整入参）
  string TaskOutput = 8;      // 任务的输出。（原始输出）
  string FailMessage = 9;     // 异常信息
  uint32 CostMilliSeconds = 10 [(trpc.go_tag) = 'json:"CostMilliSeconds"']; // 节点的总耗时。如果节点有多次调用，耗时为多次调用的总和。
  string Reply = 11;          // 当前节点的回复内容。当前参数提取、消息节点、结束回复节点可能非空
  string BelongNodeID = 12;   // 节点所属工作流被引用时的引用节点的ID，所属的工作流是被引用的时候非空。（后面如果要支持多层嵌套的话，需要修改成数组）。
  bool IsCurrent = 13;        // 当前走过的节点。前端根据这个内容来判断是否展开节点，需要在chat中设置。
  string FailCode = 14;       // 异常信息对应的错误码，为云API格式的二级错误码，如： "NodeErr.MissingParam"

  // LLM统计信息。
  repeated StatisticInfo StatisticInfos = 100;
}

enum WorkflowStatus {
  PENDING = 0;  // 未知（命中的工作流不会有初始状态）
  RUNNING = 1;  // 运行中
  FAILED = 2;   // 运行失败
  SUCCESS = 3;  // 运行完成
  CANCELED = 4; // 已取消
}

message RespondAction {
  enum ActionType {
    UNKNOWN = 0;      //
    OPTION_CARD = 1;  // 选项卡。
  }

  ActionType Type = 1;              // Action类型：
  repeated string OptionCards = 2;  // 选项卡。Type为OPTION_CARD时非空
}

// 统计信息
message StatisticInfo {
  string ModelName = 1 [(trpc.go_tag) = 'json:"ModelName"'];       // 模型名称
  uint32 FirstTokenCost = 2 [(trpc.go_tag) = 'json:"FirstTokenCost"'];  // 首token耗时
  uint32 TotalCost = 3 [(trpc.go_tag) = 'json:"TotalCost"'];       // 推理总耗时
  uint32 InputTokens = 4 [(trpc.go_tag) = 'json:"InputTokens"'];     // 输入token数量
  uint32 OutputTokens = 5 [(trpc.go_tag) = 'json:"OutputTokens"'];    // 输出token数量
  uint32 TotalTokens = 6 [(trpc.go_tag) = 'json:"TotalTokens"'];     // 输入+输出总token
  bool IsSubWorkflow = 7 ;     // 是否为子工作流的统计信息
}

message ClearSessionRequest {
  string SessionID = 1 [(trpc.go_tag) = 'valid:"required~会话ID不能为空"'];   // 应用下的会话ID。 是应用下唯一，不是全局唯一的
  RunEnvType RunEnv = 2;  // 运行环境
  string AppID = 3 [(trpc.go_tag) = 'valid:"required~应用ID不能为空"'];       // 应用ID
}
// 错误码：
// 8000001： 参数不合法
// 8000002： 运行错误
// 8000101： session不存在
message ClearSessionReply {
}

message UpsertAppToSandboxRequest {
  string AppID = 1 [(trpc.go_tag) = 'valid:"required~AppID不能为空"'];  // 应用ID。
  string RetrievalWorkflowGroupID = 2;   // 检索工作流的groupID。
  string RetrievalWorkflowModel = 3;     // 检索工作流的模型。
  //  string RetrievalEntryGroupID = 3;    // 检索词条的groupID。
}

message UpsertAppToSandboxReply {
}

message UpsertWorkflowToSandboxRequest {
  string AppID = 1 [(trpc.go_tag) = 'valid:"required~AppID不能为空"'];  // 应用ID。
  trpc.KEP.bot_task_config_wf_server.Workflow Workflow = 2 [(trpc.go_tag) = 'valid:"required~Workflow不能为空"'];// 工作流
}

message UpsertWorkflowToSandboxReply {
}

message DeleteWorkflowsInSandboxRequest {
  string AppID = 1 [(trpc.go_tag) = 'valid:"required~AppID不能为空"'];              // 应用ID
  repeated string WorkflowIDs = 2 [(trpc.go_tag) = 'valid:"required~WorkflowIDs不能为空"']; // 工作流ID
}

message DeleteWorkflowsInSandboxReply {
}

message ReleaseWorkflowAppRequest {
  string AppID = 1 [(trpc.go_tag) = 'valid:"required~AppID不能为空"'];  // 应用ID
  repeated string UpsertWorkflowIDs = 2;   // 工作流ID
  repeated string DeleteWorkflowIDs = 3;   // 工作流ID
  repeated string UpsertParameterIDs = 4;   // 变更的参数ID
  repeated string DeleteParameterIDs = 5;   // 删除的参数ID
  string RetrievalWorkflowGroupID = 6 [(trpc.go_tag) = 'valid:"required~RetrievalWorkflowGroupID不能为空"'];  // 检索工作流的groupID。
  //  string RetrievalEntryGroupID = 7 [(trpc.go_tag) = 'valid:"required~RetrievalEntryGroupID不能为空"'];   // 检索词条的groupID。
  map<string, string> WorkflowReleaseTimes =  8; // 工作流发布时间（RFC3339格式，如： 2025-05-08T15:04:49+08:00），key为workflowID。
  string RetrievalWorkflowModel = 9;     // 检索工作流的模型。
}

message ReleaseWorkflowAppReply {
}

message DeleteWorkflowAppRequest {
  string AppID = 1 [(trpc.go_tag) = 'valid:"required~AppID不能为空"'];  // 应用ID
}

message DeleteWorkflowAppReply {
}

message Parameter {
  string ParameterID = 1 [(trpc.go_tag) = 'valid:"required~ParameterID不能为空"'];            // 参数ID
  string ParameterName = 2 [(trpc.go_tag) = 'valid:"required~ParameterName不能为空"'];        // 参数名称
  string ParameterDesc = 3;   // 描述信息
  trpc.KEP.bot_task_config_wf_server.TypeEnum ValueType = 4;  // 数据类型
  repeated string CorrectExamples = 5; // 正确示例
  repeated string IncorrectExamples = 6; // 错误示例
  string CustomAsk = 7;                  // 自定义询问语
}

message UpsertParametersToSandboxRequest {
  string AppID = 1 [(trpc.go_tag) = 'valid:"required~AppID不能为空"']; // 应用ID。
  repeated Parameter Parameters = 2 [(trpc.go_tag) = 'valid:"required~Parameters不能为空"']; // 参数列表。
}

message UpsertParametersToSandboxReply {
}

message DeleteParametersInSandboxRequest {
  string AppID = 1 [(trpc.go_tag) = 'valid:"required~AppID不能为空"'];          // 应用ID
  repeated string ParameterIDs = 2 [(trpc.go_tag) = 'valid:"required~ParameterIDs不能为空"']; // 参数ID
}

message DeleteParametersInSandboxReply {
}

message UpsertVariablesToSandboxRequest {
  string AppID = 1 [(trpc.go_tag) = 'valid:"required~AppID不能为空"']; // 应用ID。
  repeated Var Variables = 2 [(trpc.go_tag) = 'valid:"required~Variables不能为空"'];
}

message Var {
  string VarID = 1;     // 变量ID
  string VarName = 2;   // 变量名称
  string VarDesc = 3;   // 变量描述
  trpc.KEP.bot_task_config_wf_server.TypeEnum ValueType = 4; // 取值类型
  string VarDefaultValue = 5; // 自定义变量默认值
}

message UpsertVariablesToSandboxReply {
}

message DeleteVariablesInSandboxRequest {
  string AppID = 1 [(trpc.go_tag) = 'valid:"required~AppID不能为空"'];         // 应用ID
  repeated string VarIDs = 2 [(trpc.go_tag) = 'valid:"required~VarIDs不能为空"'];  // 变量ID
}

message DeleteVariablesInSandboxReply {
}

message DescribeWorkflowsRequest {
  RunEnvType RunEnv = 1; // 运行环境
  string AppID = 2 [(trpc.go_tag) = 'valid:"required~AppID不能为空"']; // 应用ID
  repeated string WorkflowIDs = 3; // 工作流ID。可为空，为空的时候的返回所有的工作流
}

message DescribeWorkflowsReply {
  string AppID = 1 [(trpc.go_tag) = 'valid:"required~AppID不能为空"'];   // 应用ID。
  repeated trpc.KEP.bot_task_config_wf_server.Workflow Workflows = 2;
}

message RetrieveWorkflowsRequest {
  RunEnvType RunEnv = 1; // 运行环境
  string AppID = 2 [(trpc.go_tag) = 'valid:"required~AppID不能为空"']; // 应用ID
  string RewriteQuery = 3;        // 对话改写后query的内容，用来检索topN的工作流。
  // uint32 TopN = 4;            // 最相近的数量，DM先写死
  // float Confidence = 5;       // 置信度,暂时不需要,保证召回准确率
}

message RetrieveWorkflowsReply {
  repeated RetrieveWorkflow Workflows = 1;
}

message RetrieveWorkflow {
  string WorkflowID = 1;        // 工作流ID
  string WorkflowName = 2;      // 工作流名称
  string WorkflowDesc = 3;      // 工作流描述
  repeated string Examples = 4; // 示例问法
  float Confidence = 5;         // 置信度
}

message DescribeParametersRequest {
  RunEnvType RunEnv = 1; // 运行环境
  string AppID = 2 [(trpc.go_tag) = 'valid:"required~AppID不能为空"']; // 应用ID
  repeated string ParameterIDs = 3; // 参数ID。可为空，为空的时候的返回所有的参数
}

message DescribeParametersReply {
  map<string, Parameter> Parameters = 1;  // 参数信息。 key：ParameterID
}

message DebugWorkflowNodeRequest {
  string AppID = 1;               // 应用ID
  string NodeJSON = 2;            // 节点数据
  map<string, string> Inputs = 3; // 输入参数
  ToolInputData ToolInput = 4;    // 工具节点输入参数
  // bool Stream = 5;                // 是否流式返回。流式：Reply 增量返回，其他信息最后一包返回。 非流式： 只返回一次含全部内容的信息
}

message ToolInputData {
  string Header = 1;    // API节点头信息JSON文本
  string Query = 2;     // API节点URL参数JSON文本
  string Body = 3;      // API或代码节点输入参数JSON文本
}

message DebugWorkflowNodeReply {
  int32 Code = 1;                   // 0表示成功。
  string Message = 2;               // 错误原因
  RunNodeInfo NodeData = 3;         // 返回的节点信息
  //  bool IsFinal = 4;                 // 当前消息是否最后一条
}

message DebugWorkflowNodeDialogRequest {
  // 请求类型，包含运行、停止
  RequestType RequestType = 1;
  // 应用ID
  string AppID = 2;
  // 节点数据
  string NodeJSON = 3;
  // 输入变量
  map<string, string> Inputs = 4;
  // 会话ID。
  string SessionID = 5;
  // 用户输入
  string Query = 6;
  // 配置的对话历史
  string ConfiguredHistory = 7;
  // 对话历史原始的内容
  repeated Message QueryHistory = 8;
  // query对应的recordID
  string RelatedRecordID = 9;
  // query的回复对应的recordID
  string RecordID = 10;
  // 主模型名称
  string MainModelName = 11;
  // 改写后的query
  string RewriteQuery = 12;
  // API参数的值，用来给子工作流使用的
  map<string, Variable> CustomVariables = 13;   // 自定义参数
  // // 是否流式返回。流式：Reply 增量返回，其他信息最后一包返回。 非流式： 只返回一次含全部内容的信息
  // bool Stream = 14;
}

message DebugWorkflowNodeDialogReply {
  int32 Code = 1;                   // 0表示成功。 只要服务端的stream回复过一条消息后，error就没有作用了，只能通过code/message返回错误原因
  string Message = 2;               // 错误原因
  string SessionID = 3;             // 会话ID
  bool IsFinal = 4;                 // 当前消息是否最后一条
  Respond Respond = 5;              // 正常非空。

  // LLM统计信息。
  repeated StatisticInfo StatisticInfos = 100;
}

message StartWorkflowRunRequest {
  // 主账号Uin
  string Uin = 1;
  // 工作流运行实例ID
  string WorkflowRunID = 2;
  // 运行环境。
  RunEnvType RunEnv = 3;
  // 工作流ID
  string WorkflowID = 4;
  // 应用ID
  string AppID = 5;
  // 当前的query。
  string Query = 6;
  // 主模型名称
  string MainModelName = 7;
  // API参数
  map<string, string> CustomVariables = 8;
  // 是否为调试
  bool IsDebug = 30;
  //  // 文件信息（当前可能不需要，为空即可）
  //  repeated FileInfo FileInfos = 10;
}

message StartWorkflowRunReply {
  // 是否已经运行中
  bool IsAlreadyRunning = 1;
}

message StopWorkflowRunRequest {
  // 工作流运行实例ID
  string WorkflowRunID = 1;
}

message StopWorkflowRunReply {
}

message DescribeWorkflowRunNumRequest{
  // 主账号Uin， 如果为空，则返回所有主账号的运行中的工作流数量。
  string Uin = 1;
}

message DescribeWorkflowRunNumReply{
  map<string, UinInfo> UinInfos = 1;
}

message UinInfo {
  // 运行中的工作流数量
  int32 RunningNum = 1;
}
