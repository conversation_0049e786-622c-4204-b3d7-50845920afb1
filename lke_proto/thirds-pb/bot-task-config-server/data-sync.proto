// KEP.bot-task-config-server
//
// @(#)data-sync.proto  December 12, 2023
// Copyright(c) 2023, boyucao@Tencent. All rights reserved.

syntax = "proto3";

package trpc.KEP.bot_task_config_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP";

import "trpc.proto";

message GetUnreleasedCountReq {
    uint64 BotBizId     = 1 [(trpc.go_tag)='valid:"required~请传入正确的机器人"']; // 机器人
    uint64 CorpID       = 2; // 公司 ID
    uint64 StaffID      = 3; // 员工 ID
}

message GetUnreleasedCountRsp {
    uint32 Count        = 1; // 数量
}

// 任务同步内容
message SyncItem {
    string ID           = 1; // 唯一标识
    string Title        = 2; // 标题
    string Type = 3; // 类型: 任务流程: (旧:TASK_FLOW,新:WORKFLOW)
    string Action       = 4; // 同步动作, 'INSERT', 'UPDATE', 'DELETE'
    uint64 UpdateTime   = 5; // 更新时间, 毫秒时间戳 (ms)
    string Message      = 6; // 消息
}

// 数据同步任务
message DataSyncTask {
    uint64              TaskID          = 1; // 任务的唯一标识
    string              Operator        = 2; // 发布人
    string              Desc            = 3; // 发布说明
    uint64              UpdateTime      = 4; // 更新时间, 毫秒时间戳 (ms)
    uint32              Status          = 5; // 状态 1:待发布, 2:发布中, 3:发布成功, 4:发布失败, 5:审核中, 6:审核成功, 7:审核失败, 8:发布成功回调处理中, 9:发布暂停, 55: 发布队列等待中
    repeated SyncItem   SyncItems       = 6; // 同步数据
    uint64              SuccessCount    = 7; // 发布成功数
    uint64              FailCount       = 8; // 发布失败数
}

// 创建任务
message SendDataSyncTaskEventReq {
    string BusinessName = 1 [(trpc.go_tag)='valid:"required,in(TEXT_ROBOT)~请传入正确的业务名称"']; // 业务名称, 文本客服: TEXT_ROBOT
    uint64 BotBizId     = 2 [(trpc.go_tag)='valid:"required~请传入正确的机器人"']; // 机器人
    string Event        = 3 [(trpc.go_tag)='valid:"required,in(COLLECT|RELEASE|PAUSE|RETRY)~请传入正确的发布事件"']; // 事件: 采集: COLLECT; 发布: RELEASE; 暂停: PAUSE; 重试: RETRY;
    uint64 TaskID       = 4; // 任务 ID, 由上游主动传入; 仅 COLLECT 事件可以为空, 接口生成一个新的, 下次变更状态时, 需要携带
    uint64 CorpID       = 5; // 公司 ID
    uint64 StaffID      = 6; // 员工 ID
}

message SendDataSyncTaskEventRsp {
    uint64 TaskID       = 1; // 任务 ID, 本次同步任务的唯一标识
}

// 获取同步任务, 详情, 状态等
message GetDataSyncTaskReq {
    uint64          BotBizId      = 1 [(trpc.go_tag)='valid:"required~请传入正确的机器人"']; // 机器人
    uint64          TaskID        = 2 [(trpc.go_tag)='valid:"required~请传入正确的任务 ID"']; // 任务 ID
    uint64          CorpID        = 3; // 公司 ID
    uint64          StaffID       = 4; // 员工 ID
    repeated uint32 ReleaseStatus = 5; // 发布状态(2 待发布 3 发布中 4 已发布 5 发布失败)
}

message GetDataSyncTaskRsp {
    DataSyncTask Task   = 1; // 任务详情
}

message GetDataSyncTasksReq {
    uint64          BotBizId    = 1 [(trpc.go_tag)='valid:"required~请传入正确的机器人"']; // 机器人
    string          Query       = 2; // 查询关键字, 用于模糊匹配标题
    uint64          StartTime   = 3; // 任务更新时间起点, 时间单位 ms
    uint64          EndTime     = 4; // 任务更新时间止点, 时间单位 ms
    repeated string Status      = 5; // 状态, 状态值：1:新增, 2:修改, 3:删除, 4:发布
    uint32          Page        = 6; // 页码
    uint32          PageSize    = 7 [(trpc.go_tag)='valid:"required,range(1|200)~每页数量在1到200之间"']; // 每页数量
}

message GetDataSyncTasksRsp {
    uint32                Total = 1; // 总数
    repeated DataSyncTask Tasks = 2; // 任务列表
}

message CheckRobotReadyReq {
    uint64  BotBizId    = 1 [(trpc.go_tag)='valid:"required~请传入正确的机器人"']; // 机器人
    uint64 CorpID       = 2; // 公司 ID
    uint64 StaffID      = 3; // 员工 ID
}

message CheckRobotReadyRsp {
    bool    Ready       = 1; // 是否准备好
}

message NotifySyncTaskReq {
}

message NotifySyncTaskRsp {
}

// ReleaseAPIVarParamsReq 通知API参数发布请求
message ReleaseAPIVarParamsReq {
    uint64          BotBizId = 1 [(trpc.go_tag) = 'valid:"required~请传入正确的机器人"']; // 机器人
    uint64          TaskID = 2; // 任务 ID
    uint64          CorpID = 3; // 公司 ID
    uint64          StaffID = 4; // 员工 ID
    repeated string APIVarIDs = 5; // API参数ID列表
}

// ReleaseAPIVarParamsRsp 通知API参数发布返回
message ReleaseAPIVarParamsRsp {
    uint64 TaskID = 1; // 任务 ID, 本次同步任务的唯一标识
}