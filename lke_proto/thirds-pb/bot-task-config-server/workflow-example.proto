// KEP.bot-task-config-server
//
// @(#)corpus.proto  April 8, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

syntax = "proto3";

package trpc.KEP.bot_task_config_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF";

import "trpc.proto";
import "validate.proto";

// 创建示例问法传参
message CreateWorkflowExampleReq {
  string AppBizId = 1 [(trpc.go_tag)='valid:"required~请传入正确的应用ID"'];  // 应用ID
  string WorkflowId = 2 [(trpc.go_tag)='valid:"required~请传入工作流ID"']; // 工作流Id
  string Example = 3 [(trpc.go_tag)='valid:"required~请传入示例问法"'];  // 示例问法
}

// 示例问法创建成功出参
message CreateWorkflowExampleRsp {
    string ExampleId = 1; // 示例问法Id
}

// 获取示例问法列表请求参数
message ListWorkflowExampleReq {
  string AppBizId = 1 [(trpc.go_tag)='valid:"required~请传入正确的应用ID"'];  // 应用ID
  string WorkflowId = 2 [(trpc.go_tag)='valid:"required~请传入工作流ID"']; // 工作流Id
  string Keyword = 3;  // 搜索示例问法关键词
}

// 获取示例问法列表出参
message ListWorkflowExampleRsp {
  message ExampleData {
    string ExampleId = 1; //示例问法Id
    string Example = 2; // 示例问法
  }
 repeated ExampleData List = 1 [(trpc.go_tag) = 'json:"List"'];
 uint32 Total = 2  [(trpc.go_tag) = 'json:"Total"'];
}


// 修改示例问法请求
message UpdateWorkflowExampleReq{
  string AppBizId = 1 [(trpc.go_tag)='valid:"required~请传入正确的应用ID"'];  // 应用ID
  string ExampleId = 2 [(trpc.go_tag)='valid:"required~请传入示例问法Id"']; // 示例问法Id
  string Example = 3 [(trpc.go_tag)='valid:"required~请传入示例问法"'];  // 示例问法
}

// 修改示例问法返回
message UpdateWorkflowExampleRsp{}

// 删除示例问法请求
message DeleteWorkflowExampleReq{
  string AppBizId = 1 [(trpc.go_tag)='valid:"required~请传入正确的应用ID"'];  // 应用ID
  string ExampleId = 2 [(trpc.go_tag)='valid:"required~请传入示例问法Id"']; // 示例问法Id
}

// 删除示例问法返回
message DeleteWorkflowExampleRsp {}



// 工作流示例问法请求
message ImportWfExampleReq {
  // 应用ID
  string AppBizId = 1 [(trpc.go_tag)='valid:"required~请传入正确的应用ID"'];
  // 工作流Id
  string WorkflowId = 2 [(trpc.go_tag)='valid:"required~请传入工作流ID"'];
  // 文件名
  string FileName = 3 [(validate.rules).string = { min_len: 1, max_len: 255 }];
  // cos路径
  string CosUrl = 4 [(validate.rules).string.min_len = 1];
  // CosHash x-cos-hash-crc64ecma 头部中的 CRC64编码进行校验上传到云端的文件和本地文件的一致性
  string CosHash = 5 [(validate.rules).string.min_len = 1];
  // 文件大小
  uint64 Size = 6 [(validate.rules).uint64.gt = 0];
}

// 词条导入响应
message ImportWfExampleRsp {
  // 导入错误
  string ErrorMsg = 1;
  // 错误链接
  string ErrorLink = 2;
  // 错误链接文本
  string ErrorLinkText = 3;
}
