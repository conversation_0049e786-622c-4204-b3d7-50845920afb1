syntax = "proto3";

package trpc.KEP.bot_task_config_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF";

import "trpc.proto";
import "workflow.proto";

// 运行环境
enum EnvType {
  // 测试环境
  TEST = 0;
  // 正式环境
  PROD = 1;
}

// 调试模型类型
enum DebugModeType {
  // 同步
  SYNC = 0;
  // 异步
  ASYNC = 1;
}

// 工作流运行状态枚举
enum WorkflowRunState {
  // 排队中
  WORKFLOW_PENDING = 0;
  // 运行中
  WORKFLOW_RUNNING = 1;
  // 运行成功
  WORKFLOW_SUCCESS = 2;
  // 运行失败
  WORKFLOW_FAILED = 3;
  // 已取消
  WORKFLOW_CANCELED = 4;
  //  // 等待输入
  //  WORKFLOW_WAITING_INPUT = 5;
  //  // 正在停止
  //  WORKFLOW_STOPPING = 6;
}

// 节点任务状态枚举
enum NodeRunState {
  // 初始状态
  NODE_INIT = 0;
  // 运行中
  NODE_RUNNING = 1;
  // 运行成功
  NODE_SUCCESS = 2;
  // 运行失败
  NODE_FAILED = 3;
  // 已取消
  NODE_CANCELED = 4;
  // // 等待外部输入
  //  NODE_WAITING_INPUT = 5;
}

message CanCreateWorkflowRunReq {
  // 应用ID
  string AppBizId = 1 [(trpc.go_tag) = 'valid:"required~请传入正确的应用ID"'];
  // 所属工作流ID
  string WorkflowId = 2;
}

message CanCreateWorkflowRunRsp {
  // 是否可以创建工作流运行实例。
  bool Result = 1;
}

message CustomVariable {
  string Name = 1; // 变量名称
  string Value = 2; // 变量值
}

message CustomVariableConfig {
  string Name = 1; // 变量名称
  string Value = 2; // 变量值
  string ValueType = 3; // 变量类型。取值为workflow.proto的 TypeEnum 对应的字符串。（字符串主要为了跟API参数的接口保持一致）
  FileInfo FileInfo = 4; // 文件信息。(后面如果要支持多个文件，可以再加一个FileInfos字段。)
}

message FileInfo {
  string Type = 1;  // 文件类型
  string Name = 2;  // 文件名称
  string Size = 3;  // 文件大小
}

message SaveAppDebugModeReq {
  // 应用ID
  string AppBizId = 1 [(trpc.go_tag) = 'valid:"required~请传入正确的应用ID"'];
  // 调试模式
  DebugModeType DebugMode = 2;
}

message SaveAppDebugModeRsp {
}

message SaveAppDebugCustomVariablesReq {
  // 应用ID
  string AppBizId = 1 [(trpc.go_tag) = 'valid:"required~请传入正确的应用ID"'];
  // API参数
  repeated CustomVariableConfig CustomVariables = 2;
}

message SaveAppDebugCustomVariablesRsp {
}

message DescribeAppDebugConfigReq {
  // 应用ID
  string AppBizId = 1 [(trpc.go_tag) = 'valid:"required~请传入正确的应用ID"'];
}

message DescribeAppDebugConfigRsp {
  // 调试模式
  DebugModeType DebugMode = 1;
  // API参数
  repeated CustomVariableConfig CustomVariables = 2;
}

// 请求响应结构
message CreateWorkflowRunReq {
  // 运行环境。
  EnvType RunEnv = 1;
  // 应用ID
  string AppBizId = 2 [(trpc.go_tag) = 'valid:"required~请传入正确的应用ID"'];
  // 当前的query。
  string Query = 3;
  // API参数
  repeated CustomVariable CustomVariables = 4;
  //  // 文件信息（当前可能不需要，为空即可）
  //  repeated FileInfo FileInfos = 7;
}

message CreateWorkflowRunRsp {
  // 应用ID
  string AppBizId = 1 [(trpc.go_tag) = 'valid:"required~请传入正确的应用ID"'];
  // 新创建的任务ID
  string WorkflowRunId = 2;
  // 时间（毫秒时间戳）
  uint64 CreateTime = 3;
  // 运行环境
  EnvType RunEnv = 4;
  // 当前的query。
  string Query = 5;
  // API参数
  repeated CustomVariable CustomVariables = 6;
  //  // 文件信息（当前可能不需要，为空即可）
  //  repeated FileInfo FileInfos = 7;
}

message ListWorkflowRunsReq {
  // 运行环境
  EnvType RunEnv = 1;
  // 应用ID
  string AppBizId = 2 [(trpc.go_tag) = 'valid:"required~请传入正确的应用ID"'];
  // 页码
  uint32 Page = 3;
  // 每页数量
  uint32 PageSize = 4 [(trpc.go_tag) = 'valid:"required,range(1|200)~每页数量在1到200之间"'];
}

message ListWorkflowRunsRsp {
  // 总数
  uint32 Total = 1 [(trpc.go_tag) = 'json:"Total"'];
  // 运行实例列表
  repeated WorkflowRunBase WorkflowRuns = 2 [(trpc.go_tag) = 'json:"WorkflowRuns"'];
}

// 工作流运行实例
message WorkflowRunBase {
  // 运行环境。
  EnvType RunEnv = 1;
  // 应用ID
  string AppBizId = 2 [(trpc.go_tag) = 'valid:"required~请传入正确的应用ID"'];
  // 工作流运行实例ID
  string WorkflowRunId = 3;
  // 所属工作流ID
  string WorkflowId = 4;
  // 名称
  string Name = 5;
  // 描述
  //  string Desc = 6;
  // 运行状态
  WorkflowRunState State = 7;
  // 错误信息
  string FailMessage = 8;
  // token总数
  uint32 TotalTokens = 9;
  // 开始时间（毫秒时间戳）
  uint64 StartTime = 10;
  // 结束时间（毫秒时间戳）
  uint64 EndTime = 11;
  // 创建时间（毫秒时间戳）
  uint64 CreateTime = 12;
}

// 工作流运行实例
message WorkflowRunDetail {
  // 运行环境。
  EnvType RunEnv = 1;
  // 应用ID
  string AppBizId = 2 [(trpc.go_tag) = 'valid:"required~请传入正确的应用ID"'];
  // 工作流运行实例ID
  string WorkflowRunId = 3;
  // 所属工作流ID
  string WorkflowId = 4;
  // 名称
  string Name = 5;
  // 描述
  //  string Desc = 6;
  // 运行状态
  WorkflowRunState State = 7;
  // 错误信息
  string FailMessage = 8;
  // token总数
  uint32 TotalTokens = 9;
  // 开始时间（毫秒时间戳）
  uint64 StartTime = 10;
  // 结束时间（毫秒时间戳）
  uint64 EndTime = 11;
  // 创建时间（毫秒时间戳）
  uint64 CreateTime = 12;

  // 工作流内容
  string DialogJson = 13;
  // 当前的query。
  string Query = 14;
  // 主模型名称。
  string MainModelName = 15;
  // API参数配置
  repeated CustomVariable CustomVariables = 16;
}

// 工作流节点基本信息
message NodeRunBase {
  // 节点运行ID
  string NodeRunId = 1;
  // 节点ID
  string NodeId = 2;
  //  // 所属节点ID，用来过滤父、子工作流的节点信息。
  //  string BelongNodeId = 3;
  // 工作流运行实例ID
  string WorkflowRunId = 4;
  // 节点名称
  string NodeName = 5;
  // 节点类型
  trpc.KEP.bot_task_config_wf_server.NodeType NodeType = 6;
  // 任务状态
  NodeRunState State = 7;
  // 错误信息
  string FailMessage = 8;
  // 节点的总耗时。如果节点有多次调用，耗时为多次调用的总和。
  uint32 CostMilliseconds = 9;
  // token总数
  uint32 TotalTokens = 10;
  // 错误码
  string FailCode = 11;
}

// 工作流节点运行详情
message NodeRunDetail {
  // 节点运行ID
  string NodeRunId = 1;
  // 节点ID
  string NodeId = 2;
  //  // 所属节点ID，用来过滤父、子工作流的节点信息。
  //  string BelongNodeId = 3;
  // 工作流运行实例ID
  string WorkflowRunId = 4;
  // 节点名称
  string NodeName = 5;
  // 节点类型
  trpc.KEP.bot_task_config_wf_server.NodeType NodeType = 6;
  // 任务状态
  NodeRunState State = 7;
  // 错误信息
  string FailMessage = 8;
  // 节点的总耗时。如果节点有多次调用，耗时为多次调用的总和。
  uint32 CostMilliseconds = 9;
  // token总数
  uint32 TotalTokens = 10;

  // 节点输入
  string Input = 11;
  // 节点输出
  string Output = 12;
  // 节点原始输出
  string TaskOutput = 13;
  // 开始时间（毫秒时间戳）
  uint64 StartTime = 14;
  // 结束时间（毫秒时间戳）
  uint64 EndTime = 15;
  // 错误码
  string FailCode = 16;
  
  // LLM统计信息。
  repeated StatisticInfo StatisticInfos = 100;
}

// 统计信息
message StatisticInfo {
  // 模型名称
  string ModelName = 1;
  // 首token耗时
  uint32 FirstTokenCost = 2;
  // 推理总耗时
  uint32 TotalCost = 3;
  // 输入token数量
  uint32 InputTokens = 4;
  // 输出token数量
  uint32 OutputTokens = 5;
  // 输入+输出总token
  uint32 TotalTokens = 6;
}

message DescribeWorkflowRunReq {
  // 工作流运行实例ID
  string WorkflowRunId = 1;
  //  // 子工作流的过滤条件。用来找到对应的子工作流的所有节点的运行情况。
  //  repeated SubWorkflowFilter SubWorkflowFilters = 2;
}

message SubWorkflowFilter {
   // 所属的节点的ID
  string NodeId = 1;
  // 循环节点时非空，表示第几轮
  uint32 LoopIndex = 2;
}

message DescribeWorkflowRunRsp {
  // 运行实例信息
  WorkflowRunDetail WorkflowRun = 1;
  // 节点运行详情
  repeated NodeRunBase NodeRuns = 2;
}

message DescribeNodeRunReq{
  // 节点运行实例ID
  string NodeRunId = 1;
}

message DescribeNodeRunRsp {
  // 节点运行实例详情
  NodeRunDetail  NodeRun = 1;
}

message DescribeWorkflowReq {
}

message StopWorkflowRunReq {
  // 工作流运行实例ID
  string WorkflowRunId = 1;
}

message StopWorkflowRunRsp {
}
