// Code generated by trpc-go/trpc-go-cmdline v2.8.6. DO NOT EDIT.
// source: channel-msg-svr.proto

package channel_msg_svr

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// START ======================================= Server Service Definition ======================================= START

// ChannelMsgServiceService defines service.
type ChannelMsgServiceService interface {
	// GetWxAuthorizeUrl 获取公众号授权url
	GetWxAuthorizeUrl(ctx context.Context, req *GetWxAuthorizeUrlReq) (*GetWxAuthorizeUrlRsp, error)
}

func ChannelMsgServiceService_GetWxAuthorizeUrl_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetWxAuthorizeUrlReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ChannelMsgServiceService).GetWxAuthorizeUrl(ctx, reqbody.(*GetWxAuthorizeUrlReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// ChannelMsgServiceServer_ServiceDesc descriptor for server.RegisterService.
var ChannelMsgServiceServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.channel_msg_svr.ChannelMsgService",
	HandlerType: ((*ChannelMsgServiceService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.KEP.channel_msg_svr.ChannelMsgService/GetWxAuthorizeUrl",
			Func: ChannelMsgServiceService_GetWxAuthorizeUrl_Handler,
		},
	},
}

// RegisterChannelMsgServiceService registers service.
func RegisterChannelMsgServiceService(s server.Service, svr ChannelMsgServiceService) {
	if err := s.Register(&ChannelMsgServiceServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("ChannelMsgService register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedChannelMsgService struct{}

// GetWxAuthorizeUrl 获取公众号授权url
func (s *UnimplementedChannelMsgService) GetWxAuthorizeUrl(ctx context.Context, req *GetWxAuthorizeUrlReq) (*GetWxAuthorizeUrlRsp, error) {
	return nil, errors.New("rpc GetWxAuthorizeUrl of service ChannelMsgService is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// ChannelMsgServiceClientProxy defines service client proxy
type ChannelMsgServiceClientProxy interface {
	// GetWxAuthorizeUrl 获取公众号授权url
	GetWxAuthorizeUrl(ctx context.Context, req *GetWxAuthorizeUrlReq, opts ...client.Option) (rsp *GetWxAuthorizeUrlRsp, err error)
}

type ChannelMsgServiceClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewChannelMsgServiceClientProxy = func(opts ...client.Option) ChannelMsgServiceClientProxy {
	return &ChannelMsgServiceClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *ChannelMsgServiceClientProxyImpl) GetWxAuthorizeUrl(ctx context.Context, req *GetWxAuthorizeUrlReq, opts ...client.Option) (*GetWxAuthorizeUrlRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.channel_msg_svr.ChannelMsgService/GetWxAuthorizeUrl")
	msg.WithCalleeServiceName(ChannelMsgServiceServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("channel_msg_svr")
	msg.WithCalleeService("ChannelMsgService")
	msg.WithCalleeMethod("GetWxAuthorizeUrl")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetWxAuthorizeUrlRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
