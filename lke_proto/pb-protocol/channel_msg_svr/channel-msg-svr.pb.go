// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.21.4
// source: channel-msg-svr.proto

package channel_msg_svr

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetWxAuthorizeUrlReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string `protobuf:"bytes,1,opt,name=app_biz_id,json=appBizId,proto3" json:"app_biz_id,omitempty"`
	WxAppid  string `protobuf:"bytes,2,opt,name=wx_appid,json=wxAppid,proto3" json:"wx_appid,omitempty"` //公众号appid
	Comment  string `protobuf:"bytes,3,opt,name=comment,proto3" json:"comment,omitempty"`                // 备注
}

func (x *GetWxAuthorizeUrlReq) Reset() {
	*x = GetWxAuthorizeUrlReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_msg_svr_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWxAuthorizeUrlReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWxAuthorizeUrlReq) ProtoMessage() {}

func (x *GetWxAuthorizeUrlReq) ProtoReflect() protoreflect.Message {
	mi := &file_channel_msg_svr_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWxAuthorizeUrlReq.ProtoReflect.Descriptor instead.
func (*GetWxAuthorizeUrlReq) Descriptor() ([]byte, []int) {
	return file_channel_msg_svr_proto_rawDescGZIP(), []int{0}
}

func (x *GetWxAuthorizeUrlReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *GetWxAuthorizeUrlReq) GetWxAppid() string {
	if x != nil {
		return x.WxAppid
	}
	return ""
}

func (x *GetWxAuthorizeUrlReq) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

type GetWxAuthorizeUrlRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RedirectUrl string `protobuf:"bytes,1,opt,name=redirect_url,json=redirectUrl,proto3" json:"redirect_url,omitempty"`
}

func (x *GetWxAuthorizeUrlRsp) Reset() {
	*x = GetWxAuthorizeUrlRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_msg_svr_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWxAuthorizeUrlRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWxAuthorizeUrlRsp) ProtoMessage() {}

func (x *GetWxAuthorizeUrlRsp) ProtoReflect() protoreflect.Message {
	mi := &file_channel_msg_svr_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWxAuthorizeUrlRsp.ProtoReflect.Descriptor instead.
func (*GetWxAuthorizeUrlRsp) Descriptor() ([]byte, []int) {
	return file_channel_msg_svr_proto_rawDescGZIP(), []int{1}
}

func (x *GetWxAuthorizeUrlRsp) GetRedirectUrl() string {
	if x != nil {
		return x.RedirectUrl
	}
	return ""
}

var File_channel_msg_svr_proto protoreflect.FileDescriptor

var file_channel_msg_svr_proto_rawDesc = []byte{
	0x0a, 0x15, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2d, 0x6d, 0x73, 0x67, 0x2d, 0x73, 0x76,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x73, 0x76,
	0x72, 0x22, 0x69, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x57, 0x78, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x69, 0x7a, 0x65, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x0a, 0x61, 0x70, 0x70,
	0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61,
	0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x77, 0x78, 0x5f, 0x61, 0x70,
	0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x78, 0x41, 0x70, 0x70,
	0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x39, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x57, 0x78, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x55, 0x72,
	0x6c, 0x52, 0x73, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x55, 0x72, 0x6c, 0x32, 0x88, 0x01, 0x0a, 0x11, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x4d, 0x73, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x73, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x57, 0x78, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x55,
	0x72, 0x6c, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x57, 0x78, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x55, 0x72, 0x6c, 0x52,
	0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x57, 0x78, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x55, 0x72, 0x6c, 0x52,
	0x73, 0x70, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62,
	0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x73, 0x76, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_channel_msg_svr_proto_rawDescOnce sync.Once
	file_channel_msg_svr_proto_rawDescData = file_channel_msg_svr_proto_rawDesc
)

func file_channel_msg_svr_proto_rawDescGZIP() []byte {
	file_channel_msg_svr_proto_rawDescOnce.Do(func() {
		file_channel_msg_svr_proto_rawDescData = protoimpl.X.CompressGZIP(file_channel_msg_svr_proto_rawDescData)
	})
	return file_channel_msg_svr_proto_rawDescData
}

var file_channel_msg_svr_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_channel_msg_svr_proto_goTypes = []interface{}{
	(*GetWxAuthorizeUrlReq)(nil), // 0: trpc.KEP.channel_msg_svr.GetWxAuthorizeUrlReq
	(*GetWxAuthorizeUrlRsp)(nil), // 1: trpc.KEP.channel_msg_svr.GetWxAuthorizeUrlRsp
}
var file_channel_msg_svr_proto_depIdxs = []int32{
	0, // 0: trpc.KEP.channel_msg_svr.ChannelMsgService.GetWxAuthorizeUrl:input_type -> trpc.KEP.channel_msg_svr.GetWxAuthorizeUrlReq
	1, // 1: trpc.KEP.channel_msg_svr.ChannelMsgService.GetWxAuthorizeUrl:output_type -> trpc.KEP.channel_msg_svr.GetWxAuthorizeUrlRsp
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_channel_msg_svr_proto_init() }
func file_channel_msg_svr_proto_init() {
	if File_channel_msg_svr_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_channel_msg_svr_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWxAuthorizeUrlReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_msg_svr_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWxAuthorizeUrlRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_channel_msg_svr_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_channel_msg_svr_proto_goTypes,
		DependencyIndexes: file_channel_msg_svr_proto_depIdxs,
		MessageInfos:      file_channel_msg_svr_proto_msgTypes,
	}.Build()
	File_channel_msg_svr_proto = out.File
	file_channel_msg_svr_proto_rawDesc = nil
	file_channel_msg_svr_proto_goTypes = nil
	file_channel_msg_svr_proto_depIdxs = nil
}
