// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.19.1
// source: workflow-pdl.proto

package bot_task_config_wf_server

import (
	reflect "reflect"
	sync "sync"

	KEP_WF "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	_ "git.code.oa.com/trpc-go/trpc"
	_ "google.golang.org/protobuf/types/descriptorpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PDLTool PDL调用工具列表
type PDLToolsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tools []*PDLTool `protobuf:"bytes,1,rep,name=Tools,proto3" json:"Tools,omitempty"`
}

func (x *PDLToolsInfo) Reset() {
	*x = PDLToolsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_pdl_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PDLToolsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PDLToolsInfo) ProtoMessage() {}

func (x *PDLToolsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_pdl_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PDLToolsInfo.ProtoReflect.Descriptor instead.
func (*PDLToolsInfo) Descriptor() ([]byte, []int) {
	return file_workflow_pdl_proto_rawDescGZIP(), []int{0}
}

func (x *PDLToolsInfo) GetTools() []*PDLTool {
	if x != nil {
		return x.Tools
	}
	return nil
}

// PDLTool PDL调用工具，复用工作流节点协议
type PDLTool struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToolName  string                `protobuf:"bytes,1,opt,name=ToolName,proto3" json:"ToolName,omitempty"`   // 工具名称, 对应原节点名称，如有合并则为合并后的名称
	ToolDesc  string                `protobuf:"bytes,2,opt,name=ToolDesc,proto3" json:"ToolDesc,omitempty"`   // 工具描述
	Inputs    []*KEP_WF.InputParam  `protobuf:"bytes,3,rep,name=Inputs,proto3" json:"Inputs,omitempty"`       // 输入参数，用于function call，过滤固定值和系统变量及api参数等，如有合并则为合并后的入参
	Outputs   []*KEP_WF.OutputParam `protobuf:"bytes,4,rep,name=Outputs,proto3" json:"Outputs,omitempty"`     // 输出参数，用于function call，过滤固定值和系统变量及api参数等，如有合并则为合并后的出参
	ToolNodes []*PDLToolNode        `protobuf:"bytes,5,rep,name=ToolNodes,proto3" json:"ToolNodes,omitempty"` // 子工具, 具体运行的节点，如有合并则子工具有多个
}

func (x *PDLTool) Reset() {
	*x = PDLTool{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_pdl_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PDLTool) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PDLTool) ProtoMessage() {}

func (x *PDLTool) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_pdl_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PDLTool.ProtoReflect.Descriptor instead.
func (*PDLTool) Descriptor() ([]byte, []int) {
	return file_workflow_pdl_proto_rawDescGZIP(), []int{1}
}

func (x *PDLTool) GetToolName() string {
	if x != nil {
		return x.ToolName
	}
	return ""
}

func (x *PDLTool) GetToolDesc() string {
	if x != nil {
		return x.ToolDesc
	}
	return ""
}

func (x *PDLTool) GetInputs() []*KEP_WF.InputParam {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *PDLTool) GetOutputs() []*KEP_WF.OutputParam {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *PDLTool) GetToolNodes() []*PDLToolNode {
	if x != nil {
		return x.ToolNodes
	}
	return nil
}

// PDLToolNode PDL工具节点
type PDLToolNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeID   string          `protobuf:"bytes,1,opt,name=NodeID,proto3" json:"NodeID,omitempty"`                                                       // 节点ID
	NodeName string          `protobuf:"bytes,2,opt,name=NodeName,proto3" json:"NodeName,omitempty"`                                                   // 节点名称
	NodeDesc string          `protobuf:"bytes,3,opt,name=NodeDesc,proto3" json:"NodeDesc,omitempty"`                                                   // 节点描述
	NodeType KEP_WF.NodeType `protobuf:"varint,4,opt,name=NodeType,proto3,enum=trpc.KEP.bot_task_config_wf_server.NodeType" json:"NodeType,omitempty"` // 节点类型；字符串
	// 目前只有工具、大模型、代码、插件节点
	LLMNodeData          *KEP_WF.LLMNodeData          `protobuf:"bytes,1003,opt,name=LLMNodeData,proto3" json:"LLMNodeData,omitempty"`                   // 大模型节点 NodeType.LLM
	CodeExecutorNodeData *KEP_WF.CodeExecutorNodeData `protobuf:"bytes,1007,opt,name=CodeExecutorNodeData,proto3" json:"CodeExecutorNodeData,omitempty"` // 代码执行节点 NodeType.CODE_EXECUTOR
	ToolNodeData         *KEP_WF.ToolNodeData         `protobuf:"bytes,1008,opt,name=ToolNodeData,proto3" json:"ToolNodeData,omitempty"`                 // 工具节点 NodeType.TOOL
	PluginNodeData       *KEP_WF.PluginNodeData       `protobuf:"bytes,1015,opt,name=PluginNodeData,proto3" json:"PluginNodeData,omitempty"`             // 插件节点 NodeType.PLUGIN
	Inputs               []*KEP_WF.InputParam         `protobuf:"bytes,5,rep,name=Inputs,proto3" json:"Inputs,omitempty"`                                // 输入参数 (在 LOGIC_EVALUATOR, TOOL 类型的节点忽略)
	Outputs              []*KEP_WF.OutputParam        `protobuf:"bytes,6,rep,name=Outputs,proto3" json:"Outputs,omitempty"`                              // 输出；  （在 CODE_EXECUTOR, TOOL, END 类型的节点使用)
	NextNodeIDs          []string                     `protobuf:"bytes,7,rep,name=NextNodeIDs,proto3" json:"NextNodeIDs,omitempty"`
}

func (x *PDLToolNode) Reset() {
	*x = PDLToolNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_pdl_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PDLToolNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PDLToolNode) ProtoMessage() {}

func (x *PDLToolNode) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_pdl_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PDLToolNode.ProtoReflect.Descriptor instead.
func (*PDLToolNode) Descriptor() ([]byte, []int) {
	return file_workflow_pdl_proto_rawDescGZIP(), []int{2}
}

func (x *PDLToolNode) GetNodeID() string {
	if x != nil {
		return x.NodeID
	}
	return ""
}

func (x *PDLToolNode) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *PDLToolNode) GetNodeDesc() string {
	if x != nil {
		return x.NodeDesc
	}
	return ""
}

func (x *PDLToolNode) GetNodeType() KEP_WF.NodeType {
	if x != nil {
		return x.NodeType
	}
	return KEP_WF.NodeType(0)
}

func (x *PDLToolNode) GetLLMNodeData() *KEP_WF.LLMNodeData {
	if x != nil {
		return x.LLMNodeData
	}
	return nil
}

func (x *PDLToolNode) GetCodeExecutorNodeData() *KEP_WF.CodeExecutorNodeData {
	if x != nil {
		return x.CodeExecutorNodeData
	}
	return nil
}

func (x *PDLToolNode) GetToolNodeData() *KEP_WF.ToolNodeData {
	if x != nil {
		return x.ToolNodeData
	}
	return nil
}

func (x *PDLToolNode) GetPluginNodeData() *KEP_WF.PluginNodeData {
	if x != nil {
		return x.PluginNodeData
	}
	return nil
}

func (x *PDLToolNode) GetInputs() []*KEP_WF.InputParam {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *PDLToolNode) GetOutputs() []*KEP_WF.OutputParam {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *PDLToolNode) GetNextNodeIDs() []string {
	if x != nil {
		return x.NextNodeIDs
	}
	return nil
}

var File_workflow_pdl_proto protoreflect.FileDescriptor

var file_workflow_pdl_proto_rawDesc = []byte{
	0x0a, 0x12, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2d, 0x70, 0x64, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77,
	0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0d, 0x73, 0x77, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x51, 0x0a, 0x0c, 0x50, 0x44, 0x4c, 0x54, 0x6f, 0x6f, 0x6c,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a, 0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x44, 0x4c, 0x54, 0x6f, 0x6f,
	0x6c, 0x52, 0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x22, 0xa3, 0x02, 0x0a, 0x07, 0x50, 0x44, 0x4c,
	0x54, 0x6f, 0x6f, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x12, 0x46, 0x0a, 0x06,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x73, 0x12, 0x49, 0x0a, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12,
	0x4d, 0x0a, 0x09, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x44, 0x4c, 0x54, 0x6f, 0x6f, 0x6c, 0x4e,
	0x6f, 0x64, 0x65, 0x52, 0x09, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x22, 0xd3,
	0x05, 0x0a, 0x0b, 0x50, 0x44, 0x4c, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x73, 0x63, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x73, 0x63, 0x12, 0x48,
	0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x0b, 0x4c, 0x4c, 0x4d, 0x4e,
	0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xeb, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4c, 0x4c, 0x4d, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x0b, 0x4c, 0x4c, 0x4d, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x6d, 0x0a, 0x14,
	0x43, 0x6f, 0x64, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x18, 0xef, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x14, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x55, 0x0a, 0x0c, 0x54,
	0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xf0, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x5b, 0x0a, 0x0e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x6f, 0x64, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x18, 0xf7, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x0e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x46, 0x0a, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52,
	0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x49, 0x0a, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x6f, 0x64,
	0x65, 0x49, 0x44, 0x73, 0x42, 0x4f, 0x5a, 0x4d, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_workflow_pdl_proto_rawDescOnce sync.Once
	file_workflow_pdl_proto_rawDescData = file_workflow_pdl_proto_rawDesc
)

func file_workflow_pdl_proto_rawDescGZIP() []byte {
	file_workflow_pdl_proto_rawDescOnce.Do(func() {
		file_workflow_pdl_proto_rawDescData = protoimpl.X.CompressGZIP(file_workflow_pdl_proto_rawDescData)
	})
	return file_workflow_pdl_proto_rawDescData
}

var file_workflow_pdl_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_workflow_pdl_proto_goTypes = []interface{}{
	(*PDLToolsInfo)(nil),                // 0: trpc.KEP.bot_task_config_wf_server.PDLToolsInfo
	(*PDLTool)(nil),                     // 1: trpc.KEP.bot_task_config_wf_server.PDLTool
	(*PDLToolNode)(nil),                 // 2: trpc.KEP.bot_task_config_wf_server.PDLToolNode
	(*KEP_WF.InputParam)(nil),           // 3: trpc.KEP.bot_task_config_wf_server.InputParam
	(*KEP_WF.OutputParam)(nil),          // 4: trpc.KEP.bot_task_config_wf_server.OutputParam
	(KEP_WF.NodeType)(0),                // 5: trpc.KEP.bot_task_config_wf_server.NodeType
	(*KEP_WF.LLMNodeData)(nil),          // 6: trpc.KEP.bot_task_config_wf_server.LLMNodeData
	(*KEP_WF.CodeExecutorNodeData)(nil), // 7: trpc.KEP.bot_task_config_wf_server.CodeExecutorNodeData
	(*KEP_WF.ToolNodeData)(nil),         // 8: trpc.KEP.bot_task_config_wf_server.ToolNodeData
	(*KEP_WF.PluginNodeData)(nil),       // 9: trpc.KEP.bot_task_config_wf_server.PluginNodeData
}
var file_workflow_pdl_proto_depIdxs = []int32{
	1,  // 0: trpc.KEP.bot_task_config_wf_server.PDLToolsInfo.Tools:type_name -> trpc.KEP.bot_task_config_wf_server.PDLTool
	3,  // 1: trpc.KEP.bot_task_config_wf_server.PDLTool.Inputs:type_name -> trpc.KEP.bot_task_config_wf_server.InputParam
	4,  // 2: trpc.KEP.bot_task_config_wf_server.PDLTool.Outputs:type_name -> trpc.KEP.bot_task_config_wf_server.OutputParam
	2,  // 3: trpc.KEP.bot_task_config_wf_server.PDLTool.ToolNodes:type_name -> trpc.KEP.bot_task_config_wf_server.PDLToolNode
	5,  // 4: trpc.KEP.bot_task_config_wf_server.PDLToolNode.NodeType:type_name -> trpc.KEP.bot_task_config_wf_server.NodeType
	6,  // 5: trpc.KEP.bot_task_config_wf_server.PDLToolNode.LLMNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.LLMNodeData
	7,  // 6: trpc.KEP.bot_task_config_wf_server.PDLToolNode.CodeExecutorNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.CodeExecutorNodeData
	8,  // 7: trpc.KEP.bot_task_config_wf_server.PDLToolNode.ToolNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.ToolNodeData
	9,  // 8: trpc.KEP.bot_task_config_wf_server.PDLToolNode.PluginNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.PluginNodeData
	3,  // 9: trpc.KEP.bot_task_config_wf_server.PDLToolNode.Inputs:type_name -> trpc.KEP.bot_task_config_wf_server.InputParam
	4,  // 10: trpc.KEP.bot_task_config_wf_server.PDLToolNode.Outputs:type_name -> trpc.KEP.bot_task_config_wf_server.OutputParam
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_workflow_pdl_proto_init() }
func file_workflow_pdl_proto_init() {
	if File_workflow_pdl_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_workflow_pdl_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PDLToolsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_pdl_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PDLTool); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_pdl_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PDLToolNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_workflow_pdl_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_workflow_pdl_proto_goTypes,
		DependencyIndexes: file_workflow_pdl_proto_depIdxs,
		MessageInfos:      file_workflow_pdl_proto_msgTypes,
	}.Build()
	File_workflow_pdl_proto = out.File
	file_workflow_pdl_proto_rawDesc = nil
	file_workflow_pdl_proto_goTypes = nil
	file_workflow_pdl_proto_depIdxs = nil
}
