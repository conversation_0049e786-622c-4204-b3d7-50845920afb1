// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v3.19.1
// source: workflow-run.proto

package KEP_WF

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	_ "git.code.oa.com/devsec/protoc-gen-secv/validate"
	_ "git.code.oa.com/trpc-go/trpc"
	_ "google.golang.org/protobuf/types/descriptorpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 运行环境
type EnvType int32

const (
	// 测试环境
	EnvType_TEST EnvType = 0
	// 正式环境
	EnvType_PROD EnvType = 1
)

// Enum value maps for EnvType.
var (
	EnvType_name = map[int32]string{
		0: "TEST",
		1: "PROD",
	}
	EnvType_value = map[string]int32{
		"TEST": 0,
		"PROD": 1,
	}
)

func (x EnvType) Enum() *EnvType {
	p := new(EnvType)
	*p = x
	return p
}

func (x EnvType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnvType) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_run_proto_enumTypes[0].Descriptor()
}

func (EnvType) Type() protoreflect.EnumType {
	return &file_workflow_run_proto_enumTypes[0]
}

func (x EnvType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnvType.Descriptor instead.
func (EnvType) EnumDescriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{0}
}

// 调试模型类型
type DebugModeType int32

const (
	// 同步
	DebugModeType_SYNC DebugModeType = 0
	// 异步
	DebugModeType_ASYNC DebugModeType = 1
)

// Enum value maps for DebugModeType.
var (
	DebugModeType_name = map[int32]string{
		0: "SYNC",
		1: "ASYNC",
	}
	DebugModeType_value = map[string]int32{
		"SYNC":  0,
		"ASYNC": 1,
	}
)

func (x DebugModeType) Enum() *DebugModeType {
	p := new(DebugModeType)
	*p = x
	return p
}

func (x DebugModeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DebugModeType) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_run_proto_enumTypes[1].Descriptor()
}

func (DebugModeType) Type() protoreflect.EnumType {
	return &file_workflow_run_proto_enumTypes[1]
}

func (x DebugModeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DebugModeType.Descriptor instead.
func (DebugModeType) EnumDescriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{1}
}

// 工作流运行状态枚举
type WorkflowRunState int32

const (
	// 排队中
	WorkflowRunState_WORKFLOW_PENDING WorkflowRunState = 0
	// 运行中
	WorkflowRunState_WORKFLOW_RUNNING WorkflowRunState = 1
	// 运行成功
	WorkflowRunState_WORKFLOW_SUCCESS WorkflowRunState = 2
	// 运行失败
	WorkflowRunState_WORKFLOW_FAILED WorkflowRunState = 3
	// 已取消
	WorkflowRunState_WORKFLOW_CANCELED WorkflowRunState = 4
)

// Enum value maps for WorkflowRunState.
var (
	WorkflowRunState_name = map[int32]string{
		0: "WORKFLOW_PENDING",
		1: "WORKFLOW_RUNNING",
		2: "WORKFLOW_SUCCESS",
		3: "WORKFLOW_FAILED",
		4: "WORKFLOW_CANCELED",
	}
	WorkflowRunState_value = map[string]int32{
		"WORKFLOW_PENDING":  0,
		"WORKFLOW_RUNNING":  1,
		"WORKFLOW_SUCCESS":  2,
		"WORKFLOW_FAILED":   3,
		"WORKFLOW_CANCELED": 4,
	}
)

func (x WorkflowRunState) Enum() *WorkflowRunState {
	p := new(WorkflowRunState)
	*p = x
	return p
}

func (x WorkflowRunState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkflowRunState) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_run_proto_enumTypes[2].Descriptor()
}

func (WorkflowRunState) Type() protoreflect.EnumType {
	return &file_workflow_run_proto_enumTypes[2]
}

func (x WorkflowRunState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkflowRunState.Descriptor instead.
func (WorkflowRunState) EnumDescriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{2}
}

// 节点任务状态枚举
type NodeRunState int32

const (
	// 初始状态
	NodeRunState_NODE_INIT NodeRunState = 0
	// 运行中
	NodeRunState_NODE_RUNNING NodeRunState = 1
	// 运行成功
	NodeRunState_NODE_SUCCESS NodeRunState = 2
	// 运行失败
	NodeRunState_NODE_FAILED NodeRunState = 3
	// 已取消
	NodeRunState_NODE_CANCELED NodeRunState = 4
)

// Enum value maps for NodeRunState.
var (
	NodeRunState_name = map[int32]string{
		0: "NODE_INIT",
		1: "NODE_RUNNING",
		2: "NODE_SUCCESS",
		3: "NODE_FAILED",
		4: "NODE_CANCELED",
	}
	NodeRunState_value = map[string]int32{
		"NODE_INIT":     0,
		"NODE_RUNNING":  1,
		"NODE_SUCCESS":  2,
		"NODE_FAILED":   3,
		"NODE_CANCELED": 4,
	}
)

func (x NodeRunState) Enum() *NodeRunState {
	p := new(NodeRunState)
	*p = x
	return p
}

func (x NodeRunState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodeRunState) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_run_proto_enumTypes[3].Descriptor()
}

func (NodeRunState) Type() protoreflect.EnumType {
	return &file_workflow_run_proto_enumTypes[3]
}

func (x NodeRunState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodeRunState.Descriptor instead.
func (NodeRunState) EnumDescriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{3}
}

type CanCreateWorkflowRunReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用ID
	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty" valid:"required~请传入正确的应用ID"`
	// 所属工作流ID
	WorkflowId string `protobuf:"bytes,2,opt,name=WorkflowId,proto3" json:"WorkflowId,omitempty"`
}

func (x *CanCreateWorkflowRunReq) Reset() {
	*x = CanCreateWorkflowRunReq{}
	mi := &file_workflow_run_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CanCreateWorkflowRunReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CanCreateWorkflowRunReq) ProtoMessage() {}

func (x *CanCreateWorkflowRunReq) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CanCreateWorkflowRunReq.ProtoReflect.Descriptor instead.
func (*CanCreateWorkflowRunReq) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{0}
}

func (x *CanCreateWorkflowRunReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *CanCreateWorkflowRunReq) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

type CanCreateWorkflowRunRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否可以创建工作流运行实例。
	Result bool `protobuf:"varint,1,opt,name=Result,proto3" json:"Result,omitempty"`
}

func (x *CanCreateWorkflowRunRsp) Reset() {
	*x = CanCreateWorkflowRunRsp{}
	mi := &file_workflow_run_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CanCreateWorkflowRunRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CanCreateWorkflowRunRsp) ProtoMessage() {}

func (x *CanCreateWorkflowRunRsp) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CanCreateWorkflowRunRsp.ProtoReflect.Descriptor instead.
func (*CanCreateWorkflowRunRsp) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{1}
}

func (x *CanCreateWorkflowRunRsp) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

type CustomVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`   // 变量名称
	Value string `protobuf:"bytes,2,opt,name=Value,proto3" json:"Value,omitempty"` // 变量值
}

func (x *CustomVariable) Reset() {
	*x = CustomVariable{}
	mi := &file_workflow_run_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomVariable) ProtoMessage() {}

func (x *CustomVariable) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomVariable.ProtoReflect.Descriptor instead.
func (*CustomVariable) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{2}
}

func (x *CustomVariable) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomVariable) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type CustomVariableConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string    `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`           // 变量名称
	Value     string    `protobuf:"bytes,2,opt,name=Value,proto3" json:"Value,omitempty"`         // 变量值
	ValueType string    `protobuf:"bytes,3,opt,name=ValueType,proto3" json:"ValueType,omitempty"` // 变量类型。取值为workflow.proto的 TypeEnum 对应的字符串。（字符串主要为了跟API参数的接口保持一致）
	FileInfo  *FileInfo `protobuf:"bytes,4,opt,name=FileInfo,proto3" json:"FileInfo,omitempty"`   // 文件信息。(后面如果要支持多个文件，可以再加一个FileInfos字段。)
}

func (x *CustomVariableConfig) Reset() {
	*x = CustomVariableConfig{}
	mi := &file_workflow_run_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomVariableConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomVariableConfig) ProtoMessage() {}

func (x *CustomVariableConfig) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomVariableConfig.ProtoReflect.Descriptor instead.
func (*CustomVariableConfig) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{3}
}

func (x *CustomVariableConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomVariableConfig) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *CustomVariableConfig) GetValueType() string {
	if x != nil {
		return x.ValueType
	}
	return ""
}

func (x *CustomVariableConfig) GetFileInfo() *FileInfo {
	if x != nil {
		return x.FileInfo
	}
	return nil
}

type FileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type string `protobuf:"bytes,1,opt,name=Type,proto3" json:"Type,omitempty"` // 文件类型
	Name string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"` // 文件名称
	Size string `protobuf:"bytes,3,opt,name=Size,proto3" json:"Size,omitempty"` // 文件大小
}

func (x *FileInfo) Reset() {
	*x = FileInfo{}
	mi := &file_workflow_run_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileInfo) ProtoMessage() {}

func (x *FileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileInfo.ProtoReflect.Descriptor instead.
func (*FileInfo) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{4}
}

func (x *FileInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *FileInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FileInfo) GetSize() string {
	if x != nil {
		return x.Size
	}
	return ""
}

type SaveAppDebugModeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用ID
	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty" valid:"required~请传入正确的应用ID"`
	// 调试模式
	DebugMode DebugModeType `protobuf:"varint,2,opt,name=DebugMode,proto3,enum=trpc.KEP.bot_task_config_server.DebugModeType" json:"DebugMode,omitempty"`
}

func (x *SaveAppDebugModeReq) Reset() {
	*x = SaveAppDebugModeReq{}
	mi := &file_workflow_run_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAppDebugModeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAppDebugModeReq) ProtoMessage() {}

func (x *SaveAppDebugModeReq) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAppDebugModeReq.ProtoReflect.Descriptor instead.
func (*SaveAppDebugModeReq) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{5}
}

func (x *SaveAppDebugModeReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *SaveAppDebugModeReq) GetDebugMode() DebugModeType {
	if x != nil {
		return x.DebugMode
	}
	return DebugModeType_SYNC
}

type SaveAppDebugModeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SaveAppDebugModeRsp) Reset() {
	*x = SaveAppDebugModeRsp{}
	mi := &file_workflow_run_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAppDebugModeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAppDebugModeRsp) ProtoMessage() {}

func (x *SaveAppDebugModeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAppDebugModeRsp.ProtoReflect.Descriptor instead.
func (*SaveAppDebugModeRsp) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{6}
}

type SaveAppDebugCustomVariablesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用ID
	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty" valid:"required~请传入正确的应用ID"`
	// API参数
	CustomVariables []*CustomVariableConfig `protobuf:"bytes,2,rep,name=CustomVariables,proto3" json:"CustomVariables,omitempty"`
}

func (x *SaveAppDebugCustomVariablesReq) Reset() {
	*x = SaveAppDebugCustomVariablesReq{}
	mi := &file_workflow_run_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAppDebugCustomVariablesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAppDebugCustomVariablesReq) ProtoMessage() {}

func (x *SaveAppDebugCustomVariablesReq) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAppDebugCustomVariablesReq.ProtoReflect.Descriptor instead.
func (*SaveAppDebugCustomVariablesReq) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{7}
}

func (x *SaveAppDebugCustomVariablesReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *SaveAppDebugCustomVariablesReq) GetCustomVariables() []*CustomVariableConfig {
	if x != nil {
		return x.CustomVariables
	}
	return nil
}

type SaveAppDebugCustomVariablesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SaveAppDebugCustomVariablesRsp) Reset() {
	*x = SaveAppDebugCustomVariablesRsp{}
	mi := &file_workflow_run_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAppDebugCustomVariablesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAppDebugCustomVariablesRsp) ProtoMessage() {}

func (x *SaveAppDebugCustomVariablesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAppDebugCustomVariablesRsp.ProtoReflect.Descriptor instead.
func (*SaveAppDebugCustomVariablesRsp) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{8}
}

type DescribeAppDebugConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用ID
	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty" valid:"required~请传入正确的应用ID"`
}

func (x *DescribeAppDebugConfigReq) Reset() {
	*x = DescribeAppDebugConfigReq{}
	mi := &file_workflow_run_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DescribeAppDebugConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAppDebugConfigReq) ProtoMessage() {}

func (x *DescribeAppDebugConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAppDebugConfigReq.ProtoReflect.Descriptor instead.
func (*DescribeAppDebugConfigReq) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{9}
}

func (x *DescribeAppDebugConfigReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

type DescribeAppDebugConfigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 调试模式
	DebugMode DebugModeType `protobuf:"varint,1,opt,name=DebugMode,proto3,enum=trpc.KEP.bot_task_config_server.DebugModeType" json:"DebugMode,omitempty"`
	// API参数
	CustomVariables []*CustomVariableConfig `protobuf:"bytes,2,rep,name=CustomVariables,proto3" json:"CustomVariables,omitempty"`
}

func (x *DescribeAppDebugConfigRsp) Reset() {
	*x = DescribeAppDebugConfigRsp{}
	mi := &file_workflow_run_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DescribeAppDebugConfigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAppDebugConfigRsp) ProtoMessage() {}

func (x *DescribeAppDebugConfigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAppDebugConfigRsp.ProtoReflect.Descriptor instead.
func (*DescribeAppDebugConfigRsp) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{10}
}

func (x *DescribeAppDebugConfigRsp) GetDebugMode() DebugModeType {
	if x != nil {
		return x.DebugMode
	}
	return DebugModeType_SYNC
}

func (x *DescribeAppDebugConfigRsp) GetCustomVariables() []*CustomVariableConfig {
	if x != nil {
		return x.CustomVariables
	}
	return nil
}

// 请求响应结构
type CreateWorkflowRunReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 运行环境。
	RunEnv EnvType `protobuf:"varint,1,opt,name=RunEnv,proto3,enum=trpc.KEP.bot_task_config_server.EnvType" json:"RunEnv,omitempty"`
	// 应用ID
	AppBizId string `protobuf:"bytes,2,opt,name=AppBizId,proto3" json:"AppBizId,omitempty" valid:"required~请传入正确的应用ID"`
	// 当前的query。
	Query string `protobuf:"bytes,3,opt,name=Query,proto3" json:"Query,omitempty"`
	// API参数
	CustomVariables []*CustomVariable `protobuf:"bytes,4,rep,name=CustomVariables,proto3" json:"CustomVariables,omitempty"`
}

func (x *CreateWorkflowRunReq) Reset() {
	*x = CreateWorkflowRunReq{}
	mi := &file_workflow_run_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWorkflowRunReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowRunReq) ProtoMessage() {}

func (x *CreateWorkflowRunReq) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowRunReq.ProtoReflect.Descriptor instead.
func (*CreateWorkflowRunReq) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{11}
}

func (x *CreateWorkflowRunReq) GetRunEnv() EnvType {
	if x != nil {
		return x.RunEnv
	}
	return EnvType_TEST
}

func (x *CreateWorkflowRunReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *CreateWorkflowRunReq) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *CreateWorkflowRunReq) GetCustomVariables() []*CustomVariable {
	if x != nil {
		return x.CustomVariables
	}
	return nil
}

type CreateWorkflowRunRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用ID
	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty" valid:"required~请传入正确的应用ID"`
	// 新创建的任务ID
	WorkflowRunId string `protobuf:"bytes,2,opt,name=WorkflowRunId,proto3" json:"WorkflowRunId,omitempty"`
	// 时间（毫秒时间戳）
	CreateTime uint64 `protobuf:"varint,3,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`
	// 运行环境
	RunEnv EnvType `protobuf:"varint,4,opt,name=RunEnv,proto3,enum=trpc.KEP.bot_task_config_server.EnvType" json:"RunEnv,omitempty"`
	// 当前的query。
	Query string `protobuf:"bytes,5,opt,name=Query,proto3" json:"Query,omitempty"`
	// API参数
	CustomVariables []*CustomVariable `protobuf:"bytes,6,rep,name=CustomVariables,proto3" json:"CustomVariables,omitempty"`
}

func (x *CreateWorkflowRunRsp) Reset() {
	*x = CreateWorkflowRunRsp{}
	mi := &file_workflow_run_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWorkflowRunRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowRunRsp) ProtoMessage() {}

func (x *CreateWorkflowRunRsp) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowRunRsp.ProtoReflect.Descriptor instead.
func (*CreateWorkflowRunRsp) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{12}
}

func (x *CreateWorkflowRunRsp) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *CreateWorkflowRunRsp) GetWorkflowRunId() string {
	if x != nil {
		return x.WorkflowRunId
	}
	return ""
}

func (x *CreateWorkflowRunRsp) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *CreateWorkflowRunRsp) GetRunEnv() EnvType {
	if x != nil {
		return x.RunEnv
	}
	return EnvType_TEST
}

func (x *CreateWorkflowRunRsp) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *CreateWorkflowRunRsp) GetCustomVariables() []*CustomVariable {
	if x != nil {
		return x.CustomVariables
	}
	return nil
}

type ListWorkflowRunsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 运行环境
	RunEnv EnvType `protobuf:"varint,1,opt,name=RunEnv,proto3,enum=trpc.KEP.bot_task_config_server.EnvType" json:"RunEnv,omitempty"`
	// 应用ID
	AppBizId string `protobuf:"bytes,2,opt,name=AppBizId,proto3" json:"AppBizId,omitempty" valid:"required~请传入正确的应用ID"`
	// 页码
	Page uint32 `protobuf:"varint,3,opt,name=Page,proto3" json:"Page,omitempty"`
	// 每页数量
	PageSize uint32 `protobuf:"varint,4,opt,name=PageSize,proto3" json:"PageSize,omitempty" valid:"required,range(1|200)~每页数量在1到200之间"`
}

func (x *ListWorkflowRunsReq) Reset() {
	*x = ListWorkflowRunsReq{}
	mi := &file_workflow_run_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWorkflowRunsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowRunsReq) ProtoMessage() {}

func (x *ListWorkflowRunsReq) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowRunsReq.ProtoReflect.Descriptor instead.
func (*ListWorkflowRunsReq) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{13}
}

func (x *ListWorkflowRunsReq) GetRunEnv() EnvType {
	if x != nil {
		return x.RunEnv
	}
	return EnvType_TEST
}

func (x *ListWorkflowRunsReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *ListWorkflowRunsReq) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListWorkflowRunsReq) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListWorkflowRunsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总数
	Total uint32 `protobuf:"varint,1,opt,name=Total,proto3" json:"Total"`
	// 运行实例列表
	WorkflowRuns []*WorkflowRunBase `protobuf:"bytes,2,rep,name=WorkflowRuns,proto3" json:"WorkflowRuns"`
}

func (x *ListWorkflowRunsRsp) Reset() {
	*x = ListWorkflowRunsRsp{}
	mi := &file_workflow_run_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWorkflowRunsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowRunsRsp) ProtoMessage() {}

func (x *ListWorkflowRunsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowRunsRsp.ProtoReflect.Descriptor instead.
func (*ListWorkflowRunsRsp) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{14}
}

func (x *ListWorkflowRunsRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListWorkflowRunsRsp) GetWorkflowRuns() []*WorkflowRunBase {
	if x != nil {
		return x.WorkflowRuns
	}
	return nil
}

// 工作流运行实例
type WorkflowRunBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 运行环境。
	RunEnv EnvType `protobuf:"varint,1,opt,name=RunEnv,proto3,enum=trpc.KEP.bot_task_config_server.EnvType" json:"RunEnv,omitempty"`
	// 应用ID
	AppBizId string `protobuf:"bytes,2,opt,name=AppBizId,proto3" json:"AppBizId,omitempty" valid:"required~请传入正确的应用ID"`
	// 工作流运行实例ID
	WorkflowRunId string `protobuf:"bytes,3,opt,name=WorkflowRunId,proto3" json:"WorkflowRunId,omitempty"`
	// 所属工作流ID
	WorkflowId string `protobuf:"bytes,4,opt,name=WorkflowId,proto3" json:"WorkflowId,omitempty"`
	// 名称
	Name string `protobuf:"bytes,5,opt,name=Name,proto3" json:"Name,omitempty"`
	// 描述
	//
	//	string Desc = 6;
	//
	// 运行状态
	State WorkflowRunState `protobuf:"varint,7,opt,name=State,proto3,enum=trpc.KEP.bot_task_config_server.WorkflowRunState" json:"State,omitempty"`
	// 错误信息
	FailMessage string `protobuf:"bytes,8,opt,name=FailMessage,proto3" json:"FailMessage,omitempty"`
	// token总数
	TotalTokens uint32 `protobuf:"varint,9,opt,name=TotalTokens,proto3" json:"TotalTokens,omitempty"`
	// 开始时间（毫秒时间戳）
	StartTime uint64 `protobuf:"varint,10,opt,name=StartTime,proto3" json:"StartTime,omitempty"`
	// 结束时间（毫秒时间戳）
	EndTime uint64 `protobuf:"varint,11,opt,name=EndTime,proto3" json:"EndTime,omitempty"`
	// 创建时间（毫秒时间戳）
	CreateTime uint64 `protobuf:"varint,12,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`
}

func (x *WorkflowRunBase) Reset() {
	*x = WorkflowRunBase{}
	mi := &file_workflow_run_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowRunBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowRunBase) ProtoMessage() {}

func (x *WorkflowRunBase) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowRunBase.ProtoReflect.Descriptor instead.
func (*WorkflowRunBase) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{15}
}

func (x *WorkflowRunBase) GetRunEnv() EnvType {
	if x != nil {
		return x.RunEnv
	}
	return EnvType_TEST
}

func (x *WorkflowRunBase) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *WorkflowRunBase) GetWorkflowRunId() string {
	if x != nil {
		return x.WorkflowRunId
	}
	return ""
}

func (x *WorkflowRunBase) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *WorkflowRunBase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WorkflowRunBase) GetState() WorkflowRunState {
	if x != nil {
		return x.State
	}
	return WorkflowRunState_WORKFLOW_PENDING
}

func (x *WorkflowRunBase) GetFailMessage() string {
	if x != nil {
		return x.FailMessage
	}
	return ""
}

func (x *WorkflowRunBase) GetTotalTokens() uint32 {
	if x != nil {
		return x.TotalTokens
	}
	return 0
}

func (x *WorkflowRunBase) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *WorkflowRunBase) GetEndTime() uint64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *WorkflowRunBase) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

// 工作流运行实例
type WorkflowRunDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 运行环境。
	RunEnv EnvType `protobuf:"varint,1,opt,name=RunEnv,proto3,enum=trpc.KEP.bot_task_config_server.EnvType" json:"RunEnv,omitempty"`
	// 应用ID
	AppBizId string `protobuf:"bytes,2,opt,name=AppBizId,proto3" json:"AppBizId,omitempty" valid:"required~请传入正确的应用ID"`
	// 工作流运行实例ID
	WorkflowRunId string `protobuf:"bytes,3,opt,name=WorkflowRunId,proto3" json:"WorkflowRunId,omitempty"`
	// 所属工作流ID
	WorkflowId string `protobuf:"bytes,4,opt,name=WorkflowId,proto3" json:"WorkflowId,omitempty"`
	// 名称
	Name string `protobuf:"bytes,5,opt,name=Name,proto3" json:"Name,omitempty"`
	// 描述
	//
	//	string Desc = 6;
	//
	// 运行状态
	State WorkflowRunState `protobuf:"varint,7,opt,name=State,proto3,enum=trpc.KEP.bot_task_config_server.WorkflowRunState" json:"State,omitempty"`
	// 错误信息
	FailMessage string `protobuf:"bytes,8,opt,name=FailMessage,proto3" json:"FailMessage,omitempty"`
	// token总数
	TotalTokens uint32 `protobuf:"varint,9,opt,name=TotalTokens,proto3" json:"TotalTokens,omitempty"`
	// 开始时间（毫秒时间戳）
	StartTime uint64 `protobuf:"varint,10,opt,name=StartTime,proto3" json:"StartTime,omitempty"`
	// 结束时间（毫秒时间戳）
	EndTime uint64 `protobuf:"varint,11,opt,name=EndTime,proto3" json:"EndTime,omitempty"`
	// 创建时间（毫秒时间戳）
	CreateTime uint64 `protobuf:"varint,12,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`
	// 工作流内容
	DialogJson string `protobuf:"bytes,13,opt,name=DialogJson,proto3" json:"DialogJson,omitempty"`
	// 当前的query。
	Query string `protobuf:"bytes,14,opt,name=Query,proto3" json:"Query,omitempty"`
	// 主模型名称。
	MainModelName string `protobuf:"bytes,15,opt,name=MainModelName,proto3" json:"MainModelName,omitempty"`
	// API参数配置
	CustomVariables []*CustomVariable `protobuf:"bytes,16,rep,name=CustomVariables,proto3" json:"CustomVariables,omitempty"`
}

func (x *WorkflowRunDetail) Reset() {
	*x = WorkflowRunDetail{}
	mi := &file_workflow_run_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowRunDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowRunDetail) ProtoMessage() {}

func (x *WorkflowRunDetail) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowRunDetail.ProtoReflect.Descriptor instead.
func (*WorkflowRunDetail) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{16}
}

func (x *WorkflowRunDetail) GetRunEnv() EnvType {
	if x != nil {
		return x.RunEnv
	}
	return EnvType_TEST
}

func (x *WorkflowRunDetail) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *WorkflowRunDetail) GetWorkflowRunId() string {
	if x != nil {
		return x.WorkflowRunId
	}
	return ""
}

func (x *WorkflowRunDetail) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *WorkflowRunDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WorkflowRunDetail) GetState() WorkflowRunState {
	if x != nil {
		return x.State
	}
	return WorkflowRunState_WORKFLOW_PENDING
}

func (x *WorkflowRunDetail) GetFailMessage() string {
	if x != nil {
		return x.FailMessage
	}
	return ""
}

func (x *WorkflowRunDetail) GetTotalTokens() uint32 {
	if x != nil {
		return x.TotalTokens
	}
	return 0
}

func (x *WorkflowRunDetail) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *WorkflowRunDetail) GetEndTime() uint64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *WorkflowRunDetail) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *WorkflowRunDetail) GetDialogJson() string {
	if x != nil {
		return x.DialogJson
	}
	return ""
}

func (x *WorkflowRunDetail) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *WorkflowRunDetail) GetMainModelName() string {
	if x != nil {
		return x.MainModelName
	}
	return ""
}

func (x *WorkflowRunDetail) GetCustomVariables() []*CustomVariable {
	if x != nil {
		return x.CustomVariables
	}
	return nil
}

// 工作流节点基本信息
type NodeRunBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 节点运行ID
	NodeRunId string `protobuf:"bytes,1,opt,name=NodeRunId,proto3" json:"NodeRunId,omitempty"`
	// 节点ID
	NodeId string `protobuf:"bytes,2,opt,name=NodeId,proto3" json:"NodeId,omitempty"`
	//	// 所属节点ID，用来过滤父、子工作流的节点信息。
	//	string BelongNodeId = 3;
	//
	// 工作流运行实例ID
	WorkflowRunId string `protobuf:"bytes,4,opt,name=WorkflowRunId,proto3" json:"WorkflowRunId,omitempty"`
	// 节点名称
	NodeName string `protobuf:"bytes,5,opt,name=NodeName,proto3" json:"NodeName,omitempty"`
	// 节点类型
	NodeType NodeType `protobuf:"varint,6,opt,name=NodeType,proto3,enum=trpc.KEP.bot_task_config_wf_server.NodeType" json:"NodeType,omitempty"`
	// 任务状态
	State NodeRunState `protobuf:"varint,7,opt,name=State,proto3,enum=trpc.KEP.bot_task_config_server.NodeRunState" json:"State,omitempty"`
	// 错误信息
	FailMessage string `protobuf:"bytes,8,opt,name=FailMessage,proto3" json:"FailMessage,omitempty"`
	// 节点的总耗时。如果节点有多次调用，耗时为多次调用的总和。
	CostMilliseconds uint32 `protobuf:"varint,9,opt,name=CostMilliseconds,proto3" json:"CostMilliseconds,omitempty"`
	// token总数
	TotalTokens uint32 `protobuf:"varint,10,opt,name=TotalTokens,proto3" json:"TotalTokens,omitempty"`
	// 错误码
	FailCode string `protobuf:"bytes,11,opt,name=FailCode,proto3" json:"FailCode,omitempty"`
}

func (x *NodeRunBase) Reset() {
	*x = NodeRunBase{}
	mi := &file_workflow_run_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeRunBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeRunBase) ProtoMessage() {}

func (x *NodeRunBase) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeRunBase.ProtoReflect.Descriptor instead.
func (*NodeRunBase) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{17}
}

func (x *NodeRunBase) GetNodeRunId() string {
	if x != nil {
		return x.NodeRunId
	}
	return ""
}

func (x *NodeRunBase) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *NodeRunBase) GetWorkflowRunId() string {
	if x != nil {
		return x.WorkflowRunId
	}
	return ""
}

func (x *NodeRunBase) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *NodeRunBase) GetNodeType() NodeType {
	if x != nil {
		return x.NodeType
	}
	return NodeType_UNKNOWN
}

func (x *NodeRunBase) GetState() NodeRunState {
	if x != nil {
		return x.State
	}
	return NodeRunState_NODE_INIT
}

func (x *NodeRunBase) GetFailMessage() string {
	if x != nil {
		return x.FailMessage
	}
	return ""
}

func (x *NodeRunBase) GetCostMilliseconds() uint32 {
	if x != nil {
		return x.CostMilliseconds
	}
	return 0
}

func (x *NodeRunBase) GetTotalTokens() uint32 {
	if x != nil {
		return x.TotalTokens
	}
	return 0
}

func (x *NodeRunBase) GetFailCode() string {
	if x != nil {
		return x.FailCode
	}
	return ""
}

// 工作流节点运行详情
type NodeRunDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 节点运行ID
	NodeRunId string `protobuf:"bytes,1,opt,name=NodeRunId,proto3" json:"NodeRunId,omitempty"`
	// 节点ID
	NodeId string `protobuf:"bytes,2,opt,name=NodeId,proto3" json:"NodeId,omitempty"`
	//	// 所属节点ID，用来过滤父、子工作流的节点信息。
	//	string BelongNodeId = 3;
	//
	// 工作流运行实例ID
	WorkflowRunId string `protobuf:"bytes,4,opt,name=WorkflowRunId,proto3" json:"WorkflowRunId,omitempty"`
	// 节点名称
	NodeName string `protobuf:"bytes,5,opt,name=NodeName,proto3" json:"NodeName,omitempty"`
	// 节点类型
	NodeType NodeType `protobuf:"varint,6,opt,name=NodeType,proto3,enum=trpc.KEP.bot_task_config_wf_server.NodeType" json:"NodeType,omitempty"`
	// 任务状态
	State NodeRunState `protobuf:"varint,7,opt,name=State,proto3,enum=trpc.KEP.bot_task_config_server.NodeRunState" json:"State,omitempty"`
	// 错误信息
	FailMessage string `protobuf:"bytes,8,opt,name=FailMessage,proto3" json:"FailMessage,omitempty"`
	// 节点的总耗时。如果节点有多次调用，耗时为多次调用的总和。
	CostMilliseconds uint32 `protobuf:"varint,9,opt,name=CostMilliseconds,proto3" json:"CostMilliseconds,omitempty"`
	// token总数
	TotalTokens uint32 `protobuf:"varint,10,opt,name=TotalTokens,proto3" json:"TotalTokens,omitempty"`
	// 节点输入
	Input string `protobuf:"bytes,11,opt,name=Input,proto3" json:"Input,omitempty"`
	// 节点输出
	Output string `protobuf:"bytes,12,opt,name=Output,proto3" json:"Output,omitempty"`
	// 节点原始输出
	TaskOutput string `protobuf:"bytes,13,opt,name=TaskOutput,proto3" json:"TaskOutput,omitempty"`
	// 开始时间（毫秒时间戳）
	StartTime uint64 `protobuf:"varint,14,opt,name=StartTime,proto3" json:"StartTime,omitempty"`
	// 结束时间（毫秒时间戳）
	EndTime uint64 `protobuf:"varint,15,opt,name=EndTime,proto3" json:"EndTime,omitempty"`
	// 错误码
	FailCode string `protobuf:"bytes,16,opt,name=FailCode,proto3" json:"FailCode,omitempty"`
	// LLM统计信息。
	StatisticInfos []*StatisticInfo `protobuf:"bytes,100,rep,name=StatisticInfos,proto3" json:"StatisticInfos,omitempty"`
}

func (x *NodeRunDetail) Reset() {
	*x = NodeRunDetail{}
	mi := &file_workflow_run_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeRunDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeRunDetail) ProtoMessage() {}

func (x *NodeRunDetail) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeRunDetail.ProtoReflect.Descriptor instead.
func (*NodeRunDetail) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{18}
}

func (x *NodeRunDetail) GetNodeRunId() string {
	if x != nil {
		return x.NodeRunId
	}
	return ""
}

func (x *NodeRunDetail) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *NodeRunDetail) GetWorkflowRunId() string {
	if x != nil {
		return x.WorkflowRunId
	}
	return ""
}

func (x *NodeRunDetail) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *NodeRunDetail) GetNodeType() NodeType {
	if x != nil {
		return x.NodeType
	}
	return NodeType_UNKNOWN
}

func (x *NodeRunDetail) GetState() NodeRunState {
	if x != nil {
		return x.State
	}
	return NodeRunState_NODE_INIT
}

func (x *NodeRunDetail) GetFailMessage() string {
	if x != nil {
		return x.FailMessage
	}
	return ""
}

func (x *NodeRunDetail) GetCostMilliseconds() uint32 {
	if x != nil {
		return x.CostMilliseconds
	}
	return 0
}

func (x *NodeRunDetail) GetTotalTokens() uint32 {
	if x != nil {
		return x.TotalTokens
	}
	return 0
}

func (x *NodeRunDetail) GetInput() string {
	if x != nil {
		return x.Input
	}
	return ""
}

func (x *NodeRunDetail) GetOutput() string {
	if x != nil {
		return x.Output
	}
	return ""
}

func (x *NodeRunDetail) GetTaskOutput() string {
	if x != nil {
		return x.TaskOutput
	}
	return ""
}

func (x *NodeRunDetail) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *NodeRunDetail) GetEndTime() uint64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *NodeRunDetail) GetFailCode() string {
	if x != nil {
		return x.FailCode
	}
	return ""
}

func (x *NodeRunDetail) GetStatisticInfos() []*StatisticInfo {
	if x != nil {
		return x.StatisticInfos
	}
	return nil
}

// 统计信息
type StatisticInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 模型名称
	ModelName string `protobuf:"bytes,1,opt,name=ModelName,proto3" json:"ModelName,omitempty"`
	// 首token耗时
	FirstTokenCost uint32 `protobuf:"varint,2,opt,name=FirstTokenCost,proto3" json:"FirstTokenCost,omitempty"`
	// 推理总耗时
	TotalCost uint32 `protobuf:"varint,3,opt,name=TotalCost,proto3" json:"TotalCost,omitempty"`
	// 输入token数量
	InputTokens uint32 `protobuf:"varint,4,opt,name=InputTokens,proto3" json:"InputTokens,omitempty"`
	// 输出token数量
	OutputTokens uint32 `protobuf:"varint,5,opt,name=OutputTokens,proto3" json:"OutputTokens,omitempty"`
	// 输入+输出总token
	TotalTokens uint32 `protobuf:"varint,6,opt,name=TotalTokens,proto3" json:"TotalTokens,omitempty"`
}

func (x *StatisticInfo) Reset() {
	*x = StatisticInfo{}
	mi := &file_workflow_run_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatisticInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatisticInfo) ProtoMessage() {}

func (x *StatisticInfo) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatisticInfo.ProtoReflect.Descriptor instead.
func (*StatisticInfo) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{19}
}

func (x *StatisticInfo) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *StatisticInfo) GetFirstTokenCost() uint32 {
	if x != nil {
		return x.FirstTokenCost
	}
	return 0
}

func (x *StatisticInfo) GetTotalCost() uint32 {
	if x != nil {
		return x.TotalCost
	}
	return 0
}

func (x *StatisticInfo) GetInputTokens() uint32 {
	if x != nil {
		return x.InputTokens
	}
	return 0
}

func (x *StatisticInfo) GetOutputTokens() uint32 {
	if x != nil {
		return x.OutputTokens
	}
	return 0
}

func (x *StatisticInfo) GetTotalTokens() uint32 {
	if x != nil {
		return x.TotalTokens
	}
	return 0
}

type DescribeWorkflowRunReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 工作流运行实例ID
	WorkflowRunId string `protobuf:"bytes,1,opt,name=WorkflowRunId,proto3" json:"WorkflowRunId,omitempty"`
}

func (x *DescribeWorkflowRunReq) Reset() {
	*x = DescribeWorkflowRunReq{}
	mi := &file_workflow_run_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DescribeWorkflowRunReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeWorkflowRunReq) ProtoMessage() {}

func (x *DescribeWorkflowRunReq) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeWorkflowRunReq.ProtoReflect.Descriptor instead.
func (*DescribeWorkflowRunReq) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{20}
}

func (x *DescribeWorkflowRunReq) GetWorkflowRunId() string {
	if x != nil {
		return x.WorkflowRunId
	}
	return ""
}

type SubWorkflowFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 所属的节点的ID
	NodeId string `protobuf:"bytes,1,opt,name=NodeId,proto3" json:"NodeId,omitempty"`
	// 循环节点时非空，表示第几轮
	LoopIndex uint32 `protobuf:"varint,2,opt,name=LoopIndex,proto3" json:"LoopIndex,omitempty"`
}

func (x *SubWorkflowFilter) Reset() {
	*x = SubWorkflowFilter{}
	mi := &file_workflow_run_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubWorkflowFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubWorkflowFilter) ProtoMessage() {}

func (x *SubWorkflowFilter) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubWorkflowFilter.ProtoReflect.Descriptor instead.
func (*SubWorkflowFilter) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{21}
}

func (x *SubWorkflowFilter) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *SubWorkflowFilter) GetLoopIndex() uint32 {
	if x != nil {
		return x.LoopIndex
	}
	return 0
}

type DescribeWorkflowRunRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 运行实例信息
	WorkflowRun *WorkflowRunDetail `protobuf:"bytes,1,opt,name=WorkflowRun,proto3" json:"WorkflowRun,omitempty"`
	// 节点运行详情
	NodeRuns []*NodeRunBase `protobuf:"bytes,2,rep,name=NodeRuns,proto3" json:"NodeRuns,omitempty"`
}

func (x *DescribeWorkflowRunRsp) Reset() {
	*x = DescribeWorkflowRunRsp{}
	mi := &file_workflow_run_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DescribeWorkflowRunRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeWorkflowRunRsp) ProtoMessage() {}

func (x *DescribeWorkflowRunRsp) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeWorkflowRunRsp.ProtoReflect.Descriptor instead.
func (*DescribeWorkflowRunRsp) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{22}
}

func (x *DescribeWorkflowRunRsp) GetWorkflowRun() *WorkflowRunDetail {
	if x != nil {
		return x.WorkflowRun
	}
	return nil
}

func (x *DescribeWorkflowRunRsp) GetNodeRuns() []*NodeRunBase {
	if x != nil {
		return x.NodeRuns
	}
	return nil
}

type DescribeNodeRunReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 节点运行实例ID
	NodeRunId string `protobuf:"bytes,1,opt,name=NodeRunId,proto3" json:"NodeRunId,omitempty"`
}

func (x *DescribeNodeRunReq) Reset() {
	*x = DescribeNodeRunReq{}
	mi := &file_workflow_run_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DescribeNodeRunReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeNodeRunReq) ProtoMessage() {}

func (x *DescribeNodeRunReq) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeNodeRunReq.ProtoReflect.Descriptor instead.
func (*DescribeNodeRunReq) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{23}
}

func (x *DescribeNodeRunReq) GetNodeRunId() string {
	if x != nil {
		return x.NodeRunId
	}
	return ""
}

type DescribeNodeRunRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 节点运行实例详情
	NodeRun *NodeRunDetail `protobuf:"bytes,1,opt,name=NodeRun,proto3" json:"NodeRun,omitempty"`
}

func (x *DescribeNodeRunRsp) Reset() {
	*x = DescribeNodeRunRsp{}
	mi := &file_workflow_run_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DescribeNodeRunRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeNodeRunRsp) ProtoMessage() {}

func (x *DescribeNodeRunRsp) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeNodeRunRsp.ProtoReflect.Descriptor instead.
func (*DescribeNodeRunRsp) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{24}
}

func (x *DescribeNodeRunRsp) GetNodeRun() *NodeRunDetail {
	if x != nil {
		return x.NodeRun
	}
	return nil
}

type DescribeWorkflowReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DescribeWorkflowReq) Reset() {
	*x = DescribeWorkflowReq{}
	mi := &file_workflow_run_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DescribeWorkflowReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeWorkflowReq) ProtoMessage() {}

func (x *DescribeWorkflowReq) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeWorkflowReq.ProtoReflect.Descriptor instead.
func (*DescribeWorkflowReq) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{25}
}

type StopWorkflowRunReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 工作流运行实例ID
	WorkflowRunId string `protobuf:"bytes,1,opt,name=WorkflowRunId,proto3" json:"WorkflowRunId,omitempty"`
}

func (x *StopWorkflowRunReq) Reset() {
	*x = StopWorkflowRunReq{}
	mi := &file_workflow_run_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopWorkflowRunReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopWorkflowRunReq) ProtoMessage() {}

func (x *StopWorkflowRunReq) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopWorkflowRunReq.ProtoReflect.Descriptor instead.
func (*StopWorkflowRunReq) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{26}
}

func (x *StopWorkflowRunReq) GetWorkflowRunId() string {
	if x != nil {
		return x.WorkflowRunId
	}
	return ""
}

type StopWorkflowRunRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopWorkflowRunRsp) Reset() {
	*x = StopWorkflowRunRsp{}
	mi := &file_workflow_run_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopWorkflowRunRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopWorkflowRunRsp) ProtoMessage() {}

func (x *StopWorkflowRunRsp) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_run_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopWorkflowRunRsp.ProtoReflect.Descriptor instead.
func (*StopWorkflowRunRsp) Descriptor() ([]byte, []int) {
	return file_workflow_run_proto_rawDescGZIP(), []int{27}
}

var File_workflow_run_proto protoreflect.FileDescriptor

var file_workflow_run_proto_rawDesc = []byte{
	0x0a, 0x12, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2d, 0x72, 0x75, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x0a, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x0e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x86, 0x01, 0x0a, 0x17, 0x43, 0x61, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x4b, 0x0a,
	0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x2f, 0x92, 0xb8, 0x18, 0x2b, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x7e, 0xe8, 0xaf, 0xb7, 0xe4, 0xbc, 0xa0, 0xe5, 0x85, 0xa5, 0xe6, 0xad,
	0xa3, 0xe7, 0xa1, 0xae, 0xe7, 0x9a, 0x84, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0x49, 0x44, 0x22,
	0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x17, 0x43, 0x61,
	0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x75, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x3a, 0x0a,
	0x0e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xa5, 0x01, 0x0a, 0x14, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x46, 0x69,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x46,
	0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x46, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a,
	0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xb0, 0x01, 0x0a, 0x13, 0x53, 0x61,
	0x76, 0x65, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x4b, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x2f, 0x92, 0xb8, 0x18, 0x2b, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0xe8, 0xaf, 0xb7, 0xe4, 0xbc, 0xa0, 0xe5,
	0x85, 0xa5, 0xe6, 0xad, 0xa3, 0xe7, 0xa1, 0xae, 0xe7, 0x9a, 0x84, 0xe5, 0xba, 0x94, 0xe7, 0x94,
	0xa8, 0x49, 0x44, 0x22, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x4c,
	0x0a, 0x09, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x09, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x15, 0x0a, 0x13,
	0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x73, 0x70, 0x22, 0xce, 0x01, 0x0a, 0x1e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x44,
	0x65, 0x62, 0x75, 0x67, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x4b, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2f, 0x92, 0xb8, 0x18, 0x2b, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0xe8, 0xaf,
	0xb7, 0xe4, 0xbc, 0xa0, 0xe5, 0x85, 0xa5, 0xe6, 0xad, 0xa3, 0xe7, 0xa1, 0xae, 0xe7, 0x9a, 0x84,
	0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0x49, 0x44, 0x22, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69,
	0x7a, 0x49, 0x64, 0x12, 0x5f, 0x0a, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72,
	0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x22, 0x20, 0x0a, 0x1e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x44,
	0x65, 0x62, 0x75, 0x67, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x73, 0x52, 0x73, 0x70, 0x22, 0x68, 0x0a, 0x19, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62, 0x75, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x71, 0x12, 0x4b, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2f, 0x92, 0xb8, 0x18, 0x2b, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0xe8, 0xaf, 0xb7, 0xe4, 0xbc,
	0xa0, 0xe5, 0x85, 0xa5, 0xe6, 0xad, 0xa3, 0xe7, 0xa1, 0xae, 0xe7, 0x9a, 0x84, 0xe5, 0xba, 0x94,
	0xe7, 0x94, 0xa8, 0x49, 0x44, 0x22, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64,
	0x22, 0xca, 0x01, 0x0a, 0x19, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70,
	0x44, 0x65, 0x62, 0x75, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x12, 0x4c,
	0x0a, 0x09, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x09, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x5f, 0x0a, 0x0f,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61,
	0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x22, 0x96, 0x02,
	0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x40, 0x0a, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x12, 0x4b, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42,
	0x69, 0x7a, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2f, 0x92, 0xb8, 0x18, 0x2b,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e,
	0xe8, 0xaf, 0xb7, 0xe4, 0xbc, 0xa0, 0xe5, 0x85, 0xa5, 0xe6, 0xad, 0xa3, 0xe7, 0xa1, 0xae, 0xe7,
	0x9a, 0x84, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0x49, 0x44, 0x22, 0x52, 0x08, 0x41, 0x70, 0x70,
	0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x59, 0x0a, 0x0f, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72,
	0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72,
	0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x22, 0xdc, 0x02, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x73, 0x70, 0x12,
	0x4b, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x2f, 0x92, 0xb8, 0x18, 0x2b, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0xe8, 0xaf, 0xb7, 0xe4, 0xbc, 0xa0, 0xe5, 0x85, 0xa5,
	0xe6, 0xad, 0xa3, 0xe7, 0xa1, 0xae, 0xe7, 0x9a, 0x84, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0x49,
	0x44, 0x22, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x40, 0x0a, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x28, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x52, 0x75,
	0x6e, 0x45, 0x6e, 0x76, 0x12, 0x14, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x59, 0x0a, 0x0f, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x73, 0x22, 0x94, 0x02, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x40, 0x0a,
	0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x12,
	0x4b, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x2f, 0x92, 0xb8, 0x18, 0x2b, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0xe8, 0xaf, 0xb7, 0xe4, 0xbc, 0xa0, 0xe5, 0x85, 0xa5,
	0xe6, 0xad, 0xa3, 0xe7, 0xa1, 0xae, 0xe7, 0x9a, 0x84, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0x49,
	0x44, 0x22, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x50, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x50, 0x61, 0x67, 0x65,
	0x12, 0x5a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x42, 0x3e, 0x92, 0xb8, 0x18, 0x3a, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x28, 0x31, 0x7c,
	0x32, 0x30, 0x30, 0x29, 0x7e, 0xe6, 0xaf, 0x8f, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe9, 0x87,
	0x8f, 0xe5, 0x9c, 0xa8, 0x31, 0xe5, 0x88, 0xb0, 0x32, 0x30, 0x30, 0xe4, 0xb9, 0x8b, 0xe9, 0x97,
	0xb4, 0x22, 0x52, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xac, 0x01, 0x0a,
	0x13, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x26, 0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x42, 0x10, 0x92, 0xb8, 0x18, 0x0c, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x22, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x22, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x6d, 0x0a, 0x0c,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e,
	0x42, 0x61, 0x73, 0x65, 0x42, 0x17, 0x92, 0xb8, 0x18, 0x13, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x22,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x73, 0x22, 0x52, 0x0c, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x73, 0x22, 0xdf, 0x03, 0x0a, 0x0f,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x12,
	0x40, 0x0a, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x28, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e,
	0x76, 0x12, 0x4b, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x2f, 0x92, 0xb8, 0x18, 0x2b, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0xe8, 0xaf, 0xb7, 0xe4, 0xbc, 0xa0, 0xe5,
	0x85, 0xa5, 0xe6, 0xad, 0xa3, 0xe7, 0xa1, 0xae, 0xe7, 0x9a, 0x84, 0xe5, 0xba, 0x94, 0xe7, 0x94,
	0xa8, 0x49, 0x44, 0x22, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x24,
	0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x75, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x47, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x46, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x46, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x98, 0x05,
	0x0a, 0x11, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x40, 0x0a, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x52,
	0x75, 0x6e, 0x45, 0x6e, 0x76, 0x12, 0x4b, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2f, 0x92, 0xb8, 0x18, 0x2b, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0xe8, 0xaf, 0xb7,
	0xe4, 0xbc, 0xa0, 0xe5, 0x85, 0xa5, 0xe6, 0xad, 0xa3, 0xe7, 0xa1, 0xae, 0xe7, 0x9a, 0x84, 0xe5,
	0xba, 0x94, 0xe7, 0x94, 0xa8, 0x49, 0x44, 0x22, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a,
	0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75,
	0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x47, 0x0a, 0x05,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x46, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x46, 0x61, 0x69, 0x6c,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x4a, 0x73, 0x6f, 0x6e, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x4a, 0x73, 0x6f,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x24, 0x0a, 0x0d, 0x4d, 0x61, 0x69, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x4d, 0x61, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x59, 0x0a,
	0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x22, 0xa0, 0x03, 0x0a, 0x0b, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x75, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x4e, 0x6f, 0x64, 0x65,
	0x52, 0x75, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x24,
	0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x75, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x48, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x05, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x52, 0x75, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x46, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x46, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x2a, 0x0a, 0x10, 0x43, 0x6f, 0x73, 0x74, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x43, 0x6f, 0x73,
	0x74, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x46, 0x61, 0x69, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x46, 0x61, 0x69, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x80, 0x05, 0x0a, 0x0d,
	0x4e, 0x6f, 0x64, 0x65, 0x52, 0x75, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1c, 0x0a,
	0x09, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x4e,
	0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4e, 0x6f, 0x64,
	0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x75, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x64,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x6f, 0x64,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x43, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x46, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x46, 0x61, 0x69, 0x6c, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x43, 0x6f, 0x73, 0x74, 0x4d, 0x69,
	0x6c, 0x6c, 0x69, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x10, 0x43, 0x6f, 0x73, 0x74, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x46, 0x61,
	0x69, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x46, 0x61,
	0x69, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x56, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x64, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0xdb,
	0x01, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26,
	0x0a, 0x0e, 0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x43, 0x6f, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43,
	0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x43, 0x6f, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x22, 0x3e, 0x0a, 0x16,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x22, 0x49, 0x0a, 0x11,
	0x53, 0x75, 0x62, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x16, 0x0a, 0x06, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x4c, 0x6f, 0x6f,
	0x70, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x4c, 0x6f,
	0x6f, 0x70, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xb8, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52,
	0x73, 0x70, 0x12, 0x54, 0x0a, 0x0b, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x75, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0b, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x12, 0x48, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65,
	0x52, 0x75, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x75, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x75,
	0x6e, 0x73, 0x22, 0x32, 0x0a, 0x12, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4e, 0x6f,
	0x64, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x4e, 0x6f, 0x64, 0x65,
	0x52, 0x75, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x22, 0x5e, 0x0a, 0x12, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x07,
	0x4e, 0x6f, 0x64, 0x65, 0x52, 0x75, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x4e, 0x6f, 0x64, 0x65, 0x52, 0x75, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x07, 0x4e,
	0x6f, 0x64, 0x65, 0x52, 0x75, 0x6e, 0x22, 0x15, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x22, 0x3a, 0x0a,
	0x12, 0x53, 0x74, 0x6f, 0x70, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e,
	0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x75, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x53, 0x74, 0x6f,
	0x70, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x73, 0x70, 0x2a,
	0x1d, 0x0a, 0x07, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45,
	0x53, 0x54, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x52, 0x4f, 0x44, 0x10, 0x01, 0x2a, 0x24,
	0x0a, 0x0d, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x08, 0x0a, 0x04, 0x53, 0x59, 0x4e, 0x43, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x53, 0x59,
	0x4e, 0x43, 0x10, 0x01, 0x2a, 0x80, 0x01, 0x0a, 0x10, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x57, 0x4f, 0x52,
	0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12,
	0x14, 0x0a, 0x10, 0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x52, 0x55, 0x4e, 0x4e,
	0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x57,
	0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03,
	0x12, 0x15, 0x0a, 0x11, 0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x43, 0x41, 0x4e,
	0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x2a, 0x65, 0x0a, 0x0c, 0x4e, 0x6f, 0x64, 0x65, 0x52,
	0x75, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x44, 0x45, 0x5f,
	0x49, 0x4e, 0x49, 0x54, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x52,
	0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x44, 0x45,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x4f,
	0x44, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x4e,
	0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x42, 0x3c,
	0x5a, 0x3a, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69,
	0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x4b, 0x45, 0x50, 0x5f, 0x57, 0x46, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_workflow_run_proto_rawDescOnce sync.Once
	file_workflow_run_proto_rawDescData = file_workflow_run_proto_rawDesc
)

func file_workflow_run_proto_rawDescGZIP() []byte {
	file_workflow_run_proto_rawDescOnce.Do(func() {
		file_workflow_run_proto_rawDescData = protoimpl.X.CompressGZIP(file_workflow_run_proto_rawDescData)
	})
	return file_workflow_run_proto_rawDescData
}

var file_workflow_run_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_workflow_run_proto_msgTypes = make([]protoimpl.MessageInfo, 28)
var file_workflow_run_proto_goTypes = []any{
	(EnvType)(0),                           // 0: trpc.KEP.bot_task_config_server.EnvType
	(DebugModeType)(0),                     // 1: trpc.KEP.bot_task_config_server.DebugModeType
	(WorkflowRunState)(0),                  // 2: trpc.KEP.bot_task_config_server.WorkflowRunState
	(NodeRunState)(0),                      // 3: trpc.KEP.bot_task_config_server.NodeRunState
	(*CanCreateWorkflowRunReq)(nil),        // 4: trpc.KEP.bot_task_config_server.CanCreateWorkflowRunReq
	(*CanCreateWorkflowRunRsp)(nil),        // 5: trpc.KEP.bot_task_config_server.CanCreateWorkflowRunRsp
	(*CustomVariable)(nil),                 // 6: trpc.KEP.bot_task_config_server.CustomVariable
	(*CustomVariableConfig)(nil),           // 7: trpc.KEP.bot_task_config_server.CustomVariableConfig
	(*FileInfo)(nil),                       // 8: trpc.KEP.bot_task_config_server.FileInfo
	(*SaveAppDebugModeReq)(nil),            // 9: trpc.KEP.bot_task_config_server.SaveAppDebugModeReq
	(*SaveAppDebugModeRsp)(nil),            // 10: trpc.KEP.bot_task_config_server.SaveAppDebugModeRsp
	(*SaveAppDebugCustomVariablesReq)(nil), // 11: trpc.KEP.bot_task_config_server.SaveAppDebugCustomVariablesReq
	(*SaveAppDebugCustomVariablesRsp)(nil), // 12: trpc.KEP.bot_task_config_server.SaveAppDebugCustomVariablesRsp
	(*DescribeAppDebugConfigReq)(nil),      // 13: trpc.KEP.bot_task_config_server.DescribeAppDebugConfigReq
	(*DescribeAppDebugConfigRsp)(nil),      // 14: trpc.KEP.bot_task_config_server.DescribeAppDebugConfigRsp
	(*CreateWorkflowRunReq)(nil),           // 15: trpc.KEP.bot_task_config_server.CreateWorkflowRunReq
	(*CreateWorkflowRunRsp)(nil),           // 16: trpc.KEP.bot_task_config_server.CreateWorkflowRunRsp
	(*ListWorkflowRunsReq)(nil),            // 17: trpc.KEP.bot_task_config_server.ListWorkflowRunsReq
	(*ListWorkflowRunsRsp)(nil),            // 18: trpc.KEP.bot_task_config_server.ListWorkflowRunsRsp
	(*WorkflowRunBase)(nil),                // 19: trpc.KEP.bot_task_config_server.WorkflowRunBase
	(*WorkflowRunDetail)(nil),              // 20: trpc.KEP.bot_task_config_server.WorkflowRunDetail
	(*NodeRunBase)(nil),                    // 21: trpc.KEP.bot_task_config_server.NodeRunBase
	(*NodeRunDetail)(nil),                  // 22: trpc.KEP.bot_task_config_server.NodeRunDetail
	(*StatisticInfo)(nil),                  // 23: trpc.KEP.bot_task_config_server.StatisticInfo
	(*DescribeWorkflowRunReq)(nil),         // 24: trpc.KEP.bot_task_config_server.DescribeWorkflowRunReq
	(*SubWorkflowFilter)(nil),              // 25: trpc.KEP.bot_task_config_server.SubWorkflowFilter
	(*DescribeWorkflowRunRsp)(nil),         // 26: trpc.KEP.bot_task_config_server.DescribeWorkflowRunRsp
	(*DescribeNodeRunReq)(nil),             // 27: trpc.KEP.bot_task_config_server.DescribeNodeRunReq
	(*DescribeNodeRunRsp)(nil),             // 28: trpc.KEP.bot_task_config_server.DescribeNodeRunRsp
	(*DescribeWorkflowReq)(nil),            // 29: trpc.KEP.bot_task_config_server.DescribeWorkflowReq
	(*StopWorkflowRunReq)(nil),             // 30: trpc.KEP.bot_task_config_server.StopWorkflowRunReq
	(*StopWorkflowRunRsp)(nil),             // 31: trpc.KEP.bot_task_config_server.StopWorkflowRunRsp
	(NodeType)(0),                          // 32: trpc.KEP.bot_task_config_wf_server.NodeType
}
var file_workflow_run_proto_depIdxs = []int32{
	8,  // 0: trpc.KEP.bot_task_config_server.CustomVariableConfig.FileInfo:type_name -> trpc.KEP.bot_task_config_server.FileInfo
	1,  // 1: trpc.KEP.bot_task_config_server.SaveAppDebugModeReq.DebugMode:type_name -> trpc.KEP.bot_task_config_server.DebugModeType
	7,  // 2: trpc.KEP.bot_task_config_server.SaveAppDebugCustomVariablesReq.CustomVariables:type_name -> trpc.KEP.bot_task_config_server.CustomVariableConfig
	1,  // 3: trpc.KEP.bot_task_config_server.DescribeAppDebugConfigRsp.DebugMode:type_name -> trpc.KEP.bot_task_config_server.DebugModeType
	7,  // 4: trpc.KEP.bot_task_config_server.DescribeAppDebugConfigRsp.CustomVariables:type_name -> trpc.KEP.bot_task_config_server.CustomVariableConfig
	0,  // 5: trpc.KEP.bot_task_config_server.CreateWorkflowRunReq.RunEnv:type_name -> trpc.KEP.bot_task_config_server.EnvType
	6,  // 6: trpc.KEP.bot_task_config_server.CreateWorkflowRunReq.CustomVariables:type_name -> trpc.KEP.bot_task_config_server.CustomVariable
	0,  // 7: trpc.KEP.bot_task_config_server.CreateWorkflowRunRsp.RunEnv:type_name -> trpc.KEP.bot_task_config_server.EnvType
	6,  // 8: trpc.KEP.bot_task_config_server.CreateWorkflowRunRsp.CustomVariables:type_name -> trpc.KEP.bot_task_config_server.CustomVariable
	0,  // 9: trpc.KEP.bot_task_config_server.ListWorkflowRunsReq.RunEnv:type_name -> trpc.KEP.bot_task_config_server.EnvType
	19, // 10: trpc.KEP.bot_task_config_server.ListWorkflowRunsRsp.WorkflowRuns:type_name -> trpc.KEP.bot_task_config_server.WorkflowRunBase
	0,  // 11: trpc.KEP.bot_task_config_server.WorkflowRunBase.RunEnv:type_name -> trpc.KEP.bot_task_config_server.EnvType
	2,  // 12: trpc.KEP.bot_task_config_server.WorkflowRunBase.State:type_name -> trpc.KEP.bot_task_config_server.WorkflowRunState
	0,  // 13: trpc.KEP.bot_task_config_server.WorkflowRunDetail.RunEnv:type_name -> trpc.KEP.bot_task_config_server.EnvType
	2,  // 14: trpc.KEP.bot_task_config_server.WorkflowRunDetail.State:type_name -> trpc.KEP.bot_task_config_server.WorkflowRunState
	6,  // 15: trpc.KEP.bot_task_config_server.WorkflowRunDetail.CustomVariables:type_name -> trpc.KEP.bot_task_config_server.CustomVariable
	32, // 16: trpc.KEP.bot_task_config_server.NodeRunBase.NodeType:type_name -> trpc.KEP.bot_task_config_wf_server.NodeType
	3,  // 17: trpc.KEP.bot_task_config_server.NodeRunBase.State:type_name -> trpc.KEP.bot_task_config_server.NodeRunState
	32, // 18: trpc.KEP.bot_task_config_server.NodeRunDetail.NodeType:type_name -> trpc.KEP.bot_task_config_wf_server.NodeType
	3,  // 19: trpc.KEP.bot_task_config_server.NodeRunDetail.State:type_name -> trpc.KEP.bot_task_config_server.NodeRunState
	23, // 20: trpc.KEP.bot_task_config_server.NodeRunDetail.StatisticInfos:type_name -> trpc.KEP.bot_task_config_server.StatisticInfo
	20, // 21: trpc.KEP.bot_task_config_server.DescribeWorkflowRunRsp.WorkflowRun:type_name -> trpc.KEP.bot_task_config_server.WorkflowRunDetail
	21, // 22: trpc.KEP.bot_task_config_server.DescribeWorkflowRunRsp.NodeRuns:type_name -> trpc.KEP.bot_task_config_server.NodeRunBase
	22, // 23: trpc.KEP.bot_task_config_server.DescribeNodeRunRsp.NodeRun:type_name -> trpc.KEP.bot_task_config_server.NodeRunDetail
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_workflow_run_proto_init() }
func file_workflow_run_proto_init() {
	if File_workflow_run_proto != nil {
		return
	}
	file_workflow_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_workflow_run_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   28,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_workflow_run_proto_goTypes,
		DependencyIndexes: file_workflow_run_proto_depIdxs,
		EnumInfos:         file_workflow_run_proto_enumTypes,
		MessageInfos:      file_workflow_run_proto_msgTypes,
	}.Build()
	File_workflow_run_proto = out.File
	file_workflow_run_proto_rawDesc = nil
	file_workflow_run_proto_goTypes = nil
	file_workflow_run_proto_depIdxs = nil
}
