// Code generated by trpc-go/trpc-go-cmdline v2.7.0. DO NOT EDIT.
// source: task-config.proto

package KEP

import (
	"context"
	"errors"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/server"
	KEP_WF "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"

	_ "git.code.oa.com/devsec/protoc-gen-secv/validate"
	_ "git.code.oa.com/trpc-go/trpc"
	_ "git.code.oa.com/trpc-go/trpc-go"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
)

// START ======================================= Server Service Definition ======================================= START

// TaskConfigService defines service.
type TaskConfigService interface {
	// GetAppChatInputNum 获取应用可输入字符的数量
	//  @alias=/GetAppChatInputNum
	GetAppChatInputNum(ctx context.Context, req *GetAppChatInputNumReq) (*GetAppChatInputNumRsp, error)
	// GetAppShareURL 获取分享链接的地址
	//  @alias=/GetAppShareURL
	GetAppShareURL(ctx context.Context, req *GetAppShareURLReq) (*GetAppShareURLResp, error)
	// GetRobotIdByShareCode 通过分享码获取机器人信息请求
	//  @alias=/GetRobotIdByShareCode
	GetRobotIdByShareCode(ctx context.Context, req *GetRobotIdByShareCodeReq) (*GetRobotIdByShareCodeResp, error)
	// GetModelSupportWorkflow 获取模型支持工作流的情况
	//  @alias=/GetModelSupportWorkflow
	GetModelSupportWorkflow(ctx context.Context, req *KEP_WF.GetModelSupportWorkflowReq) (*KEP_WF.GetModelSupportWorkflowResp, error)
	// GetWorkflowReleaseStatus ======================= 发布任务 ==========================
	//  获取发布任务的状态（内部接口）
	//  @alias=/GetWorkflowReleaseStatus
	GetWorkflowReleaseStatus(ctx context.Context, req *KEP_WF.GetWorkflowReleaseStatusReq) (*KEP_WF.GetWorkflowReleaseStatusResp, error)
	// GetTaskFlowReleaseStatus 获取发布任务的状态
	//  @alias=/GetTaskFlowReleaseStatus
	GetTaskFlowReleaseStatus(ctx context.Context, req *GetTaskFlowReleaseStatusReq) (*GetTaskFlowReleaseStatusResp, error)
	// GetUnreleasedCount 获取未发布的数量
	//  @alias=/GetUnreleasedCount
	GetUnreleasedCount(ctx context.Context, req *GetUnreleasedCountReq) (*GetUnreleasedCountRsp, error)
	// SendDataSyncTaskEvent 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
	//  @alias=/SendDataSyncTaskEvent
	SendDataSyncTaskEvent(ctx context.Context, req *SendDataSyncTaskEventReq) (*SendDataSyncTaskEventRsp, error)
	// GetDataSyncTask [单个]获取同步任务, 详情, 状态等
	//  @alias=/GetDataSyncTask
	GetDataSyncTask(ctx context.Context, req *GetDataSyncTaskReq) (*GetDataSyncTaskRsp, error)
	// GetDataSyncTasks [批量]获取同步任务, 详情, 状态等
	//  @alias=/GetDataSyncTasks
	GetDataSyncTasks(ctx context.Context, req *GetDataSyncTasksReq) (*GetDataSyncTasksRsp, error)
	// CheckRobotReady 检查机器人, 是否准备好
	//  @alias=/CheckRobotReady
	CheckRobotReady(ctx context.Context, req *CheckRobotReadyReq) (*CheckRobotReadyRsp, error)
	// ReleaseAPIVarParams 通知API参数发布
	//  @alias=/ReleaseAPIVarParams
	ReleaseAPIVarParams(ctx context.Context, req *ReleaseAPIVarParamsReq) (*ReleaseAPIVarParamsRsp, error)
	// ListCategory 获取分类
	//  @alias=/ListCategory
	ListCategory(ctx context.Context, req *ListCategoryReq) (*ListCategoryRsp, error)
	// CreateCategory 创建分类
	//  @alias=/CreateCategory
	CreateCategory(ctx context.Context, req *CreateCategoryReq) (*CreateCategoryRsp, error)
	// UpdateCategory 分类修改
	//  @alias=/UpdateCategory
	UpdateCategory(ctx context.Context, req *UpdateCategoryReq) (*UpdateCategoryRsp, error)
	// DeleteCategory 分类删除
	//  @alias=/DeleteCategory
	DeleteCategory(ctx context.Context, req *DeleteCategoryReq) (*DeleteCategoryRsp, error)
	// RecoverHistoryTaskFlow ======================= 任务流 ==========================
	//  v2.1 恢复历史发布版本
	//  @alias=/RecoverHistoryTaskFlow
	RecoverHistoryTaskFlow(ctx context.Context, req *RecoverHistoryTaskFlowReq) (*RecoverHistoryTaskFlowRsp, error)
	// ListHistoryTaskFlow v2.1 获取历史版本任务流程列表
	//  @alias=/ListHistoryTaskFlow
	ListHistoryTaskFlow(ctx context.Context, req *ListHistoryTaskFlowReq) (*ListHistoryTaskFlowRsp, error)
	// DescribeHistoryTaskFlow v2.1 获取历史发布版本详情请
	//  @alias=/DescribeHistoryTaskFlow
	DescribeHistoryTaskFlow(ctx context.Context, req *DescribeHistoryTaskFlowReq) (*DescribeHistoryTaskFlowRsp, error)
	// GroupTaskFlow 分组移动任务流到不同分类
	//  @alias=/GroupTaskFlow
	GroupTaskFlow(ctx context.Context, req *GroupTaskFlowReq) (*GroupTaskFlowRsp, error)
	// ListTaskFlow 获取TaskFlow列表
	//  @alias=/ListTaskFlow
	ListTaskFlow(ctx context.Context, req *ListTaskFlowReq) (*ListTaskFlowRsp, error)
	// GetTaskFlowDetail 获取TaskFlow详情
	//  @alias=/GetTaskFlowDetail
	GetTaskFlowDetail(ctx context.Context, req *GetTaskFlowDetailReq) (*GetTaskFlowDetailRsp, error)
	// CreateTaskFlow 新建TaskFlow
	//  @alias=/CreateTaskFlow
	CreateTaskFlow(ctx context.Context, req *CreateTaskFlowReq) (*CreateTaskFlowRsp, error)
	// SaveTaskFlow 编辑/保存TaskFlow
	//  @alias=/SaveTaskFlow
	SaveTaskFlow(ctx context.Context, req *SaveTaskFlowReq) (*SaveTaskFlowRsp, error)
	// DeleteTaskFlow 删除TaskFlow
	//  @alias=/DeleteTaskFlow
	DeleteTaskFlow(ctx context.Context, req *DeleteTaskFlowReq) (*DeleteTaskFlowRsp, error)
	// ListTaskFlowPreview 获取任务流程发布列表
	//  @alias=/ListTaskFlowPreview
	ListTaskFlowPreview(ctx context.Context, req *ListTaskFlowPreviewReq) (*ListTaskFlowPreviewRsp, error)
	// ImportTaskFlow 导入TaskFlow
	//  @alias=/ImportTaskFlow
	ImportTaskFlow(ctx context.Context, req *ImportTaskFlowReq) (*ImportTaskFlowRsp, error)
	// ExportTaskFlow 导出TaskFlow
	//  @alias=/ExportTaskFlow
	ExportTaskFlow(ctx context.Context, req *ExportTaskFlowReq) (*ExportTaskFlowRsp, error)
	// InnerExportTaskFlow 内部平台导出TaskFlow
	//  @alias=/InnerExportTaskFlow
	InnerExportTaskFlow(ctx context.Context, req *InnerExportTaskFlowReq) (*InnerExportTaskFlowRsp, error)
	// PreviewTaskFlowRequestNode 预览TaskFlow的询问节点
	//  @alias=/PreviewTaskFlowRequestNode
	PreviewTaskFlowRequestNode(ctx context.Context, req *PreviewTaskFlowRequestNodeReq) (*PreviewTaskFlowRequestNodeRsp, error)
	// PreviewTaskFlowAnswerNode 预览TaskFlow的答案节点
	//  @alias=/PreviewTaskFlowAnswerNode
	PreviewTaskFlowAnswerNode(ctx context.Context, req *PreviewTaskFlowAnswerNodeReq) (*PreviewTaskFlowAnswerNodeRsp, error)
	// PreviewAnswerNodeDocument 预览答案节点的知识文档
	//  @alias=/PreviewAnswerNodeDocument
	PreviewAnswerNodeDocument(ctx context.Context, req *PreviewAnswerNodeDocumentReq) (*PreviewAnswerNodeDocumentRsp, error)
	// CopyTaskFlow 复制TaskFlow
	//  @alias=/CopyTaskFlow
	CopyTaskFlow(ctx context.Context, req *CopyTaskFlowReq) (*CopyTaskFlowRsp, error)
	// GetSlotList ======================= 槽位、实体、词条 ==========================
	//  查询槽位列表
	//  @alias=/GetSlotList
	GetSlotList(ctx context.Context, req *GetSlotListReq) (*GetSlotListRsp, error)
	// CreateSlot 新建槽位
	//  @alias=/CreateSlot
	CreateSlot(ctx context.Context, req *CreateSlotReq) (*CreateSlotRsp, error)
	// UpdateSlot 编辑槽位
	//  @alias=/UpdateSlot
	UpdateSlot(ctx context.Context, req *UpdateSlotReq) (*UpdateSlotRsp, error)
	// DeleteSlot 删除槽位
	//  @alias=/DeleteSlot
	DeleteSlot(ctx context.Context, req *DeleteSlotReq) (*DeleteSlotRsp, error)
	// GetEntryList 查询词条列表
	//  @alias=/GetEntryList
	GetEntryList(ctx context.Context, req *GetEntryListReq) (*GetEntryListRsp, error)
	// CreateEntry 新建词条
	//  @alias=/CreateEntry
	CreateEntry(ctx context.Context, req *CreateEntryReq) (*CreateEntryRsp, error)
	// UpdateEntry 编辑词条
	//  @alias=/UpdateEntry
	UpdateEntry(ctx context.Context, req *UpdateEntryReq) (*UpdateEntryRsp, error)
	// DeleteEntry 删除词条
	//  @alias=/DeleteEntry
	DeleteEntry(ctx context.Context, req *DeleteEntryReq) (*DeleteEntryRsp, error)
	// ImportEntry 导入词条
	//  @alias=/ImportEntry
	ImportEntry(ctx context.Context, req *ImportEntryReq) (*ImportEntryRsp, error)
	// RoleDefaultTemplateList 角色预设模版获取
	//  @alias=/RoleDefaultTemplateList
	RoleDefaultTemplateList(ctx context.Context, req *RoleDefaultTemplateListReq) (*RoleDefaultTemplateListRsp, error)
	// CreateExample 创建示例问法
	//  @alias=/CreateExample
	CreateExample(ctx context.Context, req *CreateExampleReq) (*CreateExampleRsp, error)
	// GetExampleList 获取示例问法列表
	//  @alias=/GetExampleList
	GetExampleList(ctx context.Context, req *GetExampleListReq) (*GetExampleListRsp, error)
	// UpdateExample 更新示例问法
	//  @alias=/UpdateExample
	UpdateExample(ctx context.Context, req *UpdateExampleReq) (*UpdateExampleRsp, error)
	// DeleteExample 删除示例问法
	//  @alias=/DeleteExample
	DeleteExample(ctx context.Context, req *DeleteExampleReq) (*DeleteExampleRsp, error)
	// CreateVar =============== 参数变量的 增删改查 ===========================
	//  创建参数变量
	//  @alias=/CreateVar
	CreateVar(ctx context.Context, req *CreateVarReq) (*CreateVarRsp, error)
	// UpdateVar 更新参数变量
	//  @alias=/UpdateVar
	UpdateVar(ctx context.Context, req *UpdateVarReq) (*UpdateVarRsp, error)
	// DeleteVar 删除参数变量
	//  @alias=/DeleteVar
	DeleteVar(ctx context.Context, req *DeleteVarReq) (*DeleteVarRsp, error)
	// DescribeVar 获取参数变量
	//  @alias=/DescribeVar
	DescribeVar(ctx context.Context, req *DescribeVarReq) (*DescribeVarRsp, error)
	// GetVarList 获取参数变量列表
	//  @alias=/GetVarList
	GetVarList(ctx context.Context, req *GetVarListReq) (*GetVarListRsp, error)
	// GetSystemVarList 获取系统变量
	//  @alias=/GetSystemVarList
	GetSystemVarList(ctx context.Context, req *GetSystemVarListReq) (*GetSystemVarListRsp, error)
	// GetPromptWordTemplateList 获取提示词模版
	//  @alias=/GetPromptWordTemplateList
	GetPromptWordTemplateList(ctx context.Context, req *GetPromptWordTemplateListReq) (*GetPromptWordTemplateListRsp, error)
	// CreateWorkflow 新建Workflow
	//  @alias=/CreateWorkflow
	CreateWorkflow(ctx context.Context, req *KEP_WF.CreateWorkflowReq) (*KEP_WF.CreateWorkflowRsp, error)
	// SaveWorkflow 编辑/保存Workflow
	//  @alias=/SaveWorkflow
	SaveWorkflow(ctx context.Context, req *KEP_WF.SaveWorkflowReq) (*KEP_WF.SaveWorkflowRsp, error)
	// CopyWorkflow 复制Workflow
	//  @alias=/CopyWorkflow
	CopyWorkflow(ctx context.Context, req *KEP_WF.CopyWorkflowReq) (*KEP_WF.CopyWorkflowRsp, error)
	// DeleteWorkflow 删除Workflow
	//  @alias=/DeleteWorkflow
	DeleteWorkflow(ctx context.Context, req *KEP_WF.DeleteWorkflowReq) (*KEP_WF.DeleteWorkflowRsp, error)
	// ListWorkflow 获取工作流列表
	//  @alias=/ListWorkflow
	ListWorkflow(ctx context.Context, req *KEP_WF.ListWorkflowReq) (*KEP_WF.ListWorkflowRsp, error)
	// ListWorkflowInner 内部服务调用该接口，目前admin会调用
	//  @alias=/ListWorkflowInner
	ListWorkflowInner(ctx context.Context, req *KEP_WF.ListWorkflowInnerReq) (*KEP_WF.ListWorkflowInnerRsp, error)
	// GetWorkflowDetail 获取某个工作流具体信息
	//  @alias=/GetWorkflowDetail
	GetWorkflowDetail(ctx context.Context, req *KEP_WF.GetWorkflowDetailReq) (*KEP_WF.GetWorkflowDetailResp, error)
	// ListNodeInfo 获取工作流的节点信息
	//  @alias=/ListNodeInfo
	ListNodeInfo(ctx context.Context, req *KEP_WF.ListNodeInfoReq) (*KEP_WF.ListNodeInfoRsp, error)
	// ListWorkflowNodeModel 获取支持工作流节点模型的信息
	//  @alias=/ListWorkflowNodeModel
	ListWorkflowNodeModel(ctx context.Context, req *KEP_WF.ListWorkflowNodeModelReq) (*KEP_WF.ListWorkflowNodeModelRsp, error)
	// ListWorkflowInfoByModelNameInner 删除前校验模型被哪些工作流引用 (内部使用，admin删除模型时使用)
	//  @alias=/ListWorkflowInfoByModelNameInner
	ListWorkflowInfoByModelNameInner(ctx context.Context, req *KEP_WF.ListWorkflowInfoByModelNameReq) (*KEP_WF.ListWorkflowInfoByModelNameRsp, error)
	// SaveAgentWorkflow ============== Agent工作流 ========================
	//  编辑/保存AgentWorkflow
	//  @alias=/SaveAgentWorkflow
	SaveAgentWorkflow(ctx context.Context, req *KEP_WF.SaveAgentWorkflowReq) (*KEP_WF.SaveAgentWorkflowRsp, error)
	// ListAgentWorkflow 获取Agent工作流列表
	//  @alias=/ListAgentWorkflow
	ListAgentWorkflow(ctx context.Context, req *KEP_WF.ListAgentWorkflowReq) (*KEP_WF.ListAgentWorkflowRsp, error)
	// GetAgentWorkflowInfo 内部服务调用该接口，目前主会话会调用
	//  @alias=/GetAgentWorkflowInfo
	GetAgentWorkflowInfo(ctx context.Context, req *KEP_WF.GetAgentWorkflowInfoReq) (*KEP_WF.GetAgentWorkflowInfoRsp, error)
	// GetAgentWorkflowDetail 获取某个Agent工作流具体信息
	//  @alias=/GetAgentWorkflowDetail
	GetAgentWorkflowDetail(ctx context.Context, req *KEP_WF.GetAgentWorkflowDetailReq) (*KEP_WF.GetAgentWorkflowDetailRsp, error)
	// GetAgentWorkflowState 获取某个Agent工作流状态
	//  @alias=/GetAgentWorkflowState
	GetAgentWorkflowState(ctx context.Context, req *KEP_WF.GetAgentWorkflowStateReq) (*KEP_WF.GetAgentWorkflowStateRsp, error)
	// ConvertToAgentWorkflow 工作流画布转换成PDL
	//  @alias=/ConvertToAgentWorkflow
	ConvertToAgentWorkflow(ctx context.Context, req *KEP_WF.ConvertToAgentWorkflowReq) (*KEP_WF.ConvertToAgentWorkflowRsp, error)
	// SwitchAgentWorkflowState 是否开启Agent工作流
	//  @alias=/SwitchAgentWorkflowState
	SwitchAgentWorkflowState(ctx context.Context, req *KEP_WF.SwitchAgentWorkflowStateReq) (*KEP_WF.SwitchAgentWorkflowStateRsp, error)
	// ListPDLVersion 获取工作流PDL版本列表
	//  @alias=/ListPDLVersion
	ListPDLVersion(ctx context.Context, req *KEP_WF.ListPDLVersionReq) (*KEP_WF.ListPDLVersionRsp, error)
	// GetPDLVersionDetail 获取工作流PDL版本详情
	//  @alias=/GetPDLVersionDetail
	GetPDLVersionDetail(ctx context.Context, req *KEP_WF.GetPDLVersionDetailReq) (*KEP_WF.GetPDLVersionDetailRsp, error)
	// CreateWorkflowExample ============== 工作流的示例问法 ========================
	//  创建示例问法
	//  @alias=/CreateWorkflowExample
	CreateWorkflowExample(ctx context.Context, req *KEP_WF.CreateWorkflowExampleReq) (*KEP_WF.CreateWorkflowExampleRsp, error)
	// ListWorkflowExample 获取示例问法列表
	//  @alias=/ListWorkflowExample
	ListWorkflowExample(ctx context.Context, req *KEP_WF.ListWorkflowExampleReq) (*KEP_WF.ListWorkflowExampleRsp, error)
	// UpdateWorkflowExample 更新示例问法
	//  @alias=/UpdateWorkflowExample
	UpdateWorkflowExample(ctx context.Context, req *KEP_WF.UpdateWorkflowExampleReq) (*KEP_WF.UpdateWorkflowExampleRsp, error)
	// DeleteWorkflowExample 删除示例问法
	//  @alias=/DeleteWorkflowExample
	DeleteWorkflowExample(ctx context.Context, req *KEP_WF.DeleteWorkflowExampleReq) (*KEP_WF.DeleteWorkflowExampleRsp, error)
	// ImportWorkflowExample 导入工作流示例问法
	//  @alias=/ImportWorkflowExample
	ImportWorkflowExample(ctx context.Context, req *KEP_WF.ImportWfExampleReq) (*KEP_WF.ImportWfExampleRsp, error)
	// SwitchWorkflowState 是否开启工作流
	//  @alias=/SwitchWorkflowState
	SwitchWorkflowState(ctx context.Context, req *KEP_WF.SwitchWorkflowStateReq) (*KEP_WF.SwitchWorkflowStateRsp, error)
	// AddWorkflowFeedback 添加工作流反馈
	//  @alias=/AddWorkflowFeedback
	AddWorkflowFeedback(ctx context.Context, req *KEP_WF.AddFlowFeedbackReq) (*KEP_WF.AddFlowFeedbackRsp, error)
	// UpdateWorkflowFeedback 修改工作流反馈
	//  @alias=/UpdateWorkflowFeedback
	UpdateWorkflowFeedback(ctx context.Context, req *KEP_WF.UpdateFlowFeedbackReq) (*KEP_WF.UpdateFlowFeedbackRsp, error)
	// DescribeWorkflowFeedback 获取工作流详情
	//  @alias=/DescribeWorkflowFeedback
	DescribeWorkflowFeedback(ctx context.Context, req *KEP_WF.DescribeWorkflowFeedReq) (*KEP_WF.DescribeWorkflowFeedRsp, error)
	// DeleteWorkflowFeedback 删除反馈
	//  @alias=/DeleteWorkflowFeedback
	DeleteWorkflowFeedback(ctx context.Context, req *KEP_WF.DeleteWorkflowFeedbackReq) (*KEP_WF.DeleteWorkflowFeedbackRsp, error)
	// ListWorkflowFeedback 查询反馈信息列表
	//  @alias=/ListWorkflowFeedback
	ListWorkflowFeedback(ctx context.Context, req *KEP_WF.ListFlowFeedbackReq) (*KEP_WF.ListFlowFeedbackRsp, error)
	// UpdateWorkflowFeedbackStatus ================= 内部(op)接口调用  start =====
	//  UpdateWorkflowFeedbackStatus 修改 workflow 反馈信息状态
	//  @alias=/UpdateWorkflowFeedbackStatus
	UpdateWorkflowFeedbackStatus(ctx context.Context, req *KEP_WF.UpdateWorkflowFeedbackStatusReq) (*KEP_WF.UpdateWorkflowFeedbackStatusRsp, error)
	// UpdateWorkflowFeedbackTapd UpdateWorkflowFeedbackTapd 修改 workflow 反馈信息关联的tapd
	//  @alias=/UpdateWorkflowFeedbackTapd
	UpdateWorkflowFeedbackTapd(ctx context.Context, req *KEP_WF.UpdateWorkflowFeedbackTapdReq) (*KEP_WF.UpdateWorkflowFeedbackTapdRsp, error) // ================= 内部(op)接口调用  end =====
	// ImportWorkflow ==================== 工作流导入导出 ================
	//  导入Workflow
	//  @alias=/ImportWorkflow
	ImportWorkflow(ctx context.Context, req *KEP_WF.ImportWorkflowReq) (*KEP_WF.ImportWorkflowRsp, error)
	// ExportWorkflow 导出Workflow
	//  @alias=/ExportWorkflow
	ExportWorkflow(ctx context.Context, req *KEP_WF.ExportWorkflowReq) (*KEP_WF.ExportWorkflowRsp, error)
	// DebugWorkflowNode ==================== 工作流节点调试 ================
	//  调试Workflow节点
	//  @alias=/DebugWorkflowNode
	DebugWorkflowNode(ctx context.Context, req *KEP_WF.DebugWorkflowNodeReq) (*KEP_WF.DebugWorkflowNodeRsp, error)
	// GetEnableCustomAsk ============== 工作流的参数提取 ========================
	//  创建参数获取是否开启了添加自定义询问话术的白名单
	//  @alias=/GetEnableCustomAsk
	GetEnableCustomAsk(ctx context.Context, req *KEP_WF.GetEnableCustomAskReq) (*KEP_WF.GetEnableCustomAskResp, error)
	// CreateParameter 创建参数
	//  @alias=/CreateParameter
	CreateParameter(ctx context.Context, req *KEP_WF.CreateParameterReq) (*KEP_WF.CreateParameterResp, error)
	// GetParameterList 获取参数列表
	//  @alias=/GetParameterList
	GetParameterList(ctx context.Context, req *KEP_WF.GetParameterListReq) (*KEP_WF.GetParameterListResp, error)
	// UpdateParameter 更新参数信息
	//  @alias=/UpdateParameter
	UpdateParameter(ctx context.Context, req *KEP_WF.UpdateParameterReq) (*KEP_WF.UpdateParameterResp, error)
	// DeleteParameter 删除参数
	//  @alias=/DeleteParameter
	DeleteParameter(ctx context.Context, req *KEP_WF.DeleteParameterReq) (*KEP_WF.DeleteParameterResp, error)
	// GetBotNodeParameterList 获取应用下工作流节点参数信息
	//  @alias=/GetBotNodeParameterList
	GetBotNodeParameterList(ctx context.Context, req *KEP_WF.GetBotNodeParameterListReq) (*KEP_WF.GetBotNodeParameterListResp, error)
	// ClearAppFlowResource 清理任务/工作流 资源
	//  @alias=/ClearAppFlowResource
	ClearAppFlowResource(ctx context.Context, req *KEP_WF.ClearAppFlowResourceReq) (*KEP_WF.ClearAppFlowResourceRsp, error)
	// DeleteAppResource 删除应用下面的 资源
	//  @alias=/DeleteAppResource
	DeleteAppResource(ctx context.Context, req *KEP_WF.DeleteAppResourceReq) (*KEP_WF.DeleteAppResourceRsp, error)
	// GetCanBeReferencedWorkflowList 获取可以被引用的工作流的列表
	//  @alias=/GetCanBeReferencedWorkflowList
	GetCanBeReferencedWorkflowList(ctx context.Context, req *KEP_WF.GetCanBeReferencedWorkflowListReq) (*KEP_WF.GetCanBeReferencedWorkflowListRsp, error)
	// GetHasBeenReferencedWorkflowList 批量获取指定工作流已经被引用的工作流的列表
	//  @alias=/GetHasBeenReferencedWorkflowList
	GetHasBeenReferencedWorkflowList(ctx context.Context, req *KEP_WF.GetHasBeenReferencedWorkflowListReq) (*KEP_WF.GetHasBeenReferencedWorkflowListRsp, error)
	// GetHasReferencedPluginToolWorkflowList 获取已经引用指定插件工具的工作流的列表
	//  @alias=/GetHasReferencedPluginToolWorkflowList
	GetHasReferencedPluginToolWorkflowList(ctx context.Context, req *KEP_WF.GetHasReferencedPluginToolWorkflowListReq) (*KEP_WF.GetHasReferencedPluginToolWorkflowListRsp, error)
	// GetParamsByWorkflowIds 批量获取指定工作流的输入输出参数
	//  @alias=/GetParamsByWorkflowIds
	GetParamsByWorkflowIds(ctx context.Context, req *KEP_WF.GetParamsByWorkflowIdsReq) (*KEP_WF.GetParamsByWorkflowIdsRsp, error)
	// GetWorkflowGuideViewed 获取新手引导是否已查看
	//  @alias=/GetWorkflowGuideViewed
	GetWorkflowGuideViewed(ctx context.Context, req *KEP_WF.GetWorkflowGuideViewedRequest) (*KEP_WF.GetWorkflowGuideViewedResponse, error)
	// MarkWorkflowGuideViewed 标记已查看工作流新手引导
	//  @alias=/MarkWorkflowGuideViewed
	MarkWorkflowGuideViewed(ctx context.Context, req *KEP_WF.MarkWorkflowGuideViewedRequest) (*KEP_WF.MarkWorkflowGuideViewedResponse, error)
	// GetExperienceWorkflowList 获取体验中心下所有工作流模版列表
	//  @alias=/GetExperienceWorkflowList
	GetExperienceWorkflowList(ctx context.Context, req *KEP_WF.GetExperienceWorkflowListReq) (*KEP_WF.GetExperienceWorkflowListRsp, error)
	// CreateExperienceWorkflow 创建体验中心指定应用下的工作流
	//  @alias=/CreateExperienceWorkflow
	CreateExperienceWorkflow(ctx context.Context, req *KEP_WF.CreateExperienceWorkflowReq) (*KEP_WF.CreateExperienceWorkflowRsp, error)
	// CanCreateWorkflowRun ======================================== 异步工作流 ========================================================
	//  判断是否能创建工作流运行实例
	//  @alias=/CanCreateWorkflowRun
	CanCreateWorkflowRun(ctx context.Context, req *KEP_WF.CanCreateWorkflowRunReq) (*KEP_WF.CanCreateWorkflowRunRsp, error)
	// SaveAppDebugMode 保存应用的调试配置的调试模式
	//  @alias=/SaveAppDebugMode
	SaveAppDebugMode(ctx context.Context, req *KEP_WF.SaveAppDebugModeReq) (*KEP_WF.SaveAppDebugModeRsp, error)
	// SaveAppDebugCustomVariables 保存应用的调试配置的AI参数的值
	//  @alias=/SaveAppDebugCustomVariables
	SaveAppDebugCustomVariables(ctx context.Context, req *KEP_WF.SaveAppDebugCustomVariablesReq) (*KEP_WF.SaveAppDebugCustomVariablesRsp, error)
	// DescribeAppDebugConfig 查看应用的调试配置
	//  @alias=/DescribeAppDebugConfig
	DescribeAppDebugConfig(ctx context.Context, req *KEP_WF.DescribeAppDebugConfigReq) (*KEP_WF.DescribeAppDebugConfigRsp, error)
	// CreateWorkflowRun 创建工作流运行实例
	//  @alias=/CreateWorkflowRun
	CreateWorkflowRun(ctx context.Context, req *KEP_WF.CreateWorkflowRunReq) (*KEP_WF.CreateWorkflowRunRsp, error)
	// ListWorkflowRuns 查询工作流运行实例列表
	//  @alias=/ListWorkflowRuns
	ListWorkflowRuns(ctx context.Context, req *KEP_WF.ListWorkflowRunsReq) (*KEP_WF.ListWorkflowRunsRsp, error)
	// DescribeWorkflowRun 查询工作流运行实例详情
	//  @alias=/DescribeWorkflowRun
	DescribeWorkflowRun(ctx context.Context, req *KEP_WF.DescribeWorkflowRunReq) (*KEP_WF.DescribeWorkflowRunRsp, error)
	// DescribeNodeRun 查看工作流节点运行详情
	//  @alias=/DescribeNodeRun
	DescribeNodeRun(ctx context.Context, req *KEP_WF.DescribeNodeRunReq) (*KEP_WF.DescribeNodeRunRsp, error)
	// StopWorkflowRun 停止工作流运行实例
	//  @alias=/StopWorkflowRun
	StopWorkflowRun(ctx context.Context, req *KEP_WF.StopWorkflowRunReq) (*KEP_WF.StopWorkflowRunRsp, error) // ======================================== 异步工作流 ========================================================
	// GetWorkflowListByDoc 批量获取文档被引用的工作流列表
	//  @alias=/GetWorkflowListByDoc
	GetWorkflowListByDoc(ctx context.Context, req *KEP_WF.GetWorkflowListByDocReq) (*KEP_WF.GetWorkflowListByDocRsp, error)
	// GetWorkflowListByAttribute 批量获取标签被引用的工作流列表
	//  @alias=/GetWorkflowListByAttribute
	GetWorkflowListByAttribute(ctx context.Context, req *KEP_WF.GetWorkflowListByAttributeReq) (*KEP_WF.GetWorkflowListByAttributeRsp, error)
	// GetWorkflowListByAttributeLabel 批量获取标签值被引用的工作流列表
	//  @alias=/GetWorkflowListByAttributeLabel
	GetWorkflowListByAttributeLabel(ctx context.Context, req *KEP_WF.GetWorkflowListByAttributeLabelReq) (*KEP_WF.GetWorkflowListByAttributeLabelRsp, error)
}

func TaskConfigService_GetAppChatInputNum_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAppChatInputNumReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetAppChatInputNum(ctx, reqbody.(*GetAppChatInputNumReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetAppShareURL_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAppShareURLReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetAppShareURL(ctx, reqbody.(*GetAppShareURLReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetRobotIdByShareCode_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetRobotIdByShareCodeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetRobotIdByShareCode(ctx, reqbody.(*GetRobotIdByShareCodeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetModelSupportWorkflow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetModelSupportWorkflowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetModelSupportWorkflow(ctx, reqbody.(*KEP_WF.GetModelSupportWorkflowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetWorkflowReleaseStatus_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetWorkflowReleaseStatusReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetWorkflowReleaseStatus(ctx, reqbody.(*KEP_WF.GetWorkflowReleaseStatusReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetTaskFlowReleaseStatus_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetTaskFlowReleaseStatusReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetTaskFlowReleaseStatus(ctx, reqbody.(*GetTaskFlowReleaseStatusReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetUnreleasedCount_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetUnreleasedCountReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetUnreleasedCount(ctx, reqbody.(*GetUnreleasedCountReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_SendDataSyncTaskEvent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SendDataSyncTaskEventReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).SendDataSyncTaskEvent(ctx, reqbody.(*SendDataSyncTaskEventReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetDataSyncTask_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetDataSyncTaskReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetDataSyncTask(ctx, reqbody.(*GetDataSyncTaskReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetDataSyncTasks_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetDataSyncTasksReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetDataSyncTasks(ctx, reqbody.(*GetDataSyncTasksReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CheckRobotReady_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckRobotReadyReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CheckRobotReady(ctx, reqbody.(*CheckRobotReadyReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ReleaseAPIVarParams_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ReleaseAPIVarParamsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ReleaseAPIVarParams(ctx, reqbody.(*ReleaseAPIVarParamsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListCategory_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListCategoryReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListCategory(ctx, reqbody.(*ListCategoryReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CreateCategory_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateCategoryReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CreateCategory(ctx, reqbody.(*CreateCategoryReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_UpdateCategory_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateCategoryReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).UpdateCategory(ctx, reqbody.(*UpdateCategoryReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DeleteCategory_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteCategoryReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DeleteCategory(ctx, reqbody.(*DeleteCategoryReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_RecoverHistoryTaskFlow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RecoverHistoryTaskFlowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).RecoverHistoryTaskFlow(ctx, reqbody.(*RecoverHistoryTaskFlowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListHistoryTaskFlow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListHistoryTaskFlowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListHistoryTaskFlow(ctx, reqbody.(*ListHistoryTaskFlowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DescribeHistoryTaskFlow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeHistoryTaskFlowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DescribeHistoryTaskFlow(ctx, reqbody.(*DescribeHistoryTaskFlowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GroupTaskFlow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GroupTaskFlowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GroupTaskFlow(ctx, reqbody.(*GroupTaskFlowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListTaskFlow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListTaskFlowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListTaskFlow(ctx, reqbody.(*ListTaskFlowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetTaskFlowDetail_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetTaskFlowDetailReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetTaskFlowDetail(ctx, reqbody.(*GetTaskFlowDetailReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CreateTaskFlow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateTaskFlowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CreateTaskFlow(ctx, reqbody.(*CreateTaskFlowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_SaveTaskFlow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SaveTaskFlowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).SaveTaskFlow(ctx, reqbody.(*SaveTaskFlowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DeleteTaskFlow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteTaskFlowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DeleteTaskFlow(ctx, reqbody.(*DeleteTaskFlowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListTaskFlowPreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListTaskFlowPreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListTaskFlowPreview(ctx, reqbody.(*ListTaskFlowPreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ImportTaskFlow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ImportTaskFlowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ImportTaskFlow(ctx, reqbody.(*ImportTaskFlowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ExportTaskFlow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ExportTaskFlowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ExportTaskFlow(ctx, reqbody.(*ExportTaskFlowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_InnerExportTaskFlow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &InnerExportTaskFlowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).InnerExportTaskFlow(ctx, reqbody.(*InnerExportTaskFlowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_PreviewTaskFlowRequestNode_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &PreviewTaskFlowRequestNodeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).PreviewTaskFlowRequestNode(ctx, reqbody.(*PreviewTaskFlowRequestNodeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_PreviewTaskFlowAnswerNode_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &PreviewTaskFlowAnswerNodeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).PreviewTaskFlowAnswerNode(ctx, reqbody.(*PreviewTaskFlowAnswerNodeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_PreviewAnswerNodeDocument_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &PreviewAnswerNodeDocumentReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).PreviewAnswerNodeDocument(ctx, reqbody.(*PreviewAnswerNodeDocumentReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CopyTaskFlow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CopyTaskFlowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CopyTaskFlow(ctx, reqbody.(*CopyTaskFlowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetSlotList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetSlotListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetSlotList(ctx, reqbody.(*GetSlotListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CreateSlot_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateSlotReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CreateSlot(ctx, reqbody.(*CreateSlotReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_UpdateSlot_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateSlotReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).UpdateSlot(ctx, reqbody.(*UpdateSlotReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DeleteSlot_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteSlotReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DeleteSlot(ctx, reqbody.(*DeleteSlotReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetEntryList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetEntryListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetEntryList(ctx, reqbody.(*GetEntryListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CreateEntry_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateEntryReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CreateEntry(ctx, reqbody.(*CreateEntryReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_UpdateEntry_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateEntryReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).UpdateEntry(ctx, reqbody.(*UpdateEntryReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DeleteEntry_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteEntryReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DeleteEntry(ctx, reqbody.(*DeleteEntryReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ImportEntry_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ImportEntryReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ImportEntry(ctx, reqbody.(*ImportEntryReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_RoleDefaultTemplateList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RoleDefaultTemplateListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).RoleDefaultTemplateList(ctx, reqbody.(*RoleDefaultTemplateListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CreateExample_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateExampleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CreateExample(ctx, reqbody.(*CreateExampleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetExampleList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetExampleListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetExampleList(ctx, reqbody.(*GetExampleListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_UpdateExample_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateExampleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).UpdateExample(ctx, reqbody.(*UpdateExampleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DeleteExample_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteExampleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DeleteExample(ctx, reqbody.(*DeleteExampleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CreateVar_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateVarReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CreateVar(ctx, reqbody.(*CreateVarReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_UpdateVar_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpdateVarReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).UpdateVar(ctx, reqbody.(*UpdateVarReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DeleteVar_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteVarReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DeleteVar(ctx, reqbody.(*DeleteVarReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DescribeVar_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeVarReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DescribeVar(ctx, reqbody.(*DescribeVarReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetVarList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetVarListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetVarList(ctx, reqbody.(*GetVarListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetSystemVarList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetSystemVarListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetSystemVarList(ctx, reqbody.(*GetSystemVarListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetPromptWordTemplateList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetPromptWordTemplateListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetPromptWordTemplateList(ctx, reqbody.(*GetPromptWordTemplateListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CreateWorkflow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.CreateWorkflowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CreateWorkflow(ctx, reqbody.(*KEP_WF.CreateWorkflowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_SaveWorkflow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.SaveWorkflowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).SaveWorkflow(ctx, reqbody.(*KEP_WF.SaveWorkflowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CopyWorkflow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.CopyWorkflowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CopyWorkflow(ctx, reqbody.(*KEP_WF.CopyWorkflowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DeleteWorkflow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.DeleteWorkflowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DeleteWorkflow(ctx, reqbody.(*KEP_WF.DeleteWorkflowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListWorkflow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ListWorkflowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListWorkflow(ctx, reqbody.(*KEP_WF.ListWorkflowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListWorkflowInner_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ListWorkflowInnerReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListWorkflowInner(ctx, reqbody.(*KEP_WF.ListWorkflowInnerReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetWorkflowDetail_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetWorkflowDetailReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetWorkflowDetail(ctx, reqbody.(*KEP_WF.GetWorkflowDetailReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListNodeInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ListNodeInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListNodeInfo(ctx, reqbody.(*KEP_WF.ListNodeInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListWorkflowNodeModel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ListWorkflowNodeModelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListWorkflowNodeModel(ctx, reqbody.(*KEP_WF.ListWorkflowNodeModelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListWorkflowInfoByModelNameInner_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ListWorkflowInfoByModelNameReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListWorkflowInfoByModelNameInner(ctx, reqbody.(*KEP_WF.ListWorkflowInfoByModelNameReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_SaveAgentWorkflow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.SaveAgentWorkflowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).SaveAgentWorkflow(ctx, reqbody.(*KEP_WF.SaveAgentWorkflowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListAgentWorkflow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ListAgentWorkflowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListAgentWorkflow(ctx, reqbody.(*KEP_WF.ListAgentWorkflowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetAgentWorkflowInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetAgentWorkflowInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetAgentWorkflowInfo(ctx, reqbody.(*KEP_WF.GetAgentWorkflowInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetAgentWorkflowDetail_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetAgentWorkflowDetailReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetAgentWorkflowDetail(ctx, reqbody.(*KEP_WF.GetAgentWorkflowDetailReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetAgentWorkflowState_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetAgentWorkflowStateReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetAgentWorkflowState(ctx, reqbody.(*KEP_WF.GetAgentWorkflowStateReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ConvertToAgentWorkflow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ConvertToAgentWorkflowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ConvertToAgentWorkflow(ctx, reqbody.(*KEP_WF.ConvertToAgentWorkflowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_SwitchAgentWorkflowState_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.SwitchAgentWorkflowStateReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).SwitchAgentWorkflowState(ctx, reqbody.(*KEP_WF.SwitchAgentWorkflowStateReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListPDLVersion_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ListPDLVersionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListPDLVersion(ctx, reqbody.(*KEP_WF.ListPDLVersionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetPDLVersionDetail_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetPDLVersionDetailReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetPDLVersionDetail(ctx, reqbody.(*KEP_WF.GetPDLVersionDetailReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CreateWorkflowExample_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.CreateWorkflowExampleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CreateWorkflowExample(ctx, reqbody.(*KEP_WF.CreateWorkflowExampleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListWorkflowExample_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ListWorkflowExampleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListWorkflowExample(ctx, reqbody.(*KEP_WF.ListWorkflowExampleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_UpdateWorkflowExample_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.UpdateWorkflowExampleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).UpdateWorkflowExample(ctx, reqbody.(*KEP_WF.UpdateWorkflowExampleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DeleteWorkflowExample_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.DeleteWorkflowExampleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DeleteWorkflowExample(ctx, reqbody.(*KEP_WF.DeleteWorkflowExampleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ImportWorkflowExample_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ImportWfExampleReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ImportWorkflowExample(ctx, reqbody.(*KEP_WF.ImportWfExampleReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_SwitchWorkflowState_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.SwitchWorkflowStateReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).SwitchWorkflowState(ctx, reqbody.(*KEP_WF.SwitchWorkflowStateReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_AddWorkflowFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.AddFlowFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).AddWorkflowFeedback(ctx, reqbody.(*KEP_WF.AddFlowFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_UpdateWorkflowFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.UpdateFlowFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).UpdateWorkflowFeedback(ctx, reqbody.(*KEP_WF.UpdateFlowFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DescribeWorkflowFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.DescribeWorkflowFeedReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DescribeWorkflowFeedback(ctx, reqbody.(*KEP_WF.DescribeWorkflowFeedReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DeleteWorkflowFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.DeleteWorkflowFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DeleteWorkflowFeedback(ctx, reqbody.(*KEP_WF.DeleteWorkflowFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListWorkflowFeedback_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ListFlowFeedbackReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListWorkflowFeedback(ctx, reqbody.(*KEP_WF.ListFlowFeedbackReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_UpdateWorkflowFeedbackStatus_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.UpdateWorkflowFeedbackStatusReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).UpdateWorkflowFeedbackStatus(ctx, reqbody.(*KEP_WF.UpdateWorkflowFeedbackStatusReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_UpdateWorkflowFeedbackTapd_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.UpdateWorkflowFeedbackTapdReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).UpdateWorkflowFeedbackTapd(ctx, reqbody.(*KEP_WF.UpdateWorkflowFeedbackTapdReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ImportWorkflow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ImportWorkflowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ImportWorkflow(ctx, reqbody.(*KEP_WF.ImportWorkflowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ExportWorkflow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ExportWorkflowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ExportWorkflow(ctx, reqbody.(*KEP_WF.ExportWorkflowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DebugWorkflowNode_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.DebugWorkflowNodeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DebugWorkflowNode(ctx, reqbody.(*KEP_WF.DebugWorkflowNodeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetEnableCustomAsk_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetEnableCustomAskReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetEnableCustomAsk(ctx, reqbody.(*KEP_WF.GetEnableCustomAskReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CreateParameter_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.CreateParameterReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CreateParameter(ctx, reqbody.(*KEP_WF.CreateParameterReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetParameterList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetParameterListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetParameterList(ctx, reqbody.(*KEP_WF.GetParameterListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_UpdateParameter_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.UpdateParameterReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).UpdateParameter(ctx, reqbody.(*KEP_WF.UpdateParameterReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DeleteParameter_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.DeleteParameterReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DeleteParameter(ctx, reqbody.(*KEP_WF.DeleteParameterReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetBotNodeParameterList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetBotNodeParameterListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetBotNodeParameterList(ctx, reqbody.(*KEP_WF.GetBotNodeParameterListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ClearAppFlowResource_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ClearAppFlowResourceReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ClearAppFlowResource(ctx, reqbody.(*KEP_WF.ClearAppFlowResourceReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DeleteAppResource_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.DeleteAppResourceReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DeleteAppResource(ctx, reqbody.(*KEP_WF.DeleteAppResourceReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetCanBeReferencedWorkflowList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetCanBeReferencedWorkflowListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetCanBeReferencedWorkflowList(ctx, reqbody.(*KEP_WF.GetCanBeReferencedWorkflowListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetHasBeenReferencedWorkflowList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetHasBeenReferencedWorkflowListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetHasBeenReferencedWorkflowList(ctx, reqbody.(*KEP_WF.GetHasBeenReferencedWorkflowListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetHasReferencedPluginToolWorkflowList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetHasReferencedPluginToolWorkflowListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetHasReferencedPluginToolWorkflowList(ctx, reqbody.(*KEP_WF.GetHasReferencedPluginToolWorkflowListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetParamsByWorkflowIds_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetParamsByWorkflowIdsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetParamsByWorkflowIds(ctx, reqbody.(*KEP_WF.GetParamsByWorkflowIdsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetWorkflowGuideViewed_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetWorkflowGuideViewedRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetWorkflowGuideViewed(ctx, reqbody.(*KEP_WF.GetWorkflowGuideViewedRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_MarkWorkflowGuideViewed_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.MarkWorkflowGuideViewedRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).MarkWorkflowGuideViewed(ctx, reqbody.(*KEP_WF.MarkWorkflowGuideViewedRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetExperienceWorkflowList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetExperienceWorkflowListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetExperienceWorkflowList(ctx, reqbody.(*KEP_WF.GetExperienceWorkflowListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CreateExperienceWorkflow_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.CreateExperienceWorkflowReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CreateExperienceWorkflow(ctx, reqbody.(*KEP_WF.CreateExperienceWorkflowReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CanCreateWorkflowRun_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.CanCreateWorkflowRunReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CanCreateWorkflowRun(ctx, reqbody.(*KEP_WF.CanCreateWorkflowRunReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_SaveAppDebugMode_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.SaveAppDebugModeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).SaveAppDebugMode(ctx, reqbody.(*KEP_WF.SaveAppDebugModeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_SaveAppDebugCustomVariables_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.SaveAppDebugCustomVariablesReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).SaveAppDebugCustomVariables(ctx, reqbody.(*KEP_WF.SaveAppDebugCustomVariablesReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DescribeAppDebugConfig_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.DescribeAppDebugConfigReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DescribeAppDebugConfig(ctx, reqbody.(*KEP_WF.DescribeAppDebugConfigReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_CreateWorkflowRun_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.CreateWorkflowRunReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).CreateWorkflowRun(ctx, reqbody.(*KEP_WF.CreateWorkflowRunReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_ListWorkflowRuns_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.ListWorkflowRunsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).ListWorkflowRuns(ctx, reqbody.(*KEP_WF.ListWorkflowRunsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DescribeWorkflowRun_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.DescribeWorkflowRunReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DescribeWorkflowRun(ctx, reqbody.(*KEP_WF.DescribeWorkflowRunReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_DescribeNodeRun_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.DescribeNodeRunReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).DescribeNodeRun(ctx, reqbody.(*KEP_WF.DescribeNodeRunReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_StopWorkflowRun_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.StopWorkflowRunReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).StopWorkflowRun(ctx, reqbody.(*KEP_WF.StopWorkflowRunReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetWorkflowListByDoc_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetWorkflowListByDocReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetWorkflowListByDoc(ctx, reqbody.(*KEP_WF.GetWorkflowListByDocReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetWorkflowListByAttribute_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetWorkflowListByAttributeReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetWorkflowListByAttribute(ctx, reqbody.(*KEP_WF.GetWorkflowListByAttributeReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func TaskConfigService_GetWorkflowListByAttributeLabel_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP_WF.GetWorkflowListByAttributeLabelReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(TaskConfigService).GetWorkflowListByAttributeLabel(ctx, reqbody.(*KEP_WF.GetWorkflowListByAttributeLabelReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// TaskConfigServer_ServiceDesc descriptor for server.RegisterService.
var TaskConfigServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.bot_task_config_server.TaskConfig",
	HandlerType: ((*TaskConfigService)(nil)),
	Methods: []server.Method{
		{
			Name: "/GetAppChatInputNum",
			Func: TaskConfigService_GetAppChatInputNum_Handler,
		},
		{
			Name: "/GetAppShareURL",
			Func: TaskConfigService_GetAppShareURL_Handler,
		},
		{
			Name: "/GetRobotIdByShareCode",
			Func: TaskConfigService_GetRobotIdByShareCode_Handler,
		},
		{
			Name: "/GetModelSupportWorkflow",
			Func: TaskConfigService_GetModelSupportWorkflow_Handler,
		},
		{
			Name: "/GetWorkflowReleaseStatus",
			Func: TaskConfigService_GetWorkflowReleaseStatus_Handler,
		},
		{
			Name: "/GetTaskFlowReleaseStatus",
			Func: TaskConfigService_GetTaskFlowReleaseStatus_Handler,
		},
		{
			Name: "/GetUnreleasedCount",
			Func: TaskConfigService_GetUnreleasedCount_Handler,
		},
		{
			Name: "/SendDataSyncTaskEvent",
			Func: TaskConfigService_SendDataSyncTaskEvent_Handler,
		},
		{
			Name: "/GetDataSyncTask",
			Func: TaskConfigService_GetDataSyncTask_Handler,
		},
		{
			Name: "/GetDataSyncTasks",
			Func: TaskConfigService_GetDataSyncTasks_Handler,
		},
		{
			Name: "/CheckRobotReady",
			Func: TaskConfigService_CheckRobotReady_Handler,
		},
		{
			Name: "/ReleaseAPIVarParams",
			Func: TaskConfigService_ReleaseAPIVarParams_Handler,
		},
		{
			Name: "/ListCategory",
			Func: TaskConfigService_ListCategory_Handler,
		},
		{
			Name: "/CreateCategory",
			Func: TaskConfigService_CreateCategory_Handler,
		},
		{
			Name: "/UpdateCategory",
			Func: TaskConfigService_UpdateCategory_Handler,
		},
		{
			Name: "/DeleteCategory",
			Func: TaskConfigService_DeleteCategory_Handler,
		},
		{
			Name: "/RecoverHistoryTaskFlow",
			Func: TaskConfigService_RecoverHistoryTaskFlow_Handler,
		},
		{
			Name: "/ListHistoryTaskFlow",
			Func: TaskConfigService_ListHistoryTaskFlow_Handler,
		},
		{
			Name: "/DescribeHistoryTaskFlow",
			Func: TaskConfigService_DescribeHistoryTaskFlow_Handler,
		},
		{
			Name: "/GroupTaskFlow",
			Func: TaskConfigService_GroupTaskFlow_Handler,
		},
		{
			Name: "/ListTaskFlow",
			Func: TaskConfigService_ListTaskFlow_Handler,
		},
		{
			Name: "/GetTaskFlowDetail",
			Func: TaskConfigService_GetTaskFlowDetail_Handler,
		},
		{
			Name: "/CreateTaskFlow",
			Func: TaskConfigService_CreateTaskFlow_Handler,
		},
		{
			Name: "/SaveTaskFlow",
			Func: TaskConfigService_SaveTaskFlow_Handler,
		},
		{
			Name: "/DeleteTaskFlow",
			Func: TaskConfigService_DeleteTaskFlow_Handler,
		},
		{
			Name: "/ListTaskFlowPreview",
			Func: TaskConfigService_ListTaskFlowPreview_Handler,
		},
		{
			Name: "/ImportTaskFlow",
			Func: TaskConfigService_ImportTaskFlow_Handler,
		},
		{
			Name: "/ExportTaskFlow",
			Func: TaskConfigService_ExportTaskFlow_Handler,
		},
		{
			Name: "/InnerExportTaskFlow",
			Func: TaskConfigService_InnerExportTaskFlow_Handler,
		},
		{
			Name: "/PreviewTaskFlowRequestNode",
			Func: TaskConfigService_PreviewTaskFlowRequestNode_Handler,
		},
		{
			Name: "/PreviewTaskFlowAnswerNode",
			Func: TaskConfigService_PreviewTaskFlowAnswerNode_Handler,
		},
		{
			Name: "/PreviewAnswerNodeDocument",
			Func: TaskConfigService_PreviewAnswerNodeDocument_Handler,
		},
		{
			Name: "/CopyTaskFlow",
			Func: TaskConfigService_CopyTaskFlow_Handler,
		},
		{
			Name: "/GetSlotList",
			Func: TaskConfigService_GetSlotList_Handler,
		},
		{
			Name: "/CreateSlot",
			Func: TaskConfigService_CreateSlot_Handler,
		},
		{
			Name: "/UpdateSlot",
			Func: TaskConfigService_UpdateSlot_Handler,
		},
		{
			Name: "/DeleteSlot",
			Func: TaskConfigService_DeleteSlot_Handler,
		},
		{
			Name: "/GetEntryList",
			Func: TaskConfigService_GetEntryList_Handler,
		},
		{
			Name: "/CreateEntry",
			Func: TaskConfigService_CreateEntry_Handler,
		},
		{
			Name: "/UpdateEntry",
			Func: TaskConfigService_UpdateEntry_Handler,
		},
		{
			Name: "/DeleteEntry",
			Func: TaskConfigService_DeleteEntry_Handler,
		},
		{
			Name: "/ImportEntry",
			Func: TaskConfigService_ImportEntry_Handler,
		},
		{
			Name: "/RoleDefaultTemplateList",
			Func: TaskConfigService_RoleDefaultTemplateList_Handler,
		},
		{
			Name: "/CreateExample",
			Func: TaskConfigService_CreateExample_Handler,
		},
		{
			Name: "/GetExampleList",
			Func: TaskConfigService_GetExampleList_Handler,
		},
		{
			Name: "/UpdateExample",
			Func: TaskConfigService_UpdateExample_Handler,
		},
		{
			Name: "/DeleteExample",
			Func: TaskConfigService_DeleteExample_Handler,
		},
		{
			Name: "/CreateVar",
			Func: TaskConfigService_CreateVar_Handler,
		},
		{
			Name: "/UpdateVar",
			Func: TaskConfigService_UpdateVar_Handler,
		},
		{
			Name: "/DeleteVar",
			Func: TaskConfigService_DeleteVar_Handler,
		},
		{
			Name: "/DescribeVar",
			Func: TaskConfigService_DescribeVar_Handler,
		},
		{
			Name: "/GetVarList",
			Func: TaskConfigService_GetVarList_Handler,
		},
		{
			Name: "/GetSystemVarList",
			Func: TaskConfigService_GetSystemVarList_Handler,
		},
		{
			Name: "/GetPromptWordTemplateList",
			Func: TaskConfigService_GetPromptWordTemplateList_Handler,
		},
		{
			Name: "/CreateWorkflow",
			Func: TaskConfigService_CreateWorkflow_Handler,
		},
		{
			Name: "/SaveWorkflow",
			Func: TaskConfigService_SaveWorkflow_Handler,
		},
		{
			Name: "/CopyWorkflow",
			Func: TaskConfigService_CopyWorkflow_Handler,
		},
		{
			Name: "/DeleteWorkflow",
			Func: TaskConfigService_DeleteWorkflow_Handler,
		},
		{
			Name: "/ListWorkflow",
			Func: TaskConfigService_ListWorkflow_Handler,
		},
		{
			Name: "/ListWorkflowInner",
			Func: TaskConfigService_ListWorkflowInner_Handler,
		},
		{
			Name: "/GetWorkflowDetail",
			Func: TaskConfigService_GetWorkflowDetail_Handler,
		},
		{
			Name: "/ListNodeInfo",
			Func: TaskConfigService_ListNodeInfo_Handler,
		},
		{
			Name: "/ListWorkflowNodeModel",
			Func: TaskConfigService_ListWorkflowNodeModel_Handler,
		},
		{
			Name: "/ListWorkflowInfoByModelNameInner",
			Func: TaskConfigService_ListWorkflowInfoByModelNameInner_Handler,
		},
		{
			Name: "/SaveAgentWorkflow",
			Func: TaskConfigService_SaveAgentWorkflow_Handler,
		},
		{
			Name: "/ListAgentWorkflow",
			Func: TaskConfigService_ListAgentWorkflow_Handler,
		},
		{
			Name: "/GetAgentWorkflowInfo",
			Func: TaskConfigService_GetAgentWorkflowInfo_Handler,
		},
		{
			Name: "/GetAgentWorkflowDetail",
			Func: TaskConfigService_GetAgentWorkflowDetail_Handler,
		},
		{
			Name: "/GetAgentWorkflowState",
			Func: TaskConfigService_GetAgentWorkflowState_Handler,
		},
		{
			Name: "/ConvertToAgentWorkflow",
			Func: TaskConfigService_ConvertToAgentWorkflow_Handler,
		},
		{
			Name: "/SwitchAgentWorkflowState",
			Func: TaskConfigService_SwitchAgentWorkflowState_Handler,
		},
		{
			Name: "/ListPDLVersion",
			Func: TaskConfigService_ListPDLVersion_Handler,
		},
		{
			Name: "/GetPDLVersionDetail",
			Func: TaskConfigService_GetPDLVersionDetail_Handler,
		},
		{
			Name: "/CreateWorkflowExample",
			Func: TaskConfigService_CreateWorkflowExample_Handler,
		},
		{
			Name: "/ListWorkflowExample",
			Func: TaskConfigService_ListWorkflowExample_Handler,
		},
		{
			Name: "/UpdateWorkflowExample",
			Func: TaskConfigService_UpdateWorkflowExample_Handler,
		},
		{
			Name: "/DeleteWorkflowExample",
			Func: TaskConfigService_DeleteWorkflowExample_Handler,
		},
		{
			Name: "/ImportWorkflowExample",
			Func: TaskConfigService_ImportWorkflowExample_Handler,
		},
		{
			Name: "/SwitchWorkflowState",
			Func: TaskConfigService_SwitchWorkflowState_Handler,
		},
		{
			Name: "/AddWorkflowFeedback",
			Func: TaskConfigService_AddWorkflowFeedback_Handler,
		},
		{
			Name: "/UpdateWorkflowFeedback",
			Func: TaskConfigService_UpdateWorkflowFeedback_Handler,
		},
		{
			Name: "/DescribeWorkflowFeedback",
			Func: TaskConfigService_DescribeWorkflowFeedback_Handler,
		},
		{
			Name: "/DeleteWorkflowFeedback",
			Func: TaskConfigService_DeleteWorkflowFeedback_Handler,
		},
		{
			Name: "/ListWorkflowFeedback",
			Func: TaskConfigService_ListWorkflowFeedback_Handler,
		},
		{
			Name: "/UpdateWorkflowFeedbackStatus",
			Func: TaskConfigService_UpdateWorkflowFeedbackStatus_Handler,
		},
		{
			Name: "/UpdateWorkflowFeedbackTapd",
			Func: TaskConfigService_UpdateWorkflowFeedbackTapd_Handler,
		},
		{
			Name: "/ImportWorkflow",
			Func: TaskConfigService_ImportWorkflow_Handler,
		},
		{
			Name: "/ExportWorkflow",
			Func: TaskConfigService_ExportWorkflow_Handler,
		},
		{
			Name: "/DebugWorkflowNode",
			Func: TaskConfigService_DebugWorkflowNode_Handler,
		},
		{
			Name: "/GetEnableCustomAsk",
			Func: TaskConfigService_GetEnableCustomAsk_Handler,
		},
		{
			Name: "/CreateParameter",
			Func: TaskConfigService_CreateParameter_Handler,
		},
		{
			Name: "/GetParameterList",
			Func: TaskConfigService_GetParameterList_Handler,
		},
		{
			Name: "/UpdateParameter",
			Func: TaskConfigService_UpdateParameter_Handler,
		},
		{
			Name: "/DeleteParameter",
			Func: TaskConfigService_DeleteParameter_Handler,
		},
		{
			Name: "/GetBotNodeParameterList",
			Func: TaskConfigService_GetBotNodeParameterList_Handler,
		},
		{
			Name: "/ClearAppFlowResource",
			Func: TaskConfigService_ClearAppFlowResource_Handler,
		},
		{
			Name: "/DeleteAppResource",
			Func: TaskConfigService_DeleteAppResource_Handler,
		},
		{
			Name: "/GetCanBeReferencedWorkflowList",
			Func: TaskConfigService_GetCanBeReferencedWorkflowList_Handler,
		},
		{
			Name: "/GetHasBeenReferencedWorkflowList",
			Func: TaskConfigService_GetHasBeenReferencedWorkflowList_Handler,
		},
		{
			Name: "/GetHasReferencedPluginToolWorkflowList",
			Func: TaskConfigService_GetHasReferencedPluginToolWorkflowList_Handler,
		},
		{
			Name: "/GetParamsByWorkflowIds",
			Func: TaskConfigService_GetParamsByWorkflowIds_Handler,
		},
		{
			Name: "/GetWorkflowGuideViewed",
			Func: TaskConfigService_GetWorkflowGuideViewed_Handler,
		},
		{
			Name: "/MarkWorkflowGuideViewed",
			Func: TaskConfigService_MarkWorkflowGuideViewed_Handler,
		},
		{
			Name: "/GetExperienceWorkflowList",
			Func: TaskConfigService_GetExperienceWorkflowList_Handler,
		},
		{
			Name: "/CreateExperienceWorkflow",
			Func: TaskConfigService_CreateExperienceWorkflow_Handler,
		},
		{
			Name: "/CanCreateWorkflowRun",
			Func: TaskConfigService_CanCreateWorkflowRun_Handler,
		},
		{
			Name: "/SaveAppDebugMode",
			Func: TaskConfigService_SaveAppDebugMode_Handler,
		},
		{
			Name: "/SaveAppDebugCustomVariables",
			Func: TaskConfigService_SaveAppDebugCustomVariables_Handler,
		},
		{
			Name: "/DescribeAppDebugConfig",
			Func: TaskConfigService_DescribeAppDebugConfig_Handler,
		},
		{
			Name: "/CreateWorkflowRun",
			Func: TaskConfigService_CreateWorkflowRun_Handler,
		},
		{
			Name: "/ListWorkflowRuns",
			Func: TaskConfigService_ListWorkflowRuns_Handler,
		},
		{
			Name: "/DescribeWorkflowRun",
			Func: TaskConfigService_DescribeWorkflowRun_Handler,
		},
		{
			Name: "/DescribeNodeRun",
			Func: TaskConfigService_DescribeNodeRun_Handler,
		},
		{
			Name: "/StopWorkflowRun",
			Func: TaskConfigService_StopWorkflowRun_Handler,
		},
		{
			Name: "/GetWorkflowListByDoc",
			Func: TaskConfigService_GetWorkflowListByDoc_Handler,
		},
		{
			Name: "/GetWorkflowListByAttribute",
			Func: TaskConfigService_GetWorkflowListByAttribute_Handler,
		},
		{
			Name: "/GetWorkflowListByAttributeLabel",
			Func: TaskConfigService_GetWorkflowListByAttributeLabel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetAppChatInputNum",
			Func: TaskConfigService_GetAppChatInputNum_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetAppShareURL",
			Func: TaskConfigService_GetAppShareURL_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetRobotIdByShareCode",
			Func: TaskConfigService_GetRobotIdByShareCode_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetModelSupportWorkflow",
			Func: TaskConfigService_GetModelSupportWorkflow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetWorkflowReleaseStatus",
			Func: TaskConfigService_GetWorkflowReleaseStatus_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetTaskFlowReleaseStatus",
			Func: TaskConfigService_GetTaskFlowReleaseStatus_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetUnreleasedCount",
			Func: TaskConfigService_GetUnreleasedCount_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/SendDataSyncTaskEvent",
			Func: TaskConfigService_SendDataSyncTaskEvent_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetDataSyncTask",
			Func: TaskConfigService_GetDataSyncTask_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetDataSyncTasks",
			Func: TaskConfigService_GetDataSyncTasks_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CheckRobotReady",
			Func: TaskConfigService_CheckRobotReady_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ReleaseAPIVarParams",
			Func: TaskConfigService_ReleaseAPIVarParams_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListCategory",
			Func: TaskConfigService_ListCategory_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CreateCategory",
			Func: TaskConfigService_CreateCategory_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/UpdateCategory",
			Func: TaskConfigService_UpdateCategory_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DeleteCategory",
			Func: TaskConfigService_DeleteCategory_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/RecoverHistoryTaskFlow",
			Func: TaskConfigService_RecoverHistoryTaskFlow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListHistoryTaskFlow",
			Func: TaskConfigService_ListHistoryTaskFlow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DescribeHistoryTaskFlow",
			Func: TaskConfigService_DescribeHistoryTaskFlow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GroupTaskFlow",
			Func: TaskConfigService_GroupTaskFlow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListTaskFlow",
			Func: TaskConfigService_ListTaskFlow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetTaskFlowDetail",
			Func: TaskConfigService_GetTaskFlowDetail_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CreateTaskFlow",
			Func: TaskConfigService_CreateTaskFlow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/SaveTaskFlow",
			Func: TaskConfigService_SaveTaskFlow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DeleteTaskFlow",
			Func: TaskConfigService_DeleteTaskFlow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListTaskFlowPreview",
			Func: TaskConfigService_ListTaskFlowPreview_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ImportTaskFlow",
			Func: TaskConfigService_ImportTaskFlow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ExportTaskFlow",
			Func: TaskConfigService_ExportTaskFlow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/InnerExportTaskFlow",
			Func: TaskConfigService_InnerExportTaskFlow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/PreviewTaskFlowRequestNode",
			Func: TaskConfigService_PreviewTaskFlowRequestNode_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/PreviewTaskFlowAnswerNode",
			Func: TaskConfigService_PreviewTaskFlowAnswerNode_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/PreviewAnswerNodeDocument",
			Func: TaskConfigService_PreviewAnswerNodeDocument_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CopyTaskFlow",
			Func: TaskConfigService_CopyTaskFlow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetSlotList",
			Func: TaskConfigService_GetSlotList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CreateSlot",
			Func: TaskConfigService_CreateSlot_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/UpdateSlot",
			Func: TaskConfigService_UpdateSlot_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DeleteSlot",
			Func: TaskConfigService_DeleteSlot_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetEntryList",
			Func: TaskConfigService_GetEntryList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CreateEntry",
			Func: TaskConfigService_CreateEntry_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/UpdateEntry",
			Func: TaskConfigService_UpdateEntry_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DeleteEntry",
			Func: TaskConfigService_DeleteEntry_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ImportEntry",
			Func: TaskConfigService_ImportEntry_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/RoleDefaultTemplateList",
			Func: TaskConfigService_RoleDefaultTemplateList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CreateExample",
			Func: TaskConfigService_CreateExample_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetExampleList",
			Func: TaskConfigService_GetExampleList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/UpdateExample",
			Func: TaskConfigService_UpdateExample_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DeleteExample",
			Func: TaskConfigService_DeleteExample_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CreateVar",
			Func: TaskConfigService_CreateVar_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/UpdateVar",
			Func: TaskConfigService_UpdateVar_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DeleteVar",
			Func: TaskConfigService_DeleteVar_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DescribeVar",
			Func: TaskConfigService_DescribeVar_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetVarList",
			Func: TaskConfigService_GetVarList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetSystemVarList",
			Func: TaskConfigService_GetSystemVarList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetPromptWordTemplateList",
			Func: TaskConfigService_GetPromptWordTemplateList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CreateWorkflow",
			Func: TaskConfigService_CreateWorkflow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/SaveWorkflow",
			Func: TaskConfigService_SaveWorkflow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CopyWorkflow",
			Func: TaskConfigService_CopyWorkflow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DeleteWorkflow",
			Func: TaskConfigService_DeleteWorkflow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListWorkflow",
			Func: TaskConfigService_ListWorkflow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListWorkflowInner",
			Func: TaskConfigService_ListWorkflowInner_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetWorkflowDetail",
			Func: TaskConfigService_GetWorkflowDetail_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListNodeInfo",
			Func: TaskConfigService_ListNodeInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListWorkflowNodeModel",
			Func: TaskConfigService_ListWorkflowNodeModel_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListWorkflowInfoByModelNameInner",
			Func: TaskConfigService_ListWorkflowInfoByModelNameInner_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/SaveAgentWorkflow",
			Func: TaskConfigService_SaveAgentWorkflow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListAgentWorkflow",
			Func: TaskConfigService_ListAgentWorkflow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetAgentWorkflowInfo",
			Func: TaskConfigService_GetAgentWorkflowInfo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetAgentWorkflowDetail",
			Func: TaskConfigService_GetAgentWorkflowDetail_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetAgentWorkflowState",
			Func: TaskConfigService_GetAgentWorkflowState_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ConvertToAgentWorkflow",
			Func: TaskConfigService_ConvertToAgentWorkflow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/SwitchAgentWorkflowState",
			Func: TaskConfigService_SwitchAgentWorkflowState_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListPDLVersion",
			Func: TaskConfigService_ListPDLVersion_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetPDLVersionDetail",
			Func: TaskConfigService_GetPDLVersionDetail_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CreateWorkflowExample",
			Func: TaskConfigService_CreateWorkflowExample_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListWorkflowExample",
			Func: TaskConfigService_ListWorkflowExample_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/UpdateWorkflowExample",
			Func: TaskConfigService_UpdateWorkflowExample_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DeleteWorkflowExample",
			Func: TaskConfigService_DeleteWorkflowExample_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ImportWorkflowExample",
			Func: TaskConfigService_ImportWorkflowExample_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/SwitchWorkflowState",
			Func: TaskConfigService_SwitchWorkflowState_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/AddWorkflowFeedback",
			Func: TaskConfigService_AddWorkflowFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/UpdateWorkflowFeedback",
			Func: TaskConfigService_UpdateWorkflowFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DescribeWorkflowFeedback",
			Func: TaskConfigService_DescribeWorkflowFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DeleteWorkflowFeedback",
			Func: TaskConfigService_DeleteWorkflowFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListWorkflowFeedback",
			Func: TaskConfigService_ListWorkflowFeedback_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/UpdateWorkflowFeedbackStatus",
			Func: TaskConfigService_UpdateWorkflowFeedbackStatus_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/UpdateWorkflowFeedbackTapd",
			Func: TaskConfigService_UpdateWorkflowFeedbackTapd_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ImportWorkflow",
			Func: TaskConfigService_ImportWorkflow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ExportWorkflow",
			Func: TaskConfigService_ExportWorkflow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DebugWorkflowNode",
			Func: TaskConfigService_DebugWorkflowNode_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetEnableCustomAsk",
			Func: TaskConfigService_GetEnableCustomAsk_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CreateParameter",
			Func: TaskConfigService_CreateParameter_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetParameterList",
			Func: TaskConfigService_GetParameterList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/UpdateParameter",
			Func: TaskConfigService_UpdateParameter_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DeleteParameter",
			Func: TaskConfigService_DeleteParameter_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetBotNodeParameterList",
			Func: TaskConfigService_GetBotNodeParameterList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ClearAppFlowResource",
			Func: TaskConfigService_ClearAppFlowResource_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DeleteAppResource",
			Func: TaskConfigService_DeleteAppResource_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetCanBeReferencedWorkflowList",
			Func: TaskConfigService_GetCanBeReferencedWorkflowList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetHasBeenReferencedWorkflowList",
			Func: TaskConfigService_GetHasBeenReferencedWorkflowList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetHasReferencedPluginToolWorkflowList",
			Func: TaskConfigService_GetHasReferencedPluginToolWorkflowList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetParamsByWorkflowIds",
			Func: TaskConfigService_GetParamsByWorkflowIds_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetWorkflowGuideViewed",
			Func: TaskConfigService_GetWorkflowGuideViewed_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/MarkWorkflowGuideViewed",
			Func: TaskConfigService_MarkWorkflowGuideViewed_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetExperienceWorkflowList",
			Func: TaskConfigService_GetExperienceWorkflowList_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CreateExperienceWorkflow",
			Func: TaskConfigService_CreateExperienceWorkflow_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CanCreateWorkflowRun",
			Func: TaskConfigService_CanCreateWorkflowRun_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/SaveAppDebugMode",
			Func: TaskConfigService_SaveAppDebugMode_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/SaveAppDebugCustomVariables",
			Func: TaskConfigService_SaveAppDebugCustomVariables_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DescribeAppDebugConfig",
			Func: TaskConfigService_DescribeAppDebugConfig_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/CreateWorkflowRun",
			Func: TaskConfigService_CreateWorkflowRun_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/ListWorkflowRuns",
			Func: TaskConfigService_ListWorkflowRuns_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DescribeWorkflowRun",
			Func: TaskConfigService_DescribeWorkflowRun_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/DescribeNodeRun",
			Func: TaskConfigService_DescribeNodeRun_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/StopWorkflowRun",
			Func: TaskConfigService_StopWorkflowRun_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetWorkflowListByDoc",
			Func: TaskConfigService_GetWorkflowListByDoc_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetWorkflowListByAttribute",
			Func: TaskConfigService_GetWorkflowListByAttribute_Handler,
		},
		{
			Name: "/trpc.KEP.bot_task_config_server.TaskConfig/GetWorkflowListByAttributeLabel",
			Func: TaskConfigService_GetWorkflowListByAttributeLabel_Handler,
		},
	},
}

// RegisterTaskConfigService registers service.
func RegisterTaskConfigService(s server.Service, svr TaskConfigService) {
	if err := s.Register(&TaskConfigServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("TaskConfig register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedTaskConfig struct{}

// GetAppChatInputNum 获取应用可输入字符的数量
//
//	@alias=/GetAppChatInputNum
func (s *UnimplementedTaskConfig) GetAppChatInputNum(ctx context.Context, req *GetAppChatInputNumReq) (*GetAppChatInputNumRsp, error) {
	return nil, errors.New("rpc GetAppChatInputNum of service TaskConfig is not implemented")
}

// GetAppShareURL 获取分享链接的地址
//
//	@alias=/GetAppShareURL
func (s *UnimplementedTaskConfig) GetAppShareURL(ctx context.Context, req *GetAppShareURLReq) (*GetAppShareURLResp, error) {
	return nil, errors.New("rpc GetAppShareURL of service TaskConfig is not implemented")
}

// GetRobotIdByShareCode 通过分享码获取机器人信息请求
//
//	@alias=/GetRobotIdByShareCode
func (s *UnimplementedTaskConfig) GetRobotIdByShareCode(ctx context.Context, req *GetRobotIdByShareCodeReq) (*GetRobotIdByShareCodeResp, error) {
	return nil, errors.New("rpc GetRobotIdByShareCode of service TaskConfig is not implemented")
}

// GetModelSupportWorkflow 获取模型支持工作流的情况
//
//	@alias=/GetModelSupportWorkflow
func (s *UnimplementedTaskConfig) GetModelSupportWorkflow(ctx context.Context, req *KEP_WF.GetModelSupportWorkflowReq) (*KEP_WF.GetModelSupportWorkflowResp, error) {
	return nil, errors.New("rpc GetModelSupportWorkflow of service TaskConfig is not implemented")
}

// GetWorkflowReleaseStatus ======================= 发布任务 ==========================
//
//	获取发布任务的状态（内部接口）
//	@alias=/GetWorkflowReleaseStatus
func (s *UnimplementedTaskConfig) GetWorkflowReleaseStatus(ctx context.Context, req *KEP_WF.GetWorkflowReleaseStatusReq) (*KEP_WF.GetWorkflowReleaseStatusResp, error) {
	return nil, errors.New("rpc GetWorkflowReleaseStatus of service TaskConfig is not implemented")
}

// GetTaskFlowReleaseStatus 获取发布任务的状态
//
//	@alias=/GetTaskFlowReleaseStatus
func (s *UnimplementedTaskConfig) GetTaskFlowReleaseStatus(ctx context.Context, req *GetTaskFlowReleaseStatusReq) (*GetTaskFlowReleaseStatusResp, error) {
	return nil, errors.New("rpc GetTaskFlowReleaseStatus of service TaskConfig is not implemented")
}

// GetUnreleasedCount 获取未发布的数量
//
//	@alias=/GetUnreleasedCount
func (s *UnimplementedTaskConfig) GetUnreleasedCount(ctx context.Context, req *GetUnreleasedCountReq) (*GetUnreleasedCountRsp, error) {
	return nil, errors.New("rpc GetUnreleasedCount of service TaskConfig is not implemented")
}

// SendDataSyncTaskEvent 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
//
//	@alias=/SendDataSyncTaskEvent
func (s *UnimplementedTaskConfig) SendDataSyncTaskEvent(ctx context.Context, req *SendDataSyncTaskEventReq) (*SendDataSyncTaskEventRsp, error) {
	return nil, errors.New("rpc SendDataSyncTaskEvent of service TaskConfig is not implemented")
}

// GetDataSyncTask [单个]获取同步任务, 详情, 状态等
//
//	@alias=/GetDataSyncTask
func (s *UnimplementedTaskConfig) GetDataSyncTask(ctx context.Context, req *GetDataSyncTaskReq) (*GetDataSyncTaskRsp, error) {
	return nil, errors.New("rpc GetDataSyncTask of service TaskConfig is not implemented")
}

// GetDataSyncTasks [批量]获取同步任务, 详情, 状态等
//
//	@alias=/GetDataSyncTasks
func (s *UnimplementedTaskConfig) GetDataSyncTasks(ctx context.Context, req *GetDataSyncTasksReq) (*GetDataSyncTasksRsp, error) {
	return nil, errors.New("rpc GetDataSyncTasks of service TaskConfig is not implemented")
}

// CheckRobotReady 检查机器人, 是否准备好
//
//	@alias=/CheckRobotReady
func (s *UnimplementedTaskConfig) CheckRobotReady(ctx context.Context, req *CheckRobotReadyReq) (*CheckRobotReadyRsp, error) {
	return nil, errors.New("rpc CheckRobotReady of service TaskConfig is not implemented")
}

// ReleaseAPIVarParams 通知API参数发布
//
//	@alias=/ReleaseAPIVarParams
func (s *UnimplementedTaskConfig) ReleaseAPIVarParams(ctx context.Context, req *ReleaseAPIVarParamsReq) (*ReleaseAPIVarParamsRsp, error) {
	return nil, errors.New("rpc ReleaseAPIVarParams of service TaskConfig is not implemented")
}

// ListCategory 获取分类
//
//	@alias=/ListCategory
func (s *UnimplementedTaskConfig) ListCategory(ctx context.Context, req *ListCategoryReq) (*ListCategoryRsp, error) {
	return nil, errors.New("rpc ListCategory of service TaskConfig is not implemented")
}

// CreateCategory 创建分类
//
//	@alias=/CreateCategory
func (s *UnimplementedTaskConfig) CreateCategory(ctx context.Context, req *CreateCategoryReq) (*CreateCategoryRsp, error) {
	return nil, errors.New("rpc CreateCategory of service TaskConfig is not implemented")
}

// UpdateCategory 分类修改
//
//	@alias=/UpdateCategory
func (s *UnimplementedTaskConfig) UpdateCategory(ctx context.Context, req *UpdateCategoryReq) (*UpdateCategoryRsp, error) {
	return nil, errors.New("rpc UpdateCategory of service TaskConfig is not implemented")
}

// DeleteCategory 分类删除
//
//	@alias=/DeleteCategory
func (s *UnimplementedTaskConfig) DeleteCategory(ctx context.Context, req *DeleteCategoryReq) (*DeleteCategoryRsp, error) {
	return nil, errors.New("rpc DeleteCategory of service TaskConfig is not implemented")
}

// RecoverHistoryTaskFlow ======================= 任务流 ==========================
//
//	v2.1 恢复历史发布版本
//	@alias=/RecoverHistoryTaskFlow
func (s *UnimplementedTaskConfig) RecoverHistoryTaskFlow(ctx context.Context, req *RecoverHistoryTaskFlowReq) (*RecoverHistoryTaskFlowRsp, error) {
	return nil, errors.New("rpc RecoverHistoryTaskFlow of service TaskConfig is not implemented")
}

// ListHistoryTaskFlow v2.1 获取历史版本任务流程列表
//
//	@alias=/ListHistoryTaskFlow
func (s *UnimplementedTaskConfig) ListHistoryTaskFlow(ctx context.Context, req *ListHistoryTaskFlowReq) (*ListHistoryTaskFlowRsp, error) {
	return nil, errors.New("rpc ListHistoryTaskFlow of service TaskConfig is not implemented")
}

// DescribeHistoryTaskFlow v2.1 获取历史发布版本详情请
//
//	@alias=/DescribeHistoryTaskFlow
func (s *UnimplementedTaskConfig) DescribeHistoryTaskFlow(ctx context.Context, req *DescribeHistoryTaskFlowReq) (*DescribeHistoryTaskFlowRsp, error) {
	return nil, errors.New("rpc DescribeHistoryTaskFlow of service TaskConfig is not implemented")
}

// GroupTaskFlow 分组移动任务流到不同分类
//
//	@alias=/GroupTaskFlow
func (s *UnimplementedTaskConfig) GroupTaskFlow(ctx context.Context, req *GroupTaskFlowReq) (*GroupTaskFlowRsp, error) {
	return nil, errors.New("rpc GroupTaskFlow of service TaskConfig is not implemented")
}

// ListTaskFlow 获取TaskFlow列表
//
//	@alias=/ListTaskFlow
func (s *UnimplementedTaskConfig) ListTaskFlow(ctx context.Context, req *ListTaskFlowReq) (*ListTaskFlowRsp, error) {
	return nil, errors.New("rpc ListTaskFlow of service TaskConfig is not implemented")
}

// GetTaskFlowDetail 获取TaskFlow详情
//
//	@alias=/GetTaskFlowDetail
func (s *UnimplementedTaskConfig) GetTaskFlowDetail(ctx context.Context, req *GetTaskFlowDetailReq) (*GetTaskFlowDetailRsp, error) {
	return nil, errors.New("rpc GetTaskFlowDetail of service TaskConfig is not implemented")
}

// CreateTaskFlow 新建TaskFlow
//
//	@alias=/CreateTaskFlow
func (s *UnimplementedTaskConfig) CreateTaskFlow(ctx context.Context, req *CreateTaskFlowReq) (*CreateTaskFlowRsp, error) {
	return nil, errors.New("rpc CreateTaskFlow of service TaskConfig is not implemented")
}

// SaveTaskFlow 编辑/保存TaskFlow
//
//	@alias=/SaveTaskFlow
func (s *UnimplementedTaskConfig) SaveTaskFlow(ctx context.Context, req *SaveTaskFlowReq) (*SaveTaskFlowRsp, error) {
	return nil, errors.New("rpc SaveTaskFlow of service TaskConfig is not implemented")
}

// DeleteTaskFlow 删除TaskFlow
//
//	@alias=/DeleteTaskFlow
func (s *UnimplementedTaskConfig) DeleteTaskFlow(ctx context.Context, req *DeleteTaskFlowReq) (*DeleteTaskFlowRsp, error) {
	return nil, errors.New("rpc DeleteTaskFlow of service TaskConfig is not implemented")
}

// ListTaskFlowPreview 获取任务流程发布列表
//
//	@alias=/ListTaskFlowPreview
func (s *UnimplementedTaskConfig) ListTaskFlowPreview(ctx context.Context, req *ListTaskFlowPreviewReq) (*ListTaskFlowPreviewRsp, error) {
	return nil, errors.New("rpc ListTaskFlowPreview of service TaskConfig is not implemented")
}

// ImportTaskFlow 导入TaskFlow
//
//	@alias=/ImportTaskFlow
func (s *UnimplementedTaskConfig) ImportTaskFlow(ctx context.Context, req *ImportTaskFlowReq) (*ImportTaskFlowRsp, error) {
	return nil, errors.New("rpc ImportTaskFlow of service TaskConfig is not implemented")
}

// ExportTaskFlow 导出TaskFlow
//
//	@alias=/ExportTaskFlow
func (s *UnimplementedTaskConfig) ExportTaskFlow(ctx context.Context, req *ExportTaskFlowReq) (*ExportTaskFlowRsp, error) {
	return nil, errors.New("rpc ExportTaskFlow of service TaskConfig is not implemented")
}

// InnerExportTaskFlow 内部平台导出TaskFlow
//
//	@alias=/InnerExportTaskFlow
func (s *UnimplementedTaskConfig) InnerExportTaskFlow(ctx context.Context, req *InnerExportTaskFlowReq) (*InnerExportTaskFlowRsp, error) {
	return nil, errors.New("rpc InnerExportTaskFlow of service TaskConfig is not implemented")
}

// PreviewTaskFlowRequestNode 预览TaskFlow的询问节点
//
//	@alias=/PreviewTaskFlowRequestNode
func (s *UnimplementedTaskConfig) PreviewTaskFlowRequestNode(ctx context.Context, req *PreviewTaskFlowRequestNodeReq) (*PreviewTaskFlowRequestNodeRsp, error) {
	return nil, errors.New("rpc PreviewTaskFlowRequestNode of service TaskConfig is not implemented")
}

// PreviewTaskFlowAnswerNode 预览TaskFlow的答案节点
//
//	@alias=/PreviewTaskFlowAnswerNode
func (s *UnimplementedTaskConfig) PreviewTaskFlowAnswerNode(ctx context.Context, req *PreviewTaskFlowAnswerNodeReq) (*PreviewTaskFlowAnswerNodeRsp, error) {
	return nil, errors.New("rpc PreviewTaskFlowAnswerNode of service TaskConfig is not implemented")
}

// PreviewAnswerNodeDocument 预览答案节点的知识文档
//
//	@alias=/PreviewAnswerNodeDocument
func (s *UnimplementedTaskConfig) PreviewAnswerNodeDocument(ctx context.Context, req *PreviewAnswerNodeDocumentReq) (*PreviewAnswerNodeDocumentRsp, error) {
	return nil, errors.New("rpc PreviewAnswerNodeDocument of service TaskConfig is not implemented")
}

// CopyTaskFlow 复制TaskFlow
//
//	@alias=/CopyTaskFlow
func (s *UnimplementedTaskConfig) CopyTaskFlow(ctx context.Context, req *CopyTaskFlowReq) (*CopyTaskFlowRsp, error) {
	return nil, errors.New("rpc CopyTaskFlow of service TaskConfig is not implemented")
}

// GetSlotList ======================= 槽位、实体、词条 ==========================
//
//	查询槽位列表
//	@alias=/GetSlotList
func (s *UnimplementedTaskConfig) GetSlotList(ctx context.Context, req *GetSlotListReq) (*GetSlotListRsp, error) {
	return nil, errors.New("rpc GetSlotList of service TaskConfig is not implemented")
}

// CreateSlot 新建槽位
//
//	@alias=/CreateSlot
func (s *UnimplementedTaskConfig) CreateSlot(ctx context.Context, req *CreateSlotReq) (*CreateSlotRsp, error) {
	return nil, errors.New("rpc CreateSlot of service TaskConfig is not implemented")
}

// UpdateSlot 编辑槽位
//
//	@alias=/UpdateSlot
func (s *UnimplementedTaskConfig) UpdateSlot(ctx context.Context, req *UpdateSlotReq) (*UpdateSlotRsp, error) {
	return nil, errors.New("rpc UpdateSlot of service TaskConfig is not implemented")
}

// DeleteSlot 删除槽位
//
//	@alias=/DeleteSlot
func (s *UnimplementedTaskConfig) DeleteSlot(ctx context.Context, req *DeleteSlotReq) (*DeleteSlotRsp, error) {
	return nil, errors.New("rpc DeleteSlot of service TaskConfig is not implemented")
}

// GetEntryList 查询词条列表
//
//	@alias=/GetEntryList
func (s *UnimplementedTaskConfig) GetEntryList(ctx context.Context, req *GetEntryListReq) (*GetEntryListRsp, error) {
	return nil, errors.New("rpc GetEntryList of service TaskConfig is not implemented")
}

// CreateEntry 新建词条
//
//	@alias=/CreateEntry
func (s *UnimplementedTaskConfig) CreateEntry(ctx context.Context, req *CreateEntryReq) (*CreateEntryRsp, error) {
	return nil, errors.New("rpc CreateEntry of service TaskConfig is not implemented")
}

// UpdateEntry 编辑词条
//
//	@alias=/UpdateEntry
func (s *UnimplementedTaskConfig) UpdateEntry(ctx context.Context, req *UpdateEntryReq) (*UpdateEntryRsp, error) {
	return nil, errors.New("rpc UpdateEntry of service TaskConfig is not implemented")
}

// DeleteEntry 删除词条
//
//	@alias=/DeleteEntry
func (s *UnimplementedTaskConfig) DeleteEntry(ctx context.Context, req *DeleteEntryReq) (*DeleteEntryRsp, error) {
	return nil, errors.New("rpc DeleteEntry of service TaskConfig is not implemented")
}

// ImportEntry 导入词条
//
//	@alias=/ImportEntry
func (s *UnimplementedTaskConfig) ImportEntry(ctx context.Context, req *ImportEntryReq) (*ImportEntryRsp, error) {
	return nil, errors.New("rpc ImportEntry of service TaskConfig is not implemented")
}

// RoleDefaultTemplateList 角色预设模版获取
//
//	@alias=/RoleDefaultTemplateList
func (s *UnimplementedTaskConfig) RoleDefaultTemplateList(ctx context.Context, req *RoleDefaultTemplateListReq) (*RoleDefaultTemplateListRsp, error) {
	return nil, errors.New("rpc RoleDefaultTemplateList of service TaskConfig is not implemented")
}

// CreateExample 创建示例问法
//
//	@alias=/CreateExample
func (s *UnimplementedTaskConfig) CreateExample(ctx context.Context, req *CreateExampleReq) (*CreateExampleRsp, error) {
	return nil, errors.New("rpc CreateExample of service TaskConfig is not implemented")
}

// GetExampleList 获取示例问法列表
//
//	@alias=/GetExampleList
func (s *UnimplementedTaskConfig) GetExampleList(ctx context.Context, req *GetExampleListReq) (*GetExampleListRsp, error) {
	return nil, errors.New("rpc GetExampleList of service TaskConfig is not implemented")
}

// UpdateExample 更新示例问法
//
//	@alias=/UpdateExample
func (s *UnimplementedTaskConfig) UpdateExample(ctx context.Context, req *UpdateExampleReq) (*UpdateExampleRsp, error) {
	return nil, errors.New("rpc UpdateExample of service TaskConfig is not implemented")
}

// DeleteExample 删除示例问法
//
//	@alias=/DeleteExample
func (s *UnimplementedTaskConfig) DeleteExample(ctx context.Context, req *DeleteExampleReq) (*DeleteExampleRsp, error) {
	return nil, errors.New("rpc DeleteExample of service TaskConfig is not implemented")
}

// CreateVar =============== 参数变量的 增删改查 ===========================
//
//	创建参数变量
//	@alias=/CreateVar
func (s *UnimplementedTaskConfig) CreateVar(ctx context.Context, req *CreateVarReq) (*CreateVarRsp, error) {
	return nil, errors.New("rpc CreateVar of service TaskConfig is not implemented")
}

// UpdateVar 更新参数变量
//
//	@alias=/UpdateVar
func (s *UnimplementedTaskConfig) UpdateVar(ctx context.Context, req *UpdateVarReq) (*UpdateVarRsp, error) {
	return nil, errors.New("rpc UpdateVar of service TaskConfig is not implemented")
}

// DeleteVar 删除参数变量
//
//	@alias=/DeleteVar
func (s *UnimplementedTaskConfig) DeleteVar(ctx context.Context, req *DeleteVarReq) (*DeleteVarRsp, error) {
	return nil, errors.New("rpc DeleteVar of service TaskConfig is not implemented")
}

// DescribeVar 获取参数变量
//
//	@alias=/DescribeVar
func (s *UnimplementedTaskConfig) DescribeVar(ctx context.Context, req *DescribeVarReq) (*DescribeVarRsp, error) {
	return nil, errors.New("rpc DescribeVar of service TaskConfig is not implemented")
}

// GetVarList 获取参数变量列表
//
//	@alias=/GetVarList
func (s *UnimplementedTaskConfig) GetVarList(ctx context.Context, req *GetVarListReq) (*GetVarListRsp, error) {
	return nil, errors.New("rpc GetVarList of service TaskConfig is not implemented")
}

// GetSystemVarList 获取系统变量
//
//	@alias=/GetSystemVarList
func (s *UnimplementedTaskConfig) GetSystemVarList(ctx context.Context, req *GetSystemVarListReq) (*GetSystemVarListRsp, error) {
	return nil, errors.New("rpc GetSystemVarList of service TaskConfig is not implemented")
}

// GetPromptWordTemplateList 获取提示词模版
//
//	@alias=/GetPromptWordTemplateList
func (s *UnimplementedTaskConfig) GetPromptWordTemplateList(ctx context.Context, req *GetPromptWordTemplateListReq) (*GetPromptWordTemplateListRsp, error) {
	return nil, errors.New("rpc GetPromptWordTemplateList of service TaskConfig is not implemented")
}

// CreateWorkflow 新建Workflow
//
//	@alias=/CreateWorkflow
func (s *UnimplementedTaskConfig) CreateWorkflow(ctx context.Context, req *KEP_WF.CreateWorkflowReq) (*KEP_WF.CreateWorkflowRsp, error) {
	return nil, errors.New("rpc CreateWorkflow of service TaskConfig is not implemented")
}

// SaveWorkflow 编辑/保存Workflow
//
//	@alias=/SaveWorkflow
func (s *UnimplementedTaskConfig) SaveWorkflow(ctx context.Context, req *KEP_WF.SaveWorkflowReq) (*KEP_WF.SaveWorkflowRsp, error) {
	return nil, errors.New("rpc SaveWorkflow of service TaskConfig is not implemented")
}

// CopyWorkflow 复制Workflow
//
//	@alias=/CopyWorkflow
func (s *UnimplementedTaskConfig) CopyWorkflow(ctx context.Context, req *KEP_WF.CopyWorkflowReq) (*KEP_WF.CopyWorkflowRsp, error) {
	return nil, errors.New("rpc CopyWorkflow of service TaskConfig is not implemented")
}

// DeleteWorkflow 删除Workflow
//
//	@alias=/DeleteWorkflow
func (s *UnimplementedTaskConfig) DeleteWorkflow(ctx context.Context, req *KEP_WF.DeleteWorkflowReq) (*KEP_WF.DeleteWorkflowRsp, error) {
	return nil, errors.New("rpc DeleteWorkflow of service TaskConfig is not implemented")
}

// ListWorkflow 获取工作流列表
//
//	@alias=/ListWorkflow
func (s *UnimplementedTaskConfig) ListWorkflow(ctx context.Context, req *KEP_WF.ListWorkflowReq) (*KEP_WF.ListWorkflowRsp, error) {
	return nil, errors.New("rpc ListWorkflow of service TaskConfig is not implemented")
}

// ListWorkflowInner 内部服务调用该接口，目前admin会调用
//
//	@alias=/ListWorkflowInner
func (s *UnimplementedTaskConfig) ListWorkflowInner(ctx context.Context, req *KEP_WF.ListWorkflowInnerReq) (*KEP_WF.ListWorkflowInnerRsp, error) {
	return nil, errors.New("rpc ListWorkflowInner of service TaskConfig is not implemented")
}

// GetWorkflowDetail 获取某个工作流具体信息
//
//	@alias=/GetWorkflowDetail
func (s *UnimplementedTaskConfig) GetWorkflowDetail(ctx context.Context, req *KEP_WF.GetWorkflowDetailReq) (*KEP_WF.GetWorkflowDetailResp, error) {
	return nil, errors.New("rpc GetWorkflowDetail of service TaskConfig is not implemented")
}

// ListNodeInfo 获取工作流的节点信息
//
//	@alias=/ListNodeInfo
func (s *UnimplementedTaskConfig) ListNodeInfo(ctx context.Context, req *KEP_WF.ListNodeInfoReq) (*KEP_WF.ListNodeInfoRsp, error) {
	return nil, errors.New("rpc ListNodeInfo of service TaskConfig is not implemented")
}

// ListWorkflowNodeModel 获取支持工作流节点模型的信息
//
//	@alias=/ListWorkflowNodeModel
func (s *UnimplementedTaskConfig) ListWorkflowNodeModel(ctx context.Context, req *KEP_WF.ListWorkflowNodeModelReq) (*KEP_WF.ListWorkflowNodeModelRsp, error) {
	return nil, errors.New("rpc ListWorkflowNodeModel of service TaskConfig is not implemented")
}

// ListWorkflowInfoByModelNameInner 删除前校验模型被哪些工作流引用 (内部使用，admin删除模型时使用)
//
//	@alias=/ListWorkflowInfoByModelNameInner
func (s *UnimplementedTaskConfig) ListWorkflowInfoByModelNameInner(ctx context.Context, req *KEP_WF.ListWorkflowInfoByModelNameReq) (*KEP_WF.ListWorkflowInfoByModelNameRsp, error) {
	return nil, errors.New("rpc ListWorkflowInfoByModelNameInner of service TaskConfig is not implemented")
}

// SaveAgentWorkflow ============== Agent工作流 ========================
//
//	编辑/保存AgentWorkflow
//	@alias=/SaveAgentWorkflow
func (s *UnimplementedTaskConfig) SaveAgentWorkflow(ctx context.Context, req *KEP_WF.SaveAgentWorkflowReq) (*KEP_WF.SaveAgentWorkflowRsp, error) {
	return nil, errors.New("rpc SaveAgentWorkflow of service TaskConfig is not implemented")
}

// ListAgentWorkflow 获取Agent工作流列表
//
//	@alias=/ListAgentWorkflow
func (s *UnimplementedTaskConfig) ListAgentWorkflow(ctx context.Context, req *KEP_WF.ListAgentWorkflowReq) (*KEP_WF.ListAgentWorkflowRsp, error) {
	return nil, errors.New("rpc ListAgentWorkflow of service TaskConfig is not implemented")
}

// GetAgentWorkflowInfo 内部服务调用该接口，目前主会话会调用
//
//	@alias=/GetAgentWorkflowInfo
func (s *UnimplementedTaskConfig) GetAgentWorkflowInfo(ctx context.Context, req *KEP_WF.GetAgentWorkflowInfoReq) (*KEP_WF.GetAgentWorkflowInfoRsp, error) {
	return nil, errors.New("rpc GetAgentWorkflowInfo of service TaskConfig is not implemented")
}

// GetAgentWorkflowDetail 获取某个Agent工作流具体信息
//
//	@alias=/GetAgentWorkflowDetail
func (s *UnimplementedTaskConfig) GetAgentWorkflowDetail(ctx context.Context, req *KEP_WF.GetAgentWorkflowDetailReq) (*KEP_WF.GetAgentWorkflowDetailRsp, error) {
	return nil, errors.New("rpc GetAgentWorkflowDetail of service TaskConfig is not implemented")
}

// GetAgentWorkflowState 获取某个Agent工作流状态
//
//	@alias=/GetAgentWorkflowState
func (s *UnimplementedTaskConfig) GetAgentWorkflowState(ctx context.Context, req *KEP_WF.GetAgentWorkflowStateReq) (*KEP_WF.GetAgentWorkflowStateRsp, error) {
	return nil, errors.New("rpc GetAgentWorkflowState of service TaskConfig is not implemented")
}

// ConvertToAgentWorkflow 工作流画布转换成PDL
//
//	@alias=/ConvertToAgentWorkflow
func (s *UnimplementedTaskConfig) ConvertToAgentWorkflow(ctx context.Context, req *KEP_WF.ConvertToAgentWorkflowReq) (*KEP_WF.ConvertToAgentWorkflowRsp, error) {
	return nil, errors.New("rpc ConvertToAgentWorkflow of service TaskConfig is not implemented")
}

// SwitchAgentWorkflowState 是否开启Agent工作流
//
//	@alias=/SwitchAgentWorkflowState
func (s *UnimplementedTaskConfig) SwitchAgentWorkflowState(ctx context.Context, req *KEP_WF.SwitchAgentWorkflowStateReq) (*KEP_WF.SwitchAgentWorkflowStateRsp, error) {
	return nil, errors.New("rpc SwitchAgentWorkflowState of service TaskConfig is not implemented")
}

// ListPDLVersion 获取工作流PDL版本列表
//
//	@alias=/ListPDLVersion
func (s *UnimplementedTaskConfig) ListPDLVersion(ctx context.Context, req *KEP_WF.ListPDLVersionReq) (*KEP_WF.ListPDLVersionRsp, error) {
	return nil, errors.New("rpc ListPDLVersion of service TaskConfig is not implemented")
}

// GetPDLVersionDetail 获取工作流PDL版本详情
//
//	@alias=/GetPDLVersionDetail
func (s *UnimplementedTaskConfig) GetPDLVersionDetail(ctx context.Context, req *KEP_WF.GetPDLVersionDetailReq) (*KEP_WF.GetPDLVersionDetailRsp, error) {
	return nil, errors.New("rpc GetPDLVersionDetail of service TaskConfig is not implemented")
}

// CreateWorkflowExample ============== 工作流的示例问法 ========================
//
//	创建示例问法
//	@alias=/CreateWorkflowExample
func (s *UnimplementedTaskConfig) CreateWorkflowExample(ctx context.Context, req *KEP_WF.CreateWorkflowExampleReq) (*KEP_WF.CreateWorkflowExampleRsp, error) {
	return nil, errors.New("rpc CreateWorkflowExample of service TaskConfig is not implemented")
}

// ListWorkflowExample 获取示例问法列表
//
//	@alias=/ListWorkflowExample
func (s *UnimplementedTaskConfig) ListWorkflowExample(ctx context.Context, req *KEP_WF.ListWorkflowExampleReq) (*KEP_WF.ListWorkflowExampleRsp, error) {
	return nil, errors.New("rpc ListWorkflowExample of service TaskConfig is not implemented")
}

// UpdateWorkflowExample 更新示例问法
//
//	@alias=/UpdateWorkflowExample
func (s *UnimplementedTaskConfig) UpdateWorkflowExample(ctx context.Context, req *KEP_WF.UpdateWorkflowExampleReq) (*KEP_WF.UpdateWorkflowExampleRsp, error) {
	return nil, errors.New("rpc UpdateWorkflowExample of service TaskConfig is not implemented")
}

// DeleteWorkflowExample 删除示例问法
//
//	@alias=/DeleteWorkflowExample
func (s *UnimplementedTaskConfig) DeleteWorkflowExample(ctx context.Context, req *KEP_WF.DeleteWorkflowExampleReq) (*KEP_WF.DeleteWorkflowExampleRsp, error) {
	return nil, errors.New("rpc DeleteWorkflowExample of service TaskConfig is not implemented")
}

// ImportWorkflowExample 导入工作流示例问法
//
//	@alias=/ImportWorkflowExample
func (s *UnimplementedTaskConfig) ImportWorkflowExample(ctx context.Context, req *KEP_WF.ImportWfExampleReq) (*KEP_WF.ImportWfExampleRsp, error) {
	return nil, errors.New("rpc ImportWorkflowExample of service TaskConfig is not implemented")
}

// SwitchWorkflowState 是否开启工作流
//
//	@alias=/SwitchWorkflowState
func (s *UnimplementedTaskConfig) SwitchWorkflowState(ctx context.Context, req *KEP_WF.SwitchWorkflowStateReq) (*KEP_WF.SwitchWorkflowStateRsp, error) {
	return nil, errors.New("rpc SwitchWorkflowState of service TaskConfig is not implemented")
}

// AddWorkflowFeedback 添加工作流反馈
//
//	@alias=/AddWorkflowFeedback
func (s *UnimplementedTaskConfig) AddWorkflowFeedback(ctx context.Context, req *KEP_WF.AddFlowFeedbackReq) (*KEP_WF.AddFlowFeedbackRsp, error) {
	return nil, errors.New("rpc AddWorkflowFeedback of service TaskConfig is not implemented")
}

// UpdateWorkflowFeedback 修改工作流反馈
//
//	@alias=/UpdateWorkflowFeedback
func (s *UnimplementedTaskConfig) UpdateWorkflowFeedback(ctx context.Context, req *KEP_WF.UpdateFlowFeedbackReq) (*KEP_WF.UpdateFlowFeedbackRsp, error) {
	return nil, errors.New("rpc UpdateWorkflowFeedback of service TaskConfig is not implemented")
}

// DescribeWorkflowFeedback 获取工作流详情
//
//	@alias=/DescribeWorkflowFeedback
func (s *UnimplementedTaskConfig) DescribeWorkflowFeedback(ctx context.Context, req *KEP_WF.DescribeWorkflowFeedReq) (*KEP_WF.DescribeWorkflowFeedRsp, error) {
	return nil, errors.New("rpc DescribeWorkflowFeedback of service TaskConfig is not implemented")
}

// DeleteWorkflowFeedback 删除反馈
//
//	@alias=/DeleteWorkflowFeedback
func (s *UnimplementedTaskConfig) DeleteWorkflowFeedback(ctx context.Context, req *KEP_WF.DeleteWorkflowFeedbackReq) (*KEP_WF.DeleteWorkflowFeedbackRsp, error) {
	return nil, errors.New("rpc DeleteWorkflowFeedback of service TaskConfig is not implemented")
}

// ListWorkflowFeedback 查询反馈信息列表
//
//	@alias=/ListWorkflowFeedback
func (s *UnimplementedTaskConfig) ListWorkflowFeedback(ctx context.Context, req *KEP_WF.ListFlowFeedbackReq) (*KEP_WF.ListFlowFeedbackRsp, error) {
	return nil, errors.New("rpc ListWorkflowFeedback of service TaskConfig is not implemented")
}

// UpdateWorkflowFeedbackStatus ================= 内部(op)接口调用  start =====
//
//	UpdateWorkflowFeedbackStatus 修改 workflow 反馈信息状态
//	@alias=/UpdateWorkflowFeedbackStatus
func (s *UnimplementedTaskConfig) UpdateWorkflowFeedbackStatus(ctx context.Context, req *KEP_WF.UpdateWorkflowFeedbackStatusReq) (*KEP_WF.UpdateWorkflowFeedbackStatusRsp, error) {
	return nil, errors.New("rpc UpdateWorkflowFeedbackStatus of service TaskConfig is not implemented")
}

// UpdateWorkflowFeedbackTapd UpdateWorkflowFeedbackTapd 修改 workflow 反馈信息关联的tapd
//
//	@alias=/UpdateWorkflowFeedbackTapd
func (s *UnimplementedTaskConfig) UpdateWorkflowFeedbackTapd(ctx context.Context, req *KEP_WF.UpdateWorkflowFeedbackTapdReq) (*KEP_WF.UpdateWorkflowFeedbackTapdRsp, error) {
	return nil, errors.New("rpc UpdateWorkflowFeedbackTapd of service TaskConfig is not implemented")
}

// ImportWorkflow ==================== 工作流导入导出 ================
//
//	导入Workflow
//	@alias=/ImportWorkflow
func (s *UnimplementedTaskConfig) ImportWorkflow(ctx context.Context, req *KEP_WF.ImportWorkflowReq) (*KEP_WF.ImportWorkflowRsp, error) {
	return nil, errors.New("rpc ImportWorkflow of service TaskConfig is not implemented")
}

// ExportWorkflow 导出Workflow
//
//	@alias=/ExportWorkflow
func (s *UnimplementedTaskConfig) ExportWorkflow(ctx context.Context, req *KEP_WF.ExportWorkflowReq) (*KEP_WF.ExportWorkflowRsp, error) {
	return nil, errors.New("rpc ExportWorkflow of service TaskConfig is not implemented")
}

// DebugWorkflowNode ==================== 工作流节点调试 ================
//
//	调试Workflow节点
//	@alias=/DebugWorkflowNode
func (s *UnimplementedTaskConfig) DebugWorkflowNode(ctx context.Context, req *KEP_WF.DebugWorkflowNodeReq) (*KEP_WF.DebugWorkflowNodeRsp, error) {
	return nil, errors.New("rpc DebugWorkflowNode of service TaskConfig is not implemented")
}

// GetEnableCustomAsk ============== 工作流的参数提取 ========================
//
//	创建参数获取是否开启了添加自定义询问话术的白名单
//	@alias=/GetEnableCustomAsk
func (s *UnimplementedTaskConfig) GetEnableCustomAsk(ctx context.Context, req *KEP_WF.GetEnableCustomAskReq) (*KEP_WF.GetEnableCustomAskResp, error) {
	return nil, errors.New("rpc GetEnableCustomAsk of service TaskConfig is not implemented")
}

// CreateParameter 创建参数
//
//	@alias=/CreateParameter
func (s *UnimplementedTaskConfig) CreateParameter(ctx context.Context, req *KEP_WF.CreateParameterReq) (*KEP_WF.CreateParameterResp, error) {
	return nil, errors.New("rpc CreateParameter of service TaskConfig is not implemented")
}

// GetParameterList 获取参数列表
//
//	@alias=/GetParameterList
func (s *UnimplementedTaskConfig) GetParameterList(ctx context.Context, req *KEP_WF.GetParameterListReq) (*KEP_WF.GetParameterListResp, error) {
	return nil, errors.New("rpc GetParameterList of service TaskConfig is not implemented")
}

// UpdateParameter 更新参数信息
//
//	@alias=/UpdateParameter
func (s *UnimplementedTaskConfig) UpdateParameter(ctx context.Context, req *KEP_WF.UpdateParameterReq) (*KEP_WF.UpdateParameterResp, error) {
	return nil, errors.New("rpc UpdateParameter of service TaskConfig is not implemented")
}

// DeleteParameter 删除参数
//
//	@alias=/DeleteParameter
func (s *UnimplementedTaskConfig) DeleteParameter(ctx context.Context, req *KEP_WF.DeleteParameterReq) (*KEP_WF.DeleteParameterResp, error) {
	return nil, errors.New("rpc DeleteParameter of service TaskConfig is not implemented")
}

// GetBotNodeParameterList 获取应用下工作流节点参数信息
//
//	@alias=/GetBotNodeParameterList
func (s *UnimplementedTaskConfig) GetBotNodeParameterList(ctx context.Context, req *KEP_WF.GetBotNodeParameterListReq) (*KEP_WF.GetBotNodeParameterListResp, error) {
	return nil, errors.New("rpc GetBotNodeParameterList of service TaskConfig is not implemented")
}

// ClearAppFlowResource 清理任务/工作流 资源
//
//	@alias=/ClearAppFlowResource
func (s *UnimplementedTaskConfig) ClearAppFlowResource(ctx context.Context, req *KEP_WF.ClearAppFlowResourceReq) (*KEP_WF.ClearAppFlowResourceRsp, error) {
	return nil, errors.New("rpc ClearAppFlowResource of service TaskConfig is not implemented")
}

// DeleteAppResource 删除应用下面的 资源
//
//	@alias=/DeleteAppResource
func (s *UnimplementedTaskConfig) DeleteAppResource(ctx context.Context, req *KEP_WF.DeleteAppResourceReq) (*KEP_WF.DeleteAppResourceRsp, error) {
	return nil, errors.New("rpc DeleteAppResource of service TaskConfig is not implemented")
}

// GetCanBeReferencedWorkflowList 获取可以被引用的工作流的列表
//
//	@alias=/GetCanBeReferencedWorkflowList
func (s *UnimplementedTaskConfig) GetCanBeReferencedWorkflowList(ctx context.Context, req *KEP_WF.GetCanBeReferencedWorkflowListReq) (*KEP_WF.GetCanBeReferencedWorkflowListRsp, error) {
	return nil, errors.New("rpc GetCanBeReferencedWorkflowList of service TaskConfig is not implemented")
}

// GetHasBeenReferencedWorkflowList 批量获取指定工作流已经被引用的工作流的列表
//
//	@alias=/GetHasBeenReferencedWorkflowList
func (s *UnimplementedTaskConfig) GetHasBeenReferencedWorkflowList(ctx context.Context, req *KEP_WF.GetHasBeenReferencedWorkflowListReq) (*KEP_WF.GetHasBeenReferencedWorkflowListRsp, error) {
	return nil, errors.New("rpc GetHasBeenReferencedWorkflowList of service TaskConfig is not implemented")
}

// GetHasReferencedPluginToolWorkflowList 获取已经引用指定插件工具的工作流的列表
//
//	@alias=/GetHasReferencedPluginToolWorkflowList
func (s *UnimplementedTaskConfig) GetHasReferencedPluginToolWorkflowList(ctx context.Context, req *KEP_WF.GetHasReferencedPluginToolWorkflowListReq) (*KEP_WF.GetHasReferencedPluginToolWorkflowListRsp, error) {
	return nil, errors.New("rpc GetHasReferencedPluginToolWorkflowList of service TaskConfig is not implemented")
}

// GetParamsByWorkflowIds 批量获取指定工作流的输入输出参数
//
//	@alias=/GetParamsByWorkflowIds
func (s *UnimplementedTaskConfig) GetParamsByWorkflowIds(ctx context.Context, req *KEP_WF.GetParamsByWorkflowIdsReq) (*KEP_WF.GetParamsByWorkflowIdsRsp, error) {
	return nil, errors.New("rpc GetParamsByWorkflowIds of service TaskConfig is not implemented")
}

// GetWorkflowGuideViewed 获取新手引导是否已查看
//
//	@alias=/GetWorkflowGuideViewed
func (s *UnimplementedTaskConfig) GetWorkflowGuideViewed(ctx context.Context, req *KEP_WF.GetWorkflowGuideViewedRequest) (*KEP_WF.GetWorkflowGuideViewedResponse, error) {
	return nil, errors.New("rpc GetWorkflowGuideViewed of service TaskConfig is not implemented")
}

// MarkWorkflowGuideViewed 标记已查看工作流新手引导
//
//	@alias=/MarkWorkflowGuideViewed
func (s *UnimplementedTaskConfig) MarkWorkflowGuideViewed(ctx context.Context, req *KEP_WF.MarkWorkflowGuideViewedRequest) (*KEP_WF.MarkWorkflowGuideViewedResponse, error) {
	return nil, errors.New("rpc MarkWorkflowGuideViewed of service TaskConfig is not implemented")
}

// GetExperienceWorkflowList 获取体验中心下所有工作流模版列表
//
//	@alias=/GetExperienceWorkflowList
func (s *UnimplementedTaskConfig) GetExperienceWorkflowList(ctx context.Context, req *KEP_WF.GetExperienceWorkflowListReq) (*KEP_WF.GetExperienceWorkflowListRsp, error) {
	return nil, errors.New("rpc GetExperienceWorkflowList of service TaskConfig is not implemented")
}

// CreateExperienceWorkflow 创建体验中心指定应用下的工作流
//
//	@alias=/CreateExperienceWorkflow
func (s *UnimplementedTaskConfig) CreateExperienceWorkflow(ctx context.Context, req *KEP_WF.CreateExperienceWorkflowReq) (*KEP_WF.CreateExperienceWorkflowRsp, error) {
	return nil, errors.New("rpc CreateExperienceWorkflow of service TaskConfig is not implemented")
}

// CanCreateWorkflowRun ======================================== 异步工作流 ========================================================
//
//	判断是否能创建工作流运行实例
//	@alias=/CanCreateWorkflowRun
func (s *UnimplementedTaskConfig) CanCreateWorkflowRun(ctx context.Context, req *KEP_WF.CanCreateWorkflowRunReq) (*KEP_WF.CanCreateWorkflowRunRsp, error) {
	return nil, errors.New("rpc CanCreateWorkflowRun of service TaskConfig is not implemented")
}

// SaveAppDebugMode 保存应用的调试配置的调试模式
//
//	@alias=/SaveAppDebugMode
func (s *UnimplementedTaskConfig) SaveAppDebugMode(ctx context.Context, req *KEP_WF.SaveAppDebugModeReq) (*KEP_WF.SaveAppDebugModeRsp, error) {
	return nil, errors.New("rpc SaveAppDebugMode of service TaskConfig is not implemented")
}

// SaveAppDebugCustomVariables 保存应用的调试配置的AI参数的值
//
//	@alias=/SaveAppDebugCustomVariables
func (s *UnimplementedTaskConfig) SaveAppDebugCustomVariables(ctx context.Context, req *KEP_WF.SaveAppDebugCustomVariablesReq) (*KEP_WF.SaveAppDebugCustomVariablesRsp, error) {
	return nil, errors.New("rpc SaveAppDebugCustomVariables of service TaskConfig is not implemented")
}

// DescribeAppDebugConfig 查看应用的调试配置
//
//	@alias=/DescribeAppDebugConfig
func (s *UnimplementedTaskConfig) DescribeAppDebugConfig(ctx context.Context, req *KEP_WF.DescribeAppDebugConfigReq) (*KEP_WF.DescribeAppDebugConfigRsp, error) {
	return nil, errors.New("rpc DescribeAppDebugConfig of service TaskConfig is not implemented")
}

// CreateWorkflowRun 创建工作流运行实例
//
//	@alias=/CreateWorkflowRun
func (s *UnimplementedTaskConfig) CreateWorkflowRun(ctx context.Context, req *KEP_WF.CreateWorkflowRunReq) (*KEP_WF.CreateWorkflowRunRsp, error) {
	return nil, errors.New("rpc CreateWorkflowRun of service TaskConfig is not implemented")
}

// ListWorkflowRuns 查询工作流运行实例列表
//
//	@alias=/ListWorkflowRuns
func (s *UnimplementedTaskConfig) ListWorkflowRuns(ctx context.Context, req *KEP_WF.ListWorkflowRunsReq) (*KEP_WF.ListWorkflowRunsRsp, error) {
	return nil, errors.New("rpc ListWorkflowRuns of service TaskConfig is not implemented")
}

// DescribeWorkflowRun 查询工作流运行实例详情
//
//	@alias=/DescribeWorkflowRun
func (s *UnimplementedTaskConfig) DescribeWorkflowRun(ctx context.Context, req *KEP_WF.DescribeWorkflowRunReq) (*KEP_WF.DescribeWorkflowRunRsp, error) {
	return nil, errors.New("rpc DescribeWorkflowRun of service TaskConfig is not implemented")
}

// DescribeNodeRun 查看工作流节点运行详情
//
//	@alias=/DescribeNodeRun
func (s *UnimplementedTaskConfig) DescribeNodeRun(ctx context.Context, req *KEP_WF.DescribeNodeRunReq) (*KEP_WF.DescribeNodeRunRsp, error) {
	return nil, errors.New("rpc DescribeNodeRun of service TaskConfig is not implemented")
}

// StopWorkflowRun 停止工作流运行实例
//
//	@alias=/StopWorkflowRun
func (s *UnimplementedTaskConfig) StopWorkflowRun(ctx context.Context, req *KEP_WF.StopWorkflowRunReq) (*KEP_WF.StopWorkflowRunRsp, error) {
	return nil, errors.New("rpc StopWorkflowRun of service TaskConfig is not implemented")
}

// GetWorkflowListByDoc 批量获取文档被引用的工作流列表
//
//	@alias=/GetWorkflowListByDoc
func (s *UnimplementedTaskConfig) GetWorkflowListByDoc(ctx context.Context, req *KEP_WF.GetWorkflowListByDocReq) (*KEP_WF.GetWorkflowListByDocRsp, error) {
	return nil, errors.New("rpc GetWorkflowListByDoc of service TaskConfig is not implemented")
}

// GetWorkflowListByAttribute 批量获取标签被引用的工作流列表
//
//	@alias=/GetWorkflowListByAttribute
func (s *UnimplementedTaskConfig) GetWorkflowListByAttribute(ctx context.Context, req *KEP_WF.GetWorkflowListByAttributeReq) (*KEP_WF.GetWorkflowListByAttributeRsp, error) {
	return nil, errors.New("rpc GetWorkflowListByAttribute of service TaskConfig is not implemented")
}

// GetWorkflowListByAttributeLabel 批量获取标签值被引用的工作流列表
//
//	@alias=/GetWorkflowListByAttributeLabel
func (s *UnimplementedTaskConfig) GetWorkflowListByAttributeLabel(ctx context.Context, req *KEP_WF.GetWorkflowListByAttributeLabelReq) (*KEP_WF.GetWorkflowListByAttributeLabelRsp, error) {
	return nil, errors.New("rpc GetWorkflowListByAttributeLabel of service TaskConfig is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// TaskConfigClientProxy defines service client proxy
type TaskConfigClientProxy interface {
	// GetAppChatInputNum 获取应用可输入字符的数量
	//  @alias=/GetAppChatInputNum
	GetAppChatInputNum(ctx context.Context, req *GetAppChatInputNumReq, opts ...client.Option) (rsp *GetAppChatInputNumRsp, err error)
	// GetAppShareURL 获取分享链接的地址
	//  @alias=/GetAppShareURL
	GetAppShareURL(ctx context.Context, req *GetAppShareURLReq, opts ...client.Option) (rsp *GetAppShareURLResp, err error)
	// GetRobotIdByShareCode 通过分享码获取机器人信息请求
	//  @alias=/GetRobotIdByShareCode
	GetRobotIdByShareCode(ctx context.Context, req *GetRobotIdByShareCodeReq, opts ...client.Option) (rsp *GetRobotIdByShareCodeResp, err error)
	// GetModelSupportWorkflow 获取模型支持工作流的情况
	//  @alias=/GetModelSupportWorkflow
	GetModelSupportWorkflow(ctx context.Context, req *KEP_WF.GetModelSupportWorkflowReq, opts ...client.Option) (rsp *KEP_WF.GetModelSupportWorkflowResp, err error)
	// GetWorkflowReleaseStatus ======================= 发布任务 ==========================
	//  获取发布任务的状态（内部接口）
	//  @alias=/GetWorkflowReleaseStatus
	GetWorkflowReleaseStatus(ctx context.Context, req *KEP_WF.GetWorkflowReleaseStatusReq, opts ...client.Option) (rsp *KEP_WF.GetWorkflowReleaseStatusResp, err error)
	// GetTaskFlowReleaseStatus 获取发布任务的状态
	//  @alias=/GetTaskFlowReleaseStatus
	GetTaskFlowReleaseStatus(ctx context.Context, req *GetTaskFlowReleaseStatusReq, opts ...client.Option) (rsp *GetTaskFlowReleaseStatusResp, err error)
	// GetUnreleasedCount 获取未发布的数量
	//  @alias=/GetUnreleasedCount
	GetUnreleasedCount(ctx context.Context, req *GetUnreleasedCountReq, opts ...client.Option) (rsp *GetUnreleasedCountRsp, err error)
	// SendDataSyncTaskEvent 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
	//  @alias=/SendDataSyncTaskEvent
	SendDataSyncTaskEvent(ctx context.Context, req *SendDataSyncTaskEventReq, opts ...client.Option) (rsp *SendDataSyncTaskEventRsp, err error)
	// GetDataSyncTask [单个]获取同步任务, 详情, 状态等
	//  @alias=/GetDataSyncTask
	GetDataSyncTask(ctx context.Context, req *GetDataSyncTaskReq, opts ...client.Option) (rsp *GetDataSyncTaskRsp, err error)
	// GetDataSyncTasks [批量]获取同步任务, 详情, 状态等
	//  @alias=/GetDataSyncTasks
	GetDataSyncTasks(ctx context.Context, req *GetDataSyncTasksReq, opts ...client.Option) (rsp *GetDataSyncTasksRsp, err error)
	// CheckRobotReady 检查机器人, 是否准备好
	//  @alias=/CheckRobotReady
	CheckRobotReady(ctx context.Context, req *CheckRobotReadyReq, opts ...client.Option) (rsp *CheckRobotReadyRsp, err error)
	// ReleaseAPIVarParams 通知API参数发布
	//  @alias=/ReleaseAPIVarParams
	ReleaseAPIVarParams(ctx context.Context, req *ReleaseAPIVarParamsReq, opts ...client.Option) (rsp *ReleaseAPIVarParamsRsp, err error)
	// ListCategory 获取分类
	//  @alias=/ListCategory
	ListCategory(ctx context.Context, req *ListCategoryReq, opts ...client.Option) (rsp *ListCategoryRsp, err error)
	// CreateCategory 创建分类
	//  @alias=/CreateCategory
	CreateCategory(ctx context.Context, req *CreateCategoryReq, opts ...client.Option) (rsp *CreateCategoryRsp, err error)
	// UpdateCategory 分类修改
	//  @alias=/UpdateCategory
	UpdateCategory(ctx context.Context, req *UpdateCategoryReq, opts ...client.Option) (rsp *UpdateCategoryRsp, err error)
	// DeleteCategory 分类删除
	//  @alias=/DeleteCategory
	DeleteCategory(ctx context.Context, req *DeleteCategoryReq, opts ...client.Option) (rsp *DeleteCategoryRsp, err error)
	// RecoverHistoryTaskFlow ======================= 任务流 ==========================
	//  v2.1 恢复历史发布版本
	//  @alias=/RecoverHistoryTaskFlow
	RecoverHistoryTaskFlow(ctx context.Context, req *RecoverHistoryTaskFlowReq, opts ...client.Option) (rsp *RecoverHistoryTaskFlowRsp, err error)
	// ListHistoryTaskFlow v2.1 获取历史版本任务流程列表
	//  @alias=/ListHistoryTaskFlow
	ListHistoryTaskFlow(ctx context.Context, req *ListHistoryTaskFlowReq, opts ...client.Option) (rsp *ListHistoryTaskFlowRsp, err error)
	// DescribeHistoryTaskFlow v2.1 获取历史发布版本详情请
	//  @alias=/DescribeHistoryTaskFlow
	DescribeHistoryTaskFlow(ctx context.Context, req *DescribeHistoryTaskFlowReq, opts ...client.Option) (rsp *DescribeHistoryTaskFlowRsp, err error)
	// GroupTaskFlow 分组移动任务流到不同分类
	//  @alias=/GroupTaskFlow
	GroupTaskFlow(ctx context.Context, req *GroupTaskFlowReq, opts ...client.Option) (rsp *GroupTaskFlowRsp, err error)
	// ListTaskFlow 获取TaskFlow列表
	//  @alias=/ListTaskFlow
	ListTaskFlow(ctx context.Context, req *ListTaskFlowReq, opts ...client.Option) (rsp *ListTaskFlowRsp, err error)
	// GetTaskFlowDetail 获取TaskFlow详情
	//  @alias=/GetTaskFlowDetail
	GetTaskFlowDetail(ctx context.Context, req *GetTaskFlowDetailReq, opts ...client.Option) (rsp *GetTaskFlowDetailRsp, err error)
	// CreateTaskFlow 新建TaskFlow
	//  @alias=/CreateTaskFlow
	CreateTaskFlow(ctx context.Context, req *CreateTaskFlowReq, opts ...client.Option) (rsp *CreateTaskFlowRsp, err error)
	// SaveTaskFlow 编辑/保存TaskFlow
	//  @alias=/SaveTaskFlow
	SaveTaskFlow(ctx context.Context, req *SaveTaskFlowReq, opts ...client.Option) (rsp *SaveTaskFlowRsp, err error)
	// DeleteTaskFlow 删除TaskFlow
	//  @alias=/DeleteTaskFlow
	DeleteTaskFlow(ctx context.Context, req *DeleteTaskFlowReq, opts ...client.Option) (rsp *DeleteTaskFlowRsp, err error)
	// ListTaskFlowPreview 获取任务流程发布列表
	//  @alias=/ListTaskFlowPreview
	ListTaskFlowPreview(ctx context.Context, req *ListTaskFlowPreviewReq, opts ...client.Option) (rsp *ListTaskFlowPreviewRsp, err error)
	// ImportTaskFlow 导入TaskFlow
	//  @alias=/ImportTaskFlow
	ImportTaskFlow(ctx context.Context, req *ImportTaskFlowReq, opts ...client.Option) (rsp *ImportTaskFlowRsp, err error)
	// ExportTaskFlow 导出TaskFlow
	//  @alias=/ExportTaskFlow
	ExportTaskFlow(ctx context.Context, req *ExportTaskFlowReq, opts ...client.Option) (rsp *ExportTaskFlowRsp, err error)
	// InnerExportTaskFlow 内部平台导出TaskFlow
	//  @alias=/InnerExportTaskFlow
	InnerExportTaskFlow(ctx context.Context, req *InnerExportTaskFlowReq, opts ...client.Option) (rsp *InnerExportTaskFlowRsp, err error)
	// PreviewTaskFlowRequestNode 预览TaskFlow的询问节点
	//  @alias=/PreviewTaskFlowRequestNode
	PreviewTaskFlowRequestNode(ctx context.Context, req *PreviewTaskFlowRequestNodeReq, opts ...client.Option) (rsp *PreviewTaskFlowRequestNodeRsp, err error)
	// PreviewTaskFlowAnswerNode 预览TaskFlow的答案节点
	//  @alias=/PreviewTaskFlowAnswerNode
	PreviewTaskFlowAnswerNode(ctx context.Context, req *PreviewTaskFlowAnswerNodeReq, opts ...client.Option) (rsp *PreviewTaskFlowAnswerNodeRsp, err error)
	// PreviewAnswerNodeDocument 预览答案节点的知识文档
	//  @alias=/PreviewAnswerNodeDocument
	PreviewAnswerNodeDocument(ctx context.Context, req *PreviewAnswerNodeDocumentReq, opts ...client.Option) (rsp *PreviewAnswerNodeDocumentRsp, err error)
	// CopyTaskFlow 复制TaskFlow
	//  @alias=/CopyTaskFlow
	CopyTaskFlow(ctx context.Context, req *CopyTaskFlowReq, opts ...client.Option) (rsp *CopyTaskFlowRsp, err error)
	// GetSlotList ======================= 槽位、实体、词条 ==========================
	//  查询槽位列表
	//  @alias=/GetSlotList
	GetSlotList(ctx context.Context, req *GetSlotListReq, opts ...client.Option) (rsp *GetSlotListRsp, err error)
	// CreateSlot 新建槽位
	//  @alias=/CreateSlot
	CreateSlot(ctx context.Context, req *CreateSlotReq, opts ...client.Option) (rsp *CreateSlotRsp, err error)
	// UpdateSlot 编辑槽位
	//  @alias=/UpdateSlot
	UpdateSlot(ctx context.Context, req *UpdateSlotReq, opts ...client.Option) (rsp *UpdateSlotRsp, err error)
	// DeleteSlot 删除槽位
	//  @alias=/DeleteSlot
	DeleteSlot(ctx context.Context, req *DeleteSlotReq, opts ...client.Option) (rsp *DeleteSlotRsp, err error)
	// GetEntryList 查询词条列表
	//  @alias=/GetEntryList
	GetEntryList(ctx context.Context, req *GetEntryListReq, opts ...client.Option) (rsp *GetEntryListRsp, err error)
	// CreateEntry 新建词条
	//  @alias=/CreateEntry
	CreateEntry(ctx context.Context, req *CreateEntryReq, opts ...client.Option) (rsp *CreateEntryRsp, err error)
	// UpdateEntry 编辑词条
	//  @alias=/UpdateEntry
	UpdateEntry(ctx context.Context, req *UpdateEntryReq, opts ...client.Option) (rsp *UpdateEntryRsp, err error)
	// DeleteEntry 删除词条
	//  @alias=/DeleteEntry
	DeleteEntry(ctx context.Context, req *DeleteEntryReq, opts ...client.Option) (rsp *DeleteEntryRsp, err error)
	// ImportEntry 导入词条
	//  @alias=/ImportEntry
	ImportEntry(ctx context.Context, req *ImportEntryReq, opts ...client.Option) (rsp *ImportEntryRsp, err error)
	// RoleDefaultTemplateList 角色预设模版获取
	//  @alias=/RoleDefaultTemplateList
	RoleDefaultTemplateList(ctx context.Context, req *RoleDefaultTemplateListReq, opts ...client.Option) (rsp *RoleDefaultTemplateListRsp, err error)
	// CreateExample 创建示例问法
	//  @alias=/CreateExample
	CreateExample(ctx context.Context, req *CreateExampleReq, opts ...client.Option) (rsp *CreateExampleRsp, err error)
	// GetExampleList 获取示例问法列表
	//  @alias=/GetExampleList
	GetExampleList(ctx context.Context, req *GetExampleListReq, opts ...client.Option) (rsp *GetExampleListRsp, err error)
	// UpdateExample 更新示例问法
	//  @alias=/UpdateExample
	UpdateExample(ctx context.Context, req *UpdateExampleReq, opts ...client.Option) (rsp *UpdateExampleRsp, err error)
	// DeleteExample 删除示例问法
	//  @alias=/DeleteExample
	DeleteExample(ctx context.Context, req *DeleteExampleReq, opts ...client.Option) (rsp *DeleteExampleRsp, err error)
	// CreateVar =============== 参数变量的 增删改查 ===========================
	//  创建参数变量
	//  @alias=/CreateVar
	CreateVar(ctx context.Context, req *CreateVarReq, opts ...client.Option) (rsp *CreateVarRsp, err error)
	// UpdateVar 更新参数变量
	//  @alias=/UpdateVar
	UpdateVar(ctx context.Context, req *UpdateVarReq, opts ...client.Option) (rsp *UpdateVarRsp, err error)
	// DeleteVar 删除参数变量
	//  @alias=/DeleteVar
	DeleteVar(ctx context.Context, req *DeleteVarReq, opts ...client.Option) (rsp *DeleteVarRsp, err error)
	// DescribeVar 获取参数变量
	//  @alias=/DescribeVar
	DescribeVar(ctx context.Context, req *DescribeVarReq, opts ...client.Option) (rsp *DescribeVarRsp, err error)
	// GetVarList 获取参数变量列表
	//  @alias=/GetVarList
	GetVarList(ctx context.Context, req *GetVarListReq, opts ...client.Option) (rsp *GetVarListRsp, err error)
	// GetSystemVarList 获取系统变量
	//  @alias=/GetSystemVarList
	GetSystemVarList(ctx context.Context, req *GetSystemVarListReq, opts ...client.Option) (rsp *GetSystemVarListRsp, err error)
	// GetPromptWordTemplateList 获取提示词模版
	//  @alias=/GetPromptWordTemplateList
	GetPromptWordTemplateList(ctx context.Context, req *GetPromptWordTemplateListReq, opts ...client.Option) (rsp *GetPromptWordTemplateListRsp, err error)
	// CreateWorkflow 新建Workflow
	//  @alias=/CreateWorkflow
	CreateWorkflow(ctx context.Context, req *KEP_WF.CreateWorkflowReq, opts ...client.Option) (rsp *KEP_WF.CreateWorkflowRsp, err error)
	// SaveWorkflow 编辑/保存Workflow
	//  @alias=/SaveWorkflow
	SaveWorkflow(ctx context.Context, req *KEP_WF.SaveWorkflowReq, opts ...client.Option) (rsp *KEP_WF.SaveWorkflowRsp, err error)
	// CopyWorkflow 复制Workflow
	//  @alias=/CopyWorkflow
	CopyWorkflow(ctx context.Context, req *KEP_WF.CopyWorkflowReq, opts ...client.Option) (rsp *KEP_WF.CopyWorkflowRsp, err error)
	// DeleteWorkflow 删除Workflow
	//  @alias=/DeleteWorkflow
	DeleteWorkflow(ctx context.Context, req *KEP_WF.DeleteWorkflowReq, opts ...client.Option) (rsp *KEP_WF.DeleteWorkflowRsp, err error)
	// ListWorkflow 获取工作流列表
	//  @alias=/ListWorkflow
	ListWorkflow(ctx context.Context, req *KEP_WF.ListWorkflowReq, opts ...client.Option) (rsp *KEP_WF.ListWorkflowRsp, err error)
	// ListWorkflowInner 内部服务调用该接口，目前admin会调用
	//  @alias=/ListWorkflowInner
	ListWorkflowInner(ctx context.Context, req *KEP_WF.ListWorkflowInnerReq, opts ...client.Option) (rsp *KEP_WF.ListWorkflowInnerRsp, err error)
	// GetWorkflowDetail 获取某个工作流具体信息
	//  @alias=/GetWorkflowDetail
	GetWorkflowDetail(ctx context.Context, req *KEP_WF.GetWorkflowDetailReq, opts ...client.Option) (rsp *KEP_WF.GetWorkflowDetailResp, err error)
	// ListNodeInfo 获取工作流的节点信息
	//  @alias=/ListNodeInfo
	ListNodeInfo(ctx context.Context, req *KEP_WF.ListNodeInfoReq, opts ...client.Option) (rsp *KEP_WF.ListNodeInfoRsp, err error)
	// ListWorkflowNodeModel 获取支持工作流节点模型的信息
	//  @alias=/ListWorkflowNodeModel
	ListWorkflowNodeModel(ctx context.Context, req *KEP_WF.ListWorkflowNodeModelReq, opts ...client.Option) (rsp *KEP_WF.ListWorkflowNodeModelRsp, err error)
	// ListWorkflowInfoByModelNameInner 删除前校验模型被哪些工作流引用 (内部使用，admin删除模型时使用)
	//  @alias=/ListWorkflowInfoByModelNameInner
	ListWorkflowInfoByModelNameInner(ctx context.Context, req *KEP_WF.ListWorkflowInfoByModelNameReq, opts ...client.Option) (rsp *KEP_WF.ListWorkflowInfoByModelNameRsp, err error)
	// SaveAgentWorkflow ============== Agent工作流 ========================
	//  编辑/保存AgentWorkflow
	//  @alias=/SaveAgentWorkflow
	SaveAgentWorkflow(ctx context.Context, req *KEP_WF.SaveAgentWorkflowReq, opts ...client.Option) (rsp *KEP_WF.SaveAgentWorkflowRsp, err error)
	// ListAgentWorkflow 获取Agent工作流列表
	//  @alias=/ListAgentWorkflow
	ListAgentWorkflow(ctx context.Context, req *KEP_WF.ListAgentWorkflowReq, opts ...client.Option) (rsp *KEP_WF.ListAgentWorkflowRsp, err error)
	// GetAgentWorkflowInfo 内部服务调用该接口，目前主会话会调用
	//  @alias=/GetAgentWorkflowInfo
	GetAgentWorkflowInfo(ctx context.Context, req *KEP_WF.GetAgentWorkflowInfoReq, opts ...client.Option) (rsp *KEP_WF.GetAgentWorkflowInfoRsp, err error)
	// GetAgentWorkflowDetail 获取某个Agent工作流具体信息
	//  @alias=/GetAgentWorkflowDetail
	GetAgentWorkflowDetail(ctx context.Context, req *KEP_WF.GetAgentWorkflowDetailReq, opts ...client.Option) (rsp *KEP_WF.GetAgentWorkflowDetailRsp, err error)
	// GetAgentWorkflowState 获取某个Agent工作流状态
	//  @alias=/GetAgentWorkflowState
	GetAgentWorkflowState(ctx context.Context, req *KEP_WF.GetAgentWorkflowStateReq, opts ...client.Option) (rsp *KEP_WF.GetAgentWorkflowStateRsp, err error)
	// ConvertToAgentWorkflow 工作流画布转换成PDL
	//  @alias=/ConvertToAgentWorkflow
	ConvertToAgentWorkflow(ctx context.Context, req *KEP_WF.ConvertToAgentWorkflowReq, opts ...client.Option) (rsp *KEP_WF.ConvertToAgentWorkflowRsp, err error)
	// SwitchAgentWorkflowState 是否开启Agent工作流
	//  @alias=/SwitchAgentWorkflowState
	SwitchAgentWorkflowState(ctx context.Context, req *KEP_WF.SwitchAgentWorkflowStateReq, opts ...client.Option) (rsp *KEP_WF.SwitchAgentWorkflowStateRsp, err error)
	// ListPDLVersion 获取工作流PDL版本列表
	//  @alias=/ListPDLVersion
	ListPDLVersion(ctx context.Context, req *KEP_WF.ListPDLVersionReq, opts ...client.Option) (rsp *KEP_WF.ListPDLVersionRsp, err error)
	// GetPDLVersionDetail 获取工作流PDL版本详情
	//  @alias=/GetPDLVersionDetail
	GetPDLVersionDetail(ctx context.Context, req *KEP_WF.GetPDLVersionDetailReq, opts ...client.Option) (rsp *KEP_WF.GetPDLVersionDetailRsp, err error)
	// CreateWorkflowExample ============== 工作流的示例问法 ========================
	//  创建示例问法
	//  @alias=/CreateWorkflowExample
	CreateWorkflowExample(ctx context.Context, req *KEP_WF.CreateWorkflowExampleReq, opts ...client.Option) (rsp *KEP_WF.CreateWorkflowExampleRsp, err error)
	// ListWorkflowExample 获取示例问法列表
	//  @alias=/ListWorkflowExample
	ListWorkflowExample(ctx context.Context, req *KEP_WF.ListWorkflowExampleReq, opts ...client.Option) (rsp *KEP_WF.ListWorkflowExampleRsp, err error)
	// UpdateWorkflowExample 更新示例问法
	//  @alias=/UpdateWorkflowExample
	UpdateWorkflowExample(ctx context.Context, req *KEP_WF.UpdateWorkflowExampleReq, opts ...client.Option) (rsp *KEP_WF.UpdateWorkflowExampleRsp, err error)
	// DeleteWorkflowExample 删除示例问法
	//  @alias=/DeleteWorkflowExample
	DeleteWorkflowExample(ctx context.Context, req *KEP_WF.DeleteWorkflowExampleReq, opts ...client.Option) (rsp *KEP_WF.DeleteWorkflowExampleRsp, err error)
	// ImportWorkflowExample 导入工作流示例问法
	//  @alias=/ImportWorkflowExample
	ImportWorkflowExample(ctx context.Context, req *KEP_WF.ImportWfExampleReq, opts ...client.Option) (rsp *KEP_WF.ImportWfExampleRsp, err error)
	// SwitchWorkflowState 是否开启工作流
	//  @alias=/SwitchWorkflowState
	SwitchWorkflowState(ctx context.Context, req *KEP_WF.SwitchWorkflowStateReq, opts ...client.Option) (rsp *KEP_WF.SwitchWorkflowStateRsp, err error)
	// AddWorkflowFeedback 添加工作流反馈
	//  @alias=/AddWorkflowFeedback
	AddWorkflowFeedback(ctx context.Context, req *KEP_WF.AddFlowFeedbackReq, opts ...client.Option) (rsp *KEP_WF.AddFlowFeedbackRsp, err error)
	// UpdateWorkflowFeedback 修改工作流反馈
	//  @alias=/UpdateWorkflowFeedback
	UpdateWorkflowFeedback(ctx context.Context, req *KEP_WF.UpdateFlowFeedbackReq, opts ...client.Option) (rsp *KEP_WF.UpdateFlowFeedbackRsp, err error)
	// DescribeWorkflowFeedback 获取工作流详情
	//  @alias=/DescribeWorkflowFeedback
	DescribeWorkflowFeedback(ctx context.Context, req *KEP_WF.DescribeWorkflowFeedReq, opts ...client.Option) (rsp *KEP_WF.DescribeWorkflowFeedRsp, err error)
	// DeleteWorkflowFeedback 删除反馈
	//  @alias=/DeleteWorkflowFeedback
	DeleteWorkflowFeedback(ctx context.Context, req *KEP_WF.DeleteWorkflowFeedbackReq, opts ...client.Option) (rsp *KEP_WF.DeleteWorkflowFeedbackRsp, err error)
	// ListWorkflowFeedback 查询反馈信息列表
	//  @alias=/ListWorkflowFeedback
	ListWorkflowFeedback(ctx context.Context, req *KEP_WF.ListFlowFeedbackReq, opts ...client.Option) (rsp *KEP_WF.ListFlowFeedbackRsp, err error)
	// UpdateWorkflowFeedbackStatus ================= 内部(op)接口调用  start =====
	//  UpdateWorkflowFeedbackStatus 修改 workflow 反馈信息状态
	//  @alias=/UpdateWorkflowFeedbackStatus
	UpdateWorkflowFeedbackStatus(ctx context.Context, req *KEP_WF.UpdateWorkflowFeedbackStatusReq, opts ...client.Option) (rsp *KEP_WF.UpdateWorkflowFeedbackStatusRsp, err error)
	// UpdateWorkflowFeedbackTapd UpdateWorkflowFeedbackTapd 修改 workflow 反馈信息关联的tapd
	//  @alias=/UpdateWorkflowFeedbackTapd
	UpdateWorkflowFeedbackTapd(ctx context.Context, req *KEP_WF.UpdateWorkflowFeedbackTapdReq, opts ...client.Option) (rsp *KEP_WF.UpdateWorkflowFeedbackTapdRsp, err error) // ================= 内部(op)接口调用  end =====
	// ImportWorkflow ==================== 工作流导入导出 ================
	//  导入Workflow
	//  @alias=/ImportWorkflow
	ImportWorkflow(ctx context.Context, req *KEP_WF.ImportWorkflowReq, opts ...client.Option) (rsp *KEP_WF.ImportWorkflowRsp, err error)
	// ExportWorkflow 导出Workflow
	//  @alias=/ExportWorkflow
	ExportWorkflow(ctx context.Context, req *KEP_WF.ExportWorkflowReq, opts ...client.Option) (rsp *KEP_WF.ExportWorkflowRsp, err error)
	// DebugWorkflowNode ==================== 工作流节点调试 ================
	//  调试Workflow节点
	//  @alias=/DebugWorkflowNode
	DebugWorkflowNode(ctx context.Context, req *KEP_WF.DebugWorkflowNodeReq, opts ...client.Option) (rsp *KEP_WF.DebugWorkflowNodeRsp, err error)
	// GetEnableCustomAsk ============== 工作流的参数提取 ========================
	//  创建参数获取是否开启了添加自定义询问话术的白名单
	//  @alias=/GetEnableCustomAsk
	GetEnableCustomAsk(ctx context.Context, req *KEP_WF.GetEnableCustomAskReq, opts ...client.Option) (rsp *KEP_WF.GetEnableCustomAskResp, err error)
	// CreateParameter 创建参数
	//  @alias=/CreateParameter
	CreateParameter(ctx context.Context, req *KEP_WF.CreateParameterReq, opts ...client.Option) (rsp *KEP_WF.CreateParameterResp, err error)
	// GetParameterList 获取参数列表
	//  @alias=/GetParameterList
	GetParameterList(ctx context.Context, req *KEP_WF.GetParameterListReq, opts ...client.Option) (rsp *KEP_WF.GetParameterListResp, err error)
	// UpdateParameter 更新参数信息
	//  @alias=/UpdateParameter
	UpdateParameter(ctx context.Context, req *KEP_WF.UpdateParameterReq, opts ...client.Option) (rsp *KEP_WF.UpdateParameterResp, err error)
	// DeleteParameter 删除参数
	//  @alias=/DeleteParameter
	DeleteParameter(ctx context.Context, req *KEP_WF.DeleteParameterReq, opts ...client.Option) (rsp *KEP_WF.DeleteParameterResp, err error)
	// GetBotNodeParameterList 获取应用下工作流节点参数信息
	//  @alias=/GetBotNodeParameterList
	GetBotNodeParameterList(ctx context.Context, req *KEP_WF.GetBotNodeParameterListReq, opts ...client.Option) (rsp *KEP_WF.GetBotNodeParameterListResp, err error)
	// ClearAppFlowResource 清理任务/工作流 资源
	//  @alias=/ClearAppFlowResource
	ClearAppFlowResource(ctx context.Context, req *KEP_WF.ClearAppFlowResourceReq, opts ...client.Option) (rsp *KEP_WF.ClearAppFlowResourceRsp, err error)
	// DeleteAppResource 删除应用下面的 资源
	//  @alias=/DeleteAppResource
	DeleteAppResource(ctx context.Context, req *KEP_WF.DeleteAppResourceReq, opts ...client.Option) (rsp *KEP_WF.DeleteAppResourceRsp, err error)
	// GetCanBeReferencedWorkflowList 获取可以被引用的工作流的列表
	//  @alias=/GetCanBeReferencedWorkflowList
	GetCanBeReferencedWorkflowList(ctx context.Context, req *KEP_WF.GetCanBeReferencedWorkflowListReq, opts ...client.Option) (rsp *KEP_WF.GetCanBeReferencedWorkflowListRsp, err error)
	// GetHasBeenReferencedWorkflowList 批量获取指定工作流已经被引用的工作流的列表
	//  @alias=/GetHasBeenReferencedWorkflowList
	GetHasBeenReferencedWorkflowList(ctx context.Context, req *KEP_WF.GetHasBeenReferencedWorkflowListReq, opts ...client.Option) (rsp *KEP_WF.GetHasBeenReferencedWorkflowListRsp, err error)
	// GetHasReferencedPluginToolWorkflowList 获取已经引用指定插件工具的工作流的列表
	//  @alias=/GetHasReferencedPluginToolWorkflowList
	GetHasReferencedPluginToolWorkflowList(ctx context.Context, req *KEP_WF.GetHasReferencedPluginToolWorkflowListReq, opts ...client.Option) (rsp *KEP_WF.GetHasReferencedPluginToolWorkflowListRsp, err error)
	// GetParamsByWorkflowIds 批量获取指定工作流的输入输出参数
	//  @alias=/GetParamsByWorkflowIds
	GetParamsByWorkflowIds(ctx context.Context, req *KEP_WF.GetParamsByWorkflowIdsReq, opts ...client.Option) (rsp *KEP_WF.GetParamsByWorkflowIdsRsp, err error)
	// GetWorkflowGuideViewed 获取新手引导是否已查看
	//  @alias=/GetWorkflowGuideViewed
	GetWorkflowGuideViewed(ctx context.Context, req *KEP_WF.GetWorkflowGuideViewedRequest, opts ...client.Option) (rsp *KEP_WF.GetWorkflowGuideViewedResponse, err error)
	// MarkWorkflowGuideViewed 标记已查看工作流新手引导
	//  @alias=/MarkWorkflowGuideViewed
	MarkWorkflowGuideViewed(ctx context.Context, req *KEP_WF.MarkWorkflowGuideViewedRequest, opts ...client.Option) (rsp *KEP_WF.MarkWorkflowGuideViewedResponse, err error)
	// GetExperienceWorkflowList 获取体验中心下所有工作流模版列表
	//  @alias=/GetExperienceWorkflowList
	GetExperienceWorkflowList(ctx context.Context, req *KEP_WF.GetExperienceWorkflowListReq, opts ...client.Option) (rsp *KEP_WF.GetExperienceWorkflowListRsp, err error)
	// CreateExperienceWorkflow 创建体验中心指定应用下的工作流
	//  @alias=/CreateExperienceWorkflow
	CreateExperienceWorkflow(ctx context.Context, req *KEP_WF.CreateExperienceWorkflowReq, opts ...client.Option) (rsp *KEP_WF.CreateExperienceWorkflowRsp, err error)
	// CanCreateWorkflowRun ======================================== 异步工作流 ========================================================
	//  判断是否能创建工作流运行实例
	//  @alias=/CanCreateWorkflowRun
	CanCreateWorkflowRun(ctx context.Context, req *KEP_WF.CanCreateWorkflowRunReq, opts ...client.Option) (rsp *KEP_WF.CanCreateWorkflowRunRsp, err error)
	// SaveAppDebugMode 保存应用的调试配置的调试模式
	//  @alias=/SaveAppDebugMode
	SaveAppDebugMode(ctx context.Context, req *KEP_WF.SaveAppDebugModeReq, opts ...client.Option) (rsp *KEP_WF.SaveAppDebugModeRsp, err error)
	// SaveAppDebugCustomVariables 保存应用的调试配置的AI参数的值
	//  @alias=/SaveAppDebugCustomVariables
	SaveAppDebugCustomVariables(ctx context.Context, req *KEP_WF.SaveAppDebugCustomVariablesReq, opts ...client.Option) (rsp *KEP_WF.SaveAppDebugCustomVariablesRsp, err error)
	// DescribeAppDebugConfig 查看应用的调试配置
	//  @alias=/DescribeAppDebugConfig
	DescribeAppDebugConfig(ctx context.Context, req *KEP_WF.DescribeAppDebugConfigReq, opts ...client.Option) (rsp *KEP_WF.DescribeAppDebugConfigRsp, err error)
	// CreateWorkflowRun 创建工作流运行实例
	//  @alias=/CreateWorkflowRun
	CreateWorkflowRun(ctx context.Context, req *KEP_WF.CreateWorkflowRunReq, opts ...client.Option) (rsp *KEP_WF.CreateWorkflowRunRsp, err error)
	// ListWorkflowRuns 查询工作流运行实例列表
	//  @alias=/ListWorkflowRuns
	ListWorkflowRuns(ctx context.Context, req *KEP_WF.ListWorkflowRunsReq, opts ...client.Option) (rsp *KEP_WF.ListWorkflowRunsRsp, err error)
	// DescribeWorkflowRun 查询工作流运行实例详情
	//  @alias=/DescribeWorkflowRun
	DescribeWorkflowRun(ctx context.Context, req *KEP_WF.DescribeWorkflowRunReq, opts ...client.Option) (rsp *KEP_WF.DescribeWorkflowRunRsp, err error)
	// DescribeNodeRun 查看工作流节点运行详情
	//  @alias=/DescribeNodeRun
	DescribeNodeRun(ctx context.Context, req *KEP_WF.DescribeNodeRunReq, opts ...client.Option) (rsp *KEP_WF.DescribeNodeRunRsp, err error)
	// StopWorkflowRun 停止工作流运行实例
	//  @alias=/StopWorkflowRun
	StopWorkflowRun(ctx context.Context, req *KEP_WF.StopWorkflowRunReq, opts ...client.Option) (rsp *KEP_WF.StopWorkflowRunRsp, err error) // ======================================== 异步工作流 ========================================================
	// GetWorkflowListByDoc 批量获取文档被引用的工作流列表
	//  @alias=/GetWorkflowListByDoc
	GetWorkflowListByDoc(ctx context.Context, req *KEP_WF.GetWorkflowListByDocReq, opts ...client.Option) (rsp *KEP_WF.GetWorkflowListByDocRsp, err error)
	// GetWorkflowListByAttribute 批量获取标签被引用的工作流列表
	//  @alias=/GetWorkflowListByAttribute
	GetWorkflowListByAttribute(ctx context.Context, req *KEP_WF.GetWorkflowListByAttributeReq, opts ...client.Option) (rsp *KEP_WF.GetWorkflowListByAttributeRsp, err error)
	// GetWorkflowListByAttributeLabel 批量获取标签值被引用的工作流列表
	//  @alias=/GetWorkflowListByAttributeLabel
	GetWorkflowListByAttributeLabel(ctx context.Context, req *KEP_WF.GetWorkflowListByAttributeLabelReq, opts ...client.Option) (rsp *KEP_WF.GetWorkflowListByAttributeLabelRsp, err error)
}

type TaskConfigClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewTaskConfigClientProxy = func(opts ...client.Option) TaskConfigClientProxy {
	return &TaskConfigClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *TaskConfigClientProxyImpl) GetAppChatInputNum(ctx context.Context, req *GetAppChatInputNumReq, opts ...client.Option) (*GetAppChatInputNumRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetAppChatInputNum")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetAppChatInputNum")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAppChatInputNumRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetAppShareURL(ctx context.Context, req *GetAppShareURLReq, opts ...client.Option) (*GetAppShareURLResp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetAppShareURL")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetAppShareURL")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAppShareURLResp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetRobotIdByShareCode(ctx context.Context, req *GetRobotIdByShareCodeReq, opts ...client.Option) (*GetRobotIdByShareCodeResp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetRobotIdByShareCode")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetRobotIdByShareCode")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetRobotIdByShareCodeResp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetModelSupportWorkflow(ctx context.Context, req *KEP_WF.GetModelSupportWorkflowReq, opts ...client.Option) (*KEP_WF.GetModelSupportWorkflowResp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetModelSupportWorkflow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetModelSupportWorkflow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetModelSupportWorkflowResp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetWorkflowReleaseStatus(ctx context.Context, req *KEP_WF.GetWorkflowReleaseStatusReq, opts ...client.Option) (*KEP_WF.GetWorkflowReleaseStatusResp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetWorkflowReleaseStatus")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetWorkflowReleaseStatus")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetWorkflowReleaseStatusResp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetTaskFlowReleaseStatus(ctx context.Context, req *GetTaskFlowReleaseStatusReq, opts ...client.Option) (*GetTaskFlowReleaseStatusResp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetTaskFlowReleaseStatus")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetTaskFlowReleaseStatus")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetTaskFlowReleaseStatusResp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetUnreleasedCount(ctx context.Context, req *GetUnreleasedCountReq, opts ...client.Option) (*GetUnreleasedCountRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetUnreleasedCount")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetUnreleasedCount")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetUnreleasedCountRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) SendDataSyncTaskEvent(ctx context.Context, req *SendDataSyncTaskEventReq, opts ...client.Option) (*SendDataSyncTaskEventRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SendDataSyncTaskEvent")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("SendDataSyncTaskEvent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SendDataSyncTaskEventRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetDataSyncTask(ctx context.Context, req *GetDataSyncTaskReq, opts ...client.Option) (*GetDataSyncTaskRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetDataSyncTask")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetDataSyncTask")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetDataSyncTaskRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetDataSyncTasks(ctx context.Context, req *GetDataSyncTasksReq, opts ...client.Option) (*GetDataSyncTasksRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetDataSyncTasks")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetDataSyncTasks")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetDataSyncTasksRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CheckRobotReady(ctx context.Context, req *CheckRobotReadyReq, opts ...client.Option) (*CheckRobotReadyRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CheckRobotReady")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CheckRobotReady")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckRobotReadyRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ReleaseAPIVarParams(ctx context.Context, req *ReleaseAPIVarParamsReq, opts ...client.Option) (*ReleaseAPIVarParamsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ReleaseAPIVarParams")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ReleaseAPIVarParams")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ReleaseAPIVarParamsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListCategory(ctx context.Context, req *ListCategoryReq, opts ...client.Option) (*ListCategoryRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListCategory")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListCategory")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListCategoryRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CreateCategory(ctx context.Context, req *CreateCategoryReq, opts ...client.Option) (*CreateCategoryRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateCategory")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CreateCategory")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateCategoryRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) UpdateCategory(ctx context.Context, req *UpdateCategoryReq, opts ...client.Option) (*UpdateCategoryRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/UpdateCategory")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("UpdateCategory")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateCategoryRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DeleteCategory(ctx context.Context, req *DeleteCategoryReq, opts ...client.Option) (*DeleteCategoryRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeleteCategory")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DeleteCategory")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteCategoryRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) RecoverHistoryTaskFlow(ctx context.Context, req *RecoverHistoryTaskFlowReq, opts ...client.Option) (*RecoverHistoryTaskFlowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/RecoverHistoryTaskFlow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("RecoverHistoryTaskFlow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RecoverHistoryTaskFlowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListHistoryTaskFlow(ctx context.Context, req *ListHistoryTaskFlowReq, opts ...client.Option) (*ListHistoryTaskFlowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListHistoryTaskFlow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListHistoryTaskFlow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListHistoryTaskFlowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DescribeHistoryTaskFlow(ctx context.Context, req *DescribeHistoryTaskFlowReq, opts ...client.Option) (*DescribeHistoryTaskFlowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeHistoryTaskFlow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DescribeHistoryTaskFlow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeHistoryTaskFlowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GroupTaskFlow(ctx context.Context, req *GroupTaskFlowReq, opts ...client.Option) (*GroupTaskFlowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GroupTaskFlow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GroupTaskFlow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GroupTaskFlowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListTaskFlow(ctx context.Context, req *ListTaskFlowReq, opts ...client.Option) (*ListTaskFlowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListTaskFlow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListTaskFlow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListTaskFlowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetTaskFlowDetail(ctx context.Context, req *GetTaskFlowDetailReq, opts ...client.Option) (*GetTaskFlowDetailRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetTaskFlowDetail")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetTaskFlowDetail")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetTaskFlowDetailRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CreateTaskFlow(ctx context.Context, req *CreateTaskFlowReq, opts ...client.Option) (*CreateTaskFlowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateTaskFlow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CreateTaskFlow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateTaskFlowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) SaveTaskFlow(ctx context.Context, req *SaveTaskFlowReq, opts ...client.Option) (*SaveTaskFlowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SaveTaskFlow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("SaveTaskFlow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SaveTaskFlowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DeleteTaskFlow(ctx context.Context, req *DeleteTaskFlowReq, opts ...client.Option) (*DeleteTaskFlowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeleteTaskFlow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DeleteTaskFlow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteTaskFlowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListTaskFlowPreview(ctx context.Context, req *ListTaskFlowPreviewReq, opts ...client.Option) (*ListTaskFlowPreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListTaskFlowPreview")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListTaskFlowPreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListTaskFlowPreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ImportTaskFlow(ctx context.Context, req *ImportTaskFlowReq, opts ...client.Option) (*ImportTaskFlowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ImportTaskFlow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ImportTaskFlow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ImportTaskFlowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ExportTaskFlow(ctx context.Context, req *ExportTaskFlowReq, opts ...client.Option) (*ExportTaskFlowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ExportTaskFlow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ExportTaskFlow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ExportTaskFlowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) InnerExportTaskFlow(ctx context.Context, req *InnerExportTaskFlowReq, opts ...client.Option) (*InnerExportTaskFlowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/InnerExportTaskFlow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("InnerExportTaskFlow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &InnerExportTaskFlowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) PreviewTaskFlowRequestNode(ctx context.Context, req *PreviewTaskFlowRequestNodeReq, opts ...client.Option) (*PreviewTaskFlowRequestNodeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/PreviewTaskFlowRequestNode")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("PreviewTaskFlowRequestNode")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &PreviewTaskFlowRequestNodeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) PreviewTaskFlowAnswerNode(ctx context.Context, req *PreviewTaskFlowAnswerNodeReq, opts ...client.Option) (*PreviewTaskFlowAnswerNodeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/PreviewTaskFlowAnswerNode")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("PreviewTaskFlowAnswerNode")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &PreviewTaskFlowAnswerNodeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) PreviewAnswerNodeDocument(ctx context.Context, req *PreviewAnswerNodeDocumentReq, opts ...client.Option) (*PreviewAnswerNodeDocumentRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/PreviewAnswerNodeDocument")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("PreviewAnswerNodeDocument")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &PreviewAnswerNodeDocumentRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CopyTaskFlow(ctx context.Context, req *CopyTaskFlowReq, opts ...client.Option) (*CopyTaskFlowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CopyTaskFlow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CopyTaskFlow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CopyTaskFlowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetSlotList(ctx context.Context, req *GetSlotListReq, opts ...client.Option) (*GetSlotListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetSlotList")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetSlotList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetSlotListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CreateSlot(ctx context.Context, req *CreateSlotReq, opts ...client.Option) (*CreateSlotRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateSlot")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CreateSlot")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateSlotRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) UpdateSlot(ctx context.Context, req *UpdateSlotReq, opts ...client.Option) (*UpdateSlotRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/UpdateSlot")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("UpdateSlot")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateSlotRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DeleteSlot(ctx context.Context, req *DeleteSlotReq, opts ...client.Option) (*DeleteSlotRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeleteSlot")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DeleteSlot")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteSlotRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetEntryList(ctx context.Context, req *GetEntryListReq, opts ...client.Option) (*GetEntryListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetEntryList")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetEntryList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetEntryListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CreateEntry(ctx context.Context, req *CreateEntryReq, opts ...client.Option) (*CreateEntryRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateEntry")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CreateEntry")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateEntryRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) UpdateEntry(ctx context.Context, req *UpdateEntryReq, opts ...client.Option) (*UpdateEntryRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/UpdateEntry")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("UpdateEntry")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateEntryRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DeleteEntry(ctx context.Context, req *DeleteEntryReq, opts ...client.Option) (*DeleteEntryRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeleteEntry")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DeleteEntry")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteEntryRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ImportEntry(ctx context.Context, req *ImportEntryReq, opts ...client.Option) (*ImportEntryRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ImportEntry")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ImportEntry")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ImportEntryRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) RoleDefaultTemplateList(ctx context.Context, req *RoleDefaultTemplateListReq, opts ...client.Option) (*RoleDefaultTemplateListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/RoleDefaultTemplateList")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("RoleDefaultTemplateList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RoleDefaultTemplateListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CreateExample(ctx context.Context, req *CreateExampleReq, opts ...client.Option) (*CreateExampleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateExample")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CreateExample")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateExampleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetExampleList(ctx context.Context, req *GetExampleListReq, opts ...client.Option) (*GetExampleListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetExampleList")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetExampleList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetExampleListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) UpdateExample(ctx context.Context, req *UpdateExampleReq, opts ...client.Option) (*UpdateExampleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/UpdateExample")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("UpdateExample")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateExampleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DeleteExample(ctx context.Context, req *DeleteExampleReq, opts ...client.Option) (*DeleteExampleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeleteExample")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DeleteExample")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteExampleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CreateVar(ctx context.Context, req *CreateVarReq, opts ...client.Option) (*CreateVarRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateVar")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CreateVar")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateVarRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) UpdateVar(ctx context.Context, req *UpdateVarReq, opts ...client.Option) (*UpdateVarRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/UpdateVar")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("UpdateVar")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpdateVarRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DeleteVar(ctx context.Context, req *DeleteVarReq, opts ...client.Option) (*DeleteVarRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeleteVar")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DeleteVar")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteVarRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DescribeVar(ctx context.Context, req *DescribeVarReq, opts ...client.Option) (*DescribeVarRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeVar")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DescribeVar")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeVarRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetVarList(ctx context.Context, req *GetVarListReq, opts ...client.Option) (*GetVarListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetVarList")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetVarList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetVarListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetSystemVarList(ctx context.Context, req *GetSystemVarListReq, opts ...client.Option) (*GetSystemVarListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetSystemVarList")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetSystemVarList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetSystemVarListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetPromptWordTemplateList(ctx context.Context, req *GetPromptWordTemplateListReq, opts ...client.Option) (*GetPromptWordTemplateListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetPromptWordTemplateList")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetPromptWordTemplateList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetPromptWordTemplateListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CreateWorkflow(ctx context.Context, req *KEP_WF.CreateWorkflowReq, opts ...client.Option) (*KEP_WF.CreateWorkflowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateWorkflow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CreateWorkflow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.CreateWorkflowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) SaveWorkflow(ctx context.Context, req *KEP_WF.SaveWorkflowReq, opts ...client.Option) (*KEP_WF.SaveWorkflowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SaveWorkflow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("SaveWorkflow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.SaveWorkflowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CopyWorkflow(ctx context.Context, req *KEP_WF.CopyWorkflowReq, opts ...client.Option) (*KEP_WF.CopyWorkflowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CopyWorkflow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CopyWorkflow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.CopyWorkflowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DeleteWorkflow(ctx context.Context, req *KEP_WF.DeleteWorkflowReq, opts ...client.Option) (*KEP_WF.DeleteWorkflowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeleteWorkflow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DeleteWorkflow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.DeleteWorkflowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListWorkflow(ctx context.Context, req *KEP_WF.ListWorkflowReq, opts ...client.Option) (*KEP_WF.ListWorkflowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListWorkflow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListWorkflow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ListWorkflowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListWorkflowInner(ctx context.Context, req *KEP_WF.ListWorkflowInnerReq, opts ...client.Option) (*KEP_WF.ListWorkflowInnerRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListWorkflowInner")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListWorkflowInner")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ListWorkflowInnerRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetWorkflowDetail(ctx context.Context, req *KEP_WF.GetWorkflowDetailReq, opts ...client.Option) (*KEP_WF.GetWorkflowDetailResp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetWorkflowDetail")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetWorkflowDetail")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetWorkflowDetailResp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListNodeInfo(ctx context.Context, req *KEP_WF.ListNodeInfoReq, opts ...client.Option) (*KEP_WF.ListNodeInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListNodeInfo")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListNodeInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ListNodeInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListWorkflowNodeModel(ctx context.Context, req *KEP_WF.ListWorkflowNodeModelReq, opts ...client.Option) (*KEP_WF.ListWorkflowNodeModelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListWorkflowNodeModel")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListWorkflowNodeModel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ListWorkflowNodeModelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListWorkflowInfoByModelNameInner(ctx context.Context, req *KEP_WF.ListWorkflowInfoByModelNameReq, opts ...client.Option) (*KEP_WF.ListWorkflowInfoByModelNameRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListWorkflowInfoByModelNameInner")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListWorkflowInfoByModelNameInner")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ListWorkflowInfoByModelNameRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) SaveAgentWorkflow(ctx context.Context, req *KEP_WF.SaveAgentWorkflowReq, opts ...client.Option) (*KEP_WF.SaveAgentWorkflowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SaveAgentWorkflow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("SaveAgentWorkflow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.SaveAgentWorkflowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListAgentWorkflow(ctx context.Context, req *KEP_WF.ListAgentWorkflowReq, opts ...client.Option) (*KEP_WF.ListAgentWorkflowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListAgentWorkflow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListAgentWorkflow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ListAgentWorkflowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetAgentWorkflowInfo(ctx context.Context, req *KEP_WF.GetAgentWorkflowInfoReq, opts ...client.Option) (*KEP_WF.GetAgentWorkflowInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetAgentWorkflowInfo")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetAgentWorkflowInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetAgentWorkflowInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetAgentWorkflowDetail(ctx context.Context, req *KEP_WF.GetAgentWorkflowDetailReq, opts ...client.Option) (*KEP_WF.GetAgentWorkflowDetailRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetAgentWorkflowDetail")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetAgentWorkflowDetail")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetAgentWorkflowDetailRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetAgentWorkflowState(ctx context.Context, req *KEP_WF.GetAgentWorkflowStateReq, opts ...client.Option) (*KEP_WF.GetAgentWorkflowStateRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetAgentWorkflowState")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetAgentWorkflowState")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetAgentWorkflowStateRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ConvertToAgentWorkflow(ctx context.Context, req *KEP_WF.ConvertToAgentWorkflowReq, opts ...client.Option) (*KEP_WF.ConvertToAgentWorkflowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ConvertToAgentWorkflow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ConvertToAgentWorkflow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ConvertToAgentWorkflowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) SwitchAgentWorkflowState(ctx context.Context, req *KEP_WF.SwitchAgentWorkflowStateReq, opts ...client.Option) (*KEP_WF.SwitchAgentWorkflowStateRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SwitchAgentWorkflowState")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("SwitchAgentWorkflowState")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.SwitchAgentWorkflowStateRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListPDLVersion(ctx context.Context, req *KEP_WF.ListPDLVersionReq, opts ...client.Option) (*KEP_WF.ListPDLVersionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListPDLVersion")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListPDLVersion")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ListPDLVersionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetPDLVersionDetail(ctx context.Context, req *KEP_WF.GetPDLVersionDetailReq, opts ...client.Option) (*KEP_WF.GetPDLVersionDetailRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetPDLVersionDetail")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetPDLVersionDetail")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetPDLVersionDetailRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CreateWorkflowExample(ctx context.Context, req *KEP_WF.CreateWorkflowExampleReq, opts ...client.Option) (*KEP_WF.CreateWorkflowExampleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateWorkflowExample")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CreateWorkflowExample")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.CreateWorkflowExampleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListWorkflowExample(ctx context.Context, req *KEP_WF.ListWorkflowExampleReq, opts ...client.Option) (*KEP_WF.ListWorkflowExampleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListWorkflowExample")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListWorkflowExample")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ListWorkflowExampleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) UpdateWorkflowExample(ctx context.Context, req *KEP_WF.UpdateWorkflowExampleReq, opts ...client.Option) (*KEP_WF.UpdateWorkflowExampleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/UpdateWorkflowExample")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("UpdateWorkflowExample")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.UpdateWorkflowExampleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DeleteWorkflowExample(ctx context.Context, req *KEP_WF.DeleteWorkflowExampleReq, opts ...client.Option) (*KEP_WF.DeleteWorkflowExampleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeleteWorkflowExample")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DeleteWorkflowExample")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.DeleteWorkflowExampleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ImportWorkflowExample(ctx context.Context, req *KEP_WF.ImportWfExampleReq, opts ...client.Option) (*KEP_WF.ImportWfExampleRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ImportWorkflowExample")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ImportWorkflowExample")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ImportWfExampleRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) SwitchWorkflowState(ctx context.Context, req *KEP_WF.SwitchWorkflowStateReq, opts ...client.Option) (*KEP_WF.SwitchWorkflowStateRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SwitchWorkflowState")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("SwitchWorkflowState")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.SwitchWorkflowStateRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) AddWorkflowFeedback(ctx context.Context, req *KEP_WF.AddFlowFeedbackReq, opts ...client.Option) (*KEP_WF.AddFlowFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/AddWorkflowFeedback")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("AddWorkflowFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.AddFlowFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) UpdateWorkflowFeedback(ctx context.Context, req *KEP_WF.UpdateFlowFeedbackReq, opts ...client.Option) (*KEP_WF.UpdateFlowFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/UpdateWorkflowFeedback")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("UpdateWorkflowFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.UpdateFlowFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DescribeWorkflowFeedback(ctx context.Context, req *KEP_WF.DescribeWorkflowFeedReq, opts ...client.Option) (*KEP_WF.DescribeWorkflowFeedRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeWorkflowFeedback")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DescribeWorkflowFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.DescribeWorkflowFeedRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DeleteWorkflowFeedback(ctx context.Context, req *KEP_WF.DeleteWorkflowFeedbackReq, opts ...client.Option) (*KEP_WF.DeleteWorkflowFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeleteWorkflowFeedback")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DeleteWorkflowFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.DeleteWorkflowFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListWorkflowFeedback(ctx context.Context, req *KEP_WF.ListFlowFeedbackReq, opts ...client.Option) (*KEP_WF.ListFlowFeedbackRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListWorkflowFeedback")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListWorkflowFeedback")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ListFlowFeedbackRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) UpdateWorkflowFeedbackStatus(ctx context.Context, req *KEP_WF.UpdateWorkflowFeedbackStatusReq, opts ...client.Option) (*KEP_WF.UpdateWorkflowFeedbackStatusRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/UpdateWorkflowFeedbackStatus")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("UpdateWorkflowFeedbackStatus")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.UpdateWorkflowFeedbackStatusRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) UpdateWorkflowFeedbackTapd(ctx context.Context, req *KEP_WF.UpdateWorkflowFeedbackTapdReq, opts ...client.Option) (*KEP_WF.UpdateWorkflowFeedbackTapdRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/UpdateWorkflowFeedbackTapd")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("UpdateWorkflowFeedbackTapd")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.UpdateWorkflowFeedbackTapdRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ImportWorkflow(ctx context.Context, req *KEP_WF.ImportWorkflowReq, opts ...client.Option) (*KEP_WF.ImportWorkflowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ImportWorkflow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ImportWorkflow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ImportWorkflowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ExportWorkflow(ctx context.Context, req *KEP_WF.ExportWorkflowReq, opts ...client.Option) (*KEP_WF.ExportWorkflowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ExportWorkflow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ExportWorkflow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ExportWorkflowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DebugWorkflowNode(ctx context.Context, req *KEP_WF.DebugWorkflowNodeReq, opts ...client.Option) (*KEP_WF.DebugWorkflowNodeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DebugWorkflowNode")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DebugWorkflowNode")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.DebugWorkflowNodeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetEnableCustomAsk(ctx context.Context, req *KEP_WF.GetEnableCustomAskReq, opts ...client.Option) (*KEP_WF.GetEnableCustomAskResp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetEnableCustomAsk")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetEnableCustomAsk")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetEnableCustomAskResp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CreateParameter(ctx context.Context, req *KEP_WF.CreateParameterReq, opts ...client.Option) (*KEP_WF.CreateParameterResp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateParameter")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CreateParameter")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.CreateParameterResp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetParameterList(ctx context.Context, req *KEP_WF.GetParameterListReq, opts ...client.Option) (*KEP_WF.GetParameterListResp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetParameterList")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetParameterList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetParameterListResp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) UpdateParameter(ctx context.Context, req *KEP_WF.UpdateParameterReq, opts ...client.Option) (*KEP_WF.UpdateParameterResp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/UpdateParameter")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("UpdateParameter")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.UpdateParameterResp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DeleteParameter(ctx context.Context, req *KEP_WF.DeleteParameterReq, opts ...client.Option) (*KEP_WF.DeleteParameterResp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeleteParameter")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DeleteParameter")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.DeleteParameterResp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetBotNodeParameterList(ctx context.Context, req *KEP_WF.GetBotNodeParameterListReq, opts ...client.Option) (*KEP_WF.GetBotNodeParameterListResp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetBotNodeParameterList")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetBotNodeParameterList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetBotNodeParameterListResp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ClearAppFlowResource(ctx context.Context, req *KEP_WF.ClearAppFlowResourceReq, opts ...client.Option) (*KEP_WF.ClearAppFlowResourceRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ClearAppFlowResource")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ClearAppFlowResource")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ClearAppFlowResourceRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DeleteAppResource(ctx context.Context, req *KEP_WF.DeleteAppResourceReq, opts ...client.Option) (*KEP_WF.DeleteAppResourceRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeleteAppResource")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DeleteAppResource")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.DeleteAppResourceRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetCanBeReferencedWorkflowList(ctx context.Context, req *KEP_WF.GetCanBeReferencedWorkflowListReq, opts ...client.Option) (*KEP_WF.GetCanBeReferencedWorkflowListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetCanBeReferencedWorkflowList")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetCanBeReferencedWorkflowList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetCanBeReferencedWorkflowListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetHasBeenReferencedWorkflowList(ctx context.Context, req *KEP_WF.GetHasBeenReferencedWorkflowListReq, opts ...client.Option) (*KEP_WF.GetHasBeenReferencedWorkflowListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetHasBeenReferencedWorkflowList")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetHasBeenReferencedWorkflowList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetHasBeenReferencedWorkflowListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetHasReferencedPluginToolWorkflowList(ctx context.Context, req *KEP_WF.GetHasReferencedPluginToolWorkflowListReq, opts ...client.Option) (*KEP_WF.GetHasReferencedPluginToolWorkflowListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetHasReferencedPluginToolWorkflowList")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetHasReferencedPluginToolWorkflowList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetHasReferencedPluginToolWorkflowListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetParamsByWorkflowIds(ctx context.Context, req *KEP_WF.GetParamsByWorkflowIdsReq, opts ...client.Option) (*KEP_WF.GetParamsByWorkflowIdsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetParamsByWorkflowIds")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetParamsByWorkflowIds")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetParamsByWorkflowIdsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetWorkflowGuideViewed(ctx context.Context, req *KEP_WF.GetWorkflowGuideViewedRequest, opts ...client.Option) (*KEP_WF.GetWorkflowGuideViewedResponse, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetWorkflowGuideViewed")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetWorkflowGuideViewed")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetWorkflowGuideViewedResponse{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) MarkWorkflowGuideViewed(ctx context.Context, req *KEP_WF.MarkWorkflowGuideViewedRequest, opts ...client.Option) (*KEP_WF.MarkWorkflowGuideViewedResponse, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/MarkWorkflowGuideViewed")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("MarkWorkflowGuideViewed")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.MarkWorkflowGuideViewedResponse{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetExperienceWorkflowList(ctx context.Context, req *KEP_WF.GetExperienceWorkflowListReq, opts ...client.Option) (*KEP_WF.GetExperienceWorkflowListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetExperienceWorkflowList")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetExperienceWorkflowList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetExperienceWorkflowListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CreateExperienceWorkflow(ctx context.Context, req *KEP_WF.CreateExperienceWorkflowReq, opts ...client.Option) (*KEP_WF.CreateExperienceWorkflowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateExperienceWorkflow")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CreateExperienceWorkflow")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.CreateExperienceWorkflowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CanCreateWorkflowRun(ctx context.Context, req *KEP_WF.CanCreateWorkflowRunReq, opts ...client.Option) (*KEP_WF.CanCreateWorkflowRunRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CanCreateWorkflowRun")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CanCreateWorkflowRun")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.CanCreateWorkflowRunRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) SaveAppDebugMode(ctx context.Context, req *KEP_WF.SaveAppDebugModeReq, opts ...client.Option) (*KEP_WF.SaveAppDebugModeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SaveAppDebugMode")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("SaveAppDebugMode")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.SaveAppDebugModeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) SaveAppDebugCustomVariables(ctx context.Context, req *KEP_WF.SaveAppDebugCustomVariablesReq, opts ...client.Option) (*KEP_WF.SaveAppDebugCustomVariablesRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SaveAppDebugCustomVariables")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("SaveAppDebugCustomVariables")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.SaveAppDebugCustomVariablesRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DescribeAppDebugConfig(ctx context.Context, req *KEP_WF.DescribeAppDebugConfigReq, opts ...client.Option) (*KEP_WF.DescribeAppDebugConfigRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeAppDebugConfig")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DescribeAppDebugConfig")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.DescribeAppDebugConfigRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) CreateWorkflowRun(ctx context.Context, req *KEP_WF.CreateWorkflowRunReq, opts ...client.Option) (*KEP_WF.CreateWorkflowRunRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateWorkflowRun")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("CreateWorkflowRun")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.CreateWorkflowRunRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) ListWorkflowRuns(ctx context.Context, req *KEP_WF.ListWorkflowRunsReq, opts ...client.Option) (*KEP_WF.ListWorkflowRunsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListWorkflowRuns")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("ListWorkflowRuns")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.ListWorkflowRunsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DescribeWorkflowRun(ctx context.Context, req *KEP_WF.DescribeWorkflowRunReq, opts ...client.Option) (*KEP_WF.DescribeWorkflowRunRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeWorkflowRun")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DescribeWorkflowRun")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.DescribeWorkflowRunRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) DescribeNodeRun(ctx context.Context, req *KEP_WF.DescribeNodeRunReq, opts ...client.Option) (*KEP_WF.DescribeNodeRunRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeNodeRun")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("DescribeNodeRun")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.DescribeNodeRunRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) StopWorkflowRun(ctx context.Context, req *KEP_WF.StopWorkflowRunReq, opts ...client.Option) (*KEP_WF.StopWorkflowRunRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/StopWorkflowRun")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("StopWorkflowRun")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.StopWorkflowRunRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetWorkflowListByDoc(ctx context.Context, req *KEP_WF.GetWorkflowListByDocReq, opts ...client.Option) (*KEP_WF.GetWorkflowListByDocRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetWorkflowListByDoc")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetWorkflowListByDoc")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetWorkflowListByDocRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetWorkflowListByAttribute(ctx context.Context, req *KEP_WF.GetWorkflowListByAttributeReq, opts ...client.Option) (*KEP_WF.GetWorkflowListByAttributeRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetWorkflowListByAttribute")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetWorkflowListByAttribute")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetWorkflowListByAttributeRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *TaskConfigClientProxyImpl) GetWorkflowListByAttributeLabel(ctx context.Context, req *KEP_WF.GetWorkflowListByAttributeLabelReq, opts ...client.Option) (*KEP_WF.GetWorkflowListByAttributeLabelRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetWorkflowListByAttributeLabel")
	msg.WithCalleeServiceName(TaskConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_server")
	msg.WithCalleeService("TaskConfig")
	msg.WithCalleeMethod("GetWorkflowListByAttributeLabel")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP_WF.GetWorkflowListByAttributeLabelRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
