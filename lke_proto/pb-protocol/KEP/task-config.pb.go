// ############################################################################
//
// PB 来源 -> https://git.woa.com/dialogue-platform/bot-dm/bot-task-config-server.git
//
// ############################################################################

// KEP.bot-task-config-server
//
// @(#)task-config.proto  December 07, 2023
// Copyright(c) 2023, boyucao@Tencent. All rights reserved.

// ####################################################################################################### //
//                                                                                                         //
//     PB 文件 [task-config.proto] 请移步到                                                                //
//                                                                                                         //
//     https://git.woa.com/raven/pb-stub/blob/master/thirds-pb/bot-task-config-server/task-config.proto    //
//                                                                                                         //
// ####################################################################################################### //

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.7
// source: task-config.proto

package KEP

import (
	reflect "reflect"

	KEP_WF "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	_ "git.code.oa.com/devsec/protoc-gen-secv/validate"
	_ "git.code.oa.com/trpc-go/trpc"
	_ "git.code.oa.com/trpc-go/trpc-go"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_task_config_proto protoreflect.FileDescriptor

var file_task_config_proto_rawDesc = []byte{
	0x0a, 0x11, 0x74, 0x61, 0x73, 0x6b, 0x2d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x1a, 0x10, 0x61, 0x70, 0x70, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0f, 0x64, 0x61, 0x74, 0x61, 0x2d, 0x73, 0x79, 0x6e,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0f, 0x74, 0x61, 0x73, 0x6b, 0x2d, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a, 0x73, 0x6c, 0x6f, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a, 0x72, 0x6f, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x0c, 0x63, 0x6f, 0x72, 0x70, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x09,
	0x76, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2d, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2d, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2d,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x12, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2d, 0x72, 0x75, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x92, 0x7b, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x84, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x43, 0x68, 0x61,
	0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x70, 0x70, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4e, 0x75, 0x6d, 0x52, 0x65,
	0x71, 0x1a, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x4e, 0x75, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x79, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x41, 0x70, 0x70, 0x53, 0x68, 0x61, 0x72, 0x65, 0x55, 0x52, 0x4c, 0x12, 0x32, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x53, 0x68, 0x61, 0x72, 0x65, 0x55, 0x52, 0x4c, 0x52, 0x65, 0x71, 0x1a,
	0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x53, 0x68, 0x61, 0x72, 0x65, 0x55, 0x52, 0x4c,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x8e, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x49, 0x64, 0x42, 0x79, 0x53, 0x68, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x39,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x42, 0x79, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x42, 0x79, 0x53, 0x68, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x94, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x12, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x3c,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70, 0x12, 0x97, 0x01, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x97, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f,
	0x77, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x84, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x64, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x8d, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63,
	0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x39, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53,
	0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x7b, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x33, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a,
	0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x73, 0x70, 0x12, 0x7e, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x53,
	0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x34,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x7b, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x6f, 0x62,
	0x6f, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x12, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x33, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x61, 0x64, 0x79, 0x52, 0x73,
	0x70, 0x12, 0x87, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x41, 0x50, 0x49,
	0x56, 0x61, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x41, 0x50, 0x49, 0x56, 0x61, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x41, 0x50, 0x49, 0x56,
	0x61, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x73, 0x70, 0x12, 0x72, 0x0a, 0x0c, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x30, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12,
	0x78, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x78, 0x0a, 0x0e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x32, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a,
	0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x73, 0x70, 0x12, 0x78, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x90, 0x01,
	0x0a, 0x16, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f,
	0x77, 0x52, 0x65, 0x71, 0x1a, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x48, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70,
	0x12, 0x87, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65,
	0x71, 0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x54,
	0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x93, 0x01, 0x0a, 0x17, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x61,
	0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x71, 0x1a, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x48, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70,
	0x12, 0x75, 0x0a, 0x0d, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f,
	0x77, 0x12, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f,
	0x77, 0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x61, 0x73, 0x6b,
	0x46, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x72, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x81, 0x01, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12,
	0x78, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f,
	0x77, 0x12, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c,
	0x6f, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x72, 0x0a, 0x0c, 0x53, 0x61, 0x76,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x76, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x61,
	0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x78, 0x0a,
	0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12,
	0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x46, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x87, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12,
	0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x73,
	0x70, 0x12, 0x78, 0x0a, 0x0e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x46,
	0x6c, 0x6f, 0x77, 0x12, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b,
	0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x78, 0x0a, 0x0e, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x32, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65,
	0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c,
	0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x87, 0x01, 0x0a, 0x13, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x37, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x49, 0x6e, 0x6e, 0x65, 0x72, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x46,
	0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12,
	0x9c, 0x01, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x46,
	0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x3e,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x3e,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x73, 0x70, 0x12, 0x99,
	0x01, 0x0a, 0x19, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c,
	0x6f, 0x77, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x3d, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x41, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x3d, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x41, 0x6e, 0x73,
	0x77, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x73, 0x70, 0x12, 0x99, 0x01, 0x0a, 0x19, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x72, 0x0a, 0x0c, 0x43, 0x6f, 0x70, 0x79, 0x54, 0x61,
	0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x54, 0x61, 0x73,
	0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x54,
	0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x6f, 0x0a, 0x0b, 0x47, 0x65,
	0x74, 0x53, 0x6c, 0x6f, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x6c, 0x6f, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2f, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x6c, 0x6f, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x6c, 0x0a, 0x0a, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x52, 0x73, 0x70, 0x12, 0x6c, 0x0a, 0x0a, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x6c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x6c, 0x6f, 0x74, 0x52, 0x73, 0x70, 0x12, 0x6c, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x6c,
	0x6f, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x6c,
	0x6f, 0x74, 0x52, 0x73, 0x70, 0x12, 0x72, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x6f, 0x0a, 0x0b, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x6f, 0x0a, 0x0b, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x2f, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x6f, 0x0a, 0x0b, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x2f, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x2f, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x6f, 0x0a, 0x0b,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x2f, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x2f, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x93, 0x01,
	0x0a, 0x17, 0x52, 0x6f, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x6c, 0x65,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x75, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x12, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x78, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x32, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x75, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x75, 0x0a, 0x0d, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x31, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x69, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x61, 0x72, 0x12,
	0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x61, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x2d,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x61, 0x72, 0x52, 0x73, 0x70, 0x12, 0x69, 0x0a,
	0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x61, 0x72, 0x12, 0x2d, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x56, 0x61, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x56, 0x61, 0x72, 0x52, 0x73, 0x70, 0x12, 0x69, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x56, 0x61, 0x72, 0x12, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x61,
	0x72, 0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x61, 0x72,
	0x52, 0x73, 0x70, 0x12, 0x6f, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x56,
	0x61, 0x72, 0x12, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x56, 0x61, 0x72,
	0x52, 0x65, 0x71, 0x1a, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x56, 0x61,
	0x72, 0x52, 0x73, 0x70, 0x12, 0x6c, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x56, 0x61, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x7e, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56,
	0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x56, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x99, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74,
	0x57, 0x6f, 0x72, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x57, 0x6f, 0x72, 0x64,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x78,
	0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x12, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x72, 0x0a, 0x0c, 0x53, 0x61, 0x76, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x76,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x72, 0x0a, 0x0c,
	0x43, 0x6f, 0x70, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x30, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43,
	0x6f, 0x70, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x30,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x43, 0x6f, 0x70, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70,
	0x12, 0x78, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x12, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x72, 0x0a, 0x0c, 0x4c, 0x69,
	0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x30, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x81,
	0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49,
	0x6e, 0x6e, 0x65, 0x72, 0x12, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x35, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x52,
	0x73, 0x70, 0x12, 0x82, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a,
	0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x72, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x8d, 0x01, 0x0a, 0x15,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71,
	0x1a, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e,
	0x6f, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x12, 0xa4, 0x01, 0x0a, 0x20,
	0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x6e, 0x65, 0x72,
	0x12, 0x3f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49,
	0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x3f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x81, 0x01, 0x0a, 0x11, 0x53, 0x61, 0x76, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x1a,
	0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x81, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x35, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x71, 0x1a, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x8a, 0x01, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x38, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x90, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x3a,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x8d, 0x01, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x90, 0x01, 0x0a, 0x16, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x54, 0x6f, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x54,
	0x6f, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65,
	0x71, 0x1a, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x54, 0x6f, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x96, 0x01,
	0x0a, 0x18, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3c, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x3c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x78, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x44,
	0x4c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x44, 0x4c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x44, 0x4c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70,
	0x12, 0x87, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x50, 0x44, 0x4c, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x44,
	0x4c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x71, 0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x44, 0x4c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x8d, 0x01, 0x0a, 0x15, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x78, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x12, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x87, 0x01, 0x0a, 0x13, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x78, 0x61, 0x6d, 0x70,
	0x6c, 0x65, 0x12, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x37, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x8d, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x39,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45,
	0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x8d, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x39,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45,
	0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x81, 0x01, 0x0a, 0x15, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x33,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x57, 0x66, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x57, 0x66, 0x45, 0x78,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x87, 0x01, 0x0a, 0x13, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x7f, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x46,
	0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x33,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x41, 0x64, 0x64, 0x46, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x52, 0x73, 0x70, 0x12, 0x88, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x36,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62,
	0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46,
	0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x8e,
	0x01, 0x0a, 0x18, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x38, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x46, 0x65,
	0x65, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x52, 0x73, 0x70, 0x12,
	0x90, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x3a, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62,
	0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52,
	0x73, 0x70, 0x12, 0x82, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x34, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x1a, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64,
	0x62, 0x61, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0xa2, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61,
	0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x40, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64, 0x62,
	0x61, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x9c, 0x01, 0x0a,
	0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x46,
	0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x61, 0x70, 0x64, 0x12, 0x3e, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64,
	0x62, 0x61, 0x63, 0x6b, 0x54, 0x61, 0x70, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x3e, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x64,
	0x62, 0x61, 0x63, 0x6b, 0x54, 0x61, 0x70, 0x64, 0x52, 0x73, 0x70, 0x12, 0x78, 0x0a, 0x0e, 0x49,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x32, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65,
	0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x73, 0x70, 0x12, 0x78, 0x0a, 0x0e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x12,
	0x81, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x62, 0x75, 0x67, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x35, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44,
	0x65, 0x62, 0x75, 0x67, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x85, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x73, 0x6b, 0x12, 0x36, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x41, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x7c, 0x0a, 0x0f, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x33,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x7f, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x34, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x7c, 0x0a, 0x0f, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x33, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x1a, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x7c, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x33, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x1a, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x94, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x42, 0x6f,
	0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x3c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x8a, 0x01,
	0x0a, 0x14, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x41, 0x70, 0x70, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x41, 0x70,
	0x70, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x41, 0x70, 0x70, 0x46, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x81, 0x01, 0x0a, 0x11, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0xa8,
	0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6e, 0x42, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x42, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6e, 0x42, 0x65, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x42, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6e, 0x42, 0x65,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0xae, 0x01, 0x0a, 0x20, 0x47, 0x65,
	0x74, 0x48, 0x61, 0x73, 0x42, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x44,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x73, 0x42, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x44, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x73, 0x42, 0x65, 0x65,
	0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0xc0, 0x01, 0x0a, 0x26, 0x47,
	0x65, 0x74, 0x48, 0x61, 0x73, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x6f, 0x6f, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x73, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x6f,
	0x6f, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x4a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x61, 0x73, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x64, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x6f, 0x6f, 0x6c, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x90, 0x01,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x73, 0x12, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x73, 0x52, 0x73, 0x70,
	0x12, 0x99, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x47, 0x75, 0x69, 0x64, 0x65, 0x56, 0x69, 0x65, 0x77, 0x65, 0x64, 0x12, 0x3e, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x56, 0x69,
	0x65, 0x77, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x56, 0x69,
	0x65, 0x77, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9c, 0x01, 0x0a,
	0x17, 0x4d, 0x61, 0x72, 0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x47, 0x75, 0x69,
	0x64, 0x65, 0x56, 0x69, 0x65, 0x77, 0x65, 0x64, 0x12, 0x3f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x61, 0x72, 0x6b,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x47, 0x75, 0x69, 0x64, 0x65, 0x56, 0x69, 0x65,
	0x77, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x19,
	0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x96, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x12, 0x3c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70,
	0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x71, 0x1a, 0x3c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x65, 0x72,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70,
	0x12, 0x8c, 0x01, 0x0a, 0x14, 0x43, 0x61, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x12, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x6e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e,
	0x52, 0x65, 0x71, 0x1a, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x80, 0x01, 0x0a, 0x10, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62, 0x75, 0x67,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x44, 0x65,
	0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x76,
	0x65, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62, 0x75, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0xa1, 0x01, 0x0a, 0x1b, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x44, 0x65,
	0x62, 0x75, 0x67, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x12, 0x3f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62, 0x75,
	0x67, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x1a, 0x3f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62,
	0x75, 0x67, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62, 0x75, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x44,
	0x65, 0x62, 0x75, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x3a, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x44, 0x65, 0x62, 0x75, 0x67,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x83, 0x01, 0x0a, 0x11,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75,
	0x6e, 0x12, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x80, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x75, 0x6e, 0x73, 0x12, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x73, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x89, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x12, 0x37, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x75, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x7d, 0x0a, 0x0f, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4e, 0x6f, 0x64, 0x65,
	0x52, 0x75, 0x6e, 0x12, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4e, 0x6f,
	0x64, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x75, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x7d, 0x0a, 0x0f, 0x53, 0x74, 0x6f, 0x70, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x75, 0x6e, 0x12, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x8a,
	0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x79, 0x44, 0x6f, 0x63, 0x12, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x44, 0x6f, 0x63, 0x52, 0x65,
	0x71, 0x1a, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x79, 0x44, 0x6f, 0x63, 0x52, 0x73, 0x70, 0x12, 0x9c, 0x01, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x79, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x3e, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x3e, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0xab, 0x01, 0x0a, 0x1f, 0x47,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x43,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x79, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x1a, 0x43, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x42, 0x39, 0x5a, 0x37, 0x67, 0x69, 0x74, 0x2e,
	0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65,
	0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f,
	0x4b, 0x45, 0x50, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_task_config_proto_goTypes = []interface{}{
	(*GetAppChatInputNumReq)(nil),                            // 0: trpc.KEP.bot_task_config_server.GetAppChatInputNumReq
	(*GetAppShareURLReq)(nil),                                // 1: trpc.KEP.bot_task_config_server.GetAppShareURLReq
	(*GetRobotIdByShareCodeReq)(nil),                         // 2: trpc.KEP.bot_task_config_server.GetRobotIdByShareCodeReq
	(*KEP_WF.GetModelSupportWorkflowReq)(nil),                // 3: trpc.KEP.bot_task_config_server.GetModelSupportWorkflowReq
	(*KEP_WF.GetWorkflowReleaseStatusReq)(nil),               // 4: trpc.KEP.bot_task_config_server.GetWorkflowReleaseStatusReq
	(*GetTaskFlowReleaseStatusReq)(nil),                      // 5: trpc.KEP.bot_task_config_server.GetTaskFlowReleaseStatusReq
	(*GetUnreleasedCountReq)(nil),                            // 6: trpc.KEP.bot_task_config_server.GetUnreleasedCountReq
	(*SendDataSyncTaskEventReq)(nil),                         // 7: trpc.KEP.bot_task_config_server.SendDataSyncTaskEventReq
	(*GetDataSyncTaskReq)(nil),                               // 8: trpc.KEP.bot_task_config_server.GetDataSyncTaskReq
	(*GetDataSyncTasksReq)(nil),                              // 9: trpc.KEP.bot_task_config_server.GetDataSyncTasksReq
	(*CheckRobotReadyReq)(nil),                               // 10: trpc.KEP.bot_task_config_server.CheckRobotReadyReq
	(*ReleaseAPIVarParamsReq)(nil),                           // 11: trpc.KEP.bot_task_config_server.ReleaseAPIVarParamsReq
	(*ListCategoryReq)(nil),                                  // 12: trpc.KEP.bot_task_config_server.ListCategoryReq
	(*CreateCategoryReq)(nil),                                // 13: trpc.KEP.bot_task_config_server.CreateCategoryReq
	(*UpdateCategoryReq)(nil),                                // 14: trpc.KEP.bot_task_config_server.UpdateCategoryReq
	(*DeleteCategoryReq)(nil),                                // 15: trpc.KEP.bot_task_config_server.DeleteCategoryReq
	(*RecoverHistoryTaskFlowReq)(nil),                        // 16: trpc.KEP.bot_task_config_server.RecoverHistoryTaskFlowReq
	(*ListHistoryTaskFlowReq)(nil),                           // 17: trpc.KEP.bot_task_config_server.ListHistoryTaskFlowReq
	(*DescribeHistoryTaskFlowReq)(nil),                       // 18: trpc.KEP.bot_task_config_server.DescribeHistoryTaskFlowReq
	(*GroupTaskFlowReq)(nil),                                 // 19: trpc.KEP.bot_task_config_server.GroupTaskFlowReq
	(*ListTaskFlowReq)(nil),                                  // 20: trpc.KEP.bot_task_config_server.ListTaskFlowReq
	(*GetTaskFlowDetailReq)(nil),                             // 21: trpc.KEP.bot_task_config_server.GetTaskFlowDetailReq
	(*CreateTaskFlowReq)(nil),                                // 22: trpc.KEP.bot_task_config_server.CreateTaskFlowReq
	(*SaveTaskFlowReq)(nil),                                  // 23: trpc.KEP.bot_task_config_server.SaveTaskFlowReq
	(*DeleteTaskFlowReq)(nil),                                // 24: trpc.KEP.bot_task_config_server.DeleteTaskFlowReq
	(*ListTaskFlowPreviewReq)(nil),                           // 25: trpc.KEP.bot_task_config_server.ListTaskFlowPreviewReq
	(*ImportTaskFlowReq)(nil),                                // 26: trpc.KEP.bot_task_config_server.ImportTaskFlowReq
	(*ExportTaskFlowReq)(nil),                                // 27: trpc.KEP.bot_task_config_server.ExportTaskFlowReq
	(*InnerExportTaskFlowReq)(nil),                           // 28: trpc.KEP.bot_task_config_server.InnerExportTaskFlowReq
	(*PreviewTaskFlowRequestNodeReq)(nil),                    // 29: trpc.KEP.bot_task_config_server.PreviewTaskFlowRequestNodeReq
	(*PreviewTaskFlowAnswerNodeReq)(nil),                     // 30: trpc.KEP.bot_task_config_server.PreviewTaskFlowAnswerNodeReq
	(*PreviewAnswerNodeDocumentReq)(nil),                     // 31: trpc.KEP.bot_task_config_server.PreviewAnswerNodeDocumentReq
	(*CopyTaskFlowReq)(nil),                                  // 32: trpc.KEP.bot_task_config_server.CopyTaskFlowReq
	(*GetSlotListReq)(nil),                                   // 33: trpc.KEP.bot_task_config_server.GetSlotListReq
	(*CreateSlotReq)(nil),                                    // 34: trpc.KEP.bot_task_config_server.CreateSlotReq
	(*UpdateSlotReq)(nil),                                    // 35: trpc.KEP.bot_task_config_server.UpdateSlotReq
	(*DeleteSlotReq)(nil),                                    // 36: trpc.KEP.bot_task_config_server.DeleteSlotReq
	(*GetEntryListReq)(nil),                                  // 37: trpc.KEP.bot_task_config_server.GetEntryListReq
	(*CreateEntryReq)(nil),                                   // 38: trpc.KEP.bot_task_config_server.CreateEntryReq
	(*UpdateEntryReq)(nil),                                   // 39: trpc.KEP.bot_task_config_server.UpdateEntryReq
	(*DeleteEntryReq)(nil),                                   // 40: trpc.KEP.bot_task_config_server.DeleteEntryReq
	(*ImportEntryReq)(nil),                                   // 41: trpc.KEP.bot_task_config_server.ImportEntryReq
	(*RoleDefaultTemplateListReq)(nil),                       // 42: trpc.KEP.bot_task_config_server.RoleDefaultTemplateListReq
	(*CreateExampleReq)(nil),                                 // 43: trpc.KEP.bot_task_config_server.CreateExampleReq
	(*GetExampleListReq)(nil),                                // 44: trpc.KEP.bot_task_config_server.GetExampleListReq
	(*UpdateExampleReq)(nil),                                 // 45: trpc.KEP.bot_task_config_server.UpdateExampleReq
	(*DeleteExampleReq)(nil),                                 // 46: trpc.KEP.bot_task_config_server.DeleteExampleReq
	(*CreateVarReq)(nil),                                     // 47: trpc.KEP.bot_task_config_server.CreateVarReq
	(*UpdateVarReq)(nil),                                     // 48: trpc.KEP.bot_task_config_server.UpdateVarReq
	(*DeleteVarReq)(nil),                                     // 49: trpc.KEP.bot_task_config_server.DeleteVarReq
	(*DescribeVarReq)(nil),                                   // 50: trpc.KEP.bot_task_config_server.DescribeVarReq
	(*GetVarListReq)(nil),                                    // 51: trpc.KEP.bot_task_config_server.GetVarListReq
	(*GetSystemVarListReq)(nil),                              // 52: trpc.KEP.bot_task_config_server.GetSystemVarListReq
	(*GetPromptWordTemplateListReq)(nil),                     // 53: trpc.KEP.bot_task_config_server.GetPromptWordTemplateListReq
	(*KEP_WF.CreateWorkflowReq)(nil),                         // 54: trpc.KEP.bot_task_config_server.CreateWorkflowReq
	(*KEP_WF.SaveWorkflowReq)(nil),                           // 55: trpc.KEP.bot_task_config_server.SaveWorkflowReq
	(*KEP_WF.CopyWorkflowReq)(nil),                           // 56: trpc.KEP.bot_task_config_server.CopyWorkflowReq
	(*KEP_WF.DeleteWorkflowReq)(nil),                         // 57: trpc.KEP.bot_task_config_server.DeleteWorkflowReq
	(*KEP_WF.ListWorkflowReq)(nil),                           // 58: trpc.KEP.bot_task_config_server.ListWorkflowReq
	(*KEP_WF.ListWorkflowInnerReq)(nil),                      // 59: trpc.KEP.bot_task_config_server.ListWorkflowInnerReq
	(*KEP_WF.GetWorkflowDetailReq)(nil),                      // 60: trpc.KEP.bot_task_config_server.GetWorkflowDetailReq
	(*KEP_WF.ListNodeInfoReq)(nil),                           // 61: trpc.KEP.bot_task_config_server.ListNodeInfoReq
	(*KEP_WF.ListWorkflowNodeModelReq)(nil),                  // 62: trpc.KEP.bot_task_config_server.ListWorkflowNodeModelReq
	(*KEP_WF.ListWorkflowInfoByModelNameReq)(nil),            // 63: trpc.KEP.bot_task_config_server.ListWorkflowInfoByModelNameReq
	(*KEP_WF.SaveAgentWorkflowReq)(nil),                      // 64: trpc.KEP.bot_task_config_server.SaveAgentWorkflowReq
	(*KEP_WF.ListAgentWorkflowReq)(nil),                      // 65: trpc.KEP.bot_task_config_server.ListAgentWorkflowReq
	(*KEP_WF.GetAgentWorkflowInfoReq)(nil),                   // 66: trpc.KEP.bot_task_config_server.GetAgentWorkflowInfoReq
	(*KEP_WF.GetAgentWorkflowDetailReq)(nil),                 // 67: trpc.KEP.bot_task_config_server.GetAgentWorkflowDetailReq
	(*KEP_WF.GetAgentWorkflowStateReq)(nil),                  // 68: trpc.KEP.bot_task_config_server.GetAgentWorkflowStateReq
	(*KEP_WF.ConvertToAgentWorkflowReq)(nil),                 // 69: trpc.KEP.bot_task_config_server.ConvertToAgentWorkflowReq
	(*KEP_WF.SwitchAgentWorkflowStateReq)(nil),               // 70: trpc.KEP.bot_task_config_server.SwitchAgentWorkflowStateReq
	(*KEP_WF.ListPDLVersionReq)(nil),                         // 71: trpc.KEP.bot_task_config_server.ListPDLVersionReq
	(*KEP_WF.GetPDLVersionDetailReq)(nil),                    // 72: trpc.KEP.bot_task_config_server.GetPDLVersionDetailReq
	(*KEP_WF.CreateWorkflowExampleReq)(nil),                  // 73: trpc.KEP.bot_task_config_server.CreateWorkflowExampleReq
	(*KEP_WF.ListWorkflowExampleReq)(nil),                    // 74: trpc.KEP.bot_task_config_server.ListWorkflowExampleReq
	(*KEP_WF.UpdateWorkflowExampleReq)(nil),                  // 75: trpc.KEP.bot_task_config_server.UpdateWorkflowExampleReq
	(*KEP_WF.DeleteWorkflowExampleReq)(nil),                  // 76: trpc.KEP.bot_task_config_server.DeleteWorkflowExampleReq
	(*KEP_WF.ImportWfExampleReq)(nil),                        // 77: trpc.KEP.bot_task_config_server.ImportWfExampleReq
	(*KEP_WF.SwitchWorkflowStateReq)(nil),                    // 78: trpc.KEP.bot_task_config_server.SwitchWorkflowStateReq
	(*KEP_WF.AddFlowFeedbackReq)(nil),                        // 79: trpc.KEP.bot_task_config_server.AddFlowFeedbackReq
	(*KEP_WF.UpdateFlowFeedbackReq)(nil),                     // 80: trpc.KEP.bot_task_config_server.UpdateFlowFeedbackReq
	(*KEP_WF.DescribeWorkflowFeedReq)(nil),                   // 81: trpc.KEP.bot_task_config_server.DescribeWorkflowFeedReq
	(*KEP_WF.DeleteWorkflowFeedbackReq)(nil),                 // 82: trpc.KEP.bot_task_config_server.DeleteWorkflowFeedbackReq
	(*KEP_WF.ListFlowFeedbackReq)(nil),                       // 83: trpc.KEP.bot_task_config_server.ListFlowFeedbackReq
	(*KEP_WF.UpdateWorkflowFeedbackStatusReq)(nil),           // 84: trpc.KEP.bot_task_config_server.UpdateWorkflowFeedbackStatusReq
	(*KEP_WF.UpdateWorkflowFeedbackTapdReq)(nil),             // 85: trpc.KEP.bot_task_config_server.UpdateWorkflowFeedbackTapdReq
	(*KEP_WF.ImportWorkflowReq)(nil),                         // 86: trpc.KEP.bot_task_config_server.ImportWorkflowReq
	(*KEP_WF.ExportWorkflowReq)(nil),                         // 87: trpc.KEP.bot_task_config_server.ExportWorkflowReq
	(*KEP_WF.DebugWorkflowNodeReq)(nil),                      // 88: trpc.KEP.bot_task_config_server.DebugWorkflowNodeReq
	(*KEP_WF.GetEnableCustomAskReq)(nil),                     // 89: trpc.KEP.bot_task_config_server.GetEnableCustomAskReq
	(*KEP_WF.CreateParameterReq)(nil),                        // 90: trpc.KEP.bot_task_config_server.CreateParameterReq
	(*KEP_WF.GetParameterListReq)(nil),                       // 91: trpc.KEP.bot_task_config_server.GetParameterListReq
	(*KEP_WF.UpdateParameterReq)(nil),                        // 92: trpc.KEP.bot_task_config_server.UpdateParameterReq
	(*KEP_WF.DeleteParameterReq)(nil),                        // 93: trpc.KEP.bot_task_config_server.DeleteParameterReq
	(*KEP_WF.GetBotNodeParameterListReq)(nil),                // 94: trpc.KEP.bot_task_config_server.GetBotNodeParameterListReq
	(*KEP_WF.ClearAppFlowResourceReq)(nil),                   // 95: trpc.KEP.bot_task_config_server.ClearAppFlowResourceReq
	(*KEP_WF.DeleteAppResourceReq)(nil),                      // 96: trpc.KEP.bot_task_config_server.DeleteAppResourceReq
	(*KEP_WF.GetCanBeReferencedWorkflowListReq)(nil),         // 97: trpc.KEP.bot_task_config_server.GetCanBeReferencedWorkflowListReq
	(*KEP_WF.GetHasBeenReferencedWorkflowListReq)(nil),       // 98: trpc.KEP.bot_task_config_server.GetHasBeenReferencedWorkflowListReq
	(*KEP_WF.GetHasReferencedPluginToolWorkflowListReq)(nil), // 99: trpc.KEP.bot_task_config_server.GetHasReferencedPluginToolWorkflowListReq
	(*KEP_WF.GetParamsByWorkflowIdsReq)(nil),                 // 100: trpc.KEP.bot_task_config_server.GetParamsByWorkflowIdsReq
	(*KEP_WF.GetWorkflowGuideViewedRequest)(nil),             // 101: trpc.KEP.bot_task_config_server.GetWorkflowGuideViewedRequest
	(*KEP_WF.MarkWorkflowGuideViewedRequest)(nil),            // 102: trpc.KEP.bot_task_config_server.MarkWorkflowGuideViewedRequest
	(*KEP_WF.GetExperienceWorkflowListReq)(nil),              // 103: trpc.KEP.bot_task_config_server.GetExperienceWorkflowListReq
	(*KEP_WF.CreateExperienceWorkflowReq)(nil),               // 104: trpc.KEP.bot_task_config_server.CreateExperienceWorkflowReq
	(*KEP_WF.CanCreateWorkflowRunReq)(nil),                   // 105: trpc.KEP.bot_task_config_server.CanCreateWorkflowRunReq
	(*KEP_WF.SaveAppDebugModeReq)(nil),                       // 106: trpc.KEP.bot_task_config_server.SaveAppDebugModeReq
	(*KEP_WF.SaveAppDebugCustomVariablesReq)(nil),            // 107: trpc.KEP.bot_task_config_server.SaveAppDebugCustomVariablesReq
	(*KEP_WF.DescribeAppDebugConfigReq)(nil),                 // 108: trpc.KEP.bot_task_config_server.DescribeAppDebugConfigReq
	(*KEP_WF.CreateWorkflowRunReq)(nil),                      // 109: trpc.KEP.bot_task_config_server.CreateWorkflowRunReq
	(*KEP_WF.ListWorkflowRunsReq)(nil),                       // 110: trpc.KEP.bot_task_config_server.ListWorkflowRunsReq
	(*KEP_WF.DescribeWorkflowRunReq)(nil),                    // 111: trpc.KEP.bot_task_config_server.DescribeWorkflowRunReq
	(*KEP_WF.DescribeNodeRunReq)(nil),                        // 112: trpc.KEP.bot_task_config_server.DescribeNodeRunReq
	(*KEP_WF.StopWorkflowRunReq)(nil),                        // 113: trpc.KEP.bot_task_config_server.StopWorkflowRunReq
	(*KEP_WF.GetWorkflowListByDocReq)(nil),                   // 114: trpc.KEP.bot_task_config_server.GetWorkflowListByDocReq
	(*KEP_WF.GetWorkflowListByAttributeReq)(nil),             // 115: trpc.KEP.bot_task_config_server.GetWorkflowListByAttributeReq
	(*KEP_WF.GetWorkflowListByAttributeLabelReq)(nil),        // 116: trpc.KEP.bot_task_config_server.GetWorkflowListByAttributeLabelReq
	(*GetAppChatInputNumRsp)(nil),                            // 117: trpc.KEP.bot_task_config_server.GetAppChatInputNumRsp
	(*GetAppShareURLResp)(nil),                               // 118: trpc.KEP.bot_task_config_server.GetAppShareURLResp
	(*GetRobotIdByShareCodeResp)(nil),                        // 119: trpc.KEP.bot_task_config_server.GetRobotIdByShareCodeResp
	(*KEP_WF.GetModelSupportWorkflowResp)(nil),               // 120: trpc.KEP.bot_task_config_server.GetModelSupportWorkflowResp
	(*KEP_WF.GetWorkflowReleaseStatusResp)(nil),              // 121: trpc.KEP.bot_task_config_server.GetWorkflowReleaseStatusResp
	(*GetTaskFlowReleaseStatusResp)(nil),                     // 122: trpc.KEP.bot_task_config_server.GetTaskFlowReleaseStatusResp
	(*GetUnreleasedCountRsp)(nil),                            // 123: trpc.KEP.bot_task_config_server.GetUnreleasedCountRsp
	(*SendDataSyncTaskEventRsp)(nil),                         // 124: trpc.KEP.bot_task_config_server.SendDataSyncTaskEventRsp
	(*GetDataSyncTaskRsp)(nil),                               // 125: trpc.KEP.bot_task_config_server.GetDataSyncTaskRsp
	(*GetDataSyncTasksRsp)(nil),                              // 126: trpc.KEP.bot_task_config_server.GetDataSyncTasksRsp
	(*CheckRobotReadyRsp)(nil),                               // 127: trpc.KEP.bot_task_config_server.CheckRobotReadyRsp
	(*ReleaseAPIVarParamsRsp)(nil),                           // 128: trpc.KEP.bot_task_config_server.ReleaseAPIVarParamsRsp
	(*ListCategoryRsp)(nil),                                  // 129: trpc.KEP.bot_task_config_server.ListCategoryRsp
	(*CreateCategoryRsp)(nil),                                // 130: trpc.KEP.bot_task_config_server.CreateCategoryRsp
	(*UpdateCategoryRsp)(nil),                                // 131: trpc.KEP.bot_task_config_server.UpdateCategoryRsp
	(*DeleteCategoryRsp)(nil),                                // 132: trpc.KEP.bot_task_config_server.DeleteCategoryRsp
	(*RecoverHistoryTaskFlowRsp)(nil),                        // 133: trpc.KEP.bot_task_config_server.RecoverHistoryTaskFlowRsp
	(*ListHistoryTaskFlowRsp)(nil),                           // 134: trpc.KEP.bot_task_config_server.ListHistoryTaskFlowRsp
	(*DescribeHistoryTaskFlowRsp)(nil),                       // 135: trpc.KEP.bot_task_config_server.DescribeHistoryTaskFlowRsp
	(*GroupTaskFlowRsp)(nil),                                 // 136: trpc.KEP.bot_task_config_server.GroupTaskFlowRsp
	(*ListTaskFlowRsp)(nil),                                  // 137: trpc.KEP.bot_task_config_server.ListTaskFlowRsp
	(*GetTaskFlowDetailRsp)(nil),                             // 138: trpc.KEP.bot_task_config_server.GetTaskFlowDetailRsp
	(*CreateTaskFlowRsp)(nil),                                // 139: trpc.KEP.bot_task_config_server.CreateTaskFlowRsp
	(*SaveTaskFlowRsp)(nil),                                  // 140: trpc.KEP.bot_task_config_server.SaveTaskFlowRsp
	(*DeleteTaskFlowRsp)(nil),                                // 141: trpc.KEP.bot_task_config_server.DeleteTaskFlowRsp
	(*ListTaskFlowPreviewRsp)(nil),                           // 142: trpc.KEP.bot_task_config_server.ListTaskFlowPreviewRsp
	(*ImportTaskFlowRsp)(nil),                                // 143: trpc.KEP.bot_task_config_server.ImportTaskFlowRsp
	(*ExportTaskFlowRsp)(nil),                                // 144: trpc.KEP.bot_task_config_server.ExportTaskFlowRsp
	(*InnerExportTaskFlowRsp)(nil),                           // 145: trpc.KEP.bot_task_config_server.InnerExportTaskFlowRsp
	(*PreviewTaskFlowRequestNodeRsp)(nil),                    // 146: trpc.KEP.bot_task_config_server.PreviewTaskFlowRequestNodeRsp
	(*PreviewTaskFlowAnswerNodeRsp)(nil),                     // 147: trpc.KEP.bot_task_config_server.PreviewTaskFlowAnswerNodeRsp
	(*PreviewAnswerNodeDocumentRsp)(nil),                     // 148: trpc.KEP.bot_task_config_server.PreviewAnswerNodeDocumentRsp
	(*CopyTaskFlowRsp)(nil),                                  // 149: trpc.KEP.bot_task_config_server.CopyTaskFlowRsp
	(*GetSlotListRsp)(nil),                                   // 150: trpc.KEP.bot_task_config_server.GetSlotListRsp
	(*CreateSlotRsp)(nil),                                    // 151: trpc.KEP.bot_task_config_server.CreateSlotRsp
	(*UpdateSlotRsp)(nil),                                    // 152: trpc.KEP.bot_task_config_server.UpdateSlotRsp
	(*DeleteSlotRsp)(nil),                                    // 153: trpc.KEP.bot_task_config_server.DeleteSlotRsp
	(*GetEntryListRsp)(nil),                                  // 154: trpc.KEP.bot_task_config_server.GetEntryListRsp
	(*CreateEntryRsp)(nil),                                   // 155: trpc.KEP.bot_task_config_server.CreateEntryRsp
	(*UpdateEntryRsp)(nil),                                   // 156: trpc.KEP.bot_task_config_server.UpdateEntryRsp
	(*DeleteEntryRsp)(nil),                                   // 157: trpc.KEP.bot_task_config_server.DeleteEntryRsp
	(*ImportEntryRsp)(nil),                                   // 158: trpc.KEP.bot_task_config_server.ImportEntryRsp
	(*RoleDefaultTemplateListRsp)(nil),                       // 159: trpc.KEP.bot_task_config_server.RoleDefaultTemplateListRsp
	(*CreateExampleRsp)(nil),                                 // 160: trpc.KEP.bot_task_config_server.CreateExampleRsp
	(*GetExampleListRsp)(nil),                                // 161: trpc.KEP.bot_task_config_server.GetExampleListRsp
	(*UpdateExampleRsp)(nil),                                 // 162: trpc.KEP.bot_task_config_server.UpdateExampleRsp
	(*DeleteExampleRsp)(nil),                                 // 163: trpc.KEP.bot_task_config_server.DeleteExampleRsp
	(*CreateVarRsp)(nil),                                     // 164: trpc.KEP.bot_task_config_server.CreateVarRsp
	(*UpdateVarRsp)(nil),                                     // 165: trpc.KEP.bot_task_config_server.UpdateVarRsp
	(*DeleteVarRsp)(nil),                                     // 166: trpc.KEP.bot_task_config_server.DeleteVarRsp
	(*DescribeVarRsp)(nil),                                   // 167: trpc.KEP.bot_task_config_server.DescribeVarRsp
	(*GetVarListRsp)(nil),                                    // 168: trpc.KEP.bot_task_config_server.GetVarListRsp
	(*GetSystemVarListRsp)(nil),                              // 169: trpc.KEP.bot_task_config_server.GetSystemVarListRsp
	(*GetPromptWordTemplateListRsp)(nil),                     // 170: trpc.KEP.bot_task_config_server.GetPromptWordTemplateListRsp
	(*KEP_WF.CreateWorkflowRsp)(nil),                         // 171: trpc.KEP.bot_task_config_server.CreateWorkflowRsp
	(*KEP_WF.SaveWorkflowRsp)(nil),                           // 172: trpc.KEP.bot_task_config_server.SaveWorkflowRsp
	(*KEP_WF.CopyWorkflowRsp)(nil),                           // 173: trpc.KEP.bot_task_config_server.CopyWorkflowRsp
	(*KEP_WF.DeleteWorkflowRsp)(nil),                         // 174: trpc.KEP.bot_task_config_server.DeleteWorkflowRsp
	(*KEP_WF.ListWorkflowRsp)(nil),                           // 175: trpc.KEP.bot_task_config_server.ListWorkflowRsp
	(*KEP_WF.ListWorkflowInnerRsp)(nil),                      // 176: trpc.KEP.bot_task_config_server.ListWorkflowInnerRsp
	(*KEP_WF.GetWorkflowDetailResp)(nil),                     // 177: trpc.KEP.bot_task_config_server.GetWorkflowDetailResp
	(*KEP_WF.ListNodeInfoRsp)(nil),                           // 178: trpc.KEP.bot_task_config_server.ListNodeInfoRsp
	(*KEP_WF.ListWorkflowNodeModelRsp)(nil),                  // 179: trpc.KEP.bot_task_config_server.ListWorkflowNodeModelRsp
	(*KEP_WF.ListWorkflowInfoByModelNameRsp)(nil),            // 180: trpc.KEP.bot_task_config_server.ListWorkflowInfoByModelNameRsp
	(*KEP_WF.SaveAgentWorkflowRsp)(nil),                      // 181: trpc.KEP.bot_task_config_server.SaveAgentWorkflowRsp
	(*KEP_WF.ListAgentWorkflowRsp)(nil),                      // 182: trpc.KEP.bot_task_config_server.ListAgentWorkflowRsp
	(*KEP_WF.GetAgentWorkflowInfoRsp)(nil),                   // 183: trpc.KEP.bot_task_config_server.GetAgentWorkflowInfoRsp
	(*KEP_WF.GetAgentWorkflowDetailRsp)(nil),                 // 184: trpc.KEP.bot_task_config_server.GetAgentWorkflowDetailRsp
	(*KEP_WF.GetAgentWorkflowStateRsp)(nil),                  // 185: trpc.KEP.bot_task_config_server.GetAgentWorkflowStateRsp
	(*KEP_WF.ConvertToAgentWorkflowRsp)(nil),                 // 186: trpc.KEP.bot_task_config_server.ConvertToAgentWorkflowRsp
	(*KEP_WF.SwitchAgentWorkflowStateRsp)(nil),               // 187: trpc.KEP.bot_task_config_server.SwitchAgentWorkflowStateRsp
	(*KEP_WF.ListPDLVersionRsp)(nil),                         // 188: trpc.KEP.bot_task_config_server.ListPDLVersionRsp
	(*KEP_WF.GetPDLVersionDetailRsp)(nil),                    // 189: trpc.KEP.bot_task_config_server.GetPDLVersionDetailRsp
	(*KEP_WF.CreateWorkflowExampleRsp)(nil),                  // 190: trpc.KEP.bot_task_config_server.CreateWorkflowExampleRsp
	(*KEP_WF.ListWorkflowExampleRsp)(nil),                    // 191: trpc.KEP.bot_task_config_server.ListWorkflowExampleRsp
	(*KEP_WF.UpdateWorkflowExampleRsp)(nil),                  // 192: trpc.KEP.bot_task_config_server.UpdateWorkflowExampleRsp
	(*KEP_WF.DeleteWorkflowExampleRsp)(nil),                  // 193: trpc.KEP.bot_task_config_server.DeleteWorkflowExampleRsp
	(*KEP_WF.ImportWfExampleRsp)(nil),                        // 194: trpc.KEP.bot_task_config_server.ImportWfExampleRsp
	(*KEP_WF.SwitchWorkflowStateRsp)(nil),                    // 195: trpc.KEP.bot_task_config_server.SwitchWorkflowStateRsp
	(*KEP_WF.AddFlowFeedbackRsp)(nil),                        // 196: trpc.KEP.bot_task_config_server.AddFlowFeedbackRsp
	(*KEP_WF.UpdateFlowFeedbackRsp)(nil),                     // 197: trpc.KEP.bot_task_config_server.UpdateFlowFeedbackRsp
	(*KEP_WF.DescribeWorkflowFeedRsp)(nil),                   // 198: trpc.KEP.bot_task_config_server.DescribeWorkflowFeedRsp
	(*KEP_WF.DeleteWorkflowFeedbackRsp)(nil),                 // 199: trpc.KEP.bot_task_config_server.DeleteWorkflowFeedbackRsp
	(*KEP_WF.ListFlowFeedbackRsp)(nil),                       // 200: trpc.KEP.bot_task_config_server.ListFlowFeedbackRsp
	(*KEP_WF.UpdateWorkflowFeedbackStatusRsp)(nil),           // 201: trpc.KEP.bot_task_config_server.UpdateWorkflowFeedbackStatusRsp
	(*KEP_WF.UpdateWorkflowFeedbackTapdRsp)(nil),             // 202: trpc.KEP.bot_task_config_server.UpdateWorkflowFeedbackTapdRsp
	(*KEP_WF.ImportWorkflowRsp)(nil),                         // 203: trpc.KEP.bot_task_config_server.ImportWorkflowRsp
	(*KEP_WF.ExportWorkflowRsp)(nil),                         // 204: trpc.KEP.bot_task_config_server.ExportWorkflowRsp
	(*KEP_WF.DebugWorkflowNodeRsp)(nil),                      // 205: trpc.KEP.bot_task_config_server.DebugWorkflowNodeRsp
	(*KEP_WF.GetEnableCustomAskResp)(nil),                    // 206: trpc.KEP.bot_task_config_server.GetEnableCustomAskResp
	(*KEP_WF.CreateParameterResp)(nil),                       // 207: trpc.KEP.bot_task_config_server.CreateParameterResp
	(*KEP_WF.GetParameterListResp)(nil),                      // 208: trpc.KEP.bot_task_config_server.GetParameterListResp
	(*KEP_WF.UpdateParameterResp)(nil),                       // 209: trpc.KEP.bot_task_config_server.UpdateParameterResp
	(*KEP_WF.DeleteParameterResp)(nil),                       // 210: trpc.KEP.bot_task_config_server.DeleteParameterResp
	(*KEP_WF.GetBotNodeParameterListResp)(nil),               // 211: trpc.KEP.bot_task_config_server.GetBotNodeParameterListResp
	(*KEP_WF.ClearAppFlowResourceRsp)(nil),                   // 212: trpc.KEP.bot_task_config_server.ClearAppFlowResourceRsp
	(*KEP_WF.DeleteAppResourceRsp)(nil),                      // 213: trpc.KEP.bot_task_config_server.DeleteAppResourceRsp
	(*KEP_WF.GetCanBeReferencedWorkflowListRsp)(nil),         // 214: trpc.KEP.bot_task_config_server.GetCanBeReferencedWorkflowListRsp
	(*KEP_WF.GetHasBeenReferencedWorkflowListRsp)(nil),       // 215: trpc.KEP.bot_task_config_server.GetHasBeenReferencedWorkflowListRsp
	(*KEP_WF.GetHasReferencedPluginToolWorkflowListRsp)(nil), // 216: trpc.KEP.bot_task_config_server.GetHasReferencedPluginToolWorkflowListRsp
	(*KEP_WF.GetParamsByWorkflowIdsRsp)(nil),                 // 217: trpc.KEP.bot_task_config_server.GetParamsByWorkflowIdsRsp
	(*KEP_WF.GetWorkflowGuideViewedResponse)(nil),            // 218: trpc.KEP.bot_task_config_server.GetWorkflowGuideViewedResponse
	(*KEP_WF.MarkWorkflowGuideViewedResponse)(nil),           // 219: trpc.KEP.bot_task_config_server.MarkWorkflowGuideViewedResponse
	(*KEP_WF.GetExperienceWorkflowListRsp)(nil),              // 220: trpc.KEP.bot_task_config_server.GetExperienceWorkflowListRsp
	(*KEP_WF.CreateExperienceWorkflowRsp)(nil),               // 221: trpc.KEP.bot_task_config_server.CreateExperienceWorkflowRsp
	(*KEP_WF.CanCreateWorkflowRunRsp)(nil),                   // 222: trpc.KEP.bot_task_config_server.CanCreateWorkflowRunRsp
	(*KEP_WF.SaveAppDebugModeRsp)(nil),                       // 223: trpc.KEP.bot_task_config_server.SaveAppDebugModeRsp
	(*KEP_WF.SaveAppDebugCustomVariablesRsp)(nil),            // 224: trpc.KEP.bot_task_config_server.SaveAppDebugCustomVariablesRsp
	(*KEP_WF.DescribeAppDebugConfigRsp)(nil),                 // 225: trpc.KEP.bot_task_config_server.DescribeAppDebugConfigRsp
	(*KEP_WF.CreateWorkflowRunRsp)(nil),                      // 226: trpc.KEP.bot_task_config_server.CreateWorkflowRunRsp
	(*KEP_WF.ListWorkflowRunsRsp)(nil),                       // 227: trpc.KEP.bot_task_config_server.ListWorkflowRunsRsp
	(*KEP_WF.DescribeWorkflowRunRsp)(nil),                    // 228: trpc.KEP.bot_task_config_server.DescribeWorkflowRunRsp
	(*KEP_WF.DescribeNodeRunRsp)(nil),                        // 229: trpc.KEP.bot_task_config_server.DescribeNodeRunRsp
	(*KEP_WF.StopWorkflowRunRsp)(nil),                        // 230: trpc.KEP.bot_task_config_server.StopWorkflowRunRsp
	(*KEP_WF.GetWorkflowListByDocRsp)(nil),                   // 231: trpc.KEP.bot_task_config_server.GetWorkflowListByDocRsp
	(*KEP_WF.GetWorkflowListByAttributeRsp)(nil),             // 232: trpc.KEP.bot_task_config_server.GetWorkflowListByAttributeRsp
	(*KEP_WF.GetWorkflowListByAttributeLabelRsp)(nil),        // 233: trpc.KEP.bot_task_config_server.GetWorkflowListByAttributeLabelRsp
}
var file_task_config_proto_depIdxs = []int32{
	0,   // 0: trpc.KEP.bot_task_config_server.TaskConfig.GetAppChatInputNum:input_type -> trpc.KEP.bot_task_config_server.GetAppChatInputNumReq
	1,   // 1: trpc.KEP.bot_task_config_server.TaskConfig.GetAppShareURL:input_type -> trpc.KEP.bot_task_config_server.GetAppShareURLReq
	2,   // 2: trpc.KEP.bot_task_config_server.TaskConfig.GetRobotIdByShareCode:input_type -> trpc.KEP.bot_task_config_server.GetRobotIdByShareCodeReq
	3,   // 3: trpc.KEP.bot_task_config_server.TaskConfig.GetModelSupportWorkflow:input_type -> trpc.KEP.bot_task_config_server.GetModelSupportWorkflowReq
	4,   // 4: trpc.KEP.bot_task_config_server.TaskConfig.GetWorkflowReleaseStatus:input_type -> trpc.KEP.bot_task_config_server.GetWorkflowReleaseStatusReq
	5,   // 5: trpc.KEP.bot_task_config_server.TaskConfig.GetTaskFlowReleaseStatus:input_type -> trpc.KEP.bot_task_config_server.GetTaskFlowReleaseStatusReq
	6,   // 6: trpc.KEP.bot_task_config_server.TaskConfig.GetUnreleasedCount:input_type -> trpc.KEP.bot_task_config_server.GetUnreleasedCountReq
	7,   // 7: trpc.KEP.bot_task_config_server.TaskConfig.SendDataSyncTaskEvent:input_type -> trpc.KEP.bot_task_config_server.SendDataSyncTaskEventReq
	8,   // 8: trpc.KEP.bot_task_config_server.TaskConfig.GetDataSyncTask:input_type -> trpc.KEP.bot_task_config_server.GetDataSyncTaskReq
	9,   // 9: trpc.KEP.bot_task_config_server.TaskConfig.GetDataSyncTasks:input_type -> trpc.KEP.bot_task_config_server.GetDataSyncTasksReq
	10,  // 10: trpc.KEP.bot_task_config_server.TaskConfig.CheckRobotReady:input_type -> trpc.KEP.bot_task_config_server.CheckRobotReadyReq
	11,  // 11: trpc.KEP.bot_task_config_server.TaskConfig.ReleaseAPIVarParams:input_type -> trpc.KEP.bot_task_config_server.ReleaseAPIVarParamsReq
	12,  // 12: trpc.KEP.bot_task_config_server.TaskConfig.ListCategory:input_type -> trpc.KEP.bot_task_config_server.ListCategoryReq
	13,  // 13: trpc.KEP.bot_task_config_server.TaskConfig.CreateCategory:input_type -> trpc.KEP.bot_task_config_server.CreateCategoryReq
	14,  // 14: trpc.KEP.bot_task_config_server.TaskConfig.UpdateCategory:input_type -> trpc.KEP.bot_task_config_server.UpdateCategoryReq
	15,  // 15: trpc.KEP.bot_task_config_server.TaskConfig.DeleteCategory:input_type -> trpc.KEP.bot_task_config_server.DeleteCategoryReq
	16,  // 16: trpc.KEP.bot_task_config_server.TaskConfig.RecoverHistoryTaskFlow:input_type -> trpc.KEP.bot_task_config_server.RecoverHistoryTaskFlowReq
	17,  // 17: trpc.KEP.bot_task_config_server.TaskConfig.ListHistoryTaskFlow:input_type -> trpc.KEP.bot_task_config_server.ListHistoryTaskFlowReq
	18,  // 18: trpc.KEP.bot_task_config_server.TaskConfig.DescribeHistoryTaskFlow:input_type -> trpc.KEP.bot_task_config_server.DescribeHistoryTaskFlowReq
	19,  // 19: trpc.KEP.bot_task_config_server.TaskConfig.GroupTaskFlow:input_type -> trpc.KEP.bot_task_config_server.GroupTaskFlowReq
	20,  // 20: trpc.KEP.bot_task_config_server.TaskConfig.ListTaskFlow:input_type -> trpc.KEP.bot_task_config_server.ListTaskFlowReq
	21,  // 21: trpc.KEP.bot_task_config_server.TaskConfig.GetTaskFlowDetail:input_type -> trpc.KEP.bot_task_config_server.GetTaskFlowDetailReq
	22,  // 22: trpc.KEP.bot_task_config_server.TaskConfig.CreateTaskFlow:input_type -> trpc.KEP.bot_task_config_server.CreateTaskFlowReq
	23,  // 23: trpc.KEP.bot_task_config_server.TaskConfig.SaveTaskFlow:input_type -> trpc.KEP.bot_task_config_server.SaveTaskFlowReq
	24,  // 24: trpc.KEP.bot_task_config_server.TaskConfig.DeleteTaskFlow:input_type -> trpc.KEP.bot_task_config_server.DeleteTaskFlowReq
	25,  // 25: trpc.KEP.bot_task_config_server.TaskConfig.ListTaskFlowPreview:input_type -> trpc.KEP.bot_task_config_server.ListTaskFlowPreviewReq
	26,  // 26: trpc.KEP.bot_task_config_server.TaskConfig.ImportTaskFlow:input_type -> trpc.KEP.bot_task_config_server.ImportTaskFlowReq
	27,  // 27: trpc.KEP.bot_task_config_server.TaskConfig.ExportTaskFlow:input_type -> trpc.KEP.bot_task_config_server.ExportTaskFlowReq
	28,  // 28: trpc.KEP.bot_task_config_server.TaskConfig.InnerExportTaskFlow:input_type -> trpc.KEP.bot_task_config_server.InnerExportTaskFlowReq
	29,  // 29: trpc.KEP.bot_task_config_server.TaskConfig.PreviewTaskFlowRequestNode:input_type -> trpc.KEP.bot_task_config_server.PreviewTaskFlowRequestNodeReq
	30,  // 30: trpc.KEP.bot_task_config_server.TaskConfig.PreviewTaskFlowAnswerNode:input_type -> trpc.KEP.bot_task_config_server.PreviewTaskFlowAnswerNodeReq
	31,  // 31: trpc.KEP.bot_task_config_server.TaskConfig.PreviewAnswerNodeDocument:input_type -> trpc.KEP.bot_task_config_server.PreviewAnswerNodeDocumentReq
	32,  // 32: trpc.KEP.bot_task_config_server.TaskConfig.CopyTaskFlow:input_type -> trpc.KEP.bot_task_config_server.CopyTaskFlowReq
	33,  // 33: trpc.KEP.bot_task_config_server.TaskConfig.GetSlotList:input_type -> trpc.KEP.bot_task_config_server.GetSlotListReq
	34,  // 34: trpc.KEP.bot_task_config_server.TaskConfig.CreateSlot:input_type -> trpc.KEP.bot_task_config_server.CreateSlotReq
	35,  // 35: trpc.KEP.bot_task_config_server.TaskConfig.UpdateSlot:input_type -> trpc.KEP.bot_task_config_server.UpdateSlotReq
	36,  // 36: trpc.KEP.bot_task_config_server.TaskConfig.DeleteSlot:input_type -> trpc.KEP.bot_task_config_server.DeleteSlotReq
	37,  // 37: trpc.KEP.bot_task_config_server.TaskConfig.GetEntryList:input_type -> trpc.KEP.bot_task_config_server.GetEntryListReq
	38,  // 38: trpc.KEP.bot_task_config_server.TaskConfig.CreateEntry:input_type -> trpc.KEP.bot_task_config_server.CreateEntryReq
	39,  // 39: trpc.KEP.bot_task_config_server.TaskConfig.UpdateEntry:input_type -> trpc.KEP.bot_task_config_server.UpdateEntryReq
	40,  // 40: trpc.KEP.bot_task_config_server.TaskConfig.DeleteEntry:input_type -> trpc.KEP.bot_task_config_server.DeleteEntryReq
	41,  // 41: trpc.KEP.bot_task_config_server.TaskConfig.ImportEntry:input_type -> trpc.KEP.bot_task_config_server.ImportEntryReq
	42,  // 42: trpc.KEP.bot_task_config_server.TaskConfig.RoleDefaultTemplateList:input_type -> trpc.KEP.bot_task_config_server.RoleDefaultTemplateListReq
	43,  // 43: trpc.KEP.bot_task_config_server.TaskConfig.CreateExample:input_type -> trpc.KEP.bot_task_config_server.CreateExampleReq
	44,  // 44: trpc.KEP.bot_task_config_server.TaskConfig.GetExampleList:input_type -> trpc.KEP.bot_task_config_server.GetExampleListReq
	45,  // 45: trpc.KEP.bot_task_config_server.TaskConfig.UpdateExample:input_type -> trpc.KEP.bot_task_config_server.UpdateExampleReq
	46,  // 46: trpc.KEP.bot_task_config_server.TaskConfig.DeleteExample:input_type -> trpc.KEP.bot_task_config_server.DeleteExampleReq
	47,  // 47: trpc.KEP.bot_task_config_server.TaskConfig.CreateVar:input_type -> trpc.KEP.bot_task_config_server.CreateVarReq
	48,  // 48: trpc.KEP.bot_task_config_server.TaskConfig.UpdateVar:input_type -> trpc.KEP.bot_task_config_server.UpdateVarReq
	49,  // 49: trpc.KEP.bot_task_config_server.TaskConfig.DeleteVar:input_type -> trpc.KEP.bot_task_config_server.DeleteVarReq
	50,  // 50: trpc.KEP.bot_task_config_server.TaskConfig.DescribeVar:input_type -> trpc.KEP.bot_task_config_server.DescribeVarReq
	51,  // 51: trpc.KEP.bot_task_config_server.TaskConfig.GetVarList:input_type -> trpc.KEP.bot_task_config_server.GetVarListReq
	52,  // 52: trpc.KEP.bot_task_config_server.TaskConfig.GetSystemVarList:input_type -> trpc.KEP.bot_task_config_server.GetSystemVarListReq
	53,  // 53: trpc.KEP.bot_task_config_server.TaskConfig.GetPromptWordTemplateList:input_type -> trpc.KEP.bot_task_config_server.GetPromptWordTemplateListReq
	54,  // 54: trpc.KEP.bot_task_config_server.TaskConfig.CreateWorkflow:input_type -> trpc.KEP.bot_task_config_server.CreateWorkflowReq
	55,  // 55: trpc.KEP.bot_task_config_server.TaskConfig.SaveWorkflow:input_type -> trpc.KEP.bot_task_config_server.SaveWorkflowReq
	56,  // 56: trpc.KEP.bot_task_config_server.TaskConfig.CopyWorkflow:input_type -> trpc.KEP.bot_task_config_server.CopyWorkflowReq
	57,  // 57: trpc.KEP.bot_task_config_server.TaskConfig.DeleteWorkflow:input_type -> trpc.KEP.bot_task_config_server.DeleteWorkflowReq
	58,  // 58: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflow:input_type -> trpc.KEP.bot_task_config_server.ListWorkflowReq
	59,  // 59: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflowInner:input_type -> trpc.KEP.bot_task_config_server.ListWorkflowInnerReq
	60,  // 60: trpc.KEP.bot_task_config_server.TaskConfig.GetWorkflowDetail:input_type -> trpc.KEP.bot_task_config_server.GetWorkflowDetailReq
	61,  // 61: trpc.KEP.bot_task_config_server.TaskConfig.ListNodeInfo:input_type -> trpc.KEP.bot_task_config_server.ListNodeInfoReq
	62,  // 62: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflowNodeModel:input_type -> trpc.KEP.bot_task_config_server.ListWorkflowNodeModelReq
	63,  // 63: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflowInfoByModelNameInner:input_type -> trpc.KEP.bot_task_config_server.ListWorkflowInfoByModelNameReq
	64,  // 64: trpc.KEP.bot_task_config_server.TaskConfig.SaveAgentWorkflow:input_type -> trpc.KEP.bot_task_config_server.SaveAgentWorkflowReq
	65,  // 65: trpc.KEP.bot_task_config_server.TaskConfig.ListAgentWorkflow:input_type -> trpc.KEP.bot_task_config_server.ListAgentWorkflowReq
	66,  // 66: trpc.KEP.bot_task_config_server.TaskConfig.GetAgentWorkflowInfo:input_type -> trpc.KEP.bot_task_config_server.GetAgentWorkflowInfoReq
	67,  // 67: trpc.KEP.bot_task_config_server.TaskConfig.GetAgentWorkflowDetail:input_type -> trpc.KEP.bot_task_config_server.GetAgentWorkflowDetailReq
	68,  // 68: trpc.KEP.bot_task_config_server.TaskConfig.GetAgentWorkflowState:input_type -> trpc.KEP.bot_task_config_server.GetAgentWorkflowStateReq
	69,  // 69: trpc.KEP.bot_task_config_server.TaskConfig.ConvertToAgentWorkflow:input_type -> trpc.KEP.bot_task_config_server.ConvertToAgentWorkflowReq
	70,  // 70: trpc.KEP.bot_task_config_server.TaskConfig.SwitchAgentWorkflowState:input_type -> trpc.KEP.bot_task_config_server.SwitchAgentWorkflowStateReq
	71,  // 71: trpc.KEP.bot_task_config_server.TaskConfig.ListPDLVersion:input_type -> trpc.KEP.bot_task_config_server.ListPDLVersionReq
	72,  // 72: trpc.KEP.bot_task_config_server.TaskConfig.GetPDLVersionDetail:input_type -> trpc.KEP.bot_task_config_server.GetPDLVersionDetailReq
	73,  // 73: trpc.KEP.bot_task_config_server.TaskConfig.CreateWorkflowExample:input_type -> trpc.KEP.bot_task_config_server.CreateWorkflowExampleReq
	74,  // 74: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflowExample:input_type -> trpc.KEP.bot_task_config_server.ListWorkflowExampleReq
	75,  // 75: trpc.KEP.bot_task_config_server.TaskConfig.UpdateWorkflowExample:input_type -> trpc.KEP.bot_task_config_server.UpdateWorkflowExampleReq
	76,  // 76: trpc.KEP.bot_task_config_server.TaskConfig.DeleteWorkflowExample:input_type -> trpc.KEP.bot_task_config_server.DeleteWorkflowExampleReq
	77,  // 77: trpc.KEP.bot_task_config_server.TaskConfig.ImportWorkflowExample:input_type -> trpc.KEP.bot_task_config_server.ImportWfExampleReq
	78,  // 78: trpc.KEP.bot_task_config_server.TaskConfig.SwitchWorkflowState:input_type -> trpc.KEP.bot_task_config_server.SwitchWorkflowStateReq
	79,  // 79: trpc.KEP.bot_task_config_server.TaskConfig.AddWorkflowFeedback:input_type -> trpc.KEP.bot_task_config_server.AddFlowFeedbackReq
	80,  // 80: trpc.KEP.bot_task_config_server.TaskConfig.UpdateWorkflowFeedback:input_type -> trpc.KEP.bot_task_config_server.UpdateFlowFeedbackReq
	81,  // 81: trpc.KEP.bot_task_config_server.TaskConfig.DescribeWorkflowFeedback:input_type -> trpc.KEP.bot_task_config_server.DescribeWorkflowFeedReq
	82,  // 82: trpc.KEP.bot_task_config_server.TaskConfig.DeleteWorkflowFeedback:input_type -> trpc.KEP.bot_task_config_server.DeleteWorkflowFeedbackReq
	83,  // 83: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflowFeedback:input_type -> trpc.KEP.bot_task_config_server.ListFlowFeedbackReq
	84,  // 84: trpc.KEP.bot_task_config_server.TaskConfig.UpdateWorkflowFeedbackStatus:input_type -> trpc.KEP.bot_task_config_server.UpdateWorkflowFeedbackStatusReq
	85,  // 85: trpc.KEP.bot_task_config_server.TaskConfig.UpdateWorkflowFeedbackTapd:input_type -> trpc.KEP.bot_task_config_server.UpdateWorkflowFeedbackTapdReq
	86,  // 86: trpc.KEP.bot_task_config_server.TaskConfig.ImportWorkflow:input_type -> trpc.KEP.bot_task_config_server.ImportWorkflowReq
	87,  // 87: trpc.KEP.bot_task_config_server.TaskConfig.ExportWorkflow:input_type -> trpc.KEP.bot_task_config_server.ExportWorkflowReq
	88,  // 88: trpc.KEP.bot_task_config_server.TaskConfig.DebugWorkflowNode:input_type -> trpc.KEP.bot_task_config_server.DebugWorkflowNodeReq
	89,  // 89: trpc.KEP.bot_task_config_server.TaskConfig.GetEnableCustomAsk:input_type -> trpc.KEP.bot_task_config_server.GetEnableCustomAskReq
	90,  // 90: trpc.KEP.bot_task_config_server.TaskConfig.CreateParameter:input_type -> trpc.KEP.bot_task_config_server.CreateParameterReq
	91,  // 91: trpc.KEP.bot_task_config_server.TaskConfig.GetParameterList:input_type -> trpc.KEP.bot_task_config_server.GetParameterListReq
	92,  // 92: trpc.KEP.bot_task_config_server.TaskConfig.UpdateParameter:input_type -> trpc.KEP.bot_task_config_server.UpdateParameterReq
	93,  // 93: trpc.KEP.bot_task_config_server.TaskConfig.DeleteParameter:input_type -> trpc.KEP.bot_task_config_server.DeleteParameterReq
	94,  // 94: trpc.KEP.bot_task_config_server.TaskConfig.GetBotNodeParameterList:input_type -> trpc.KEP.bot_task_config_server.GetBotNodeParameterListReq
	95,  // 95: trpc.KEP.bot_task_config_server.TaskConfig.ClearAppFlowResource:input_type -> trpc.KEP.bot_task_config_server.ClearAppFlowResourceReq
	96,  // 96: trpc.KEP.bot_task_config_server.TaskConfig.DeleteAppResource:input_type -> trpc.KEP.bot_task_config_server.DeleteAppResourceReq
	97,  // 97: trpc.KEP.bot_task_config_server.TaskConfig.GetCanBeReferencedWorkflowList:input_type -> trpc.KEP.bot_task_config_server.GetCanBeReferencedWorkflowListReq
	98,  // 98: trpc.KEP.bot_task_config_server.TaskConfig.GetHasBeenReferencedWorkflowList:input_type -> trpc.KEP.bot_task_config_server.GetHasBeenReferencedWorkflowListReq
	99,  // 99: trpc.KEP.bot_task_config_server.TaskConfig.GetHasReferencedPluginToolWorkflowList:input_type -> trpc.KEP.bot_task_config_server.GetHasReferencedPluginToolWorkflowListReq
	100, // 100: trpc.KEP.bot_task_config_server.TaskConfig.GetParamsByWorkflowIds:input_type -> trpc.KEP.bot_task_config_server.GetParamsByWorkflowIdsReq
	101, // 101: trpc.KEP.bot_task_config_server.TaskConfig.GetWorkflowGuideViewed:input_type -> trpc.KEP.bot_task_config_server.GetWorkflowGuideViewedRequest
	102, // 102: trpc.KEP.bot_task_config_server.TaskConfig.MarkWorkflowGuideViewed:input_type -> trpc.KEP.bot_task_config_server.MarkWorkflowGuideViewedRequest
	103, // 103: trpc.KEP.bot_task_config_server.TaskConfig.GetExperienceWorkflowList:input_type -> trpc.KEP.bot_task_config_server.GetExperienceWorkflowListReq
	104, // 104: trpc.KEP.bot_task_config_server.TaskConfig.CreateExperienceWorkflow:input_type -> trpc.KEP.bot_task_config_server.CreateExperienceWorkflowReq
	105, // 105: trpc.KEP.bot_task_config_server.TaskConfig.CanCreateWorkflowRun:input_type -> trpc.KEP.bot_task_config_server.CanCreateWorkflowRunReq
	106, // 106: trpc.KEP.bot_task_config_server.TaskConfig.SaveAppDebugMode:input_type -> trpc.KEP.bot_task_config_server.SaveAppDebugModeReq
	107, // 107: trpc.KEP.bot_task_config_server.TaskConfig.SaveAppDebugCustomVariables:input_type -> trpc.KEP.bot_task_config_server.SaveAppDebugCustomVariablesReq
	108, // 108: trpc.KEP.bot_task_config_server.TaskConfig.DescribeAppDebugConfig:input_type -> trpc.KEP.bot_task_config_server.DescribeAppDebugConfigReq
	109, // 109: trpc.KEP.bot_task_config_server.TaskConfig.CreateWorkflowRun:input_type -> trpc.KEP.bot_task_config_server.CreateWorkflowRunReq
	110, // 110: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflowRuns:input_type -> trpc.KEP.bot_task_config_server.ListWorkflowRunsReq
	111, // 111: trpc.KEP.bot_task_config_server.TaskConfig.DescribeWorkflowRun:input_type -> trpc.KEP.bot_task_config_server.DescribeWorkflowRunReq
	112, // 112: trpc.KEP.bot_task_config_server.TaskConfig.DescribeNodeRun:input_type -> trpc.KEP.bot_task_config_server.DescribeNodeRunReq
	113, // 113: trpc.KEP.bot_task_config_server.TaskConfig.StopWorkflowRun:input_type -> trpc.KEP.bot_task_config_server.StopWorkflowRunReq
	114, // 114: trpc.KEP.bot_task_config_server.TaskConfig.GetWorkflowListByDoc:input_type -> trpc.KEP.bot_task_config_server.GetWorkflowListByDocReq
	115, // 115: trpc.KEP.bot_task_config_server.TaskConfig.GetWorkflowListByAttribute:input_type -> trpc.KEP.bot_task_config_server.GetWorkflowListByAttributeReq
	116, // 116: trpc.KEP.bot_task_config_server.TaskConfig.GetWorkflowListByAttributeLabel:input_type -> trpc.KEP.bot_task_config_server.GetWorkflowListByAttributeLabelReq
	117, // 117: trpc.KEP.bot_task_config_server.TaskConfig.GetAppChatInputNum:output_type -> trpc.KEP.bot_task_config_server.GetAppChatInputNumRsp
	118, // 118: trpc.KEP.bot_task_config_server.TaskConfig.GetAppShareURL:output_type -> trpc.KEP.bot_task_config_server.GetAppShareURLResp
	119, // 119: trpc.KEP.bot_task_config_server.TaskConfig.GetRobotIdByShareCode:output_type -> trpc.KEP.bot_task_config_server.GetRobotIdByShareCodeResp
	120, // 120: trpc.KEP.bot_task_config_server.TaskConfig.GetModelSupportWorkflow:output_type -> trpc.KEP.bot_task_config_server.GetModelSupportWorkflowResp
	121, // 121: trpc.KEP.bot_task_config_server.TaskConfig.GetWorkflowReleaseStatus:output_type -> trpc.KEP.bot_task_config_server.GetWorkflowReleaseStatusResp
	122, // 122: trpc.KEP.bot_task_config_server.TaskConfig.GetTaskFlowReleaseStatus:output_type -> trpc.KEP.bot_task_config_server.GetTaskFlowReleaseStatusResp
	123, // 123: trpc.KEP.bot_task_config_server.TaskConfig.GetUnreleasedCount:output_type -> trpc.KEP.bot_task_config_server.GetUnreleasedCountRsp
	124, // 124: trpc.KEP.bot_task_config_server.TaskConfig.SendDataSyncTaskEvent:output_type -> trpc.KEP.bot_task_config_server.SendDataSyncTaskEventRsp
	125, // 125: trpc.KEP.bot_task_config_server.TaskConfig.GetDataSyncTask:output_type -> trpc.KEP.bot_task_config_server.GetDataSyncTaskRsp
	126, // 126: trpc.KEP.bot_task_config_server.TaskConfig.GetDataSyncTasks:output_type -> trpc.KEP.bot_task_config_server.GetDataSyncTasksRsp
	127, // 127: trpc.KEP.bot_task_config_server.TaskConfig.CheckRobotReady:output_type -> trpc.KEP.bot_task_config_server.CheckRobotReadyRsp
	128, // 128: trpc.KEP.bot_task_config_server.TaskConfig.ReleaseAPIVarParams:output_type -> trpc.KEP.bot_task_config_server.ReleaseAPIVarParamsRsp
	129, // 129: trpc.KEP.bot_task_config_server.TaskConfig.ListCategory:output_type -> trpc.KEP.bot_task_config_server.ListCategoryRsp
	130, // 130: trpc.KEP.bot_task_config_server.TaskConfig.CreateCategory:output_type -> trpc.KEP.bot_task_config_server.CreateCategoryRsp
	131, // 131: trpc.KEP.bot_task_config_server.TaskConfig.UpdateCategory:output_type -> trpc.KEP.bot_task_config_server.UpdateCategoryRsp
	132, // 132: trpc.KEP.bot_task_config_server.TaskConfig.DeleteCategory:output_type -> trpc.KEP.bot_task_config_server.DeleteCategoryRsp
	133, // 133: trpc.KEP.bot_task_config_server.TaskConfig.RecoverHistoryTaskFlow:output_type -> trpc.KEP.bot_task_config_server.RecoverHistoryTaskFlowRsp
	134, // 134: trpc.KEP.bot_task_config_server.TaskConfig.ListHistoryTaskFlow:output_type -> trpc.KEP.bot_task_config_server.ListHistoryTaskFlowRsp
	135, // 135: trpc.KEP.bot_task_config_server.TaskConfig.DescribeHistoryTaskFlow:output_type -> trpc.KEP.bot_task_config_server.DescribeHistoryTaskFlowRsp
	136, // 136: trpc.KEP.bot_task_config_server.TaskConfig.GroupTaskFlow:output_type -> trpc.KEP.bot_task_config_server.GroupTaskFlowRsp
	137, // 137: trpc.KEP.bot_task_config_server.TaskConfig.ListTaskFlow:output_type -> trpc.KEP.bot_task_config_server.ListTaskFlowRsp
	138, // 138: trpc.KEP.bot_task_config_server.TaskConfig.GetTaskFlowDetail:output_type -> trpc.KEP.bot_task_config_server.GetTaskFlowDetailRsp
	139, // 139: trpc.KEP.bot_task_config_server.TaskConfig.CreateTaskFlow:output_type -> trpc.KEP.bot_task_config_server.CreateTaskFlowRsp
	140, // 140: trpc.KEP.bot_task_config_server.TaskConfig.SaveTaskFlow:output_type -> trpc.KEP.bot_task_config_server.SaveTaskFlowRsp
	141, // 141: trpc.KEP.bot_task_config_server.TaskConfig.DeleteTaskFlow:output_type -> trpc.KEP.bot_task_config_server.DeleteTaskFlowRsp
	142, // 142: trpc.KEP.bot_task_config_server.TaskConfig.ListTaskFlowPreview:output_type -> trpc.KEP.bot_task_config_server.ListTaskFlowPreviewRsp
	143, // 143: trpc.KEP.bot_task_config_server.TaskConfig.ImportTaskFlow:output_type -> trpc.KEP.bot_task_config_server.ImportTaskFlowRsp
	144, // 144: trpc.KEP.bot_task_config_server.TaskConfig.ExportTaskFlow:output_type -> trpc.KEP.bot_task_config_server.ExportTaskFlowRsp
	145, // 145: trpc.KEP.bot_task_config_server.TaskConfig.InnerExportTaskFlow:output_type -> trpc.KEP.bot_task_config_server.InnerExportTaskFlowRsp
	146, // 146: trpc.KEP.bot_task_config_server.TaskConfig.PreviewTaskFlowRequestNode:output_type -> trpc.KEP.bot_task_config_server.PreviewTaskFlowRequestNodeRsp
	147, // 147: trpc.KEP.bot_task_config_server.TaskConfig.PreviewTaskFlowAnswerNode:output_type -> trpc.KEP.bot_task_config_server.PreviewTaskFlowAnswerNodeRsp
	148, // 148: trpc.KEP.bot_task_config_server.TaskConfig.PreviewAnswerNodeDocument:output_type -> trpc.KEP.bot_task_config_server.PreviewAnswerNodeDocumentRsp
	149, // 149: trpc.KEP.bot_task_config_server.TaskConfig.CopyTaskFlow:output_type -> trpc.KEP.bot_task_config_server.CopyTaskFlowRsp
	150, // 150: trpc.KEP.bot_task_config_server.TaskConfig.GetSlotList:output_type -> trpc.KEP.bot_task_config_server.GetSlotListRsp
	151, // 151: trpc.KEP.bot_task_config_server.TaskConfig.CreateSlot:output_type -> trpc.KEP.bot_task_config_server.CreateSlotRsp
	152, // 152: trpc.KEP.bot_task_config_server.TaskConfig.UpdateSlot:output_type -> trpc.KEP.bot_task_config_server.UpdateSlotRsp
	153, // 153: trpc.KEP.bot_task_config_server.TaskConfig.DeleteSlot:output_type -> trpc.KEP.bot_task_config_server.DeleteSlotRsp
	154, // 154: trpc.KEP.bot_task_config_server.TaskConfig.GetEntryList:output_type -> trpc.KEP.bot_task_config_server.GetEntryListRsp
	155, // 155: trpc.KEP.bot_task_config_server.TaskConfig.CreateEntry:output_type -> trpc.KEP.bot_task_config_server.CreateEntryRsp
	156, // 156: trpc.KEP.bot_task_config_server.TaskConfig.UpdateEntry:output_type -> trpc.KEP.bot_task_config_server.UpdateEntryRsp
	157, // 157: trpc.KEP.bot_task_config_server.TaskConfig.DeleteEntry:output_type -> trpc.KEP.bot_task_config_server.DeleteEntryRsp
	158, // 158: trpc.KEP.bot_task_config_server.TaskConfig.ImportEntry:output_type -> trpc.KEP.bot_task_config_server.ImportEntryRsp
	159, // 159: trpc.KEP.bot_task_config_server.TaskConfig.RoleDefaultTemplateList:output_type -> trpc.KEP.bot_task_config_server.RoleDefaultTemplateListRsp
	160, // 160: trpc.KEP.bot_task_config_server.TaskConfig.CreateExample:output_type -> trpc.KEP.bot_task_config_server.CreateExampleRsp
	161, // 161: trpc.KEP.bot_task_config_server.TaskConfig.GetExampleList:output_type -> trpc.KEP.bot_task_config_server.GetExampleListRsp
	162, // 162: trpc.KEP.bot_task_config_server.TaskConfig.UpdateExample:output_type -> trpc.KEP.bot_task_config_server.UpdateExampleRsp
	163, // 163: trpc.KEP.bot_task_config_server.TaskConfig.DeleteExample:output_type -> trpc.KEP.bot_task_config_server.DeleteExampleRsp
	164, // 164: trpc.KEP.bot_task_config_server.TaskConfig.CreateVar:output_type -> trpc.KEP.bot_task_config_server.CreateVarRsp
	165, // 165: trpc.KEP.bot_task_config_server.TaskConfig.UpdateVar:output_type -> trpc.KEP.bot_task_config_server.UpdateVarRsp
	166, // 166: trpc.KEP.bot_task_config_server.TaskConfig.DeleteVar:output_type -> trpc.KEP.bot_task_config_server.DeleteVarRsp
	167, // 167: trpc.KEP.bot_task_config_server.TaskConfig.DescribeVar:output_type -> trpc.KEP.bot_task_config_server.DescribeVarRsp
	168, // 168: trpc.KEP.bot_task_config_server.TaskConfig.GetVarList:output_type -> trpc.KEP.bot_task_config_server.GetVarListRsp
	169, // 169: trpc.KEP.bot_task_config_server.TaskConfig.GetSystemVarList:output_type -> trpc.KEP.bot_task_config_server.GetSystemVarListRsp
	170, // 170: trpc.KEP.bot_task_config_server.TaskConfig.GetPromptWordTemplateList:output_type -> trpc.KEP.bot_task_config_server.GetPromptWordTemplateListRsp
	171, // 171: trpc.KEP.bot_task_config_server.TaskConfig.CreateWorkflow:output_type -> trpc.KEP.bot_task_config_server.CreateWorkflowRsp
	172, // 172: trpc.KEP.bot_task_config_server.TaskConfig.SaveWorkflow:output_type -> trpc.KEP.bot_task_config_server.SaveWorkflowRsp
	173, // 173: trpc.KEP.bot_task_config_server.TaskConfig.CopyWorkflow:output_type -> trpc.KEP.bot_task_config_server.CopyWorkflowRsp
	174, // 174: trpc.KEP.bot_task_config_server.TaskConfig.DeleteWorkflow:output_type -> trpc.KEP.bot_task_config_server.DeleteWorkflowRsp
	175, // 175: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflow:output_type -> trpc.KEP.bot_task_config_server.ListWorkflowRsp
	176, // 176: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflowInner:output_type -> trpc.KEP.bot_task_config_server.ListWorkflowInnerRsp
	177, // 177: trpc.KEP.bot_task_config_server.TaskConfig.GetWorkflowDetail:output_type -> trpc.KEP.bot_task_config_server.GetWorkflowDetailResp
	178, // 178: trpc.KEP.bot_task_config_server.TaskConfig.ListNodeInfo:output_type -> trpc.KEP.bot_task_config_server.ListNodeInfoRsp
	179, // 179: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflowNodeModel:output_type -> trpc.KEP.bot_task_config_server.ListWorkflowNodeModelRsp
	180, // 180: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflowInfoByModelNameInner:output_type -> trpc.KEP.bot_task_config_server.ListWorkflowInfoByModelNameRsp
	181, // 181: trpc.KEP.bot_task_config_server.TaskConfig.SaveAgentWorkflow:output_type -> trpc.KEP.bot_task_config_server.SaveAgentWorkflowRsp
	182, // 182: trpc.KEP.bot_task_config_server.TaskConfig.ListAgentWorkflow:output_type -> trpc.KEP.bot_task_config_server.ListAgentWorkflowRsp
	183, // 183: trpc.KEP.bot_task_config_server.TaskConfig.GetAgentWorkflowInfo:output_type -> trpc.KEP.bot_task_config_server.GetAgentWorkflowInfoRsp
	184, // 184: trpc.KEP.bot_task_config_server.TaskConfig.GetAgentWorkflowDetail:output_type -> trpc.KEP.bot_task_config_server.GetAgentWorkflowDetailRsp
	185, // 185: trpc.KEP.bot_task_config_server.TaskConfig.GetAgentWorkflowState:output_type -> trpc.KEP.bot_task_config_server.GetAgentWorkflowStateRsp
	186, // 186: trpc.KEP.bot_task_config_server.TaskConfig.ConvertToAgentWorkflow:output_type -> trpc.KEP.bot_task_config_server.ConvertToAgentWorkflowRsp
	187, // 187: trpc.KEP.bot_task_config_server.TaskConfig.SwitchAgentWorkflowState:output_type -> trpc.KEP.bot_task_config_server.SwitchAgentWorkflowStateRsp
	188, // 188: trpc.KEP.bot_task_config_server.TaskConfig.ListPDLVersion:output_type -> trpc.KEP.bot_task_config_server.ListPDLVersionRsp
	189, // 189: trpc.KEP.bot_task_config_server.TaskConfig.GetPDLVersionDetail:output_type -> trpc.KEP.bot_task_config_server.GetPDLVersionDetailRsp
	190, // 190: trpc.KEP.bot_task_config_server.TaskConfig.CreateWorkflowExample:output_type -> trpc.KEP.bot_task_config_server.CreateWorkflowExampleRsp
	191, // 191: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflowExample:output_type -> trpc.KEP.bot_task_config_server.ListWorkflowExampleRsp
	192, // 192: trpc.KEP.bot_task_config_server.TaskConfig.UpdateWorkflowExample:output_type -> trpc.KEP.bot_task_config_server.UpdateWorkflowExampleRsp
	193, // 193: trpc.KEP.bot_task_config_server.TaskConfig.DeleteWorkflowExample:output_type -> trpc.KEP.bot_task_config_server.DeleteWorkflowExampleRsp
	194, // 194: trpc.KEP.bot_task_config_server.TaskConfig.ImportWorkflowExample:output_type -> trpc.KEP.bot_task_config_server.ImportWfExampleRsp
	195, // 195: trpc.KEP.bot_task_config_server.TaskConfig.SwitchWorkflowState:output_type -> trpc.KEP.bot_task_config_server.SwitchWorkflowStateRsp
	196, // 196: trpc.KEP.bot_task_config_server.TaskConfig.AddWorkflowFeedback:output_type -> trpc.KEP.bot_task_config_server.AddFlowFeedbackRsp
	197, // 197: trpc.KEP.bot_task_config_server.TaskConfig.UpdateWorkflowFeedback:output_type -> trpc.KEP.bot_task_config_server.UpdateFlowFeedbackRsp
	198, // 198: trpc.KEP.bot_task_config_server.TaskConfig.DescribeWorkflowFeedback:output_type -> trpc.KEP.bot_task_config_server.DescribeWorkflowFeedRsp
	199, // 199: trpc.KEP.bot_task_config_server.TaskConfig.DeleteWorkflowFeedback:output_type -> trpc.KEP.bot_task_config_server.DeleteWorkflowFeedbackRsp
	200, // 200: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflowFeedback:output_type -> trpc.KEP.bot_task_config_server.ListFlowFeedbackRsp
	201, // 201: trpc.KEP.bot_task_config_server.TaskConfig.UpdateWorkflowFeedbackStatus:output_type -> trpc.KEP.bot_task_config_server.UpdateWorkflowFeedbackStatusRsp
	202, // 202: trpc.KEP.bot_task_config_server.TaskConfig.UpdateWorkflowFeedbackTapd:output_type -> trpc.KEP.bot_task_config_server.UpdateWorkflowFeedbackTapdRsp
	203, // 203: trpc.KEP.bot_task_config_server.TaskConfig.ImportWorkflow:output_type -> trpc.KEP.bot_task_config_server.ImportWorkflowRsp
	204, // 204: trpc.KEP.bot_task_config_server.TaskConfig.ExportWorkflow:output_type -> trpc.KEP.bot_task_config_server.ExportWorkflowRsp
	205, // 205: trpc.KEP.bot_task_config_server.TaskConfig.DebugWorkflowNode:output_type -> trpc.KEP.bot_task_config_server.DebugWorkflowNodeRsp
	206, // 206: trpc.KEP.bot_task_config_server.TaskConfig.GetEnableCustomAsk:output_type -> trpc.KEP.bot_task_config_server.GetEnableCustomAskResp
	207, // 207: trpc.KEP.bot_task_config_server.TaskConfig.CreateParameter:output_type -> trpc.KEP.bot_task_config_server.CreateParameterResp
	208, // 208: trpc.KEP.bot_task_config_server.TaskConfig.GetParameterList:output_type -> trpc.KEP.bot_task_config_server.GetParameterListResp
	209, // 209: trpc.KEP.bot_task_config_server.TaskConfig.UpdateParameter:output_type -> trpc.KEP.bot_task_config_server.UpdateParameterResp
	210, // 210: trpc.KEP.bot_task_config_server.TaskConfig.DeleteParameter:output_type -> trpc.KEP.bot_task_config_server.DeleteParameterResp
	211, // 211: trpc.KEP.bot_task_config_server.TaskConfig.GetBotNodeParameterList:output_type -> trpc.KEP.bot_task_config_server.GetBotNodeParameterListResp
	212, // 212: trpc.KEP.bot_task_config_server.TaskConfig.ClearAppFlowResource:output_type -> trpc.KEP.bot_task_config_server.ClearAppFlowResourceRsp
	213, // 213: trpc.KEP.bot_task_config_server.TaskConfig.DeleteAppResource:output_type -> trpc.KEP.bot_task_config_server.DeleteAppResourceRsp
	214, // 214: trpc.KEP.bot_task_config_server.TaskConfig.GetCanBeReferencedWorkflowList:output_type -> trpc.KEP.bot_task_config_server.GetCanBeReferencedWorkflowListRsp
	215, // 215: trpc.KEP.bot_task_config_server.TaskConfig.GetHasBeenReferencedWorkflowList:output_type -> trpc.KEP.bot_task_config_server.GetHasBeenReferencedWorkflowListRsp
	216, // 216: trpc.KEP.bot_task_config_server.TaskConfig.GetHasReferencedPluginToolWorkflowList:output_type -> trpc.KEP.bot_task_config_server.GetHasReferencedPluginToolWorkflowListRsp
	217, // 217: trpc.KEP.bot_task_config_server.TaskConfig.GetParamsByWorkflowIds:output_type -> trpc.KEP.bot_task_config_server.GetParamsByWorkflowIdsRsp
	218, // 218: trpc.KEP.bot_task_config_server.TaskConfig.GetWorkflowGuideViewed:output_type -> trpc.KEP.bot_task_config_server.GetWorkflowGuideViewedResponse
	219, // 219: trpc.KEP.bot_task_config_server.TaskConfig.MarkWorkflowGuideViewed:output_type -> trpc.KEP.bot_task_config_server.MarkWorkflowGuideViewedResponse
	220, // 220: trpc.KEP.bot_task_config_server.TaskConfig.GetExperienceWorkflowList:output_type -> trpc.KEP.bot_task_config_server.GetExperienceWorkflowListRsp
	221, // 221: trpc.KEP.bot_task_config_server.TaskConfig.CreateExperienceWorkflow:output_type -> trpc.KEP.bot_task_config_server.CreateExperienceWorkflowRsp
	222, // 222: trpc.KEP.bot_task_config_server.TaskConfig.CanCreateWorkflowRun:output_type -> trpc.KEP.bot_task_config_server.CanCreateWorkflowRunRsp
	223, // 223: trpc.KEP.bot_task_config_server.TaskConfig.SaveAppDebugMode:output_type -> trpc.KEP.bot_task_config_server.SaveAppDebugModeRsp
	224, // 224: trpc.KEP.bot_task_config_server.TaskConfig.SaveAppDebugCustomVariables:output_type -> trpc.KEP.bot_task_config_server.SaveAppDebugCustomVariablesRsp
	225, // 225: trpc.KEP.bot_task_config_server.TaskConfig.DescribeAppDebugConfig:output_type -> trpc.KEP.bot_task_config_server.DescribeAppDebugConfigRsp
	226, // 226: trpc.KEP.bot_task_config_server.TaskConfig.CreateWorkflowRun:output_type -> trpc.KEP.bot_task_config_server.CreateWorkflowRunRsp
	227, // 227: trpc.KEP.bot_task_config_server.TaskConfig.ListWorkflowRuns:output_type -> trpc.KEP.bot_task_config_server.ListWorkflowRunsRsp
	228, // 228: trpc.KEP.bot_task_config_server.TaskConfig.DescribeWorkflowRun:output_type -> trpc.KEP.bot_task_config_server.DescribeWorkflowRunRsp
	229, // 229: trpc.KEP.bot_task_config_server.TaskConfig.DescribeNodeRun:output_type -> trpc.KEP.bot_task_config_server.DescribeNodeRunRsp
	230, // 230: trpc.KEP.bot_task_config_server.TaskConfig.StopWorkflowRun:output_type -> trpc.KEP.bot_task_config_server.StopWorkflowRunRsp
	231, // 231: trpc.KEP.bot_task_config_server.TaskConfig.GetWorkflowListByDoc:output_type -> trpc.KEP.bot_task_config_server.GetWorkflowListByDocRsp
	232, // 232: trpc.KEP.bot_task_config_server.TaskConfig.GetWorkflowListByAttribute:output_type -> trpc.KEP.bot_task_config_server.GetWorkflowListByAttributeRsp
	233, // 233: trpc.KEP.bot_task_config_server.TaskConfig.GetWorkflowListByAttributeLabel:output_type -> trpc.KEP.bot_task_config_server.GetWorkflowListByAttributeLabelRsp
	117, // [117:234] is the sub-list for method output_type
	0,   // [0:117] is the sub-list for method input_type
	0,   // [0:0] is the sub-list for extension type_name
	0,   // [0:0] is the sub-list for extension extendee
	0,   // [0:0] is the sub-list for field type_name
}

func init() { file_task_config_proto_init() }
func file_task_config_proto_init() {
	if File_task_config_proto != nil {
		return
	}
	file_app_manage_proto_init()
	file_data_sync_proto_init()
	file_category_proto_init()
	file_task_flow_proto_init()
	file_slot_proto_init()
	file_role_proto_init()
	file_corpus_proto_init()
	file_var_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_task_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_task_config_proto_goTypes,
		DependencyIndexes: file_task_config_proto_depIdxs,
	}.Build()
	File_task_config_proto = out.File
	file_task_config_proto_rawDesc = nil
	file_task_config_proto_goTypes = nil
	file_task_config_proto_depIdxs = nil
}
