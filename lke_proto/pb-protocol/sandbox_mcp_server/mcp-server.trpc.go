// Code generated by trpc-go/trpc-go-cmdline v2.8.5. DO NOT EDIT.
// source: mcp-server.proto

package sandbox_mcp_server

import (
	"context"
	"errors"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/server"

	_ "git.code.oa.com/trpc-go/trpc-go"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
)

// START ======================================= Server Service Definition ======================================= START

// McpServerService defines service.
type McpServerService interface {
	// Echo Echo 方法用于测试服务是否部署成功
	//  @alias=/Echo
	Echo(ctx context.Context, req *EchoReq) (*EchoRsp, error)
}

func McpServerService_Echo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &EchoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(McpServerService).Echo(ctx, reqbody.(*EchoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// McpServerServer_ServiceDesc descriptor for server.RegisterService.
var McpServerServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.sandbox_mcp_server.McpServer",
	HandlerType: ((*McpServerService)(nil)),
	Methods: []server.Method{
		{
			Name: "/Echo",
			Func: McpServerService_Echo_Handler,
		},
		{
			Name: "/trpc.KEP.sandbox_mcp_server.McpServer/Echo",
			Func: McpServerService_Echo_Handler,
		},
	},
}

// RegisterMcpServerService registers service.
func RegisterMcpServerService(s server.Service, svr McpServerService) {
	if err := s.Register(&McpServerServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("McpServer register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedMcpServer struct{}

// Echo Echo 方法用于测试服务是否部署成功
//
//	@alias=/Echo
func (s *UnimplementedMcpServer) Echo(ctx context.Context, req *EchoReq) (*EchoRsp, error) {
	return nil, errors.New("rpc Echo of service McpServer is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// McpServerClientProxy defines service client proxy
type McpServerClientProxy interface {
	// Echo Echo 方法用于测试服务是否部署成功
	//  @alias=/Echo
	Echo(ctx context.Context, req *EchoReq, opts ...client.Option) (rsp *EchoRsp, err error)
}

type McpServerClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewMcpServerClientProxy = func(opts ...client.Option) McpServerClientProxy {
	return &McpServerClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *McpServerClientProxyImpl) Echo(ctx context.Context, req *EchoReq, opts ...client.Option) (*EchoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/Echo")
	msg.WithCalleeServiceName(McpServerServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("sandbox_mcp_server")
	msg.WithCalleeService("McpServer")
	msg.WithCalleeMethod("Echo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &EchoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
