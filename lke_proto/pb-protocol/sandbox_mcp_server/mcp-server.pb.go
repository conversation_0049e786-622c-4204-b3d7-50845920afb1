// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: mcp-server.proto

package sandbox_mcp_server

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EchoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EchoReq) Reset() {
	*x = EchoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mcp_server_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EchoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoReq) ProtoMessage() {}

func (x *EchoReq) ProtoReflect() protoreflect.Message {
	mi := &file_mcp_server_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoReq.ProtoReflect.Descriptor instead.
func (*EchoReq) Descriptor() ([]byte, []int) {
	return file_mcp_server_proto_rawDescGZIP(), []int{0}
}

type EchoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EchoRsp) Reset() {
	*x = EchoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_mcp_server_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EchoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoRsp) ProtoMessage() {}

func (x *EchoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_mcp_server_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoRsp.ProtoReflect.Descriptor instead.
func (*EchoRsp) Descriptor() ([]byte, []int) {
	return file_mcp_server_proto_rawDescGZIP(), []int{1}
}

var File_mcp_server_proto protoreflect.FileDescriptor

var file_mcp_server_proto_rawDesc = []byte{
	0x0a, 0x10, 0x6d, 0x63, 0x70, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1b, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x73, 0x61, 0x6e,
	0x64, 0x62, 0x6f, 0x78, 0x5f, 0x6d, 0x63, 0x70, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a,
	0x0a, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x09, 0x0a, 0x07, 0x45,
	0x63, 0x68, 0x6f, 0x52, 0x65, 0x71, 0x22, 0x09, 0x0a, 0x07, 0x45, 0x63, 0x68, 0x6f, 0x52, 0x73,
	0x70, 0x32, 0x61, 0x0a, 0x09, 0x4d, 0x63, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x54,
	0x0a, 0x04, 0x45, 0x63, 0x68, 0x6f, 0x12, 0x24, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x5f, 0x6d, 0x63, 0x70, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x63, 0x68, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x5f,
	0x6d, 0x63, 0x70, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x63, 0x68, 0x6f, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x42, 0x48, 0x5a, 0x46, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x73, 0x61, 0x6e, 0x64,
	0x62, 0x6f, 0x78, 0x5f, 0x6d, 0x63, 0x70, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_mcp_server_proto_rawDescOnce sync.Once
	file_mcp_server_proto_rawDescData = file_mcp_server_proto_rawDesc
)

func file_mcp_server_proto_rawDescGZIP() []byte {
	file_mcp_server_proto_rawDescOnce.Do(func() {
		file_mcp_server_proto_rawDescData = protoimpl.X.CompressGZIP(file_mcp_server_proto_rawDescData)
	})
	return file_mcp_server_proto_rawDescData
}

var file_mcp_server_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_mcp_server_proto_goTypes = []any{
	(*EchoReq)(nil), // 0: trpc.KEP.sandbox_mcp_server.EchoReq
	(*EchoRsp)(nil), // 1: trpc.KEP.sandbox_mcp_server.EchoRsp
}
var file_mcp_server_proto_depIdxs = []int32{
	0, // 0: trpc.KEP.sandbox_mcp_server.McpServer.Echo:input_type -> trpc.KEP.sandbox_mcp_server.EchoReq
	1, // 1: trpc.KEP.sandbox_mcp_server.McpServer.Echo:output_type -> trpc.KEP.sandbox_mcp_server.EchoRsp
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_mcp_server_proto_init() }
func file_mcp_server_proto_init() {
	if File_mcp_server_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_mcp_server_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*EchoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_mcp_server_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*EchoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_mcp_server_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_mcp_server_proto_goTypes,
		DependencyIndexes: file_mcp_server_proto_depIdxs,
		MessageInfos:      file_mcp_server_proto_msgTypes,
	}.Build()
	File_mcp_server_proto = out.File
	file_mcp_server_proto_rawDesc = nil
	file_mcp_server_proto_goTypes = nil
	file_mcp_server_proto_depIdxs = nil
}
