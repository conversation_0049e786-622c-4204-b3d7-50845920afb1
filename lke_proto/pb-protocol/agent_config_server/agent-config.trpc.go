// Code generated by trpc-go/trpc-go-cmdline v2.8.31. DO NOT EDIT.
// source: agent-config.proto

package agent_config_server

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
	KEP "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

// START ======================================= Server Service Definition ======================================= START

// AgentConfigService defines service.
type AgentConfigService interface {
	// Echo Echo 方法用于测试服务是否部署成功
	//  @alias=/Echo
	Echo(ctx context.Context, req *EchoReq) (*EchoRsp, error)
	// DescribeAgentConfig 查询Agent配置信息
	//  @alias=/DescribeAgentConfig
	DescribeAgentConfig(ctx context.Context, req *DescribeAgentConfigReq) (*DescribeAgentConfigRsp, error)
	// CreateAgent 创建Agent
	//  @alias=/CreateAgent
	CreateAgent(ctx context.Context, req *CreateAgentReq) (*CreateAgentRsp, error)
	// CreateStartingAgent 创建启动Agent
	//  @alias=/CreateStartingAgent
	CreateStartingAgent(ctx context.Context, req *CreateStartingAgentReq) (*CreateStartingAgentRsp, error)
	// CopyAgent 复制Agent，一般用来导入PDL作为协同Agent
	//  @alias=/CopyAgent
	CopyAgent(ctx context.Context, req *CopyAgentReq) (*CopyAgentRsp, error)
	// CopyAgentApp 复制Agent模式的App，一般用来复制Agent模式的App下的所有Agent
	//  @alias=/CopyAgentApp
	CopyAgentApp(ctx context.Context, req *CopyAgentAppReq) (*CopyAgentAppRsp, error)
	// DeleteAgent 删除Agent
	//  @alias=/DeleteAgent
	DeleteAgent(ctx context.Context, req *DeleteAgentReq) (*DeleteAgentRsp, error)
	// ModifyAgent 修改Agent
	//  @alias=/ModifyAgent
	ModifyAgent(ctx context.Context, req *ModifyAgentReq) (*ModifyAgentRsp, error)
	// ModifyAgentHandoffList 修改Agent转交关系列表
	//  @alias=/ModifyAgentHandoffList
	ModifyAgentHandoffList(ctx context.Context, req *ModifyAgentHandoffListReq) (*ModifyAgentHandoffListRsp, error)
	// DescribeAppAgentList 查询指定应用下的Agent列表
	//  @alias=/DescribeAppAgentList
	DescribeAppAgentList(ctx context.Context, req *DescribeAppAgentListReq) (*DescribeAppAgentListRsp, error)
	// DescribeAgentList 查询Agent列表
	//  @alias=/DescribeAgentList
	DescribeAgentList(ctx context.Context, req *DescribeAgentListReq) (*DescribeAgentListRsp, error)
	// ListAppAgentReleasePreview ======================= 发布 ==========================
	//  获取应用Agent发布列表
	//  @alias=/ListAppAgentReleasePreview
	ListAppAgentReleasePreview(ctx context.Context, req *ListAppAgentReleasePreviewReq) (*ListAppAgentReleasePreviewRsp, error) // ======================================================
}

func AgentConfigService_Echo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &EchoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigService).Echo(ctx, reqbody.(*EchoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigService_DescribeAgentConfig_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeAgentConfigReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigService).DescribeAgentConfig(ctx, reqbody.(*DescribeAgentConfigReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigService_CreateAgent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateAgentReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigService).CreateAgent(ctx, reqbody.(*CreateAgentReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigService_CreateStartingAgent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateStartingAgentReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigService).CreateStartingAgent(ctx, reqbody.(*CreateStartingAgentReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigService_CopyAgent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CopyAgentReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigService).CopyAgent(ctx, reqbody.(*CopyAgentReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigService_CopyAgentApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CopyAgentAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigService).CopyAgentApp(ctx, reqbody.(*CopyAgentAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigService_DeleteAgent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteAgentReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigService).DeleteAgent(ctx, reqbody.(*DeleteAgentReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigService_ModifyAgent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyAgentReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigService).ModifyAgent(ctx, reqbody.(*ModifyAgentReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigService_ModifyAgentHandoffList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyAgentHandoffListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigService).ModifyAgentHandoffList(ctx, reqbody.(*ModifyAgentHandoffListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigService_DescribeAppAgentList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeAppAgentListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigService).DescribeAppAgentList(ctx, reqbody.(*DescribeAppAgentListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigService_DescribeAgentList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeAgentListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigService).DescribeAgentList(ctx, reqbody.(*DescribeAgentListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigService_ListAppAgentReleasePreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListAppAgentReleasePreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigService).ListAppAgentReleasePreview(ctx, reqbody.(*ListAppAgentReleasePreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// AgentConfigServer_ServiceDesc descriptor for server.RegisterService.
var AgentConfigServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.agent_config_server.AgentConfig",
	HandlerType: ((*AgentConfigService)(nil)),
	Methods: []server.Method{
		{
			Name: "/Echo",
			Func: AgentConfigService_Echo_Handler,
		},
		{
			Name: "/DescribeAgentConfig",
			Func: AgentConfigService_DescribeAgentConfig_Handler,
		},
		{
			Name: "/CreateAgent",
			Func: AgentConfigService_CreateAgent_Handler,
		},
		{
			Name: "/CreateStartingAgent",
			Func: AgentConfigService_CreateStartingAgent_Handler,
		},
		{
			Name: "/CopyAgent",
			Func: AgentConfigService_CopyAgent_Handler,
		},
		{
			Name: "/CopyAgentApp",
			Func: AgentConfigService_CopyAgentApp_Handler,
		},
		{
			Name: "/DeleteAgent",
			Func: AgentConfigService_DeleteAgent_Handler,
		},
		{
			Name: "/ModifyAgent",
			Func: AgentConfigService_ModifyAgent_Handler,
		},
		{
			Name: "/ModifyAgentHandoffList",
			Func: AgentConfigService_ModifyAgentHandoffList_Handler,
		},
		{
			Name: "/DescribeAppAgentList",
			Func: AgentConfigService_DescribeAppAgentList_Handler,
		},
		{
			Name: "/DescribeAgentList",
			Func: AgentConfigService_DescribeAgentList_Handler,
		},
		{
			Name: "/ListAppAgentReleasePreview",
			Func: AgentConfigService_ListAppAgentReleasePreview_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfig/Echo",
			Func: AgentConfigService_Echo_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfig/DescribeAgentConfig",
			Func: AgentConfigService_DescribeAgentConfig_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfig/CreateAgent",
			Func: AgentConfigService_CreateAgent_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfig/CreateStartingAgent",
			Func: AgentConfigService_CreateStartingAgent_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfig/CopyAgent",
			Func: AgentConfigService_CopyAgent_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfig/CopyAgentApp",
			Func: AgentConfigService_CopyAgentApp_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfig/DeleteAgent",
			Func: AgentConfigService_DeleteAgent_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfig/ModifyAgent",
			Func: AgentConfigService_ModifyAgent_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfig/ModifyAgentHandoffList",
			Func: AgentConfigService_ModifyAgentHandoffList_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfig/DescribeAppAgentList",
			Func: AgentConfigService_DescribeAppAgentList_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfig/DescribeAgentList",
			Func: AgentConfigService_DescribeAgentList_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfig/ListAppAgentReleasePreview",
			Func: AgentConfigService_ListAppAgentReleasePreview_Handler,
		},
	},
}

// RegisterAgentConfigService registers service.
func RegisterAgentConfigService(s server.Service, svr AgentConfigService) {
	if err := s.Register(&AgentConfigServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("AgentConfig register error:%v", err))
	}
}

// AgentConfigApiService defines service.
type AgentConfigApiService interface {
	// CreateStartingAgent ======================= Agent插件 ==========================
	//  创建启动Agent，可重入
	//  @alias=/CreateStartingAgent
	CreateStartingAgent(ctx context.Context, req *CreateStartingAgentReq) (*CreateStartingAgentRsp, error)
	// CopyAgentApp 复制Agent模式的App，一般用来复制Agent模式的App下的所有Agent
	//  @alias=/CopyAgentApp
	CopyAgentApp(ctx context.Context, req *CopyAgentAppReq) (*CopyAgentAppRsp, error)
	// DescribeAppAgentList 查询指定应用下的Agent列表
	//  @alias=/DescribeAppAgentList
	DescribeAppAgentList(ctx context.Context, req *DescribeAppAgentListReq) (*DescribeAppAgentListRsp, error)
	// DescribeAgentHasReferencedPluginToolList 查询引用指定插件工具的Agent列表
	//  @alias=/DescribeAgentHasReferencedPluginToolList
	DescribeAgentHasReferencedPluginToolList(ctx context.Context, req *DescribeAgentHasReferencedPluginToolListReq) (*DescribeAgentHasReferencedPluginToolListRsp, error)
	// GetUnreleasedCount 获取未发布的数量
	//  @alias=/GetUnreleasedCount
	GetUnreleasedCount(ctx context.Context, req *KEP.GetUnreleasedCountReq) (*KEP.GetUnreleasedCountRsp, error)
	// SendDataSyncTaskEvent 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
	//  @alias=/SendDataSyncTaskEvent
	SendDataSyncTaskEvent(ctx context.Context, req *KEP.SendDataSyncTaskEventReq) (*KEP.SendDataSyncTaskEventRsp, error)
	// GetDataSyncTask [单个]获取同步任务, 详情, 状态等
	//  @alias=/GetDataSyncTask
	GetDataSyncTask(ctx context.Context, req *KEP.GetDataSyncTaskReq) (*KEP.GetDataSyncTaskRsp, error)
	// ClearAgentAppResource ======================= 清理任务 ==========================
	//  清理任务/指定应用下的Agent相关资源
	//  @alias=/ClearAgentAppResource
	ClearAgentAppResource(ctx context.Context, req *ClearAgentAppResourceReq) (*ClearAgentAppResourceRsp, error) // ===========================================================
	// AgentMigrate Agent数据库迁移接口，内部专用
	//  @alias=/AgentMigrate
	AgentMigrate(ctx context.Context, req *AgentMigrateReq) (*AgentMigrateRsp, error)
}

func AgentConfigApiService_CreateStartingAgent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateStartingAgentReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigApiService).CreateStartingAgent(ctx, reqbody.(*CreateStartingAgentReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigApiService_CopyAgentApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CopyAgentAppReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigApiService).CopyAgentApp(ctx, reqbody.(*CopyAgentAppReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigApiService_DescribeAppAgentList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeAppAgentListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigApiService).DescribeAppAgentList(ctx, reqbody.(*DescribeAppAgentListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigApiService_DescribeAgentHasReferencedPluginToolList_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeAgentHasReferencedPluginToolListReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigApiService).DescribeAgentHasReferencedPluginToolList(ctx, reqbody.(*DescribeAgentHasReferencedPluginToolListReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigApiService_GetUnreleasedCount_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP.GetUnreleasedCountReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigApiService).GetUnreleasedCount(ctx, reqbody.(*KEP.GetUnreleasedCountReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigApiService_SendDataSyncTaskEvent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP.SendDataSyncTaskEventReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigApiService).SendDataSyncTaskEvent(ctx, reqbody.(*KEP.SendDataSyncTaskEventReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigApiService_GetDataSyncTask_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP.GetDataSyncTaskReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigApiService).GetDataSyncTask(ctx, reqbody.(*KEP.GetDataSyncTaskReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigApiService_ClearAgentAppResource_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ClearAgentAppResourceReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigApiService).ClearAgentAppResource(ctx, reqbody.(*ClearAgentAppResourceReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AgentConfigApiService_AgentMigrate_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AgentMigrateReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AgentConfigApiService).AgentMigrate(ctx, reqbody.(*AgentMigrateReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// AgentConfigApiServer_ServiceDesc descriptor for server.RegisterService.
var AgentConfigApiServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.agent_config_server.AgentConfigApi",
	HandlerType: ((*AgentConfigApiService)(nil)),
	Methods: []server.Method{
		{
			Name: "/CreateStartingAgent",
			Func: AgentConfigApiService_CreateStartingAgent_Handler,
		},
		{
			Name: "/CopyAgentApp",
			Func: AgentConfigApiService_CopyAgentApp_Handler,
		},
		{
			Name: "/DescribeAppAgentList",
			Func: AgentConfigApiService_DescribeAppAgentList_Handler,
		},
		{
			Name: "/DescribeAgentHasReferencedPluginToolList",
			Func: AgentConfigApiService_DescribeAgentHasReferencedPluginToolList_Handler,
		},
		{
			Name: "/GetUnreleasedCount",
			Func: AgentConfigApiService_GetUnreleasedCount_Handler,
		},
		{
			Name: "/SendDataSyncTaskEvent",
			Func: AgentConfigApiService_SendDataSyncTaskEvent_Handler,
		},
		{
			Name: "/GetDataSyncTask",
			Func: AgentConfigApiService_GetDataSyncTask_Handler,
		},
		{
			Name: "/ClearAgentAppResource",
			Func: AgentConfigApiService_ClearAgentAppResource_Handler,
		},
		{
			Name: "/AgentMigrate",
			Func: AgentConfigApiService_AgentMigrate_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfigApi/CreateStartingAgent",
			Func: AgentConfigApiService_CreateStartingAgent_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfigApi/CopyAgentApp",
			Func: AgentConfigApiService_CopyAgentApp_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfigApi/DescribeAppAgentList",
			Func: AgentConfigApiService_DescribeAppAgentList_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfigApi/DescribeAgentHasReferencedPluginToolList",
			Func: AgentConfigApiService_DescribeAgentHasReferencedPluginToolList_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfigApi/GetUnreleasedCount",
			Func: AgentConfigApiService_GetUnreleasedCount_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfigApi/SendDataSyncTaskEvent",
			Func: AgentConfigApiService_SendDataSyncTaskEvent_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfigApi/GetDataSyncTask",
			Func: AgentConfigApiService_GetDataSyncTask_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfigApi/ClearAgentAppResource",
			Func: AgentConfigApiService_ClearAgentAppResource_Handler,
		},
		{
			Name: "/trpc.KEP.agent_config_server.AgentConfigApi/AgentMigrate",
			Func: AgentConfigApiService_AgentMigrate_Handler,
		},
	},
}

// RegisterAgentConfigApiService registers service.
func RegisterAgentConfigApiService(s server.Service, svr AgentConfigApiService) {
	if err := s.Register(&AgentConfigApiServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("AgentConfigApi register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedAgentConfig struct{}

// Echo Echo 方法用于测试服务是否部署成功
//
//	@alias=/Echo
func (s *UnimplementedAgentConfig) Echo(ctx context.Context, req *EchoReq) (*EchoRsp, error) {
	return nil, errors.New("rpc Echo of service AgentConfig is not implemented")
}

// DescribeAgentConfig 查询Agent配置信息
//
//	@alias=/DescribeAgentConfig
func (s *UnimplementedAgentConfig) DescribeAgentConfig(ctx context.Context, req *DescribeAgentConfigReq) (*DescribeAgentConfigRsp, error) {
	return nil, errors.New("rpc DescribeAgentConfig of service AgentConfig is not implemented")
}

// CreateAgent 创建Agent
//
//	@alias=/CreateAgent
func (s *UnimplementedAgentConfig) CreateAgent(ctx context.Context, req *CreateAgentReq) (*CreateAgentRsp, error) {
	return nil, errors.New("rpc CreateAgent of service AgentConfig is not implemented")
}

// CreateStartingAgent 创建启动Agent
//
//	@alias=/CreateStartingAgent
func (s *UnimplementedAgentConfig) CreateStartingAgent(ctx context.Context, req *CreateStartingAgentReq) (*CreateStartingAgentRsp, error) {
	return nil, errors.New("rpc CreateStartingAgent of service AgentConfig is not implemented")
}

// CopyAgent 复制Agent，一般用来导入PDL作为协同Agent
//
//	@alias=/CopyAgent
func (s *UnimplementedAgentConfig) CopyAgent(ctx context.Context, req *CopyAgentReq) (*CopyAgentRsp, error) {
	return nil, errors.New("rpc CopyAgent of service AgentConfig is not implemented")
}

// CopyAgentApp 复制Agent模式的App，一般用来复制Agent模式的App下的所有Agent
//
//	@alias=/CopyAgentApp
func (s *UnimplementedAgentConfig) CopyAgentApp(ctx context.Context, req *CopyAgentAppReq) (*CopyAgentAppRsp, error) {
	return nil, errors.New("rpc CopyAgentApp of service AgentConfig is not implemented")
}

// DeleteAgent 删除Agent
//
//	@alias=/DeleteAgent
func (s *UnimplementedAgentConfig) DeleteAgent(ctx context.Context, req *DeleteAgentReq) (*DeleteAgentRsp, error) {
	return nil, errors.New("rpc DeleteAgent of service AgentConfig is not implemented")
}

// ModifyAgent 修改Agent
//
//	@alias=/ModifyAgent
func (s *UnimplementedAgentConfig) ModifyAgent(ctx context.Context, req *ModifyAgentReq) (*ModifyAgentRsp, error) {
	return nil, errors.New("rpc ModifyAgent of service AgentConfig is not implemented")
}

// ModifyAgentHandoffList 修改Agent转交关系列表
//
//	@alias=/ModifyAgentHandoffList
func (s *UnimplementedAgentConfig) ModifyAgentHandoffList(ctx context.Context, req *ModifyAgentHandoffListReq) (*ModifyAgentHandoffListRsp, error) {
	return nil, errors.New("rpc ModifyAgentHandoffList of service AgentConfig is not implemented")
}

// DescribeAppAgentList 查询指定应用下的Agent列表
//
//	@alias=/DescribeAppAgentList
func (s *UnimplementedAgentConfig) DescribeAppAgentList(ctx context.Context, req *DescribeAppAgentListReq) (*DescribeAppAgentListRsp, error) {
	return nil, errors.New("rpc DescribeAppAgentList of service AgentConfig is not implemented")
}

// DescribeAgentList 查询Agent列表
//
//	@alias=/DescribeAgentList
func (s *UnimplementedAgentConfig) DescribeAgentList(ctx context.Context, req *DescribeAgentListReq) (*DescribeAgentListRsp, error) {
	return nil, errors.New("rpc DescribeAgentList of service AgentConfig is not implemented")
}

// ListAppAgentReleasePreview ======================= 发布 ==========================
//
//	获取应用Agent发布列表
//	@alias=/ListAppAgentReleasePreview
func (s *UnimplementedAgentConfig) ListAppAgentReleasePreview(ctx context.Context, req *ListAppAgentReleasePreviewReq) (*ListAppAgentReleasePreviewRsp, error) {
	return nil, errors.New("rpc ListAppAgentReleasePreview of service AgentConfig is not implemented")
}

type UnimplementedAgentConfigApi struct{}

// CreateStartingAgent ======================= Agent插件 ==========================
//
//	创建启动Agent，可重入
//	@alias=/CreateStartingAgent
func (s *UnimplementedAgentConfigApi) CreateStartingAgent(ctx context.Context, req *CreateStartingAgentReq) (*CreateStartingAgentRsp, error) {
	return nil, errors.New("rpc CreateStartingAgent of service AgentConfigApi is not implemented")
}

// CopyAgentApp 复制Agent模式的App，一般用来复制Agent模式的App下的所有Agent
//
//	@alias=/CopyAgentApp
func (s *UnimplementedAgentConfigApi) CopyAgentApp(ctx context.Context, req *CopyAgentAppReq) (*CopyAgentAppRsp, error) {
	return nil, errors.New("rpc CopyAgentApp of service AgentConfigApi is not implemented")
}

// DescribeAppAgentList 查询指定应用下的Agent列表
//
//	@alias=/DescribeAppAgentList
func (s *UnimplementedAgentConfigApi) DescribeAppAgentList(ctx context.Context, req *DescribeAppAgentListReq) (*DescribeAppAgentListRsp, error) {
	return nil, errors.New("rpc DescribeAppAgentList of service AgentConfigApi is not implemented")
}

// DescribeAgentHasReferencedPluginToolList 查询引用指定插件工具的Agent列表
//
//	@alias=/DescribeAgentHasReferencedPluginToolList
func (s *UnimplementedAgentConfigApi) DescribeAgentHasReferencedPluginToolList(ctx context.Context, req *DescribeAgentHasReferencedPluginToolListReq) (*DescribeAgentHasReferencedPluginToolListRsp, error) {
	return nil, errors.New("rpc DescribeAgentHasReferencedPluginToolList of service AgentConfigApi is not implemented")
}

// GetUnreleasedCount 获取未发布的数量
//
//	@alias=/GetUnreleasedCount
func (s *UnimplementedAgentConfigApi) GetUnreleasedCount(ctx context.Context, req *KEP.GetUnreleasedCountReq) (*KEP.GetUnreleasedCountRsp, error) {
	return nil, errors.New("rpc GetUnreleasedCount of service AgentConfigApi is not implemented")
}

// SendDataSyncTaskEvent 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
//
//	@alias=/SendDataSyncTaskEvent
func (s *UnimplementedAgentConfigApi) SendDataSyncTaskEvent(ctx context.Context, req *KEP.SendDataSyncTaskEventReq) (*KEP.SendDataSyncTaskEventRsp, error) {
	return nil, errors.New("rpc SendDataSyncTaskEvent of service AgentConfigApi is not implemented")
}

// GetDataSyncTask [单个]获取同步任务, 详情, 状态等
//
//	@alias=/GetDataSyncTask
func (s *UnimplementedAgentConfigApi) GetDataSyncTask(ctx context.Context, req *KEP.GetDataSyncTaskReq) (*KEP.GetDataSyncTaskRsp, error) {
	return nil, errors.New("rpc GetDataSyncTask of service AgentConfigApi is not implemented")
}

// ClearAgentAppResource ======================= 清理任务 ==========================
//
//	清理任务/指定应用下的Agent相关资源
//	@alias=/ClearAgentAppResource
func (s *UnimplementedAgentConfigApi) ClearAgentAppResource(ctx context.Context, req *ClearAgentAppResourceReq) (*ClearAgentAppResourceRsp, error) {
	return nil, errors.New("rpc ClearAgentAppResource of service AgentConfigApi is not implemented")
}

// AgentMigrate Agent数据库迁移接口，内部专用
//
//	@alias=/AgentMigrate
func (s *UnimplementedAgentConfigApi) AgentMigrate(ctx context.Context, req *AgentMigrateReq) (*AgentMigrateRsp, error) {
	return nil, errors.New("rpc AgentMigrate of service AgentConfigApi is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// AgentConfigClientProxy defines service client proxy
type AgentConfigClientProxy interface {
	// Echo Echo 方法用于测试服务是否部署成功
	//  @alias=/Echo
	Echo(ctx context.Context, req *EchoReq, opts ...client.Option) (rsp *EchoRsp, err error)

	// DescribeAgentConfig 查询Agent配置信息
	//  @alias=/DescribeAgentConfig
	DescribeAgentConfig(ctx context.Context, req *DescribeAgentConfigReq, opts ...client.Option) (rsp *DescribeAgentConfigRsp, err error)

	// CreateAgent 创建Agent
	//  @alias=/CreateAgent
	CreateAgent(ctx context.Context, req *CreateAgentReq, opts ...client.Option) (rsp *CreateAgentRsp, err error)

	// CreateStartingAgent 创建启动Agent
	//  @alias=/CreateStartingAgent
	CreateStartingAgent(ctx context.Context, req *CreateStartingAgentReq, opts ...client.Option) (rsp *CreateStartingAgentRsp, err error)

	// CopyAgent 复制Agent，一般用来导入PDL作为协同Agent
	//  @alias=/CopyAgent
	CopyAgent(ctx context.Context, req *CopyAgentReq, opts ...client.Option) (rsp *CopyAgentRsp, err error)

	// CopyAgentApp 复制Agent模式的App，一般用来复制Agent模式的App下的所有Agent
	//  @alias=/CopyAgentApp
	CopyAgentApp(ctx context.Context, req *CopyAgentAppReq, opts ...client.Option) (rsp *CopyAgentAppRsp, err error)

	// DeleteAgent 删除Agent
	//  @alias=/DeleteAgent
	DeleteAgent(ctx context.Context, req *DeleteAgentReq, opts ...client.Option) (rsp *DeleteAgentRsp, err error)

	// ModifyAgent 修改Agent
	//  @alias=/ModifyAgent
	ModifyAgent(ctx context.Context, req *ModifyAgentReq, opts ...client.Option) (rsp *ModifyAgentRsp, err error)

	// ModifyAgentHandoffList 修改Agent转交关系列表
	//  @alias=/ModifyAgentHandoffList
	ModifyAgentHandoffList(ctx context.Context, req *ModifyAgentHandoffListReq, opts ...client.Option) (rsp *ModifyAgentHandoffListRsp, err error)

	// DescribeAppAgentList 查询指定应用下的Agent列表
	//  @alias=/DescribeAppAgentList
	DescribeAppAgentList(ctx context.Context, req *DescribeAppAgentListReq, opts ...client.Option) (rsp *DescribeAppAgentListRsp, err error)

	// DescribeAgentList 查询Agent列表
	//  @alias=/DescribeAgentList
	DescribeAgentList(ctx context.Context, req *DescribeAgentListReq, opts ...client.Option) (rsp *DescribeAgentListRsp, err error)

	// ListAppAgentReleasePreview ======================= 发布 ==========================
	//  获取应用Agent发布列表
	//  @alias=/ListAppAgentReleasePreview
	ListAppAgentReleasePreview(ctx context.Context, req *ListAppAgentReleasePreviewReq, opts ...client.Option) (rsp *ListAppAgentReleasePreviewRsp, err error) // ======================================================

}

type AgentConfigClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewAgentConfigClientProxy = func(opts ...client.Option) AgentConfigClientProxy {
	return &AgentConfigClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *AgentConfigClientProxyImpl) Echo(ctx context.Context, req *EchoReq, opts ...client.Option) (*EchoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/Echo")
	msg.WithCalleeServiceName(AgentConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfig")
	msg.WithCalleeMethod("Echo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &EchoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigClientProxyImpl) DescribeAgentConfig(ctx context.Context, req *DescribeAgentConfigReq, opts ...client.Option) (*DescribeAgentConfigRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeAgentConfig")
	msg.WithCalleeServiceName(AgentConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfig")
	msg.WithCalleeMethod("DescribeAgentConfig")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeAgentConfigRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigClientProxyImpl) CreateAgent(ctx context.Context, req *CreateAgentReq, opts ...client.Option) (*CreateAgentRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateAgent")
	msg.WithCalleeServiceName(AgentConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfig")
	msg.WithCalleeMethod("CreateAgent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateAgentRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigClientProxyImpl) CreateStartingAgent(ctx context.Context, req *CreateStartingAgentReq, opts ...client.Option) (*CreateStartingAgentRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateStartingAgent")
	msg.WithCalleeServiceName(AgentConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfig")
	msg.WithCalleeMethod("CreateStartingAgent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateStartingAgentRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigClientProxyImpl) CopyAgent(ctx context.Context, req *CopyAgentReq, opts ...client.Option) (*CopyAgentRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CopyAgent")
	msg.WithCalleeServiceName(AgentConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfig")
	msg.WithCalleeMethod("CopyAgent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CopyAgentRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigClientProxyImpl) CopyAgentApp(ctx context.Context, req *CopyAgentAppReq, opts ...client.Option) (*CopyAgentAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CopyAgentApp")
	msg.WithCalleeServiceName(AgentConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfig")
	msg.WithCalleeMethod("CopyAgentApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CopyAgentAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigClientProxyImpl) DeleteAgent(ctx context.Context, req *DeleteAgentReq, opts ...client.Option) (*DeleteAgentRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeleteAgent")
	msg.WithCalleeServiceName(AgentConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfig")
	msg.WithCalleeMethod("DeleteAgent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteAgentRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigClientProxyImpl) ModifyAgent(ctx context.Context, req *ModifyAgentReq, opts ...client.Option) (*ModifyAgentRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ModifyAgent")
	msg.WithCalleeServiceName(AgentConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfig")
	msg.WithCalleeMethod("ModifyAgent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyAgentRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigClientProxyImpl) ModifyAgentHandoffList(ctx context.Context, req *ModifyAgentHandoffListReq, opts ...client.Option) (*ModifyAgentHandoffListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ModifyAgentHandoffList")
	msg.WithCalleeServiceName(AgentConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfig")
	msg.WithCalleeMethod("ModifyAgentHandoffList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyAgentHandoffListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigClientProxyImpl) DescribeAppAgentList(ctx context.Context, req *DescribeAppAgentListReq, opts ...client.Option) (*DescribeAppAgentListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeAppAgentList")
	msg.WithCalleeServiceName(AgentConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfig")
	msg.WithCalleeMethod("DescribeAppAgentList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeAppAgentListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigClientProxyImpl) DescribeAgentList(ctx context.Context, req *DescribeAgentListReq, opts ...client.Option) (*DescribeAgentListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeAgentList")
	msg.WithCalleeServiceName(AgentConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfig")
	msg.WithCalleeMethod("DescribeAgentList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeAgentListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigClientProxyImpl) ListAppAgentReleasePreview(ctx context.Context, req *ListAppAgentReleasePreviewReq, opts ...client.Option) (*ListAppAgentReleasePreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListAppAgentReleasePreview")
	msg.WithCalleeServiceName(AgentConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfig")
	msg.WithCalleeMethod("ListAppAgentReleasePreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListAppAgentReleasePreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// AgentConfigApiClientProxy defines service client proxy
type AgentConfigApiClientProxy interface {
	// CreateStartingAgent ======================= Agent插件 ==========================
	//  创建启动Agent，可重入
	//  @alias=/CreateStartingAgent
	CreateStartingAgent(ctx context.Context, req *CreateStartingAgentReq, opts ...client.Option) (rsp *CreateStartingAgentRsp, err error)

	// CopyAgentApp 复制Agent模式的App，一般用来复制Agent模式的App下的所有Agent
	//  @alias=/CopyAgentApp
	CopyAgentApp(ctx context.Context, req *CopyAgentAppReq, opts ...client.Option) (rsp *CopyAgentAppRsp, err error)

	// DescribeAppAgentList 查询指定应用下的Agent列表
	//  @alias=/DescribeAppAgentList
	DescribeAppAgentList(ctx context.Context, req *DescribeAppAgentListReq, opts ...client.Option) (rsp *DescribeAppAgentListRsp, err error)

	// DescribeAgentHasReferencedPluginToolList 查询引用指定插件工具的Agent列表
	//  @alias=/DescribeAgentHasReferencedPluginToolList
	DescribeAgentHasReferencedPluginToolList(ctx context.Context, req *DescribeAgentHasReferencedPluginToolListReq, opts ...client.Option) (rsp *DescribeAgentHasReferencedPluginToolListRsp, err error)

	// GetUnreleasedCount 获取未发布的数量
	//  @alias=/GetUnreleasedCount
	GetUnreleasedCount(ctx context.Context, req *KEP.GetUnreleasedCountReq, opts ...client.Option) (rsp *KEP.GetUnreleasedCountRsp, err error)

	// SendDataSyncTaskEvent 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
	//  @alias=/SendDataSyncTaskEvent
	SendDataSyncTaskEvent(ctx context.Context, req *KEP.SendDataSyncTaskEventReq, opts ...client.Option) (rsp *KEP.SendDataSyncTaskEventRsp, err error)

	// GetDataSyncTask [单个]获取同步任务, 详情, 状态等
	//  @alias=/GetDataSyncTask
	GetDataSyncTask(ctx context.Context, req *KEP.GetDataSyncTaskReq, opts ...client.Option) (rsp *KEP.GetDataSyncTaskRsp, err error)

	// ClearAgentAppResource ======================= 清理任务 ==========================
	//  清理任务/指定应用下的Agent相关资源
	//  @alias=/ClearAgentAppResource
	ClearAgentAppResource(ctx context.Context, req *ClearAgentAppResourceReq, opts ...client.Option) (rsp *ClearAgentAppResourceRsp, err error) // ===========================================================

	// AgentMigrate Agent数据库迁移接口，内部专用
	//  @alias=/AgentMigrate
	AgentMigrate(ctx context.Context, req *AgentMigrateReq, opts ...client.Option) (rsp *AgentMigrateRsp, err error)
}

type AgentConfigApiClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewAgentConfigApiClientProxy = func(opts ...client.Option) AgentConfigApiClientProxy {
	return &AgentConfigApiClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *AgentConfigApiClientProxyImpl) CreateStartingAgent(ctx context.Context, req *CreateStartingAgentReq, opts ...client.Option) (*CreateStartingAgentRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateStartingAgent")
	msg.WithCalleeServiceName(AgentConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfigApi")
	msg.WithCalleeMethod("CreateStartingAgent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateStartingAgentRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigApiClientProxyImpl) CopyAgentApp(ctx context.Context, req *CopyAgentAppReq, opts ...client.Option) (*CopyAgentAppRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CopyAgentApp")
	msg.WithCalleeServiceName(AgentConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfigApi")
	msg.WithCalleeMethod("CopyAgentApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CopyAgentAppRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigApiClientProxyImpl) DescribeAppAgentList(ctx context.Context, req *DescribeAppAgentListReq, opts ...client.Option) (*DescribeAppAgentListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeAppAgentList")
	msg.WithCalleeServiceName(AgentConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfigApi")
	msg.WithCalleeMethod("DescribeAppAgentList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeAppAgentListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigApiClientProxyImpl) DescribeAgentHasReferencedPluginToolList(ctx context.Context, req *DescribeAgentHasReferencedPluginToolListReq, opts ...client.Option) (*DescribeAgentHasReferencedPluginToolListRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeAgentHasReferencedPluginToolList")
	msg.WithCalleeServiceName(AgentConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfigApi")
	msg.WithCalleeMethod("DescribeAgentHasReferencedPluginToolList")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeAgentHasReferencedPluginToolListRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigApiClientProxyImpl) GetUnreleasedCount(ctx context.Context, req *KEP.GetUnreleasedCountReq, opts ...client.Option) (*KEP.GetUnreleasedCountRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetUnreleasedCount")
	msg.WithCalleeServiceName(AgentConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfigApi")
	msg.WithCalleeMethod("GetUnreleasedCount")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP.GetUnreleasedCountRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigApiClientProxyImpl) SendDataSyncTaskEvent(ctx context.Context, req *KEP.SendDataSyncTaskEventReq, opts ...client.Option) (*KEP.SendDataSyncTaskEventRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SendDataSyncTaskEvent")
	msg.WithCalleeServiceName(AgentConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfigApi")
	msg.WithCalleeMethod("SendDataSyncTaskEvent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP.SendDataSyncTaskEventRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigApiClientProxyImpl) GetDataSyncTask(ctx context.Context, req *KEP.GetDataSyncTaskReq, opts ...client.Option) (*KEP.GetDataSyncTaskRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetDataSyncTask")
	msg.WithCalleeServiceName(AgentConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfigApi")
	msg.WithCalleeMethod("GetDataSyncTask")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP.GetDataSyncTaskRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigApiClientProxyImpl) ClearAgentAppResource(ctx context.Context, req *ClearAgentAppResourceReq, opts ...client.Option) (*ClearAgentAppResourceRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ClearAgentAppResource")
	msg.WithCalleeServiceName(AgentConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfigApi")
	msg.WithCalleeMethod("ClearAgentAppResource")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ClearAgentAppResourceRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AgentConfigApiClientProxyImpl) AgentMigrate(ctx context.Context, req *AgentMigrateReq, opts ...client.Option) (*AgentMigrateRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/AgentMigrate")
	msg.WithCalleeServiceName(AgentConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("agent_config_server")
	msg.WithCalleeService("AgentConfigApi")
	msg.WithCalleeMethod("AgentMigrate")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AgentMigrateRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
