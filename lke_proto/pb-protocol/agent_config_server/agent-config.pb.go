// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        v3.6.1
// source: agent-config.proto

package agent_config_server

import (
	reflect "reflect"
	sync "sync"

	KEP "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	plugin_config_server "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	proto "github.com/golang/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

type EnvType int32

const (
	EnvType_TEST EnvType = 0 // 测试环境
	EnvType_PROD EnvType = 1 // 正式环境
)

// Enum value maps for EnvType.
var (
	EnvType_name = map[int32]string{
		0: "TEST",
		1: "PROD",
	}
	EnvType_value = map[string]int32{
		"TEST": 0,
		"PROD": 1,
	}
)

func (x EnvType) Enum() *EnvType {
	p := new(EnvType)
	*p = x
	return p
}

func (x EnvType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnvType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_config_proto_enumTypes[0].Descriptor()
}

func (EnvType) Type() protoreflect.EnumType {
	return &file_agent_config_proto_enumTypes[0]
}

func (x EnvType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnvType.Descriptor instead.
func (EnvType) EnumDescriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{0}
}

// 调用方式
type CallingMethodTypeEnum int32

const (
	CallingMethodTypeEnum_NON_STREAMING CallingMethodTypeEnum = 0 // 非流式
	CallingMethodTypeEnum_STREAMING     CallingMethodTypeEnum = 1 // 流式
)

// Enum value maps for CallingMethodTypeEnum.
var (
	CallingMethodTypeEnum_name = map[int32]string{
		0: "NON_STREAMING",
		1: "STREAMING",
	}
	CallingMethodTypeEnum_value = map[string]int32{
		"NON_STREAMING": 0,
		"STREAMING":     1,
	}
)

func (x CallingMethodTypeEnum) Enum() *CallingMethodTypeEnum {
	p := new(CallingMethodTypeEnum)
	*p = x
	return p
}

func (x CallingMethodTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallingMethodTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_config_proto_enumTypes[1].Descriptor()
}

func (CallingMethodTypeEnum) Type() protoreflect.EnumType {
	return &file_agent_config_proto_enumTypes[1]
}

func (x CallingMethodTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CallingMethodTypeEnum.Descriptor instead.
func (CallingMethodTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{1}
}

type AnalysisMethodTypeEnum int32

const (
	AnalysisMethodTypeEnum_COVER     AnalysisMethodTypeEnum = 0 // 覆盖
	AnalysisMethodTypeEnum_INCREMENT AnalysisMethodTypeEnum = 1 // 增量
)

// Enum value maps for AnalysisMethodTypeEnum.
var (
	AnalysisMethodTypeEnum_name = map[int32]string{
		0: "COVER",
		1: "INCREMENT",
	}
	AnalysisMethodTypeEnum_value = map[string]int32{
		"COVER":     0,
		"INCREMENT": 1,
	}
)

func (x AnalysisMethodTypeEnum) Enum() *AnalysisMethodTypeEnum {
	p := new(AnalysisMethodTypeEnum)
	*p = x
	return p
}

func (x AnalysisMethodTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnalysisMethodTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_config_proto_enumTypes[2].Descriptor()
}

func (AnalysisMethodTypeEnum) Type() protoreflect.EnumType {
	return &file_agent_config_proto_enumTypes[2]
}

func (x AnalysisMethodTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnalysisMethodTypeEnum.Descriptor instead.
func (AnalysisMethodTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{2}
}

// 输入值来源类型
type InputSourceEnum int32

const (
	InputSourceEnum_UNSPECIFIED     InputSourceEnum = 0 // 保留字段，兼容旧版本
	InputSourceEnum_USER_INPUT      InputSourceEnum = 1 // 用户输入
	InputSourceEnum_CUSTOM_VARIABLE InputSourceEnum = 3 // 自定义变量（API参数）
)

// Enum value maps for InputSourceEnum.
var (
	InputSourceEnum_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "USER_INPUT",
		3: "CUSTOM_VARIABLE",
	}
	InputSourceEnum_value = map[string]int32{
		"UNSPECIFIED":     0,
		"USER_INPUT":      1,
		"CUSTOM_VARIABLE": 3,
	}
)

func (x InputSourceEnum) Enum() *InputSourceEnum {
	p := new(InputSourceEnum)
	*p = x
	return p
}

func (x InputSourceEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InputSourceEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_config_proto_enumTypes[3].Descriptor()
}

func (InputSourceEnum) Type() protoreflect.EnumType {
	return &file_agent_config_proto_enumTypes[3]
}

func (x InputSourceEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InputSourceEnum.Descriptor instead.
func (InputSourceEnum) EnumDescriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{3}
}

type AgentTypeEnum int32

const (
	AgentTypeEnum_AGENT_TYPE_UNSPECIFIED AgentTypeEnum = 0 // 保留字段，兼容旧版本
	AgentTypeEnum_AGENT_TYPE_KB_AGENT    AgentTypeEnum = 1 // 知识库检索Agent
)

// Enum value maps for AgentTypeEnum.
var (
	AgentTypeEnum_name = map[int32]string{
		0: "AGENT_TYPE_UNSPECIFIED",
		1: "AGENT_TYPE_KB_AGENT",
	}
	AgentTypeEnum_value = map[string]int32{
		"AGENT_TYPE_UNSPECIFIED": 0,
		"AGENT_TYPE_KB_AGENT":    1,
	}
)

func (x AgentTypeEnum) Enum() *AgentTypeEnum {
	p := new(AgentTypeEnum)
	*p = x
	return p
}

func (x AgentTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AgentTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_config_proto_enumTypes[4].Descriptor()
}

func (AgentTypeEnum) Type() protoreflect.EnumType {
	return &file_agent_config_proto_enumTypes[4]
}

func (x AgentTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AgentTypeEnum.Descriptor instead.
func (AgentTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{4}
}

type KnowledgeFilterEnum int32

const (
	KnowledgeFilterEnum_KNOWLEDGE_FILTER_TYPE_ALL        KnowledgeFilterEnum = 0 // 全部知识
	KnowledgeFilterEnum_KNOWLEDGE_FILTER_TYPE_DOC_AND_QA KnowledgeFilterEnum = 1 // 按文档和问答
	KnowledgeFilterEnum_KNOWLEDGE_FILTER_TYPE_TAG        KnowledgeFilterEnum = 2 // 按标签
)

// Enum value maps for KnowledgeFilterEnum.
var (
	KnowledgeFilterEnum_name = map[int32]string{
		0: "KNOWLEDGE_FILTER_TYPE_ALL",
		1: "KNOWLEDGE_FILTER_TYPE_DOC_AND_QA",
		2: "KNOWLEDGE_FILTER_TYPE_TAG",
	}
	KnowledgeFilterEnum_value = map[string]int32{
		"KNOWLEDGE_FILTER_TYPE_ALL":        0,
		"KNOWLEDGE_FILTER_TYPE_DOC_AND_QA": 1,
		"KNOWLEDGE_FILTER_TYPE_TAG":        2,
	}
)

func (x KnowledgeFilterEnum) Enum() *KnowledgeFilterEnum {
	p := new(KnowledgeFilterEnum)
	*p = x
	return p
}

func (x KnowledgeFilterEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeFilterEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_config_proto_enumTypes[5].Descriptor()
}

func (KnowledgeFilterEnum) Type() protoreflect.EnumType {
	return &file_agent_config_proto_enumTypes[5]
}

func (x KnowledgeFilterEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeFilterEnum.Descriptor instead.
func (KnowledgeFilterEnum) EnumDescriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{5}
}

type DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum int32

const (
	DescribeAgentListRsp_AgentPluginInfo_PLUGIN_INFO_TYPE_UNSPECIFIED  DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum = 0 // 保留字段，兼容旧版本
	DescribeAgentListRsp_AgentPluginInfo_PLUGIN_INFO_TYPE_KNOWLEDGE_QA DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum = 1 // 知识库问答插件
)

// Enum value maps for DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum.
var (
	DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum_name = map[int32]string{
		0: "PLUGIN_INFO_TYPE_UNSPECIFIED",
		1: "PLUGIN_INFO_TYPE_KNOWLEDGE_QA",
	}
	DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum_value = map[string]int32{
		"PLUGIN_INFO_TYPE_UNSPECIFIED":  0,
		"PLUGIN_INFO_TYPE_KNOWLEDGE_QA": 1,
	}
)

func (x DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum) Enum() *DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum {
	p := new(DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum)
	*p = x
	return p
}

func (x DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_config_proto_enumTypes[6].Descriptor()
}

func (DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum) Type() protoreflect.EnumType {
	return &file_agent_config_proto_enumTypes[6]
}

func (x DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum.Descriptor instead.
func (DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{23, 2, 0}
}

type DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum int32

const (
	DescribeAppAgentListRsp_AgentPluginInfo_PLUGIN_INFO_TYPE_UNSPECIFIED  DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum = 0 // 保留字段，兼容旧版本
	DescribeAppAgentListRsp_AgentPluginInfo_PLUGIN_INFO_TYPE_KNOWLEDGE_QA DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum = 1 // 知识库问答插件
)

// Enum value maps for DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum.
var (
	DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum_name = map[int32]string{
		0: "PLUGIN_INFO_TYPE_UNSPECIFIED",
		1: "PLUGIN_INFO_TYPE_KNOWLEDGE_QA",
	}
	DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum_value = map[string]int32{
		"PLUGIN_INFO_TYPE_UNSPECIFIED":  0,
		"PLUGIN_INFO_TYPE_KNOWLEDGE_QA": 1,
	}
)

func (x DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum) Enum() *DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum {
	p := new(DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum)
	*p = x
	return p
}

func (x DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_config_proto_enumTypes[7].Descriptor()
}

func (DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum) Type() protoreflect.EnumType {
	return &file_agent_config_proto_enumTypes[7]
}

func (x DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum.Descriptor instead.
func (DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{25, 1, 0}
}

type AgentPluginInfo_PluginInfoTypeEnum int32

const (
	AgentPluginInfo_PLUGIN_INFO_TYPE_UNSPECIFIED  AgentPluginInfo_PluginInfoTypeEnum = 0 // 保留字段，兼容旧版本
	AgentPluginInfo_PLUGIN_INFO_TYPE_KNOWLEDGE_QA AgentPluginInfo_PluginInfoTypeEnum = 1 // 知识库问答插件
)

// Enum value maps for AgentPluginInfo_PluginInfoTypeEnum.
var (
	AgentPluginInfo_PluginInfoTypeEnum_name = map[int32]string{
		0: "PLUGIN_INFO_TYPE_UNSPECIFIED",
		1: "PLUGIN_INFO_TYPE_KNOWLEDGE_QA",
	}
	AgentPluginInfo_PluginInfoTypeEnum_value = map[string]int32{
		"PLUGIN_INFO_TYPE_UNSPECIFIED":  0,
		"PLUGIN_INFO_TYPE_KNOWLEDGE_QA": 1,
	}
)

func (x AgentPluginInfo_PluginInfoTypeEnum) Enum() *AgentPluginInfo_PluginInfoTypeEnum {
	p := new(AgentPluginInfo_PluginInfoTypeEnum)
	*p = x
	return p
}

func (x AgentPluginInfo_PluginInfoTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AgentPluginInfo_PluginInfoTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_config_proto_enumTypes[8].Descriptor()
}

func (AgentPluginInfo_PluginInfoTypeEnum) Type() protoreflect.EnumType {
	return &file_agent_config_proto_enumTypes[8]
}

func (x AgentPluginInfo_PluginInfoTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AgentPluginInfo_PluginInfoTypeEnum.Descriptor instead.
func (AgentPluginInfo_PluginInfoTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{33, 0}
}

type KnowledgeFilterTag_OperatorEnum int32

const (
	KnowledgeFilterTag_AND KnowledgeFilterTag_OperatorEnum = 0 // AND
	KnowledgeFilterTag_OR  KnowledgeFilterTag_OperatorEnum = 1 // OR
)

// Enum value maps for KnowledgeFilterTag_OperatorEnum.
var (
	KnowledgeFilterTag_OperatorEnum_name = map[int32]string{
		0: "AND",
		1: "OR",
	}
	KnowledgeFilterTag_OperatorEnum_value = map[string]int32{
		"AND": 0,
		"OR":  1,
	}
)

func (x KnowledgeFilterTag_OperatorEnum) Enum() *KnowledgeFilterTag_OperatorEnum {
	p := new(KnowledgeFilterTag_OperatorEnum)
	*p = x
	return p
}

func (x KnowledgeFilterTag_OperatorEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeFilterTag_OperatorEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_config_proto_enumTypes[9].Descriptor()
}

func (KnowledgeFilterTag_OperatorEnum) Type() protoreflect.EnumType {
	return &file_agent_config_proto_enumTypes[9]
}

func (x KnowledgeFilterTag_OperatorEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeFilterTag_OperatorEnum.Descriptor instead.
func (KnowledgeFilterTag_OperatorEnum) EnumDescriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{45, 0}
}

type EchoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EchoReq) Reset() {
	*x = EchoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EchoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoReq) ProtoMessage() {}

func (x *EchoReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoReq.ProtoReflect.Descriptor instead.
func (*EchoReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{0}
}

type EchoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EchoRsp) Reset() {
	*x = EchoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EchoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoRsp) ProtoMessage() {}

func (x *EchoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoRsp.ProtoReflect.Descriptor instead.
func (*EchoRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{1}
}

// Agent模型信息
// borrowed from https://git.woa.com/dialogue-platform/lke_proto/blob/master/thirds-pb/bot-admin-config-server/bot-admin-api.proto#L1292
// 目前只用到如下字段：history_limit, model_name, temperature, top_p, 并新增字段model_context_words_limit, instructions_words_limit, model_alias_name
// TODO：建议直接引用，不过需要将AgentModelInfo单独放到类似"bot-admin-config-server/bot-admin-config.proto"，这个proto不能引用其他proto
// import "bot-admin-config-server/bot-admin-api.proto";
type AgentModelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 提示词
	Prompt string `protobuf:"bytes,1,opt,name=prompt,json=Prompt,proto3" json:"prompt,omitempty"`
	// 提示词内容字符限制
	PromptWordsLimit uint32 `protobuf:"varint,2,opt,name=prompt_words_limit,json=PromptWordsLimit,proto3" json:"prompt_words_limit,omitempty"`
	// 对话历史条数限制
	HistoryLimit uint32 `protobuf:"varint,6,opt,name=history_limit,json=HistoryLimit,proto3" json:"history_limit,omitempty"`
	// 对话历史条数限制
	HistoryWordsLimit uint32 `protobuf:"varint,7,opt,name=history_words_limit,json=HistoryWordsLimit,proto3" json:"history_words_limit,omitempty"`
	// 模型名称 (trpc 接口使用)
	ModelName string `protobuf:"bytes,8,opt,name=model_name,json=ModelName,proto3" json:"model_name,omitempty"`
	// 下游服务名 (trpc 接口使用)
	ServiceName string `protobuf:"bytes,9,opt,name=service_name,json=ServiceName,proto3" json:"service_name,omitempty"`
	// 模型是否开启
	IsEnabled bool `protobuf:"varint,10,opt,name=is_enabled,json=IsEnabled,proto3" json:"is_enabled,omitempty"`
	// 模型调用接口路由 (http 接口使用)
	//
	// Deprecated: Do not use.
	Path string `protobuf:"bytes,3,opt,name=path,json=Path,proto3" json:"path,omitempty"`
	// 模型调用接口地址 (http 接口使用)
	//
	// Deprecated: Do not use.
	Target string `protobuf:"bytes,4,opt,name=target,json=Target,proto3" json:"target,omitempty"`
	// 模型类型 (http 接口使用)
	//
	// Deprecated: Do not use.
	Type uint32 `protobuf:"varint,5,opt,name=type,json=Type,proto3" json:"type,omitempty"`
	// 对话限制长度
	ChatWordsLimit uint32 `protobuf:"varint,11,opt,name=chat_words_limit,json=ChatWordsLimit,proto3" json:"chat_words_limit,omitempty"`
	// 模型的top_k配置
	TopK uint32 `protobuf:"varint,12,opt,name=top_k,json=TopK,proto3" json:"top_k,omitempty"`
	// 指定版本的prompt
	Prompts map[string]string `protobuf:"bytes,13,rep,name=prompts,json=Prompts,proto3" json:"prompts,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 温度
	Temperature float32 `protobuf:"fixed32,14,opt,name=temperature,json=Temperature,proto3" json:"temperature,omitempty"`
	// TopP
	TopP float32 `protobuf:"fixed32,15,opt,name=top_p,json=TopP,proto3" json:"top_p,omitempty"`
	// prompt版本
	PromptVersion string `protobuf:"bytes,16,opt,name=prompt_version,json=PromptVersion,proto3" json:"prompt_version,omitempty"`
	// 模型上下文长度字符限制
	ModelContextWordsLimit string `protobuf:"bytes,17,opt,name=model_context_words_limit,json=ModelContextWordsLimit,proto3" json:"model_context_words_limit,omitempty"`
	// 指令长度字符限制
	InstructionsWordsLimit uint32 `protobuf:"varint,18,opt,name=instructions_words_limit,json=InstructionsWordsLimit,proto3" json:"instructions_words_limit,omitempty"`
	// 模型别名
	ModelAliasName string `protobuf:"bytes,19,opt,name=model_alias_name,json=ModelAliasName,proto3" json:"model_alias_name,omitempty"`
}

func (x *AgentModelInfo) Reset() {
	*x = AgentModelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentModelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentModelInfo) ProtoMessage() {}

func (x *AgentModelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentModelInfo.ProtoReflect.Descriptor instead.
func (*AgentModelInfo) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{2}
}

func (x *AgentModelInfo) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *AgentModelInfo) GetPromptWordsLimit() uint32 {
	if x != nil {
		return x.PromptWordsLimit
	}
	return 0
}

func (x *AgentModelInfo) GetHistoryLimit() uint32 {
	if x != nil {
		return x.HistoryLimit
	}
	return 0
}

func (x *AgentModelInfo) GetHistoryWordsLimit() uint32 {
	if x != nil {
		return x.HistoryWordsLimit
	}
	return 0
}

func (x *AgentModelInfo) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *AgentModelInfo) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *AgentModelInfo) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

// Deprecated: Do not use.
func (x *AgentModelInfo) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

// Deprecated: Do not use.
func (x *AgentModelInfo) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

// Deprecated: Do not use.
func (x *AgentModelInfo) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AgentModelInfo) GetChatWordsLimit() uint32 {
	if x != nil {
		return x.ChatWordsLimit
	}
	return 0
}

func (x *AgentModelInfo) GetTopK() uint32 {
	if x != nil {
		return x.TopK
	}
	return 0
}

func (x *AgentModelInfo) GetPrompts() map[string]string {
	if x != nil {
		return x.Prompts
	}
	return nil
}

func (x *AgentModelInfo) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *AgentModelInfo) GetTopP() float32 {
	if x != nil {
		return x.TopP
	}
	return 0
}

func (x *AgentModelInfo) GetPromptVersion() string {
	if x != nil {
		return x.PromptVersion
	}
	return ""
}

func (x *AgentModelInfo) GetModelContextWordsLimit() string {
	if x != nil {
		return x.ModelContextWordsLimit
	}
	return ""
}

func (x *AgentModelInfo) GetInstructionsWordsLimit() uint32 {
	if x != nil {
		return x.InstructionsWordsLimit
	}
	return 0
}

func (x *AgentModelInfo) GetModelAliasName() string {
	if x != nil {
		return x.ModelAliasName
	}
	return ""
}

// Agent信息
type Agent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId    string `protobuf:"bytes,1,opt,name=AgentId,proto3" json:"AgentId,omitempty"`       // AgentID，全局唯一索引
	WorkflowId string `protobuf:"bytes,2,opt,name=WorkflowId,proto3" json:"WorkflowId,omitempty"` // WorkflowID，非空则当前Agent从workflow转换而来
	Name       string `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty"`             // Agent名称，同一个应用内，Agent名称不能重复
	IconUrl    string `protobuf:"bytes,4,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`       // 插件图标url
	// 注意，当AgentType为AGENT_INFO_TYPE_KB_AGENT时
	Instructions       string             `protobuf:"bytes,5,opt,name=Instructions,proto3" json:"Instructions,omitempty"`                                             // Agent指令；当该Agent被调用时，将作为“系统提示词”使用，描述Agent应执行的操作和响应方式
	HandoffDescription string             `protobuf:"bytes,6,opt,name=HandoffDescription,proto3" json:"HandoffDescription,omitempty"`                                 // 当Agent作为转交目标时的描述，用于让其他Agent的LLM理解其功能和转交时机
	Handoffs           []string           `protobuf:"bytes,7,rep,name=Handoffs,proto3" json:"Handoffs,omitempty"`                                                     // Agent可转交的子AgentId列表
	Model              *AgentModelInfo    `protobuf:"bytes,8,opt,name=Model,proto3" json:"Model,omitempty"`                                                           // Agent调用LLM时使用的模型配置
	Tools              []*AgentToolInfo   `protobuf:"bytes,9,rep,name=Tools,proto3" json:"Tools,omitempty"`                                                           // Agent可使用的工具列表
	Plugins            []*AgentPluginInfo `protobuf:"bytes,10,rep,name=Plugins,proto3" json:"Plugins,omitempty"`                                                      // Agent可使用的工具列表
	IsStartingAgent    bool               `protobuf:"varint,11,opt,name=IsStartingAgent,proto3" json:"IsStartingAgent,omitempty"`                                     // 当前Agent是否是启动Agent
	AgentType          AgentTypeEnum      `protobuf:"varint,12,opt,name=AgentType,proto3,enum=trpc.KEP.agent_config_server.AgentTypeEnum" json:"AgentType,omitempty"` // Agent类型
}

func (x *Agent) Reset() {
	*x = Agent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Agent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Agent) ProtoMessage() {}

func (x *Agent) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Agent.ProtoReflect.Descriptor instead.
func (*Agent) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{3}
}

func (x *Agent) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *Agent) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *Agent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Agent) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *Agent) GetInstructions() string {
	if x != nil {
		return x.Instructions
	}
	return ""
}

func (x *Agent) GetHandoffDescription() string {
	if x != nil {
		return x.HandoffDescription
	}
	return ""
}

func (x *Agent) GetHandoffs() []string {
	if x != nil {
		return x.Handoffs
	}
	return nil
}

func (x *Agent) GetModel() *AgentModelInfo {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *Agent) GetTools() []*AgentToolInfo {
	if x != nil {
		return x.Tools
	}
	return nil
}

func (x *Agent) GetPlugins() []*AgentPluginInfo {
	if x != nil {
		return x.Plugins
	}
	return nil
}

func (x *Agent) GetIsStartingAgent() bool {
	if x != nil {
		return x.IsStartingAgent
	}
	return false
}

func (x *Agent) GetAgentType() AgentTypeEnum {
	if x != nil {
		return x.AgentType
	}
	return AgentTypeEnum_AGENT_TYPE_UNSPECIFIED
}

// 知识库检索Agent提示词
type AgentInstructionsForKbAgent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RetrieveRule string `protobuf:"bytes,1,opt,name=RetrieveRule,proto3" json:"RetrieveRule,omitempty"` // 检索规则
	ExampleQa    string `protobuf:"bytes,2,opt,name=ExampleQa,proto3" json:"ExampleQa,omitempty"`       // 示例问答
	// 以下字段为查询接口专用
	KnowledgeSchemas []*AgentKnowledgeSchema `protobuf:"bytes,3,rep,name=KnowledgeSchemas,proto3" json:"KnowledgeSchemas,omitempty"` // 知识库Schema -- 查询接口使用, 需要实时去应用中心拉取
}

func (x *AgentInstructionsForKbAgent) Reset() {
	*x = AgentInstructionsForKbAgent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentInstructionsForKbAgent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentInstructionsForKbAgent) ProtoMessage() {}

func (x *AgentInstructionsForKbAgent) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentInstructionsForKbAgent.ProtoReflect.Descriptor instead.
func (*AgentInstructionsForKbAgent) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{4}
}

func (x *AgentInstructionsForKbAgent) GetRetrieveRule() string {
	if x != nil {
		return x.RetrieveRule
	}
	return ""
}

func (x *AgentInstructionsForKbAgent) GetExampleQa() string {
	if x != nil {
		return x.ExampleQa
	}
	return ""
}

func (x *AgentInstructionsForKbAgent) GetKnowledgeSchemas() []*AgentKnowledgeSchema {
	if x != nil {
		return x.KnowledgeSchemas
	}
	return nil
}

type AgentKnowledgeSchema struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessId string `protobuf:"bytes,1,opt,name=BusinessId,proto3" json:"BusinessId,omitempty"` // 文档："doc_xxxxx" 文档聚类："doc_cluster_xxxxx"
	Name       string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`             // 名称，文件名或者文件聚类名
	Summary    string `protobuf:"bytes,3,opt,name=Summary,proto3" json:"Summary,omitempty"`       // 文件摘要或者文件聚类摘要
}

func (x *AgentKnowledgeSchema) Reset() {
	*x = AgentKnowledgeSchema{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentKnowledgeSchema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentKnowledgeSchema) ProtoMessage() {}

func (x *AgentKnowledgeSchema) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentKnowledgeSchema.ProtoReflect.Descriptor instead.
func (*AgentKnowledgeSchema) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{5}
}

func (x *AgentKnowledgeSchema) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *AgentKnowledgeSchema) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AgentKnowledgeSchema) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

type DescribeAgentConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId  string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"`   // 应用ID
	ModelName string `protobuf:"bytes,2,opt,name=ModelName,proto3" json:"ModelName,omitempty"` // 模型名称, 为空则获取应用下所有模型配置
}

func (x *DescribeAgentConfigReq) Reset() {
	*x = DescribeAgentConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAgentConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAgentConfigReq) ProtoMessage() {}

func (x *DescribeAgentConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAgentConfigReq.ProtoReflect.Descriptor instead.
func (*DescribeAgentConfigReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{6}
}

func (x *DescribeAgentConfigReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *DescribeAgentConfigReq) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

type DescribeAgentConfigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelInfos []*DescribeAgentConfigRsp_AgentModelInfo `protobuf:"bytes,1,rep,name=ModelInfos,proto3" json:"ModelInfos,omitempty"` // 应用下的模型配置列表
}

func (x *DescribeAgentConfigRsp) Reset() {
	*x = DescribeAgentConfigRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAgentConfigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAgentConfigRsp) ProtoMessage() {}

func (x *DescribeAgentConfigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAgentConfigRsp.ProtoReflect.Descriptor instead.
func (*DescribeAgentConfigRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{7}
}

func (x *DescribeAgentConfigRsp) GetModelInfos() []*DescribeAgentConfigRsp_AgentModelInfo {
	if x != nil {
		return x.ModelInfos
	}
	return nil
}

type CreateAgentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用ID
	Agent    *Agent `protobuf:"bytes,2,opt,name=Agent,proto3" json:"Agent,omitempty"`       // Agent信息, 应用创建的第一个Agent为启动Agent
}

func (x *CreateAgentReq) Reset() {
	*x = CreateAgentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAgentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAgentReq) ProtoMessage() {}

func (x *CreateAgentReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAgentReq.ProtoReflect.Descriptor instead.
func (*CreateAgentReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{8}
}

func (x *CreateAgentReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *CreateAgentReq) GetAgent() *Agent {
	if x != nil {
		return x.Agent
	}
	return nil
}

type CreateAgentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId string `protobuf:"bytes,1,opt,name=AgentId,proto3" json:"AgentId,omitempty"` // AgentID，全局唯一索引
}

func (x *CreateAgentRsp) Reset() {
	*x = CreateAgentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAgentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAgentRsp) ProtoMessage() {}

func (x *CreateAgentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAgentRsp.ProtoReflect.Descriptor instead.
func (*CreateAgentRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{9}
}

func (x *CreateAgentRsp) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

type CreateStartingAgentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用ID
	Agent    *Agent `protobuf:"bytes,2,opt,name=Agent,proto3" json:"Agent,omitempty"`       // Agent信息，如果应用不存在启动Agent，则创建此启动Agent
}

func (x *CreateStartingAgentReq) Reset() {
	*x = CreateStartingAgentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStartingAgentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStartingAgentReq) ProtoMessage() {}

func (x *CreateStartingAgentReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStartingAgentReq.ProtoReflect.Descriptor instead.
func (*CreateStartingAgentReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{10}
}

func (x *CreateStartingAgentReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *CreateStartingAgentReq) GetAgent() *Agent {
	if x != nil {
		return x.Agent
	}
	return nil
}

type CreateStartingAgentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId string `protobuf:"bytes,1,opt,name=AgentId,proto3" json:"AgentId,omitempty"` // AgentID，全局唯一索引
}

func (x *CreateStartingAgentRsp) Reset() {
	*x = CreateStartingAgentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStartingAgentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStartingAgentRsp) ProtoMessage() {}

func (x *CreateStartingAgentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStartingAgentRsp.ProtoReflect.Descriptor instead.
func (*CreateStartingAgentRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{11}
}

func (x *CreateStartingAgentRsp) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

type CopyAgentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceAppBizId     string `protobuf:"bytes,1,opt,name=SourceAppBizId,proto3" json:"SourceAppBizId,omitempty"`         // 源应用ID
	SourceAgentId      string `protobuf:"bytes,2,opt,name=SourceAgentId,proto3" json:"SourceAgentId,omitempty"`           // 源AgentID，全局唯一索引，非空则直接复制这个Agent
	SourceWorkflowId   string `protobuf:"bytes,3,opt,name=SourceWorkflowId,proto3" json:"SourceWorkflowId,omitempty"`     // 源WorkflowID，AgentId为空则转换这个workflow为Agent并复制Agent
	SourceWorkflowName string `protobuf:"bytes,4,opt,name=SourceWorkflowName,proto3" json:"SourceWorkflowName,omitempty"` // 源Workflow名称，workflow转换为Agent时作为新Agent的名称
	TargetAppBizId     string `protobuf:"bytes,5,opt,name=TargetAppBizId,proto3" json:"TargetAppBizId,omitempty"`         // 目的应用ID
}

func (x *CopyAgentReq) Reset() {
	*x = CopyAgentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyAgentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyAgentReq) ProtoMessage() {}

func (x *CopyAgentReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyAgentReq.ProtoReflect.Descriptor instead.
func (*CopyAgentReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{12}
}

func (x *CopyAgentReq) GetSourceAppBizId() string {
	if x != nil {
		return x.SourceAppBizId
	}
	return ""
}

func (x *CopyAgentReq) GetSourceAgentId() string {
	if x != nil {
		return x.SourceAgentId
	}
	return ""
}

func (x *CopyAgentReq) GetSourceWorkflowId() string {
	if x != nil {
		return x.SourceWorkflowId
	}
	return ""
}

func (x *CopyAgentReq) GetSourceWorkflowName() string {
	if x != nil {
		return x.SourceWorkflowName
	}
	return ""
}

func (x *CopyAgentReq) GetTargetAppBizId() string {
	if x != nil {
		return x.TargetAppBizId
	}
	return ""
}

type CopyAgentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetAgentId string `protobuf:"bytes,1,opt,name=TargetAgentId,proto3" json:"TargetAgentId,omitempty"` // 复制后新的目的AgentID
}

func (x *CopyAgentRsp) Reset() {
	*x = CopyAgentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyAgentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyAgentRsp) ProtoMessage() {}

func (x *CopyAgentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyAgentRsp.ProtoReflect.Descriptor instead.
func (*CopyAgentRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{13}
}

func (x *CopyAgentRsp) GetTargetAgentId() string {
	if x != nil {
		return x.TargetAgentId
	}
	return ""
}

type CopyAgentAppReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceAppBizId string `protobuf:"bytes,1,opt,name=SourceAppBizId,proto3" json:"SourceAppBizId,omitempty"` // 源应用ID
	Force          bool   `protobuf:"varint,3,opt,name=Force,proto3" json:"Force,omitempty"`                  // 是否强制复制，如果为true，则会删除目标应用的所有Agent，并重新创建
	TargetAppBizId string `protobuf:"bytes,2,opt,name=TargetAppBizId,proto3" json:"TargetAppBizId,omitempty"` // 目的应用ID
}

func (x *CopyAgentAppReq) Reset() {
	*x = CopyAgentAppReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyAgentAppReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyAgentAppReq) ProtoMessage() {}

func (x *CopyAgentAppReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyAgentAppReq.ProtoReflect.Descriptor instead.
func (*CopyAgentAppReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{14}
}

func (x *CopyAgentAppReq) GetSourceAppBizId() string {
	if x != nil {
		return x.SourceAppBizId
	}
	return ""
}

func (x *CopyAgentAppReq) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

func (x *CopyAgentAppReq) GetTargetAppBizId() string {
	if x != nil {
		return x.TargetAppBizId
	}
	return ""
}

type CopyAgentAppRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CopyAgentAppRsp) Reset() {
	*x = CopyAgentAppRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyAgentAppRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyAgentAppRsp) ProtoMessage() {}

func (x *CopyAgentAppRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyAgentAppRsp.ProtoReflect.Descriptor instead.
func (*CopyAgentAppRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{15}
}

type DeleteAgentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用ID， 必填
	AgentId  string `protobuf:"bytes,2,opt,name=AgentId,proto3" json:"AgentId,omitempty"`   // AgentID， 必填
}

func (x *DeleteAgentReq) Reset() {
	*x = DeleteAgentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAgentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAgentReq) ProtoMessage() {}

func (x *DeleteAgentReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAgentReq.ProtoReflect.Descriptor instead.
func (*DeleteAgentReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteAgentReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *DeleteAgentReq) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

type DeleteAgentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId string `protobuf:"bytes,1,opt,name=AgentId,proto3" json:"AgentId,omitempty"` // AgentID
}

func (x *DeleteAgentRsp) Reset() {
	*x = DeleteAgentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAgentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAgentRsp) ProtoMessage() {}

func (x *DeleteAgentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAgentRsp.ProtoReflect.Descriptor instead.
func (*DeleteAgentRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{17}
}

func (x *DeleteAgentRsp) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

type ModifyAgentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用ID， 必填
	Agent    *Agent `protobuf:"bytes,2,opt,name=Agent,proto3" json:"Agent,omitempty"`       // 更新后的Agent信息
}

func (x *ModifyAgentReq) Reset() {
	*x = ModifyAgentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyAgentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyAgentReq) ProtoMessage() {}

func (x *ModifyAgentReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyAgentReq.ProtoReflect.Descriptor instead.
func (*ModifyAgentReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{18}
}

func (x *ModifyAgentReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *ModifyAgentReq) GetAgent() *Agent {
	if x != nil {
		return x.Agent
	}
	return nil
}

type ModifyAgentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ModifyAgentRsp) Reset() {
	*x = ModifyAgentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyAgentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyAgentRsp) ProtoMessage() {}

func (x *ModifyAgentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyAgentRsp.ProtoReflect.Descriptor instead.
func (*ModifyAgentRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{19}
}

type ModifyAgentHandoffListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string                             `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用ID
	Agents   []*ModifyAgentHandoffListReq_Agent `protobuf:"bytes,2,rep,name=Agents,proto3" json:"Agents,omitempty"`     // Agent可转交的子Agent列表
}

func (x *ModifyAgentHandoffListReq) Reset() {
	*x = ModifyAgentHandoffListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyAgentHandoffListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyAgentHandoffListReq) ProtoMessage() {}

func (x *ModifyAgentHandoffListReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyAgentHandoffListReq.ProtoReflect.Descriptor instead.
func (*ModifyAgentHandoffListReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{20}
}

func (x *ModifyAgentHandoffListReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *ModifyAgentHandoffListReq) GetAgents() []*ModifyAgentHandoffListReq_Agent {
	if x != nil {
		return x.Agents
	}
	return nil
}

type ModifyAgentHandoffListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ModifyAgentHandoffListRsp) Reset() {
	*x = ModifyAgentHandoffListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyAgentHandoffListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyAgentHandoffListRsp) ProtoMessage() {}

func (x *ModifyAgentHandoffListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyAgentHandoffListRsp.ProtoReflect.Descriptor instead.
func (*ModifyAgentHandoffListRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{21}
}

type DescribeAgentListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId   string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"`     // appid
	SearchWord string `protobuf:"bytes,2,opt,name=SearchWord,proto3" json:"SearchWord,omitempty"` // 过滤词
	// 分页参数
	PageSize   int32 `protobuf:"varint,3,opt,name=PageSize,proto3" json:"PageSize,omitempty"`     // 每页数目
	PageNumber int32 `protobuf:"varint,4,opt,name=PageNumber,proto3" json:"PageNumber,omitempty"` // 页码
}

func (x *DescribeAgentListReq) Reset() {
	*x = DescribeAgentListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAgentListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAgentListReq) ProtoMessage() {}

func (x *DescribeAgentListReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAgentListReq.ProtoReflect.Descriptor instead.
func (*DescribeAgentListReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{22}
}

func (x *DescribeAgentListReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *DescribeAgentListReq) GetSearchWord() string {
	if x != nil {
		return x.SearchWord
	}
	return ""
}

func (x *DescribeAgentListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *DescribeAgentListReq) GetPageNumber() int32 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

type DescribeAgentListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Agents []*DescribeAgentListRsp_AppAgent `protobuf:"bytes,1,rep,name=Agents,proto3" json:"Agents,omitempty"` // 应用Agent信息列表
	// 分页参数
	Total uint32 `protobuf:"varint,2,opt,name=Total,proto3" json:"Total,omitempty"` // 总数目
}

func (x *DescribeAgentListRsp) Reset() {
	*x = DescribeAgentListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAgentListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAgentListRsp) ProtoMessage() {}

func (x *DescribeAgentListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAgentListRsp.ProtoReflect.Descriptor instead.
func (*DescribeAgentListRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{23}
}

func (x *DescribeAgentListRsp) GetAgents() []*DescribeAgentListRsp_AppAgent {
	if x != nil {
		return x.Agents
	}
	return nil
}

func (x *DescribeAgentListRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type DescribeAppAgentListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string  `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"`                                          // 应用ID, 必须指定
	EnvType  EnvType `protobuf:"varint,2,opt,name=EnvType,proto3,enum=trpc.KEP.agent_config_server.EnvType" json:"EnvType,omitempty"` // 环境标识
}

func (x *DescribeAppAgentListReq) Reset() {
	*x = DescribeAppAgentListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAppAgentListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAppAgentListReq) ProtoMessage() {}

func (x *DescribeAppAgentListReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAppAgentListReq.ProtoReflect.Descriptor instead.
func (*DescribeAppAgentListReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{24}
}

func (x *DescribeAppAgentListReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *DescribeAppAgentListReq) GetEnvType() EnvType {
	if x != nil {
		return x.EnvType
	}
	return EnvType_TEST
}

type DescribeAppAgentListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StaringAgentId string                           `protobuf:"bytes,1,opt,name=StaringAgentId,proto3" json:"StaringAgentId,omitempty"` // 入口启动AgentID
	Agents         []*DescribeAppAgentListRsp_Agent `protobuf:"bytes,2,rep,name=Agents,proto3" json:"Agents,omitempty"`                 // 应用Agent信息列表
}

func (x *DescribeAppAgentListRsp) Reset() {
	*x = DescribeAppAgentListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAppAgentListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAppAgentListRsp) ProtoMessage() {}

func (x *DescribeAppAgentListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAppAgentListRsp.ProtoReflect.Descriptor instead.
func (*DescribeAppAgentListRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{25}
}

func (x *DescribeAppAgentListRsp) GetStaringAgentId() string {
	if x != nil {
		return x.StaringAgentId
	}
	return ""
}

func (x *DescribeAppAgentListRsp) GetAgents() []*DescribeAppAgentListRsp_Agent {
	if x != nil {
		return x.Agents
	}
	return nil
}

type DescribeAgentHasReferencedPluginToolListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId string `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件ID
	ToolId   string `protobuf:"bytes,2,opt,name=ToolId,proto3" json:"ToolId,omitempty"`     // 工具ID
}

func (x *DescribeAgentHasReferencedPluginToolListReq) Reset() {
	*x = DescribeAgentHasReferencedPluginToolListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAgentHasReferencedPluginToolListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAgentHasReferencedPluginToolListReq) ProtoMessage() {}

func (x *DescribeAgentHasReferencedPluginToolListReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAgentHasReferencedPluginToolListReq.ProtoReflect.Descriptor instead.
func (*DescribeAgentHasReferencedPluginToolListReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{26}
}

func (x *DescribeAgentHasReferencedPluginToolListReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *DescribeAgentHasReferencedPluginToolListReq) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

type DescribeAgentHasReferencedPluginToolListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ToolRefs []*AgentToolRef `protobuf:"bytes,1,rep,name=ToolRefs,proto3" json:"ToolRefs,omitempty"` // 工具引用信息
}

func (x *DescribeAgentHasReferencedPluginToolListRsp) Reset() {
	*x = DescribeAgentHasReferencedPluginToolListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAgentHasReferencedPluginToolListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAgentHasReferencedPluginToolListRsp) ProtoMessage() {}

func (x *DescribeAgentHasReferencedPluginToolListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAgentHasReferencedPluginToolListRsp.ProtoReflect.Descriptor instead.
func (*DescribeAgentHasReferencedPluginToolListRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{27}
}

func (x *DescribeAgentHasReferencedPluginToolListRsp) GetToolRefs() []*AgentToolRef {
	if x != nil {
		return x.ToolRefs
	}
	return nil
}

type AgentToolRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"` // 应用id
	ToolId   string `protobuf:"bytes,2,opt,name=ToolId,proto3" json:"ToolId,omitempty"`     // 工具id
	PluginId string `protobuf:"bytes,3,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件id
}

func (x *AgentToolRef) Reset() {
	*x = AgentToolRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentToolRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentToolRef) ProtoMessage() {}

func (x *AgentToolRef) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentToolRef.ProtoReflect.Descriptor instead.
func (*AgentToolRef) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{28}
}

func (x *AgentToolRef) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *AgentToolRef) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *AgentToolRef) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

// 获取应用Agent发布列表请求
type ListAppAgentReleasePreviewReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BotBizId      uint64   `protobuf:"varint,1,opt,name=BotBizId,proto3" json:"BotBizId,omitempty" valid:"required~请传入正确的应用ID"`                  // 机器人
	Query         string   `protobuf:"bytes,2,opt,name=Query,proto3" json:"Query,omitempty"`                         // 查询关键字, 用于模糊匹配标题
	StartTime     uint64   `protobuf:"varint,3,opt,name=StartTime,proto3" json:"StartTime,omitempty"`                // 任务更新时间起点, 时间单位 unix 秒
	EndTime       uint64   `protobuf:"varint,4,opt,name=EndTime,proto3" json:"EndTime,omitempty"`                    // 任务更新时间止点, 时间单位 unix 秒
	Actions       []uint32 `protobuf:"varint,5,rep,packed,name=Actions,proto3" json:"Actions,omitempty"`             // 状态, 状态值：1:新增, 2:修改, 3:删除, 4:发布
	PageNumber    uint32   `protobuf:"varint,6,opt,name=PageNumber,proto3" json:"PageNumber,omitempty"`              // 页码
	PageSize      uint32   `protobuf:"varint,7,opt,name=PageSize,proto3" json:"PageSize,omitempty" valid:"required,range(1|200)~每页数量在1到200之间"`                  // 每页数量
	ReleaseBizId  uint64   `protobuf:"varint,8,opt,name=ReleaseBizId,proto3" json:"ReleaseBizId,omitempty"`          // 发布任务 ID
	ReleaseStatus []uint32 `protobuf:"varint,9,rep,packed,name=ReleaseStatus,proto3" json:"ReleaseStatus,omitempty"` // 发布状态(2 待发布 3 发布中 4 已发布 5 发布失败)
}

func (x *ListAppAgentReleasePreviewReq) Reset() {
	*x = ListAppAgentReleasePreviewReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppAgentReleasePreviewReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppAgentReleasePreviewReq) ProtoMessage() {}

func (x *ListAppAgentReleasePreviewReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppAgentReleasePreviewReq.ProtoReflect.Descriptor instead.
func (*ListAppAgentReleasePreviewReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{29}
}

func (x *ListAppAgentReleasePreviewReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

func (x *ListAppAgentReleasePreviewReq) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *ListAppAgentReleasePreviewReq) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListAppAgentReleasePreviewReq) GetEndTime() uint64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListAppAgentReleasePreviewReq) GetActions() []uint32 {
	if x != nil {
		return x.Actions
	}
	return nil
}

func (x *ListAppAgentReleasePreviewReq) GetPageNumber() uint32 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

func (x *ListAppAgentReleasePreviewReq) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListAppAgentReleasePreviewReq) GetReleaseBizId() uint64 {
	if x != nil {
		return x.ReleaseBizId
	}
	return 0
}

func (x *ListAppAgentReleasePreviewReq) GetReleaseStatus() []uint32 {
	if x != nil {
		return x.ReleaseStatus
	}
	return nil
}

// 获取应用Agent发布列表返回
type ListAppAgentReleasePreviewRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total uint32                                    `protobuf:"varint,1,opt,name=Total,proto3" json:"Total,omitempty"` // 总数
	List  []*ListAppAgentReleasePreviewRsp_AppAgent `protobuf:"bytes,2,rep,name=List,proto3" json:"List,omitempty"`    // 列表
}

func (x *ListAppAgentReleasePreviewRsp) Reset() {
	*x = ListAppAgentReleasePreviewRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppAgentReleasePreviewRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppAgentReleasePreviewRsp) ProtoMessage() {}

func (x *ListAppAgentReleasePreviewRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppAgentReleasePreviewRsp.ProtoReflect.Descriptor instead.
func (*ListAppAgentReleasePreviewRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{30}
}

func (x *ListAppAgentReleasePreviewRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListAppAgentReleasePreviewRsp) GetList() []*ListAppAgentReleasePreviewRsp_AppAgent {
	if x != nil {
		return x.List
	}
	return nil
}

type ClearAgentAppResourceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用业务ID
	AppBizId string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"`
	// 任务ID
	TaskId uint64 `protobuf:"varint,2,opt,name=TaskId,proto3" json:"TaskId,omitempty"`
}

func (x *ClearAgentAppResourceReq) Reset() {
	*x = ClearAgentAppResourceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearAgentAppResourceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearAgentAppResourceReq) ProtoMessage() {}

func (x *ClearAgentAppResourceReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearAgentAppResourceReq.ProtoReflect.Descriptor instead.
func (*ClearAgentAppResourceReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{31}
}

func (x *ClearAgentAppResourceReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *ClearAgentAppResourceReq) GetTaskId() uint64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

type ClearAgentAppResourceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ClearAgentAppResourceRsp) Reset() {
	*x = ClearAgentAppResourceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearAgentAppResourceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearAgentAppResourceRsp) ProtoMessage() {}

func (x *ClearAgentAppResourceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearAgentAppResourceRsp.ProtoReflect.Descriptor instead.
func (*ClearAgentAppResourceRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{32}
}

// Agent的插件信息（前端用）
type AgentPluginInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId string               `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件ID
	Headers  []*AgentPluginHeader `protobuf:"bytes,2,rep,name=Headers,proto3" json:"Headers,omitempty"`   // 应用配置的插件header信息
	Model    *AgentModelInfo      `protobuf:"bytes,3,opt,name=Model,proto3" json:"Model,omitempty"`       // 插件调用LLM时使用的模型配置，一般用于指定知识库问答插件的生成模型
	// 以下为定制插件专用
	PluginInfoType AgentPluginInfo_PluginInfoTypeEnum `protobuf:"varint,10,opt,name=PluginInfoType,proto3,enum=trpc.KEP.agent_config_server.AgentPluginInfo_PluginInfoTypeEnum" json:"PluginInfoType,omitempty"` // 插件类型
	KnowledgeQa    *AgentKnowledgeQAPlugin            `protobuf:"bytes,11,opt,name=KnowledgeQa,proto3" json:"KnowledgeQa,omitempty"`                                                                             // 知识库问答插件配置 when PluginType = PLUGIN_TYPE_KNOWLEDGE_QA
}

func (x *AgentPluginInfo) Reset() {
	*x = AgentPluginInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentPluginInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentPluginInfo) ProtoMessage() {}

func (x *AgentPluginInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentPluginInfo.ProtoReflect.Descriptor instead.
func (*AgentPluginInfo) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{33}
}

func (x *AgentPluginInfo) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *AgentPluginInfo) GetHeaders() []*AgentPluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *AgentPluginInfo) GetModel() *AgentModelInfo {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *AgentPluginInfo) GetPluginInfoType() AgentPluginInfo_PluginInfoTypeEnum {
	if x != nil {
		return x.PluginInfoType
	}
	return AgentPluginInfo_PLUGIN_INFO_TYPE_UNSPECIFIED
}

func (x *AgentPluginInfo) GetKnowledgeQa() *AgentKnowledgeQAPlugin {
	if x != nil {
		return x.KnowledgeQa
	}
	return nil
}

// Agent的工具信息（前端用）
type AgentToolInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId           string                              `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"`                                                         // 插件ID
	PluginName         string                              `protobuf:"bytes,2,opt,name=PluginName,proto3" json:"PluginName,omitempty"`                                                     // 插件名称
	IconUrl            string                              `protobuf:"bytes,3,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`                                                           // 插件图标url
	PluginType         plugin_config_server.PluginTypeEnum `protobuf:"varint,4,opt,name=PluginType,proto3,enum=trpc.KEP.plugin_config_server.PluginTypeEnum" json:"PluginType,omitempty"`  // 插件类型
	ToolId             string                              `protobuf:"bytes,5,opt,name=ToolId,proto3" json:"ToolId,omitempty"`                                                             // 工具id
	ToolName           string                              `protobuf:"bytes,6,opt,name=ToolName,proto3" json:"ToolName,omitempty"`                                                         // 工具名称
	ToolDesc           string                              `protobuf:"bytes,7,opt,name=ToolDesc,proto3" json:"ToolDesc,omitempty"`                                                         // 工具描述信息
	Inputs             []*AgentToolReqParam                `protobuf:"bytes,8,rep,name=Inputs,proto3" json:"Inputs,omitempty"`                                                             // 输入参数
	Outputs            []*AgentToolRspParam                `protobuf:"bytes,9,rep,name=Outputs,proto3" json:"Outputs,omitempty"`                                                           // 输出参数
	CreateType         plugin_config_server.CreateTypeEnum `protobuf:"varint,10,opt,name=CreateType,proto3,enum=trpc.KEP.plugin_config_server.CreateTypeEnum" json:"CreateType,omitempty"` // 创建方式 0-服务 1-代码 2-MCP
	McpServer          *AgentMCPServerInfo                 `protobuf:"bytes,11,opt,name=McpServer,proto3" json:"McpServer,omitempty"`                                                      // MCP插件的配置信息
	IsBindingKnowledge bool                                `protobuf:"varint,12,opt,name=IsBindingKnowledge,proto3" json:"IsBindingKnowledge,omitempty"`                                   //该工具是否和知识库绑定
	Status             int32                               `protobuf:"varint,13,opt,name=Status,proto3" json:"Status,omitempty"`                                                           //插件状态 1-成功(可用)，2-不可用
	CallingMethod      string                              `protobuf:"bytes,14,opt,name=CallingMethod,proto3" json:"CallingMethod,omitempty"`                                              // CallingMethodTypeEnum 枚举值
	Headers            []*AgentPluginHeader                `protobuf:"bytes,20,rep,name=Headers,proto3" json:"Headers,omitempty"`                                                          // 应用配置的插件header信息
}

func (x *AgentToolInfo) Reset() {
	*x = AgentToolInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentToolInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentToolInfo) ProtoMessage() {}

func (x *AgentToolInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentToolInfo.ProtoReflect.Descriptor instead.
func (*AgentToolInfo) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{34}
}

func (x *AgentToolInfo) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *AgentToolInfo) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *AgentToolInfo) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *AgentToolInfo) GetPluginType() plugin_config_server.PluginTypeEnum {
	if x != nil {
		return x.PluginType
	}
	return plugin_config_server.PluginTypeEnum_CUSTOM
}

func (x *AgentToolInfo) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *AgentToolInfo) GetToolName() string {
	if x != nil {
		return x.ToolName
	}
	return ""
}

func (x *AgentToolInfo) GetToolDesc() string {
	if x != nil {
		return x.ToolDesc
	}
	return ""
}

func (x *AgentToolInfo) GetInputs() []*AgentToolReqParam {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *AgentToolInfo) GetOutputs() []*AgentToolRspParam {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *AgentToolInfo) GetCreateType() plugin_config_server.CreateTypeEnum {
	if x != nil {
		return x.CreateType
	}
	return plugin_config_server.CreateTypeEnum_SERVICE
}

func (x *AgentToolInfo) GetMcpServer() *AgentMCPServerInfo {
	if x != nil {
		return x.McpServer
	}
	return nil
}

func (x *AgentToolInfo) GetIsBindingKnowledge() bool {
	if x != nil {
		return x.IsBindingKnowledge
	}
	return false
}

func (x *AgentToolInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AgentToolInfo) GetCallingMethod() string {
	if x != nil {
		return x.CallingMethod
	}
	return ""
}

func (x *AgentToolInfo) GetHeaders() []*AgentPluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

// MCP插件的配置信息, 因为云API不支持map类型，所以这里用repeated来表示
// borrowed from trpc.KEP.plugin_config_server.MCPServerInfo
type AgentMCPServerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	McpServerUrl   string               `protobuf:"bytes,1,opt,name=McpServerUrl,proto3" json:"McpServerUrl,omitempty"`      // mcp server地址
	Headers        []*AgentPluginHeader `protobuf:"bytes,2,rep,name=Headers,proto3" json:"Headers,omitempty"`                // mcp server header信息
	Timeout        int32                `protobuf:"varint,3,opt,name=Timeout,proto3" json:"Timeout,omitempty"`               // 超时时间，单位秒
	SseReadTimeout int32                `protobuf:"varint,4,opt,name=SseReadTimeout,proto3" json:"SseReadTimeout,omitempty"` // sse服务超时时间，单位秒
}

func (x *AgentMCPServerInfo) Reset() {
	*x = AgentMCPServerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentMCPServerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentMCPServerInfo) ProtoMessage() {}

func (x *AgentMCPServerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentMCPServerInfo.ProtoReflect.Descriptor instead.
func (*AgentMCPServerInfo) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{35}
}

func (x *AgentMCPServerInfo) GetMcpServerUrl() string {
	if x != nil {
		return x.McpServerUrl
	}
	return ""
}

func (x *AgentMCPServerInfo) GetHeaders() []*AgentPluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *AgentMCPServerInfo) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *AgentMCPServerInfo) GetSseReadTimeout() int32 {
	if x != nil {
		return x.SseReadTimeout
	}
	return 0
}

// Agent配置MCP插件header信息
type AgentPluginHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParamName    string `protobuf:"bytes,1,opt,name=ParamName,proto3" json:"ParamName,omitempty"`        // 参数名称
	ParamValue   string `protobuf:"bytes,2,opt,name=ParamValue,proto3" json:"ParamValue,omitempty"`      // 参数值, Input未指定时，使用该值，兼容旧版本
	GlobalHidden bool   `protobuf:"varint,3,opt,name=GlobalHidden,proto3" json:"GlobalHidden,omitempty"` // header参数配置是否隐藏不可见，true-隐藏不可见，false-可见
	IsRequired   bool   `protobuf:"varint,4,opt,name=IsRequired,proto3" json:"IsRequired,omitempty"`     // 是否必选
	Input        *Input `protobuf:"bytes,8,opt,name=Input,proto3" json:"Input,omitempty"`                // 输入的值
}

func (x *AgentPluginHeader) Reset() {
	*x = AgentPluginHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentPluginHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentPluginHeader) ProtoMessage() {}

func (x *AgentPluginHeader) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentPluginHeader.ProtoReflect.Descriptor instead.
func (*AgentPluginHeader) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{36}
}

func (x *AgentPluginHeader) GetParamName() string {
	if x != nil {
		return x.ParamName
	}
	return ""
}

func (x *AgentPluginHeader) GetParamValue() string {
	if x != nil {
		return x.ParamValue
	}
	return ""
}

func (x *AgentPluginHeader) GetGlobalHidden() bool {
	if x != nil {
		return x.GlobalHidden
	}
	return false
}

func (x *AgentPluginHeader) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *AgentPluginHeader) GetInput() *Input {
	if x != nil {
		return x.Input
	}
	return nil
}

// 应用工具的请求参数定义
type AgentToolReqParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string                        `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                                              // 参数名称
	Desc         string                        `protobuf:"bytes,2,opt,name=Desc,proto3" json:"Desc,omitempty"`                                              // 参数描述
	Type         plugin_config_server.TypeEnum `protobuf:"varint,3,opt,name=Type,proto3,enum=trpc.KEP.plugin_config_server.TypeEnum" json:"Type,omitempty"` // 参数类型
	IsRequired   bool                          `protobuf:"varint,4,opt,name=IsRequired,proto3" json:"IsRequired,omitempty"`                                 // 是否必选
	DefaultValue string                        `protobuf:"bytes,5,opt,name=DefaultValue,proto3" json:"DefaultValue,omitempty"`                              // 默认值, Input未指定时，使用该值，兼容旧版本
	SubParams    []*AgentToolReqParam          `protobuf:"bytes,6,rep,name=SubParams,proto3" json:"SubParams,omitempty"`                                    // 子参数,ParamType 是OBJECT 或 ARRAY<>类型有用
	AgentHidden  bool                          `protobuf:"varint,7,opt,name=AgentHidden,proto3" json:"AgentHidden,omitempty"`                               //agent模式下模型是否可见
	OneOf        []*AgentToolReqParam          `protobuf:"bytes,8,rep,name=OneOf,proto3" json:"OneOf,omitempty"`                                            // OneOf类型的参数
	AnyOf        []*AgentToolReqParam          `protobuf:"bytes,9,rep,name=AnyOf,proto3" json:"AnyOf,omitempty"`                                            // AnyOf类型的参数
	Input        *Input                        `protobuf:"bytes,10,opt,name=Input,proto3" json:"Input,omitempty"`                                           // 输入的值
}

func (x *AgentToolReqParam) Reset() {
	*x = AgentToolReqParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentToolReqParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentToolReqParam) ProtoMessage() {}

func (x *AgentToolReqParam) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentToolReqParam.ProtoReflect.Descriptor instead.
func (*AgentToolReqParam) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{37}
}

func (x *AgentToolReqParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AgentToolReqParam) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *AgentToolReqParam) GetType() plugin_config_server.TypeEnum {
	if x != nil {
		return x.Type
	}
	return plugin_config_server.TypeEnum_STRING
}

func (x *AgentToolReqParam) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *AgentToolReqParam) GetDefaultValue() string {
	if x != nil {
		return x.DefaultValue
	}
	return ""
}

func (x *AgentToolReqParam) GetSubParams() []*AgentToolReqParam {
	if x != nil {
		return x.SubParams
	}
	return nil
}

func (x *AgentToolReqParam) GetAgentHidden() bool {
	if x != nil {
		return x.AgentHidden
	}
	return false
}

func (x *AgentToolReqParam) GetOneOf() []*AgentToolReqParam {
	if x != nil {
		return x.OneOf
	}
	return nil
}

func (x *AgentToolReqParam) GetAnyOf() []*AgentToolReqParam {
	if x != nil {
		return x.AnyOf
	}
	return nil
}

func (x *AgentToolReqParam) GetInput() *Input {
	if x != nil {
		return x.Input
	}
	return nil
}

// 应用工具的响应参数定义
type AgentToolRspParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string                        `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                                              // 参数名称
	Desc           string                        `protobuf:"bytes,2,opt,name=Desc,proto3" json:"Desc,omitempty"`                                              // 变量描述
	Type           plugin_config_server.TypeEnum `protobuf:"varint,3,opt,name=Type,proto3,enum=trpc.KEP.plugin_config_server.TypeEnum" json:"Type,omitempty"` // 参数类型
	SubParams      []*AgentToolRspParam          `protobuf:"bytes,4,rep,name=SubParams,proto3" json:"SubParams,omitempty"`                                    // 只对 OBJECT 或 ARRAY_OBJECT 类型有用
	AgentHidden    bool                          `protobuf:"varint,5,opt,name=AgentHidden,proto3" json:"AgentHidden,omitempty"`                               //agent模式下模型是否可见
	AnalysisMethod string                        `protobuf:"bytes,6,opt,name=AnalysisMethod,proto3" json:"AnalysisMethod,omitempty"`                          // 解析方式, AnalysisMethodTypeEnum 枚举值
}

func (x *AgentToolRspParam) Reset() {
	*x = AgentToolRspParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentToolRspParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentToolRspParam) ProtoMessage() {}

func (x *AgentToolRspParam) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentToolRspParam.ProtoReflect.Descriptor instead.
func (*AgentToolRspParam) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{38}
}

func (x *AgentToolRspParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AgentToolRspParam) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *AgentToolRspParam) GetType() plugin_config_server.TypeEnum {
	if x != nil {
		return x.Type
	}
	return plugin_config_server.TypeEnum_STRING
}

func (x *AgentToolRspParam) GetSubParams() []*AgentToolRspParam {
	if x != nil {
		return x.SubParams
	}
	return nil
}

func (x *AgentToolRspParam) GetAgentHidden() bool {
	if x != nil {
		return x.AgentHidden
	}
	return false
}

func (x *AgentToolRspParam) GetAnalysisMethod() string {
	if x != nil {
		return x.AnalysisMethod
	}
	return ""
}

// ------------------------------------------------ 基础结构体定义 start  ------------------------------------------------
// borrowed from https://git.woa.com/dialogue-platform/lke_proto/blob/master/thirds-pb/bot-task-config-server/workflow.proto#L282
// 输入值，支持直接赋值和引用
type Input struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InputType      InputSourceEnum `protobuf:"varint,1,opt,name=InputType,proto3,enum=trpc.KEP.agent_config_server.InputSourceEnum" json:"InputType,omitempty"` // 输入来源类型
	UserInputValue *UserInputValue `protobuf:"bytes,2,opt,name=UserInputValue,proto3" json:"UserInputValue,omitempty"`                                          // 用户手写输入
	CustomVarId    string          `protobuf:"bytes,4,opt,name=CustomVarId,proto3" json:"CustomVarId,omitempty"`                                                // 自定义变量（API参数）
}

func (x *Input) Reset() {
	*x = Input{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Input) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Input) ProtoMessage() {}

func (x *Input) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Input.ProtoReflect.Descriptor instead.
func (*Input) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{39}
}

func (x *Input) GetInputType() InputSourceEnum {
	if x != nil {
		return x.InputType
	}
	return InputSourceEnum_UNSPECIFIED
}

func (x *Input) GetUserInputValue() *UserInputValue {
	if x != nil {
		return x.UserInputValue
	}
	return nil
}

func (x *Input) GetCustomVarId() string {
	if x != nil {
		return x.CustomVarId
	}
	return ""
}

// 用户手写输入
type UserInputValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Values []string `protobuf:"bytes,1,rep,name=Values,proto3" json:"Values,omitempty"` // 用户输入的值
}

func (x *UserInputValue) Reset() {
	*x = UserInputValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInputValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInputValue) ProtoMessage() {}

func (x *UserInputValue) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInputValue.ProtoReflect.Descriptor instead.
func (*UserInputValue) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{40}
}

func (x *UserInputValue) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

// 系统参数
type SystemVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name               string `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                              // 系统参数名
	DialogHistoryLimit int32  `protobuf:"varint,2,opt,name=DialogHistoryLimit,proto3" json:"DialogHistoryLimit,omitempty"` // 对话历史轮数的配置；如果Input是系统变量中的“对话历史”时才使用；
}

func (x *SystemVariable) Reset() {
	*x = SystemVariable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemVariable) ProtoMessage() {}

func (x *SystemVariable) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemVariable.ProtoReflect.Descriptor instead.
func (*SystemVariable) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{41}
}

func (x *SystemVariable) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SystemVariable) GetDialogHistoryLimit() int32 {
	if x != nil {
		return x.DialogHistoryLimit
	}
	return 0
}

// 知识库问答插件
// borrowed from https://git.woa.com/dialogue-platform/lke_proto/blob/master/thirds-pb/bot-task-config-server/workflow.proto#L377
type AgentKnowledgeQAPlugin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *KnowledgeFilter `protobuf:"bytes,1,opt,name=Filter,proto3" json:"Filter,omitempty"` // 知识检索筛选范围
}

func (x *AgentKnowledgeQAPlugin) Reset() {
	*x = AgentKnowledgeQAPlugin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentKnowledgeQAPlugin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentKnowledgeQAPlugin) ProtoMessage() {}

func (x *AgentKnowledgeQAPlugin) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentKnowledgeQAPlugin.ProtoReflect.Descriptor instead.
func (*AgentKnowledgeQAPlugin) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{42}
}

func (x *AgentKnowledgeQAPlugin) GetFilter() *KnowledgeFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type KnowledgeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilterType   KnowledgeFilterEnum          `protobuf:"varint,1,opt,name=FilterType,proto3,enum=trpc.KEP.agent_config_server.KnowledgeFilterEnum" json:"FilterType,omitempty"` // 知识检索筛选方式: [全部知识]、[文档和问答]、 [标签]
	DocAndAnswer *KnowledgeFilterDocAndAnswer `protobuf:"bytes,2,opt,name=DocAndAnswer,proto3" json:"DocAndAnswer,omitempty"`                                                    // 文档和问答过滤器
	Tag          *KnowledgeFilterTag          `protobuf:"bytes,3,opt,name=Tag,proto3" json:"Tag,omitempty"`                                                                      // 标签过滤器
}

func (x *KnowledgeFilter) Reset() {
	*x = KnowledgeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeFilter) ProtoMessage() {}

func (x *KnowledgeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeFilter.ProtoReflect.Descriptor instead.
func (*KnowledgeFilter) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{43}
}

func (x *KnowledgeFilter) GetFilterType() KnowledgeFilterEnum {
	if x != nil {
		return x.FilterType
	}
	return KnowledgeFilterEnum_KNOWLEDGE_FILTER_TYPE_ALL
}

func (x *KnowledgeFilter) GetDocAndAnswer() *KnowledgeFilterDocAndAnswer {
	if x != nil {
		return x.DocAndAnswer
	}
	return nil
}

func (x *KnowledgeFilter) GetTag() *KnowledgeFilterTag {
	if x != nil {
		return x.Tag
	}
	return nil
}

type KnowledgeFilterDocAndAnswer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DocBizIds []string `protobuf:"bytes,5,rep,name=DocBizIds,proto3" json:"DocBizIds,omitempty"` // 文档ID列表
	AllQa     bool     `protobuf:"varint,6,opt,name=AllQa,proto3" json:"AllQa,omitempty"`        // 问答
}

func (x *KnowledgeFilterDocAndAnswer) Reset() {
	*x = KnowledgeFilterDocAndAnswer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeFilterDocAndAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeFilterDocAndAnswer) ProtoMessage() {}

func (x *KnowledgeFilterDocAndAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeFilterDocAndAnswer.ProtoReflect.Descriptor instead.
func (*KnowledgeFilterDocAndAnswer) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{44}
}

func (x *KnowledgeFilterDocAndAnswer) GetDocBizIds() []string {
	if x != nil {
		return x.DocBizIds
	}
	return nil
}

func (x *KnowledgeFilterDocAndAnswer) GetAllQa() bool {
	if x != nil {
		return x.AllQa
	}
	return false
}

// 知识检索范围组 --  TAG 时生效
type KnowledgeFilterTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Operator KnowledgeFilterTag_OperatorEnum          `protobuf:"varint,1,opt,name=Operator,proto3,enum=trpc.KEP.agent_config_server.KnowledgeFilterTag_OperatorEnum" json:"Operator,omitempty"` // 标签之间的关系，可以是 AND OR
	Labels   []*KnowledgeFilterTag_KnowledgeAttrLabel `protobuf:"bytes,2,rep,name=Labels,proto3" json:"Labels,omitempty"`                                                                        // 标签
}

func (x *KnowledgeFilterTag) Reset() {
	*x = KnowledgeFilterTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeFilterTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeFilterTag) ProtoMessage() {}

func (x *KnowledgeFilterTag) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeFilterTag.ProtoReflect.Descriptor instead.
func (*KnowledgeFilterTag) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{45}
}

func (x *KnowledgeFilterTag) GetOperator() KnowledgeFilterTag_OperatorEnum {
	if x != nil {
		return x.Operator
	}
	return KnowledgeFilterTag_AND
}

func (x *KnowledgeFilterTag) GetLabels() []*KnowledgeFilterTag_KnowledgeAttrLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

type AgentMigrateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartRobotId int32    `protobuf:"varint,1,opt,name=StartRobotId,proto3" json:"StartRobotId,omitempty"` // 从哪个RobotId 开始迁移, 为0则从第一个开始
	MigrateLimit int32    `protobuf:"varint,2,opt,name=MigrateLimit,proto3" json:"MigrateLimit,omitempty"` // 迁移的数量限制, 为0则不限制
	AppIds       []string `protobuf:"bytes,3,rep,name=AppIds,proto3" json:"AppIds,omitempty"`              //  如果指定了 appid 列表，直接上面参数无效，迁移指定的列表
}

func (x *AgentMigrateReq) Reset() {
	*x = AgentMigrateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentMigrateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentMigrateReq) ProtoMessage() {}

func (x *AgentMigrateReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentMigrateReq.ProtoReflect.Descriptor instead.
func (*AgentMigrateReq) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{46}
}

func (x *AgentMigrateReq) GetStartRobotId() int32 {
	if x != nil {
		return x.StartRobotId
	}
	return 0
}

func (x *AgentMigrateReq) GetMigrateLimit() int32 {
	if x != nil {
		return x.MigrateLimit
	}
	return 0
}

func (x *AgentMigrateReq) GetAppIds() []string {
	if x != nil {
		return x.AppIds
	}
	return nil
}

type AgentMigrateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastRobotId int32 `protobuf:"varint,1,opt,name=LastRobotId,proto3" json:"LastRobotId,omitempty"` // 迁移的最后一个 RobotId
}

func (x *AgentMigrateRsp) Reset() {
	*x = AgentMigrateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentMigrateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentMigrateRsp) ProtoMessage() {}

func (x *AgentMigrateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentMigrateRsp.ProtoReflect.Descriptor instead.
func (*AgentMigrateRsp) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{47}
}

func (x *AgentMigrateRsp) GetLastRobotId() int32 {
	if x != nil {
		return x.LastRobotId
	}
	return 0
}

type DescribeAgentConfigRsp_AgentModelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 模型名称 (trpc 接口使用)
	ModelName string `protobuf:"bytes,8,opt,name=model_name,json=ModelName,proto3" json:"model_name,omitempty"`
	// 指令长度字符限制, 为0则不限制
	InstructionsWordsLimit uint32 `protobuf:"varint,18,opt,name=instructions_words_limit,json=InstructionsWordsLimit,proto3" json:"instructions_words_limit,omitempty"`
}

func (x *DescribeAgentConfigRsp_AgentModelInfo) Reset() {
	*x = DescribeAgentConfigRsp_AgentModelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAgentConfigRsp_AgentModelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAgentConfigRsp_AgentModelInfo) ProtoMessage() {}

func (x *DescribeAgentConfigRsp_AgentModelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAgentConfigRsp_AgentModelInfo.ProtoReflect.Descriptor instead.
func (*DescribeAgentConfigRsp_AgentModelInfo) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{7, 0}
}

func (x *DescribeAgentConfigRsp_AgentModelInfo) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *DescribeAgentConfigRsp_AgentModelInfo) GetInstructionsWordsLimit() uint32 {
	if x != nil {
		return x.InstructionsWordsLimit
	}
	return 0
}

type ModifyAgentHandoffListReq_Agent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId  string   `protobuf:"bytes,1,opt,name=AgentId,proto3" json:"AgentId,omitempty"`   // AgentID
	Handoffs []string `protobuf:"bytes,2,rep,name=Handoffs,proto3" json:"Handoffs,omitempty"` // Agent可转交的子AgentId列表
}

func (x *ModifyAgentHandoffListReq_Agent) Reset() {
	*x = ModifyAgentHandoffListReq_Agent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyAgentHandoffListReq_Agent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyAgentHandoffListReq_Agent) ProtoMessage() {}

func (x *ModifyAgentHandoffListReq_Agent) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyAgentHandoffListReq_Agent.ProtoReflect.Descriptor instead.
func (*ModifyAgentHandoffListReq_Agent) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{20, 0}
}

func (x *ModifyAgentHandoffListReq_Agent) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *ModifyAgentHandoffListReq_Agent) GetHandoffs() []string {
	if x != nil {
		return x.Handoffs
	}
	return nil
}

type DescribeAgentListRsp_AppAgent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Agent      *DescribeAgentListRsp_Agent `protobuf:"bytes,1,opt,name=Agent,proto3" json:"Agent,omitempty"`            // Agent信息列表
	AppBizId   string                      `protobuf:"bytes,2,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"`      // 应用ID
	AppName    string                      `protobuf:"bytes,3,opt,name=AppName,proto3" json:"AppName,omitempty"`        // 应用名称
	From       string                      `protobuf:"bytes,4,opt,name=From,proto3" json:"From,omitempty"`              // 来源
	IsWorkflow bool                        `protobuf:"varint,5,opt,name=IsWorkflow,proto3" json:"IsWorkflow,omitempty"` // 是否是workflow
	Developer  string                      `protobuf:"bytes,6,opt,name=Developer,proto3" json:"Developer,omitempty"`    // 开发者
}

func (x *DescribeAgentListRsp_AppAgent) Reset() {
	*x = DescribeAgentListRsp_AppAgent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAgentListRsp_AppAgent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAgentListRsp_AppAgent) ProtoMessage() {}

func (x *DescribeAgentListRsp_AppAgent) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAgentListRsp_AppAgent.ProtoReflect.Descriptor instead.
func (*DescribeAgentListRsp_AppAgent) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{23, 0}
}

func (x *DescribeAgentListRsp_AppAgent) GetAgent() *DescribeAgentListRsp_Agent {
	if x != nil {
		return x.Agent
	}
	return nil
}

func (x *DescribeAgentListRsp_AppAgent) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *DescribeAgentListRsp_AppAgent) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *DescribeAgentListRsp_AppAgent) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *DescribeAgentListRsp_AppAgent) GetIsWorkflow() bool {
	if x != nil {
		return x.IsWorkflow
	}
	return false
}

func (x *DescribeAgentListRsp_AppAgent) GetDeveloper() string {
	if x != nil {
		return x.Developer
	}
	return ""
}

type DescribeAgentListRsp_Agent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId            string                                  `protobuf:"bytes,1,opt,name=AgentId,proto3" json:"AgentId,omitempty"`                                                       // AgentID，全局唯一索引
	WorkflowId         string                                  `protobuf:"bytes,2,opt,name=WorkflowId,proto3" json:"WorkflowId,omitempty"`                                                 // WorkflowID，非空则当前Agent从workflow转换而来
	Name               string                                  `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty"`                                                             // Agent名称，同一个应用内，Agent名称不能重复
	IconUrl            string                                  `protobuf:"bytes,4,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`                                                       // 插件图标url
	Instructions       string                                  `protobuf:"bytes,5,opt,name=Instructions,proto3" json:"Instructions,omitempty"`                                             // Agent指令；当该Agent被调用时，将作为“系统提示词”使用，描述Agent应执行的操作和响应方式
	HandoffDescription string                                  `protobuf:"bytes,6,opt,name=HandoffDescription,proto3" json:"HandoffDescription,omitempty"`                                 // 当Agent作为转交目标时的描述，用于让其他Agent的LLM理解其功能和转交时机
	Handoffs           []string                                `protobuf:"bytes,7,rep,name=Handoffs,proto3" json:"Handoffs,omitempty"`                                                     // Agent可转交的子AgentId列表
	Model              *DescribeAgentListRsp_AgentModelInfo    `protobuf:"bytes,8,opt,name=Model,proto3" json:"Model,omitempty"`                                                           // Agent调用LLM时使用的模型配置
	Tools              []*AgentToolInfo                        `protobuf:"bytes,9,rep,name=Tools,proto3" json:"Tools,omitempty"`                                                           // Agent可使用的工具列表
	Plugins            []*DescribeAgentListRsp_AgentPluginInfo `protobuf:"bytes,10,rep,name=Plugins,proto3" json:"Plugins,omitempty"`                                                      // Agent可使用的工具列表
	IsStartingAgent    bool                                    `protobuf:"varint,11,opt,name=IsStartingAgent,proto3" json:"IsStartingAgent,omitempty"`                                     // 当前Agent是否是启动Agent
	AgentType          AgentTypeEnum                           `protobuf:"varint,12,opt,name=AgentType,proto3,enum=trpc.KEP.agent_config_server.AgentTypeEnum" json:"AgentType,omitempty"` // Agent类型
}

func (x *DescribeAgentListRsp_Agent) Reset() {
	*x = DescribeAgentListRsp_Agent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAgentListRsp_Agent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAgentListRsp_Agent) ProtoMessage() {}

func (x *DescribeAgentListRsp_Agent) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAgentListRsp_Agent.ProtoReflect.Descriptor instead.
func (*DescribeAgentListRsp_Agent) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{23, 1}
}

func (x *DescribeAgentListRsp_Agent) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *DescribeAgentListRsp_Agent) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *DescribeAgentListRsp_Agent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DescribeAgentListRsp_Agent) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *DescribeAgentListRsp_Agent) GetInstructions() string {
	if x != nil {
		return x.Instructions
	}
	return ""
}

func (x *DescribeAgentListRsp_Agent) GetHandoffDescription() string {
	if x != nil {
		return x.HandoffDescription
	}
	return ""
}

func (x *DescribeAgentListRsp_Agent) GetHandoffs() []string {
	if x != nil {
		return x.Handoffs
	}
	return nil
}

func (x *DescribeAgentListRsp_Agent) GetModel() *DescribeAgentListRsp_AgentModelInfo {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *DescribeAgentListRsp_Agent) GetTools() []*AgentToolInfo {
	if x != nil {
		return x.Tools
	}
	return nil
}

func (x *DescribeAgentListRsp_Agent) GetPlugins() []*DescribeAgentListRsp_AgentPluginInfo {
	if x != nil {
		return x.Plugins
	}
	return nil
}

func (x *DescribeAgentListRsp_Agent) GetIsStartingAgent() bool {
	if x != nil {
		return x.IsStartingAgent
	}
	return false
}

func (x *DescribeAgentListRsp_Agent) GetAgentType() AgentTypeEnum {
	if x != nil {
		return x.AgentType
	}
	return AgentTypeEnum_AGENT_TYPE_UNSPECIFIED
}

// Agent的插件信息（前端用）
type DescribeAgentListRsp_AgentPluginInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId string                               `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件ID
	Headers  []*AgentPluginHeader                 `protobuf:"bytes,2,rep,name=Headers,proto3" json:"Headers,omitempty"`   // 应用配置的插件header信息
	Model    *DescribeAgentListRsp_AgentModelInfo `protobuf:"bytes,3,opt,name=Model,proto3" json:"Model,omitempty"`       // 插件调用LLM时使用的模型配置，一般用于指定知识库问答插件的生成模型
	// 以下为定制插件专用
	PluginInfoType DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum `protobuf:"varint,10,opt,name=PluginInfoType,proto3,enum=trpc.KEP.agent_config_server.DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum" json:"PluginInfoType,omitempty"` // 插件类型
	KnowledgeQa    *AgentKnowledgeQAPlugin                                 `protobuf:"bytes,11,opt,name=KnowledgeQa,proto3" json:"KnowledgeQa,omitempty"`                                                                                                  // 知识库问答插件配置 when PluginType = PLUGIN_TYPE_KNOWLEDGE_QA
}

func (x *DescribeAgentListRsp_AgentPluginInfo) Reset() {
	*x = DescribeAgentListRsp_AgentPluginInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAgentListRsp_AgentPluginInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAgentListRsp_AgentPluginInfo) ProtoMessage() {}

func (x *DescribeAgentListRsp_AgentPluginInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAgentListRsp_AgentPluginInfo.ProtoReflect.Descriptor instead.
func (*DescribeAgentListRsp_AgentPluginInfo) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{23, 2}
}

func (x *DescribeAgentListRsp_AgentPluginInfo) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *DescribeAgentListRsp_AgentPluginInfo) GetHeaders() []*AgentPluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *DescribeAgentListRsp_AgentPluginInfo) GetModel() *DescribeAgentListRsp_AgentModelInfo {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *DescribeAgentListRsp_AgentPluginInfo) GetPluginInfoType() DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum {
	if x != nil {
		return x.PluginInfoType
	}
	return DescribeAgentListRsp_AgentPluginInfo_PLUGIN_INFO_TYPE_UNSPECIFIED
}

func (x *DescribeAgentListRsp_AgentPluginInfo) GetKnowledgeQa() *AgentKnowledgeQAPlugin {
	if x != nil {
		return x.KnowledgeQa
	}
	return nil
}

type DescribeAgentListRsp_AgentModelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 对话历史条数限制
	HistoryLimit uint32 `protobuf:"varint,6,opt,name=history_limit,json=HistoryLimit,proto3" json:"history_limit,omitempty"`
	// 模型名称 (trpc 接口使用)
	ModelName string `protobuf:"bytes,8,opt,name=model_name,json=ModelName,proto3" json:"model_name,omitempty"`
	// 模型是否开启
	IsEnabled bool `protobuf:"varint,10,opt,name=is_enabled,json=IsEnabled,proto3" json:"is_enabled,omitempty"`
	// 温度
	Temperature float32 `protobuf:"fixed32,14,opt,name=temperature,json=Temperature,proto3" json:"temperature,omitempty"`
	// TopP
	TopP float32 `protobuf:"fixed32,15,opt,name=top_p,json=TopP,proto3" json:"top_p,omitempty"`
	// 模型上下文长度字符限制
	ModelContextWordsLimit string `protobuf:"bytes,17,opt,name=model_context_words_limit,json=ModelContextWordsLimit,proto3" json:"model_context_words_limit,omitempty"`
	// 指令长度字符限制
	InstructionsWordsLimit uint32 `protobuf:"varint,18,opt,name=instructions_words_limit,json=InstructionsWordsLimit,proto3" json:"instructions_words_limit,omitempty"`
	// 模型别名
	ModelAliasName string `protobuf:"bytes,19,opt,name=model_alias_name,json=ModelAliasName,proto3" json:"model_alias_name,omitempty"`
}

func (x *DescribeAgentListRsp_AgentModelInfo) Reset() {
	*x = DescribeAgentListRsp_AgentModelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAgentListRsp_AgentModelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAgentListRsp_AgentModelInfo) ProtoMessage() {}

func (x *DescribeAgentListRsp_AgentModelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAgentListRsp_AgentModelInfo.ProtoReflect.Descriptor instead.
func (*DescribeAgentListRsp_AgentModelInfo) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{23, 3}
}

func (x *DescribeAgentListRsp_AgentModelInfo) GetHistoryLimit() uint32 {
	if x != nil {
		return x.HistoryLimit
	}
	return 0
}

func (x *DescribeAgentListRsp_AgentModelInfo) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *DescribeAgentListRsp_AgentModelInfo) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *DescribeAgentListRsp_AgentModelInfo) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *DescribeAgentListRsp_AgentModelInfo) GetTopP() float32 {
	if x != nil {
		return x.TopP
	}
	return 0
}

func (x *DescribeAgentListRsp_AgentModelInfo) GetModelContextWordsLimit() string {
	if x != nil {
		return x.ModelContextWordsLimit
	}
	return ""
}

func (x *DescribeAgentListRsp_AgentModelInfo) GetInstructionsWordsLimit() uint32 {
	if x != nil {
		return x.InstructionsWordsLimit
	}
	return 0
}

func (x *DescribeAgentListRsp_AgentModelInfo) GetModelAliasName() string {
	if x != nil {
		return x.ModelAliasName
	}
	return ""
}

type DescribeAppAgentListRsp_Agent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId            string                                     `protobuf:"bytes,1,opt,name=AgentId,proto3" json:"AgentId,omitempty"`                                                       // AgentID，全局唯一索引
	WorkflowId         string                                     `protobuf:"bytes,2,opt,name=WorkflowId,proto3" json:"WorkflowId,omitempty"`                                                 // WorkflowID，非空则当前Agent从workflow转换而来
	Name               string                                     `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty"`                                                             // Agent名称，同一个应用内，Agent名称不能重复
	IconUrl            string                                     `protobuf:"bytes,4,opt,name=IconUrl,proto3" json:"IconUrl,omitempty"`                                                       // 插件图标url
	Instructions       string                                     `protobuf:"bytes,5,opt,name=Instructions,proto3" json:"Instructions,omitempty"`                                             // Agent指令；当该Agent被调用时，将作为“系统提示词”使用，描述Agent应执行的操作和响应方式
	HandoffDescription string                                     `protobuf:"bytes,6,opt,name=HandoffDescription,proto3" json:"HandoffDescription,omitempty"`                                 // 当Agent作为转交目标时的描述，用于让其他Agent的LLM理解其功能和转交时机
	Handoffs           []string                                   `protobuf:"bytes,7,rep,name=Handoffs,proto3" json:"Handoffs,omitempty"`                                                     // Agent可转交的子AgentId列表
	Model              *DescribeAppAgentListRsp_AgentModelInfo    `protobuf:"bytes,8,opt,name=Model,proto3" json:"Model,omitempty"`                                                           // Agent调用LLM时使用的模型配置
	Tools              []*AgentToolInfo                           `protobuf:"bytes,9,rep,name=Tools,proto3" json:"Tools,omitempty"`                                                           // Agent可使用的工具列表
	Plugins            []*DescribeAppAgentListRsp_AgentPluginInfo `protobuf:"bytes,10,rep,name=Plugins,proto3" json:"Plugins,omitempty"`                                                      // Agent可使用的工具列表
	IsStartingAgent    bool                                       `protobuf:"varint,11,opt,name=IsStartingAgent,proto3" json:"IsStartingAgent,omitempty"`                                     // 当前Agent是否是启动Agent
	AgentType          AgentTypeEnum                              `protobuf:"varint,12,opt,name=AgentType,proto3,enum=trpc.KEP.agent_config_server.AgentTypeEnum" json:"AgentType,omitempty"` // Agent类型
}

func (x *DescribeAppAgentListRsp_Agent) Reset() {
	*x = DescribeAppAgentListRsp_Agent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAppAgentListRsp_Agent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAppAgentListRsp_Agent) ProtoMessage() {}

func (x *DescribeAppAgentListRsp_Agent) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAppAgentListRsp_Agent.ProtoReflect.Descriptor instead.
func (*DescribeAppAgentListRsp_Agent) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{25, 0}
}

func (x *DescribeAppAgentListRsp_Agent) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *DescribeAppAgentListRsp_Agent) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *DescribeAppAgentListRsp_Agent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DescribeAppAgentListRsp_Agent) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *DescribeAppAgentListRsp_Agent) GetInstructions() string {
	if x != nil {
		return x.Instructions
	}
	return ""
}

func (x *DescribeAppAgentListRsp_Agent) GetHandoffDescription() string {
	if x != nil {
		return x.HandoffDescription
	}
	return ""
}

func (x *DescribeAppAgentListRsp_Agent) GetHandoffs() []string {
	if x != nil {
		return x.Handoffs
	}
	return nil
}

func (x *DescribeAppAgentListRsp_Agent) GetModel() *DescribeAppAgentListRsp_AgentModelInfo {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *DescribeAppAgentListRsp_Agent) GetTools() []*AgentToolInfo {
	if x != nil {
		return x.Tools
	}
	return nil
}

func (x *DescribeAppAgentListRsp_Agent) GetPlugins() []*DescribeAppAgentListRsp_AgentPluginInfo {
	if x != nil {
		return x.Plugins
	}
	return nil
}

func (x *DescribeAppAgentListRsp_Agent) GetIsStartingAgent() bool {
	if x != nil {
		return x.IsStartingAgent
	}
	return false
}

func (x *DescribeAppAgentListRsp_Agent) GetAgentType() AgentTypeEnum {
	if x != nil {
		return x.AgentType
	}
	return AgentTypeEnum_AGENT_TYPE_UNSPECIFIED
}

// Agent的插件信息（前端用）
type DescribeAppAgentListRsp_AgentPluginInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginId string                                  `protobuf:"bytes,1,opt,name=PluginId,proto3" json:"PluginId,omitempty"` // 插件ID
	Headers  []*AgentPluginHeader                    `protobuf:"bytes,2,rep,name=Headers,proto3" json:"Headers,omitempty"`   // 应用配置的插件header信息
	Model    *DescribeAppAgentListRsp_AgentModelInfo `protobuf:"bytes,3,opt,name=Model,proto3" json:"Model,omitempty"`       // 插件调用LLM时使用的模型配置，一般用于指定知识库问答插件的生成模型
	// 以下为定制插件专用
	PluginInfoType DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum `protobuf:"varint,10,opt,name=PluginInfoType,proto3,enum=trpc.KEP.agent_config_server.DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum" json:"PluginInfoType,omitempty"` // 插件信息类型
	KnowledgeQa    *AgentKnowledgeQAPlugin                                    `protobuf:"bytes,11,opt,name=KnowledgeQa,proto3" json:"KnowledgeQa,omitempty"`                                                                                                     // 知识库问答插件配置 when PluginType = PLUGIN_TYPE_KNOWLEDGE_QA
}

func (x *DescribeAppAgentListRsp_AgentPluginInfo) Reset() {
	*x = DescribeAppAgentListRsp_AgentPluginInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAppAgentListRsp_AgentPluginInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAppAgentListRsp_AgentPluginInfo) ProtoMessage() {}

func (x *DescribeAppAgentListRsp_AgentPluginInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAppAgentListRsp_AgentPluginInfo.ProtoReflect.Descriptor instead.
func (*DescribeAppAgentListRsp_AgentPluginInfo) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{25, 1}
}

func (x *DescribeAppAgentListRsp_AgentPluginInfo) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *DescribeAppAgentListRsp_AgentPluginInfo) GetHeaders() []*AgentPluginHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *DescribeAppAgentListRsp_AgentPluginInfo) GetModel() *DescribeAppAgentListRsp_AgentModelInfo {
	if x != nil {
		return x.Model
	}
	return nil
}

func (x *DescribeAppAgentListRsp_AgentPluginInfo) GetPluginInfoType() DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum {
	if x != nil {
		return x.PluginInfoType
	}
	return DescribeAppAgentListRsp_AgentPluginInfo_PLUGIN_INFO_TYPE_UNSPECIFIED
}

func (x *DescribeAppAgentListRsp_AgentPluginInfo) GetKnowledgeQa() *AgentKnowledgeQAPlugin {
	if x != nil {
		return x.KnowledgeQa
	}
	return nil
}

type DescribeAppAgentListRsp_AgentModelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 对话历史条数限制
	HistoryLimit uint32 `protobuf:"varint,6,opt,name=history_limit,json=HistoryLimit,proto3" json:"history_limit,omitempty"`
	// 模型名称 (trpc 接口使用)
	ModelName string `protobuf:"bytes,8,opt,name=model_name,json=ModelName,proto3" json:"model_name,omitempty"`
	// 模型是否开启
	IsEnabled bool `protobuf:"varint,10,opt,name=is_enabled,json=IsEnabled,proto3" json:"is_enabled,omitempty"`
	// 温度
	Temperature float32 `protobuf:"fixed32,14,opt,name=temperature,json=Temperature,proto3" json:"temperature,omitempty"`
	// TopP
	TopP float32 `protobuf:"fixed32,15,opt,name=top_p,json=TopP,proto3" json:"top_p,omitempty"`
	// 模型上下文长度字符限制
	ModelContextWordsLimit string `protobuf:"bytes,17,opt,name=model_context_words_limit,json=ModelContextWordsLimit,proto3" json:"model_context_words_limit,omitempty"`
	// 指令长度字符限制
	InstructionsWordsLimit uint32 `protobuf:"varint,18,opt,name=instructions_words_limit,json=InstructionsWordsLimit,proto3" json:"instructions_words_limit,omitempty"`
	// 模型别名
	ModelAliasName string `protobuf:"bytes,19,opt,name=model_alias_name,json=ModelAliasName,proto3" json:"model_alias_name,omitempty"`
}

func (x *DescribeAppAgentListRsp_AgentModelInfo) Reset() {
	*x = DescribeAppAgentListRsp_AgentModelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeAppAgentListRsp_AgentModelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeAppAgentListRsp_AgentModelInfo) ProtoMessage() {}

func (x *DescribeAppAgentListRsp_AgentModelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeAppAgentListRsp_AgentModelInfo.ProtoReflect.Descriptor instead.
func (*DescribeAppAgentListRsp_AgentModelInfo) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{25, 2}
}

func (x *DescribeAppAgentListRsp_AgentModelInfo) GetHistoryLimit() uint32 {
	if x != nil {
		return x.HistoryLimit
	}
	return 0
}

func (x *DescribeAppAgentListRsp_AgentModelInfo) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *DescribeAppAgentListRsp_AgentModelInfo) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *DescribeAppAgentListRsp_AgentModelInfo) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *DescribeAppAgentListRsp_AgentModelInfo) GetTopP() float32 {
	if x != nil {
		return x.TopP
	}
	return 0
}

func (x *DescribeAppAgentListRsp_AgentModelInfo) GetModelContextWordsLimit() string {
	if x != nil {
		return x.ModelContextWordsLimit
	}
	return ""
}

func (x *DescribeAppAgentListRsp_AgentModelInfo) GetInstructionsWordsLimit() uint32 {
	if x != nil {
		return x.InstructionsWordsLimit
	}
	return 0
}

func (x *DescribeAppAgentListRsp_AgentModelInfo) GetModelAliasName() string {
	if x != nil {
		return x.ModelAliasName
	}
	return ""
}

// AppAgent 发布列表详情
type ListAppAgentReleasePreviewRsp_AppAgent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId    string `protobuf:"bytes,1,opt,name=AgentId,proto3" json:"AgentId,omitempty"`        // 工具ID
	AgentName  string `protobuf:"bytes,2,opt,name=AgentName,proto3" json:"AgentName,omitempty"`    // 工具名称
	UpdateTime uint64 `protobuf:"varint,3,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty"` // 更新时间, unix 秒时间戳 (s)
	Action     uint32 `protobuf:"varint,4,opt,name=Action,proto3" json:"Action,omitempty"`         // 状态, 状态值：1:新增, 2:修改, 3:删除, 4:发布
	ActionDesc string `protobuf:"bytes,5,opt,name=ActionDesc,proto3" json:"ActionDesc,omitempty"`  // 状态, 状态值：1:新增, 2:修改, 3:删除, 4:发布
	Message    string `protobuf:"bytes,6,opt,name=Message,proto3" json:"Message,omitempty"`        // 发布消息
}

func (x *ListAppAgentReleasePreviewRsp_AppAgent) Reset() {
	*x = ListAppAgentReleasePreviewRsp_AppAgent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppAgentReleasePreviewRsp_AppAgent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppAgentReleasePreviewRsp_AppAgent) ProtoMessage() {}

func (x *ListAppAgentReleasePreviewRsp_AppAgent) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppAgentReleasePreviewRsp_AppAgent.ProtoReflect.Descriptor instead.
func (*ListAppAgentReleasePreviewRsp_AppAgent) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{30, 0}
}

func (x *ListAppAgentReleasePreviewRsp_AppAgent) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *ListAppAgentReleasePreviewRsp_AppAgent) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *ListAppAgentReleasePreviewRsp_AppAgent) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *ListAppAgentReleasePreviewRsp_AppAgent) GetAction() uint32 {
	if x != nil {
		return x.Action
	}
	return 0
}

func (x *ListAppAgentReleasePreviewRsp_AppAgent) GetActionDesc() string {
	if x != nil {
		return x.ActionDesc
	}
	return ""
}

func (x *ListAppAgentReleasePreviewRsp_AppAgent) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type KnowledgeFilterTag_KnowledgeAttrLabel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributeBizId uint64   `protobuf:"varint,1,opt,name=AttributeBizId,proto3" json:"AttributeBizId,omitempty"` // 属性ID
	Inputs         []*Input `protobuf:"bytes,2,rep,name=Inputs,proto3" json:"Inputs,omitempty"`                  // 标签值，标签值之间是或的关系，只有匹配的，才会进行知识检索，否则报检索不到
}

func (x *KnowledgeFilterTag_KnowledgeAttrLabel) Reset() {
	*x = KnowledgeFilterTag_KnowledgeAttrLabel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_agent_config_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeFilterTag_KnowledgeAttrLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeFilterTag_KnowledgeAttrLabel) ProtoMessage() {}

func (x *KnowledgeFilterTag_KnowledgeAttrLabel) ProtoReflect() protoreflect.Message {
	mi := &file_agent_config_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeFilterTag_KnowledgeAttrLabel.ProtoReflect.Descriptor instead.
func (*KnowledgeFilterTag_KnowledgeAttrLabel) Descriptor() ([]byte, []int) {
	return file_agent_config_proto_rawDescGZIP(), []int{45, 0}
}

func (x *KnowledgeFilterTag_KnowledgeAttrLabel) GetAttributeBizId() uint64 {
	if x != nil {
		return x.AttributeBizId
	}
	return 0
}

func (x *KnowledgeFilterTag_KnowledgeAttrLabel) GetInputs() []*Input {
	if x != nil {
		return x.Inputs
	}
	return nil
}

var File_agent_config_proto protoreflect.FileDescriptor

var file_agent_config_proto_rawDesc = []byte{
	0x0a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x1a, 0x0a, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x2d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2d, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2f, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x2d, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x62, 0x6f, 0x74, 0x2d, 0x74, 0x61,
	0x73, 0x6b, 0x2d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2f, 0x64, 0x61, 0x74, 0x61, 0x2d, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x09, 0x0a, 0x07, 0x45, 0x63, 0x68, 0x6f, 0x52, 0x65, 0x71, 0x22, 0x09, 0x0a, 0x07, 0x45,
	0x63, 0x68, 0x6f, 0x52, 0x73, 0x70, 0x22, 0xa5, 0x06, 0x0a, 0x0e, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x50, 0x72, 0x6f, 0x6d, 0x70,
	0x74, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x50,
	0x72, 0x6f, 0x6d, 0x70, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x23, 0x0a, 0x0d, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f,
	0x77, 0x6f, 0x72, 0x64, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x11, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x49, 0x73, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1a, 0x0a,
	0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x06, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x43, 0x68, 0x61,
	0x74, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x13, 0x0a, 0x05, 0x74,
	0x6f, 0x70, 0x5f, 0x6b, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x54, 0x6f, 0x70, 0x4b,
	0x12, 0x53, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x2e,
	0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x50, 0x72,
	0x6f, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x54, 0x65, 0x6d, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x70,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x54, 0x6f, 0x70, 0x50, 0x12, 0x25, 0x0a, 0x0e,
	0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x19, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x38,
	0x0a, 0x18, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x77,
	0x6f, 0x72, 0x64, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x16, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x57, 0x6f,
	0x72, 0x64, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x4e, 0x61,
	0x6d, 0x65, 0x1a, 0x3a, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa4,
	0x04, 0x0a, 0x05, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c,
	0x12, 0x22, 0x0a, 0x0c, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x73,
	0x12, 0x42, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x41, 0x0a, 0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x12, 0x47, 0x0a, 0x07, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73,
	0x12, 0x28, 0x0a, 0x0f, 0x49, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x49, 0x73, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x09, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xbf, 0x01, 0x0a, 0x1b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x46, 0x6f, 0x72, 0x4b, 0x62,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x65, 0x52, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x76, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x45, 0x78, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x51, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x45, 0x78,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x51, 0x61, 0x12, 0x5e, 0x0a, 0x10, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x10, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x22, 0x64, 0x0a, 0x14, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12,
	0x1e, 0x0a, 0x0a, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x22, 0x52, 0x0a,
	0x16, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69,
	0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69,
	0x7a, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0xe8, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x12, 0x63, 0x0a, 0x0a,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x43, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x1a, 0x69, 0x0a, 0x0e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x16, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x67, 0x0a, 0x0e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a,
	0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x05, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x05,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x22, 0x2a, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x6f, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41,
	0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41,
	0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x05, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x22, 0x32, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xe0, 0x01, 0x0a, 0x0c, 0x43, 0x6f, 0x70, 0x79, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12,
	0x24, 0x0a, 0x0d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49,
	0x64, 0x12, 0x2e, 0x0a, 0x12, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x26, 0x0a, 0x0e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x70, 0x70, 0x42, 0x69,
	0x7a, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x34, 0x0a, 0x0c, 0x43, 0x6f, 0x70,
	0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0x77, 0x0a, 0x0f, 0x43, 0x6f, 0x70, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x52,
	0x65, 0x71, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x70, 0x70, 0x42,
	0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x46, 0x6f,
	0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x46, 0x6f, 0x72, 0x63, 0x65,
	0x12, 0x26, 0x0a, 0x0e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x11, 0x0a, 0x0f, 0x43, 0x6f, 0x70, 0x79,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x52, 0x73, 0x70, 0x22, 0x46, 0x0a, 0x0e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a,
	0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x22, 0x2a, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0x67, 0x0a, 0x0e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x39, 0x0a,
	0x05, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x52, 0x05, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x22, 0x10, 0x0a, 0x0e, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0xcd, 0x01, 0x0a, 0x19, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x66,
	0x66, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42,
	0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42,
	0x69, 0x7a, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x06, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x48,
	0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x2e, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x52, 0x06, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x3d, 0x0a, 0x05, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x73, 0x22, 0x1b, 0x0a, 0x19, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22, 0x8e, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x57, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x57, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x61, 0x67, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x50, 0x61,
	0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x89, 0x0e, 0x0a, 0x14, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73,
	0x70, 0x12, 0x53, 0x0a, 0x06, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x73, 0x70, 0x2e, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x06,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x1a, 0xe2, 0x01, 0x0a,
	0x08, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x05, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x2e, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x52, 0x05, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70,
	0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70,
	0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x46, 0x72, 0x6f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x46,
	0x72, 0x6f, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x49, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x72, 0x1a, 0xce, 0x04, 0x0a, 0x05, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x63, 0x6f,
	0x6e, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x49, 0x63, 0x6f, 0x6e,
	0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x49, 0x6e, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x48, 0x61, 0x6e, 0x64, 0x6f,
	0x66, 0x66, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6f,
	0x66, 0x66, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6f,
	0x66, 0x66, 0x73, 0x12, 0x57, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x41, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x41, 0x0a, 0x05,
	0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x12,
	0x5c, 0x0a, 0x07, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x42, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x73, 0x70, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x12, 0x28, 0x0a,
	0x0f, 0x49, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x49, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x09, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x1a, 0x83, 0x04, 0x0a, 0x0f, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x49, 0x64, 0x12, 0x49, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x57, 0x0a,
	0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70,
	0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x7d, 0x0a, 0x0e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x55,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x73, 0x70, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x79, 0x70,
	0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x0b, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x51, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x41, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x52, 0x0b, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x61, 0x22, 0x59, 0x0a,
	0x12, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x4c, 0x55, 0x47, 0x49, 0x4e, 0x5f, 0x49, 0x4e,
	0x46, 0x4f, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x4c, 0x55, 0x47, 0x49, 0x4e, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45,
	0x44, 0x47, 0x45, 0x5f, 0x51, 0x41, 0x10, 0x01, 0x1a, 0xc9, 0x02, 0x0a, 0x0e, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x68,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0c, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x49, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x20,
	0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0b, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x04, 0x54, 0x6f, 0x70, 0x50, 0x12, 0x39, 0x0a, 0x19, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x38, 0x0a, 0x18, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x16, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x57, 0x6f, 0x72, 0x64, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x6c, 0x69, 0x61, 0x73,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x76, 0x0a, 0x17, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x07, 0x45,
	0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x76, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x07, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x22, 0xc6, 0x0c, 0x0a,
	0x17, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x72,
	0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x53, 0x74, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x53, 0x0a, 0x06, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0xd4, 0x04, 0x0a, 0x05, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x6e, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x49,
	0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x48,
	0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x48,
	0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x48,
	0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x73, 0x12, 0x5a, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70,
	0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x2e, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x41, 0x0a, 0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x05, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x12, 0x5f, 0x0a, 0x07, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41,
	0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x2e, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07,
	0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x49, 0x73, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x49, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x12, 0x49, 0x0a, 0x09, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x09, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x8a, 0x04, 0x0a,
	0x0f, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x07,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x07,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x5a, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70,
	0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x2e, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x80, 0x01, 0x0a, 0x0e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x58, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x73, 0x70, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x0b, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x51, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x41, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x52, 0x0b, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x61, 0x22, 0x59,
	0x0a, 0x12, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x79, 0x70, 0x65,
	0x45, 0x6e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x4c, 0x55, 0x47, 0x49, 0x4e, 0x5f, 0x49,
	0x4e, 0x46, 0x4f, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x4c, 0x55, 0x47, 0x49, 0x4e,
	0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x4c,
	0x45, 0x44, 0x47, 0x45, 0x5f, 0x51, 0x41, 0x10, 0x01, 0x1a, 0xc9, 0x02, 0x0a, 0x0e, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x0a, 0x0d,
	0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x49, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x04, 0x54, 0x6f, 0x70, 0x50, 0x12, 0x39, 0x0a, 0x19, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x38, 0x0a, 0x18, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x16, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x6c, 0x69, 0x61,
	0x73, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x61, 0x0a, 0x2b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x64, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x6f, 0x6f, 0x6c, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x22, 0x75, 0x0a, 0x2b, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x6f, 0x6f, 0x6c,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x46, 0x0a, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x52,
	0x65, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x6f,
	0x6f, 0x6c, 0x52, 0x65, 0x66, 0x52, 0x08, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x66, 0x73, 0x22,
	0x5e, 0x0a, 0x0c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x66, 0x12,
	0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x54,
	0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x6f, 0x6f,
	0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x22,
	0x9a, 0x03, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65,
	0x71, 0x12, 0x4b, 0x0a, 0x08, 0x42, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x2f, 0x92, 0xb8, 0x18, 0x2b, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0xe8, 0xaf, 0xb7, 0xe4, 0xbc, 0xa0, 0xe5,
	0x85, 0xa5, 0xe6, 0xad, 0xa3, 0xe7, 0xa1, 0xae, 0xe7, 0x9a, 0x84, 0xe5, 0xba, 0x94, 0xe7, 0x94,
	0xa8, 0x49, 0x44, 0x22, 0x52, 0x08, 0x42, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x07, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x61, 0x67, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x50, 0x61, 0x67, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x5a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x3e, 0x92, 0xb8, 0x18, 0x3a, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x2c, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x28, 0x31, 0x7c, 0x32, 0x30, 0x30, 0x29, 0x7e, 0xe6, 0xaf, 0x8f, 0xe9, 0xa1,
	0xb5, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0xe5, 0x9c, 0xa8, 0x31, 0xe5, 0x88, 0xb0, 0x32, 0x30,
	0x30, 0xe4, 0xb9, 0x8b, 0xe9, 0x97, 0xb4, 0x22, 0x52, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x42, 0x69, 0x7a,
	0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0d, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xc6, 0x02, 0x0a,
	0x1d, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x73, 0x70, 0x12, 0x14,
	0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x58, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x44, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x73, 0x70, 0x2e,
	0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0xb4,
	0x01, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x4e, 0x0a, 0x18, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x54,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x1a, 0x0a, 0x18, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x73,
	0x70, 0x22, 0xd9, 0x03, 0x0a, 0x0f, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x12, 0x49, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x42, 0x0a, 0x05,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x68, 0x0a, 0x0e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0e, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x0b, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x41, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x52, 0x0b, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x51, 0x61, 0x22, 0x59, 0x0a, 0x12, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x4c, 0x55, 0x47,
	0x49, 0x4e, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x4c,
	0x55, 0x47, 0x49, 0x4e, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4b,
	0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x51, 0x41, 0x10, 0x01, 0x22, 0xf0, 0x05,
	0x0a, 0x0d, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x49,
	0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x49, 0x63,
	0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x4d, 0x0a, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x6f, 0x6f, 0x6c,
	0x44, 0x65, 0x73, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x54, 0x6f, 0x6f, 0x6c,
	0x44, 0x65, 0x73, 0x63, 0x12, 0x47, 0x0a, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x49, 0x0a,
	0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52,
	0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x4d, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4e, 0x0a, 0x09, 0x4d, 0x63, 0x70, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d,
	0x43, 0x50, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x4d, 0x63,
	0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x12, 0x49, 0x73, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x12, 0x49, 0x73, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x24, 0x0a, 0x0d, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x49, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x22, 0xc5, 0x01, 0x0a, 0x12, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x43, 0x50, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x4d, 0x63, 0x70, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4d,
	0x63, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x49, 0x0a, 0x07, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x12, 0x26, 0x0a, 0x0e, 0x53, 0x73, 0x65, 0x52, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x53, 0x73, 0x65, 0x52, 0x65, 0x61,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x22, 0xd0, 0x01, 0x0a, 0x11, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1c,
	0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e,
	0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x12, 0x39, 0x0a, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x22, 0xf6, 0x03, 0x0a, 0x11,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x3b, 0x0a, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x49, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x4d, 0x0a, 0x09, 0x53, 0x75,
	0x62, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09,
	0x53, 0x75, 0x62, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x12, 0x45, 0x0a, 0x05, 0x4f,
	0x6e, 0x65, 0x4f, 0x66, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54,
	0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x05, 0x4f, 0x6e, 0x65,
	0x4f, 0x66, 0x12, 0x45, 0x0a, 0x05, 0x41, 0x6e, 0x79, 0x4f, 0x66, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x52, 0x05, 0x41, 0x6e, 0x79, 0x4f, 0x66, 0x12, 0x39, 0x0a, 0x05, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x05, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x22, 0x91, 0x02, 0x0a, 0x11, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x6f,
	0x6f, 0x6c, 0x52, 0x73, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65,
	0x73, 0x63, 0x12, 0x3b, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x4d, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x52, 0x09, 0x53, 0x75, 0x62, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20,
	0x0a, 0x0b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e,
	0x12, 0x26, 0x0a, 0x0e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x69, 0x73, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22, 0xcc, 0x01, 0x0a, 0x05, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x12, 0x4b, 0x0a, 0x09, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x54, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56,
	0x61, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x56, 0x61, 0x72, 0x49, 0x64, 0x22, 0x28, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x22, 0x54, 0x0a, 0x0e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x44, 0x69, 0x61, 0x6c, 0x6f,
	0x67, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x12, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x5f, 0x0a, 0x16, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x41, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x12, 0x45, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x87, 0x02, 0x0a, 0x0f, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x51, 0x0a, 0x0a,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x5d, 0x0a, 0x0c, 0x44, 0x6f, 0x63, 0x41, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x44, 0x6f, 0x63, 0x41, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72,
	0x52, 0x0c, 0x44, 0x6f, 0x63, 0x41, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x42,
	0x0a, 0x03, 0x54, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x03, 0x54,
	0x61, 0x67, 0x22, 0x51, 0x0a, 0x1b, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x44, 0x6f, 0x63, 0x41, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x6f, 0x63, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x44, 0x6f, 0x63, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x41, 0x6c, 0x6c, 0x51, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x41, 0x6c, 0x6c, 0x51, 0x61, 0x22, 0xe8, 0x02, 0x0a, 0x12, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12, 0x59, 0x0a, 0x08,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67,
	0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x5b, 0x0a, 0x06, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x41, 0x74, 0x74, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x1a, 0x79, 0x0a, 0x12, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x41, 0x74, 0x74, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x42, 0x69, 0x7a,
	0x49, 0x64, 0x12, 0x3b, 0x0a, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x22,
	0x1f, 0x0a, 0x0c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x07, 0x0a, 0x03, 0x41, 0x4e, 0x44, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x52, 0x10, 0x01,
	0x22, 0x71, 0x0a, 0x0f, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x4d, 0x69, 0x67, 0x72, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x4d,
	0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x41,
	0x70, 0x70, 0x49, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x41, 0x70, 0x70,
	0x49, 0x64, 0x73, 0x22, 0x33, 0x0a, 0x0f, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x69, 0x67, 0x72,
	0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x6f,
	0x62, 0x6f, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x4c, 0x61, 0x73,
	0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x2a, 0x1d, 0x0a, 0x07, 0x45, 0x6e, 0x76, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45, 0x53, 0x54, 0x10, 0x00, 0x12, 0x08, 0x0a,
	0x04, 0x50, 0x52, 0x4f, 0x44, 0x10, 0x01, 0x2a, 0x39, 0x0a, 0x15, 0x43, 0x61, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d,
	0x12, 0x11, 0x0a, 0x0d, 0x4e, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x49, 0x4e,
	0x47, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x2a, 0x32, 0x0a, 0x16, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x09, 0x0a, 0x05,
	0x43, 0x4f, 0x56, 0x45, 0x52, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e, 0x43, 0x52, 0x45,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x2a, 0x47, 0x0a, 0x0f, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x03, 0x2a,
	0x44, 0x0a, 0x0d, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d,
	0x12, 0x1a, 0x0a, 0x16, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13,
	0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4b, 0x42, 0x5f, 0x41, 0x47,
	0x45, 0x4e, 0x54, 0x10, 0x01, 0x2a, 0x79, 0x0a, 0x13, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x19,
	0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x4b,
	0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x51, 0x41, 0x10,
	0x01, 0x12, 0x1d, 0x0a, 0x19, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x46,
	0x49, 0x4c, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x41, 0x47, 0x10, 0x02,
	0x32, 0xbf, 0x0b, 0x0a, 0x0b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x56, 0x0a, 0x04, 0x45, 0x63, 0x68, 0x6f, 0x12, 0x25, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x63, 0x68, 0x6f, 0x52, 0x65, 0x71, 0x1a,
	0x25, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45,
	0x63, 0x68, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x83, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x6b,
	0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x83, 0x01, 0x0a, 0x13,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x12, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x65, 0x0a, 0x09, 0x43, 0x6f, 0x70, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x2a,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6f,
	0x70, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2a, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x6e, 0x0a, 0x0c, 0x43, 0x6f, 0x70, 0x79,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x12, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x41, 0x70, 0x70, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x0b, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x8c, 0x01, 0x0a, 0x16, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x37, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x86, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70,
	0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x11, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x96, 0x01, 0x0a, 0x1a, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52,
	0x73, 0x70, 0x32, 0xe0, 0x09, 0x0a, 0x0e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x41, 0x70, 0x69, 0x12, 0x83, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x6e, 0x0a, 0x0c, 0x43,
	0x6f, 0x70, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x12, 0x2d, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x14,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x35, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x41, 0x70, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x12, 0xc2, 0x01, 0x0a, 0x28, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x64, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x6f, 0x6f, 0x6c, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x49, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x48, 0x61,
	0x73, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x54, 0x6f, 0x6f, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x49, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x73, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x6f, 0x6f, 0x6c,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x84, 0x01, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x55, 0x6e, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x64,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x6e,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x8d, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e,
	0x63, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x39, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x7b, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x53, 0x79, 0x6e,
	0x63, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x87, 0x01,
	0x0a, 0x15, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43,
	0x6c, 0x65, 0x61, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x6c, 0x0a, 0x0c, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x69, 0x67, 0x72,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d, 0x69, 0x67, 0x72, 0x61,
	0x74, 0x65, 0x52, 0x73, 0x70, 0x42, 0x49, 0x5a, 0x47, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_config_proto_rawDescOnce sync.Once
	file_agent_config_proto_rawDescData = file_agent_config_proto_rawDesc
)

func file_agent_config_proto_rawDescGZIP() []byte {
	file_agent_config_proto_rawDescOnce.Do(func() {
		file_agent_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_config_proto_rawDescData)
	})
	return file_agent_config_proto_rawDescData
}

var file_agent_config_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_agent_config_proto_msgTypes = make([]protoimpl.MessageInfo, 60)
var file_agent_config_proto_goTypes = []interface{}{
	(EnvType)(0),                // 0: trpc.KEP.agent_config_server.EnvType
	(CallingMethodTypeEnum)(0),  // 1: trpc.KEP.agent_config_server.CallingMethodTypeEnum
	(AnalysisMethodTypeEnum)(0), // 2: trpc.KEP.agent_config_server.AnalysisMethodTypeEnum
	(InputSourceEnum)(0),        // 3: trpc.KEP.agent_config_server.InputSourceEnum
	(AgentTypeEnum)(0),          // 4: trpc.KEP.agent_config_server.AgentTypeEnum
	(KnowledgeFilterEnum)(0),    // 5: trpc.KEP.agent_config_server.KnowledgeFilterEnum
	(DescribeAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum)(0),    // 6: trpc.KEP.agent_config_server.DescribeAgentListRsp.AgentPluginInfo.PluginInfoTypeEnum
	(DescribeAppAgentListRsp_AgentPluginInfo_PluginInfoTypeEnum)(0), // 7: trpc.KEP.agent_config_server.DescribeAppAgentListRsp.AgentPluginInfo.PluginInfoTypeEnum
	(AgentPluginInfo_PluginInfoTypeEnum)(0),                         // 8: trpc.KEP.agent_config_server.AgentPluginInfo.PluginInfoTypeEnum
	(KnowledgeFilterTag_OperatorEnum)(0),                            // 9: trpc.KEP.agent_config_server.KnowledgeFilterTag.OperatorEnum
	(*EchoReq)(nil),                                                 // 10: trpc.KEP.agent_config_server.EchoReq
	(*EchoRsp)(nil),                                                 // 11: trpc.KEP.agent_config_server.EchoRsp
	(*AgentModelInfo)(nil),                                          // 12: trpc.KEP.agent_config_server.AgentModelInfo
	(*Agent)(nil),                                                   // 13: trpc.KEP.agent_config_server.Agent
	(*AgentInstructionsForKbAgent)(nil),                             // 14: trpc.KEP.agent_config_server.AgentInstructionsForKbAgent
	(*AgentKnowledgeSchema)(nil),                                    // 15: trpc.KEP.agent_config_server.AgentKnowledgeSchema
	(*DescribeAgentConfigReq)(nil),                                  // 16: trpc.KEP.agent_config_server.DescribeAgentConfigReq
	(*DescribeAgentConfigRsp)(nil),                                  // 17: trpc.KEP.agent_config_server.DescribeAgentConfigRsp
	(*CreateAgentReq)(nil),                                          // 18: trpc.KEP.agent_config_server.CreateAgentReq
	(*CreateAgentRsp)(nil),                                          // 19: trpc.KEP.agent_config_server.CreateAgentRsp
	(*CreateStartingAgentReq)(nil),                                  // 20: trpc.KEP.agent_config_server.CreateStartingAgentReq
	(*CreateStartingAgentRsp)(nil),                                  // 21: trpc.KEP.agent_config_server.CreateStartingAgentRsp
	(*CopyAgentReq)(nil),                                            // 22: trpc.KEP.agent_config_server.CopyAgentReq
	(*CopyAgentRsp)(nil),                                            // 23: trpc.KEP.agent_config_server.CopyAgentRsp
	(*CopyAgentAppReq)(nil),                                         // 24: trpc.KEP.agent_config_server.CopyAgentAppReq
	(*CopyAgentAppRsp)(nil),                                         // 25: trpc.KEP.agent_config_server.CopyAgentAppRsp
	(*DeleteAgentReq)(nil),                                          // 26: trpc.KEP.agent_config_server.DeleteAgentReq
	(*DeleteAgentRsp)(nil),                                          // 27: trpc.KEP.agent_config_server.DeleteAgentRsp
	(*ModifyAgentReq)(nil),                                          // 28: trpc.KEP.agent_config_server.ModifyAgentReq
	(*ModifyAgentRsp)(nil),                                          // 29: trpc.KEP.agent_config_server.ModifyAgentRsp
	(*ModifyAgentHandoffListReq)(nil),                               // 30: trpc.KEP.agent_config_server.ModifyAgentHandoffListReq
	(*ModifyAgentHandoffListRsp)(nil),                               // 31: trpc.KEP.agent_config_server.ModifyAgentHandoffListRsp
	(*DescribeAgentListReq)(nil),                                    // 32: trpc.KEP.agent_config_server.DescribeAgentListReq
	(*DescribeAgentListRsp)(nil),                                    // 33: trpc.KEP.agent_config_server.DescribeAgentListRsp
	(*DescribeAppAgentListReq)(nil),                                 // 34: trpc.KEP.agent_config_server.DescribeAppAgentListReq
	(*DescribeAppAgentListRsp)(nil),                                 // 35: trpc.KEP.agent_config_server.DescribeAppAgentListRsp
	(*DescribeAgentHasReferencedPluginToolListReq)(nil),             // 36: trpc.KEP.agent_config_server.DescribeAgentHasReferencedPluginToolListReq
	(*DescribeAgentHasReferencedPluginToolListRsp)(nil),             // 37: trpc.KEP.agent_config_server.DescribeAgentHasReferencedPluginToolListRsp
	(*AgentToolRef)(nil),                                            // 38: trpc.KEP.agent_config_server.AgentToolRef
	(*ListAppAgentReleasePreviewReq)(nil),                           // 39: trpc.KEP.agent_config_server.ListAppAgentReleasePreviewReq
	(*ListAppAgentReleasePreviewRsp)(nil),                           // 40: trpc.KEP.agent_config_server.ListAppAgentReleasePreviewRsp
	(*ClearAgentAppResourceReq)(nil),                                // 41: trpc.KEP.agent_config_server.ClearAgentAppResourceReq
	(*ClearAgentAppResourceRsp)(nil),                                // 42: trpc.KEP.agent_config_server.ClearAgentAppResourceRsp
	(*AgentPluginInfo)(nil),                                         // 43: trpc.KEP.agent_config_server.AgentPluginInfo
	(*AgentToolInfo)(nil),                                           // 44: trpc.KEP.agent_config_server.AgentToolInfo
	(*AgentMCPServerInfo)(nil),                                      // 45: trpc.KEP.agent_config_server.AgentMCPServerInfo
	(*AgentPluginHeader)(nil),                                       // 46: trpc.KEP.agent_config_server.AgentPluginHeader
	(*AgentToolReqParam)(nil),                                       // 47: trpc.KEP.agent_config_server.AgentToolReqParam
	(*AgentToolRspParam)(nil),                                       // 48: trpc.KEP.agent_config_server.AgentToolRspParam
	(*Input)(nil),                                                   // 49: trpc.KEP.agent_config_server.Input
	(*UserInputValue)(nil),                                          // 50: trpc.KEP.agent_config_server.UserInputValue
	(*SystemVariable)(nil),                                          // 51: trpc.KEP.agent_config_server.SystemVariable
	(*AgentKnowledgeQAPlugin)(nil),                                  // 52: trpc.KEP.agent_config_server.AgentKnowledgeQAPlugin
	(*KnowledgeFilter)(nil),                                         // 53: trpc.KEP.agent_config_server.KnowledgeFilter
	(*KnowledgeFilterDocAndAnswer)(nil),                             // 54: trpc.KEP.agent_config_server.KnowledgeFilterDocAndAnswer
	(*KnowledgeFilterTag)(nil),                                      // 55: trpc.KEP.agent_config_server.KnowledgeFilterTag
	(*AgentMigrateReq)(nil),                                         // 56: trpc.KEP.agent_config_server.AgentMigrateReq
	(*AgentMigrateRsp)(nil),                                         // 57: trpc.KEP.agent_config_server.AgentMigrateRsp
	nil,                                                             // 58: trpc.KEP.agent_config_server.AgentModelInfo.PromptsEntry
	(*DescribeAgentConfigRsp_AgentModelInfo)(nil),                   // 59: trpc.KEP.agent_config_server.DescribeAgentConfigRsp.AgentModelInfo
	(*ModifyAgentHandoffListReq_Agent)(nil),                         // 60: trpc.KEP.agent_config_server.ModifyAgentHandoffListReq.Agent
	(*DescribeAgentListRsp_AppAgent)(nil),                           // 61: trpc.KEP.agent_config_server.DescribeAgentListRsp.AppAgent
	(*DescribeAgentListRsp_Agent)(nil),                              // 62: trpc.KEP.agent_config_server.DescribeAgentListRsp.Agent
	(*DescribeAgentListRsp_AgentPluginInfo)(nil),                    // 63: trpc.KEP.agent_config_server.DescribeAgentListRsp.AgentPluginInfo
	(*DescribeAgentListRsp_AgentModelInfo)(nil),                     // 64: trpc.KEP.agent_config_server.DescribeAgentListRsp.AgentModelInfo
	(*DescribeAppAgentListRsp_Agent)(nil),                           // 65: trpc.KEP.agent_config_server.DescribeAppAgentListRsp.Agent
	(*DescribeAppAgentListRsp_AgentPluginInfo)(nil),                 // 66: trpc.KEP.agent_config_server.DescribeAppAgentListRsp.AgentPluginInfo
	(*DescribeAppAgentListRsp_AgentModelInfo)(nil),                  // 67: trpc.KEP.agent_config_server.DescribeAppAgentListRsp.AgentModelInfo
	(*ListAppAgentReleasePreviewRsp_AppAgent)(nil),                  // 68: trpc.KEP.agent_config_server.ListAppAgentReleasePreviewRsp.AppAgent
	(*KnowledgeFilterTag_KnowledgeAttrLabel)(nil),                   // 69: trpc.KEP.agent_config_server.KnowledgeFilterTag.KnowledgeAttrLabel
	(plugin_config_server.PluginTypeEnum)(0),                        // 70: trpc.KEP.plugin_config_server.PluginTypeEnum
	(plugin_config_server.CreateTypeEnum)(0),                        // 71: trpc.KEP.plugin_config_server.CreateTypeEnum
	(plugin_config_server.TypeEnum)(0),                              // 72: trpc.KEP.plugin_config_server.TypeEnum
	(*KEP.GetUnreleasedCountReq)(nil),                               // 73: trpc.KEP.bot_task_config_server.GetUnreleasedCountReq
	(*KEP.SendDataSyncTaskEventReq)(nil),                            // 74: trpc.KEP.bot_task_config_server.SendDataSyncTaskEventReq
	(*KEP.GetDataSyncTaskReq)(nil),                                  // 75: trpc.KEP.bot_task_config_server.GetDataSyncTaskReq
	(*KEP.GetUnreleasedCountRsp)(nil),                               // 76: trpc.KEP.bot_task_config_server.GetUnreleasedCountRsp
	(*KEP.SendDataSyncTaskEventRsp)(nil),                            // 77: trpc.KEP.bot_task_config_server.SendDataSyncTaskEventRsp
	(*KEP.GetDataSyncTaskRsp)(nil),                                  // 78: trpc.KEP.bot_task_config_server.GetDataSyncTaskRsp
}
var file_agent_config_proto_depIdxs = []int32{
	58, // 0: trpc.KEP.agent_config_server.AgentModelInfo.prompts:type_name -> trpc.KEP.agent_config_server.AgentModelInfo.PromptsEntry
	12, // 1: trpc.KEP.agent_config_server.Agent.Model:type_name -> trpc.KEP.agent_config_server.AgentModelInfo
	44, // 2: trpc.KEP.agent_config_server.Agent.Tools:type_name -> trpc.KEP.agent_config_server.AgentToolInfo
	43, // 3: trpc.KEP.agent_config_server.Agent.Plugins:type_name -> trpc.KEP.agent_config_server.AgentPluginInfo
	4,  // 4: trpc.KEP.agent_config_server.Agent.AgentType:type_name -> trpc.KEP.agent_config_server.AgentTypeEnum
	15, // 5: trpc.KEP.agent_config_server.AgentInstructionsForKbAgent.KnowledgeSchemas:type_name -> trpc.KEP.agent_config_server.AgentKnowledgeSchema
	59, // 6: trpc.KEP.agent_config_server.DescribeAgentConfigRsp.ModelInfos:type_name -> trpc.KEP.agent_config_server.DescribeAgentConfigRsp.AgentModelInfo
	13, // 7: trpc.KEP.agent_config_server.CreateAgentReq.Agent:type_name -> trpc.KEP.agent_config_server.Agent
	13, // 8: trpc.KEP.agent_config_server.CreateStartingAgentReq.Agent:type_name -> trpc.KEP.agent_config_server.Agent
	13, // 9: trpc.KEP.agent_config_server.ModifyAgentReq.Agent:type_name -> trpc.KEP.agent_config_server.Agent
	60, // 10: trpc.KEP.agent_config_server.ModifyAgentHandoffListReq.Agents:type_name -> trpc.KEP.agent_config_server.ModifyAgentHandoffListReq.Agent
	61, // 11: trpc.KEP.agent_config_server.DescribeAgentListRsp.Agents:type_name -> trpc.KEP.agent_config_server.DescribeAgentListRsp.AppAgent
	0,  // 12: trpc.KEP.agent_config_server.DescribeAppAgentListReq.EnvType:type_name -> trpc.KEP.agent_config_server.EnvType
	65, // 13: trpc.KEP.agent_config_server.DescribeAppAgentListRsp.Agents:type_name -> trpc.KEP.agent_config_server.DescribeAppAgentListRsp.Agent
	38, // 14: trpc.KEP.agent_config_server.DescribeAgentHasReferencedPluginToolListRsp.ToolRefs:type_name -> trpc.KEP.agent_config_server.AgentToolRef
	68, // 15: trpc.KEP.agent_config_server.ListAppAgentReleasePreviewRsp.List:type_name -> trpc.KEP.agent_config_server.ListAppAgentReleasePreviewRsp.AppAgent
	46, // 16: trpc.KEP.agent_config_server.AgentPluginInfo.Headers:type_name -> trpc.KEP.agent_config_server.AgentPluginHeader
	12, // 17: trpc.KEP.agent_config_server.AgentPluginInfo.Model:type_name -> trpc.KEP.agent_config_server.AgentModelInfo
	8,  // 18: trpc.KEP.agent_config_server.AgentPluginInfo.PluginInfoType:type_name -> trpc.KEP.agent_config_server.AgentPluginInfo.PluginInfoTypeEnum
	52, // 19: trpc.KEP.agent_config_server.AgentPluginInfo.KnowledgeQa:type_name -> trpc.KEP.agent_config_server.AgentKnowledgeQAPlugin
	70, // 20: trpc.KEP.agent_config_server.AgentToolInfo.PluginType:type_name -> trpc.KEP.plugin_config_server.PluginTypeEnum
	47, // 21: trpc.KEP.agent_config_server.AgentToolInfo.Inputs:type_name -> trpc.KEP.agent_config_server.AgentToolReqParam
	48, // 22: trpc.KEP.agent_config_server.AgentToolInfo.Outputs:type_name -> trpc.KEP.agent_config_server.AgentToolRspParam
	71, // 23: trpc.KEP.agent_config_server.AgentToolInfo.CreateType:type_name -> trpc.KEP.plugin_config_server.CreateTypeEnum
	45, // 24: trpc.KEP.agent_config_server.AgentToolInfo.McpServer:type_name -> trpc.KEP.agent_config_server.AgentMCPServerInfo
	46, // 25: trpc.KEP.agent_config_server.AgentToolInfo.Headers:type_name -> trpc.KEP.agent_config_server.AgentPluginHeader
	46, // 26: trpc.KEP.agent_config_server.AgentMCPServerInfo.Headers:type_name -> trpc.KEP.agent_config_server.AgentPluginHeader
	49, // 27: trpc.KEP.agent_config_server.AgentPluginHeader.Input:type_name -> trpc.KEP.agent_config_server.Input
	72, // 28: trpc.KEP.agent_config_server.AgentToolReqParam.Type:type_name -> trpc.KEP.plugin_config_server.TypeEnum
	47, // 29: trpc.KEP.agent_config_server.AgentToolReqParam.SubParams:type_name -> trpc.KEP.agent_config_server.AgentToolReqParam
	47, // 30: trpc.KEP.agent_config_server.AgentToolReqParam.OneOf:type_name -> trpc.KEP.agent_config_server.AgentToolReqParam
	47, // 31: trpc.KEP.agent_config_server.AgentToolReqParam.AnyOf:type_name -> trpc.KEP.agent_config_server.AgentToolReqParam
	49, // 32: trpc.KEP.agent_config_server.AgentToolReqParam.Input:type_name -> trpc.KEP.agent_config_server.Input
	72, // 33: trpc.KEP.agent_config_server.AgentToolRspParam.Type:type_name -> trpc.KEP.plugin_config_server.TypeEnum
	48, // 34: trpc.KEP.agent_config_server.AgentToolRspParam.SubParams:type_name -> trpc.KEP.agent_config_server.AgentToolRspParam
	3,  // 35: trpc.KEP.agent_config_server.Input.InputType:type_name -> trpc.KEP.agent_config_server.InputSourceEnum
	50, // 36: trpc.KEP.agent_config_server.Input.UserInputValue:type_name -> trpc.KEP.agent_config_server.UserInputValue
	53, // 37: trpc.KEP.agent_config_server.AgentKnowledgeQAPlugin.Filter:type_name -> trpc.KEP.agent_config_server.KnowledgeFilter
	5,  // 38: trpc.KEP.agent_config_server.KnowledgeFilter.FilterType:type_name -> trpc.KEP.agent_config_server.KnowledgeFilterEnum
	54, // 39: trpc.KEP.agent_config_server.KnowledgeFilter.DocAndAnswer:type_name -> trpc.KEP.agent_config_server.KnowledgeFilterDocAndAnswer
	55, // 40: trpc.KEP.agent_config_server.KnowledgeFilter.Tag:type_name -> trpc.KEP.agent_config_server.KnowledgeFilterTag
	9,  // 41: trpc.KEP.agent_config_server.KnowledgeFilterTag.Operator:type_name -> trpc.KEP.agent_config_server.KnowledgeFilterTag.OperatorEnum
	69, // 42: trpc.KEP.agent_config_server.KnowledgeFilterTag.Labels:type_name -> trpc.KEP.agent_config_server.KnowledgeFilterTag.KnowledgeAttrLabel
	62, // 43: trpc.KEP.agent_config_server.DescribeAgentListRsp.AppAgent.Agent:type_name -> trpc.KEP.agent_config_server.DescribeAgentListRsp.Agent
	64, // 44: trpc.KEP.agent_config_server.DescribeAgentListRsp.Agent.Model:type_name -> trpc.KEP.agent_config_server.DescribeAgentListRsp.AgentModelInfo
	44, // 45: trpc.KEP.agent_config_server.DescribeAgentListRsp.Agent.Tools:type_name -> trpc.KEP.agent_config_server.AgentToolInfo
	63, // 46: trpc.KEP.agent_config_server.DescribeAgentListRsp.Agent.Plugins:type_name -> trpc.KEP.agent_config_server.DescribeAgentListRsp.AgentPluginInfo
	4,  // 47: trpc.KEP.agent_config_server.DescribeAgentListRsp.Agent.AgentType:type_name -> trpc.KEP.agent_config_server.AgentTypeEnum
	46, // 48: trpc.KEP.agent_config_server.DescribeAgentListRsp.AgentPluginInfo.Headers:type_name -> trpc.KEP.agent_config_server.AgentPluginHeader
	64, // 49: trpc.KEP.agent_config_server.DescribeAgentListRsp.AgentPluginInfo.Model:type_name -> trpc.KEP.agent_config_server.DescribeAgentListRsp.AgentModelInfo
	6,  // 50: trpc.KEP.agent_config_server.DescribeAgentListRsp.AgentPluginInfo.PluginInfoType:type_name -> trpc.KEP.agent_config_server.DescribeAgentListRsp.AgentPluginInfo.PluginInfoTypeEnum
	52, // 51: trpc.KEP.agent_config_server.DescribeAgentListRsp.AgentPluginInfo.KnowledgeQa:type_name -> trpc.KEP.agent_config_server.AgentKnowledgeQAPlugin
	67, // 52: trpc.KEP.agent_config_server.DescribeAppAgentListRsp.Agent.Model:type_name -> trpc.KEP.agent_config_server.DescribeAppAgentListRsp.AgentModelInfo
	44, // 53: trpc.KEP.agent_config_server.DescribeAppAgentListRsp.Agent.Tools:type_name -> trpc.KEP.agent_config_server.AgentToolInfo
	66, // 54: trpc.KEP.agent_config_server.DescribeAppAgentListRsp.Agent.Plugins:type_name -> trpc.KEP.agent_config_server.DescribeAppAgentListRsp.AgentPluginInfo
	4,  // 55: trpc.KEP.agent_config_server.DescribeAppAgentListRsp.Agent.AgentType:type_name -> trpc.KEP.agent_config_server.AgentTypeEnum
	46, // 56: trpc.KEP.agent_config_server.DescribeAppAgentListRsp.AgentPluginInfo.Headers:type_name -> trpc.KEP.agent_config_server.AgentPluginHeader
	67, // 57: trpc.KEP.agent_config_server.DescribeAppAgentListRsp.AgentPluginInfo.Model:type_name -> trpc.KEP.agent_config_server.DescribeAppAgentListRsp.AgentModelInfo
	7,  // 58: trpc.KEP.agent_config_server.DescribeAppAgentListRsp.AgentPluginInfo.PluginInfoType:type_name -> trpc.KEP.agent_config_server.DescribeAppAgentListRsp.AgentPluginInfo.PluginInfoTypeEnum
	52, // 59: trpc.KEP.agent_config_server.DescribeAppAgentListRsp.AgentPluginInfo.KnowledgeQa:type_name -> trpc.KEP.agent_config_server.AgentKnowledgeQAPlugin
	49, // 60: trpc.KEP.agent_config_server.KnowledgeFilterTag.KnowledgeAttrLabel.Inputs:type_name -> trpc.KEP.agent_config_server.Input
	10, // 61: trpc.KEP.agent_config_server.AgentConfig.Echo:input_type -> trpc.KEP.agent_config_server.EchoReq
	16, // 62: trpc.KEP.agent_config_server.AgentConfig.DescribeAgentConfig:input_type -> trpc.KEP.agent_config_server.DescribeAgentConfigReq
	18, // 63: trpc.KEP.agent_config_server.AgentConfig.CreateAgent:input_type -> trpc.KEP.agent_config_server.CreateAgentReq
	20, // 64: trpc.KEP.agent_config_server.AgentConfig.CreateStartingAgent:input_type -> trpc.KEP.agent_config_server.CreateStartingAgentReq
	22, // 65: trpc.KEP.agent_config_server.AgentConfig.CopyAgent:input_type -> trpc.KEP.agent_config_server.CopyAgentReq
	24, // 66: trpc.KEP.agent_config_server.AgentConfig.CopyAgentApp:input_type -> trpc.KEP.agent_config_server.CopyAgentAppReq
	26, // 67: trpc.KEP.agent_config_server.AgentConfig.DeleteAgent:input_type -> trpc.KEP.agent_config_server.DeleteAgentReq
	28, // 68: trpc.KEP.agent_config_server.AgentConfig.ModifyAgent:input_type -> trpc.KEP.agent_config_server.ModifyAgentReq
	30, // 69: trpc.KEP.agent_config_server.AgentConfig.ModifyAgentHandoffList:input_type -> trpc.KEP.agent_config_server.ModifyAgentHandoffListReq
	34, // 70: trpc.KEP.agent_config_server.AgentConfig.DescribeAppAgentList:input_type -> trpc.KEP.agent_config_server.DescribeAppAgentListReq
	32, // 71: trpc.KEP.agent_config_server.AgentConfig.DescribeAgentList:input_type -> trpc.KEP.agent_config_server.DescribeAgentListReq
	39, // 72: trpc.KEP.agent_config_server.AgentConfig.ListAppAgentReleasePreview:input_type -> trpc.KEP.agent_config_server.ListAppAgentReleasePreviewReq
	20, // 73: trpc.KEP.agent_config_server.AgentConfigApi.CreateStartingAgent:input_type -> trpc.KEP.agent_config_server.CreateStartingAgentReq
	24, // 74: trpc.KEP.agent_config_server.AgentConfigApi.CopyAgentApp:input_type -> trpc.KEP.agent_config_server.CopyAgentAppReq
	34, // 75: trpc.KEP.agent_config_server.AgentConfigApi.DescribeAppAgentList:input_type -> trpc.KEP.agent_config_server.DescribeAppAgentListReq
	36, // 76: trpc.KEP.agent_config_server.AgentConfigApi.DescribeAgentHasReferencedPluginToolList:input_type -> trpc.KEP.agent_config_server.DescribeAgentHasReferencedPluginToolListReq
	73, // 77: trpc.KEP.agent_config_server.AgentConfigApi.GetUnreleasedCount:input_type -> trpc.KEP.bot_task_config_server.GetUnreleasedCountReq
	74, // 78: trpc.KEP.agent_config_server.AgentConfigApi.SendDataSyncTaskEvent:input_type -> trpc.KEP.bot_task_config_server.SendDataSyncTaskEventReq
	75, // 79: trpc.KEP.agent_config_server.AgentConfigApi.GetDataSyncTask:input_type -> trpc.KEP.bot_task_config_server.GetDataSyncTaskReq
	41, // 80: trpc.KEP.agent_config_server.AgentConfigApi.ClearAgentAppResource:input_type -> trpc.KEP.agent_config_server.ClearAgentAppResourceReq
	56, // 81: trpc.KEP.agent_config_server.AgentConfigApi.AgentMigrate:input_type -> trpc.KEP.agent_config_server.AgentMigrateReq
	11, // 82: trpc.KEP.agent_config_server.AgentConfig.Echo:output_type -> trpc.KEP.agent_config_server.EchoRsp
	17, // 83: trpc.KEP.agent_config_server.AgentConfig.DescribeAgentConfig:output_type -> trpc.KEP.agent_config_server.DescribeAgentConfigRsp
	19, // 84: trpc.KEP.agent_config_server.AgentConfig.CreateAgent:output_type -> trpc.KEP.agent_config_server.CreateAgentRsp
	21, // 85: trpc.KEP.agent_config_server.AgentConfig.CreateStartingAgent:output_type -> trpc.KEP.agent_config_server.CreateStartingAgentRsp
	23, // 86: trpc.KEP.agent_config_server.AgentConfig.CopyAgent:output_type -> trpc.KEP.agent_config_server.CopyAgentRsp
	25, // 87: trpc.KEP.agent_config_server.AgentConfig.CopyAgentApp:output_type -> trpc.KEP.agent_config_server.CopyAgentAppRsp
	27, // 88: trpc.KEP.agent_config_server.AgentConfig.DeleteAgent:output_type -> trpc.KEP.agent_config_server.DeleteAgentRsp
	29, // 89: trpc.KEP.agent_config_server.AgentConfig.ModifyAgent:output_type -> trpc.KEP.agent_config_server.ModifyAgentRsp
	31, // 90: trpc.KEP.agent_config_server.AgentConfig.ModifyAgentHandoffList:output_type -> trpc.KEP.agent_config_server.ModifyAgentHandoffListRsp
	35, // 91: trpc.KEP.agent_config_server.AgentConfig.DescribeAppAgentList:output_type -> trpc.KEP.agent_config_server.DescribeAppAgentListRsp
	33, // 92: trpc.KEP.agent_config_server.AgentConfig.DescribeAgentList:output_type -> trpc.KEP.agent_config_server.DescribeAgentListRsp
	40, // 93: trpc.KEP.agent_config_server.AgentConfig.ListAppAgentReleasePreview:output_type -> trpc.KEP.agent_config_server.ListAppAgentReleasePreviewRsp
	21, // 94: trpc.KEP.agent_config_server.AgentConfigApi.CreateStartingAgent:output_type -> trpc.KEP.agent_config_server.CreateStartingAgentRsp
	25, // 95: trpc.KEP.agent_config_server.AgentConfigApi.CopyAgentApp:output_type -> trpc.KEP.agent_config_server.CopyAgentAppRsp
	35, // 96: trpc.KEP.agent_config_server.AgentConfigApi.DescribeAppAgentList:output_type -> trpc.KEP.agent_config_server.DescribeAppAgentListRsp
	37, // 97: trpc.KEP.agent_config_server.AgentConfigApi.DescribeAgentHasReferencedPluginToolList:output_type -> trpc.KEP.agent_config_server.DescribeAgentHasReferencedPluginToolListRsp
	76, // 98: trpc.KEP.agent_config_server.AgentConfigApi.GetUnreleasedCount:output_type -> trpc.KEP.bot_task_config_server.GetUnreleasedCountRsp
	77, // 99: trpc.KEP.agent_config_server.AgentConfigApi.SendDataSyncTaskEvent:output_type -> trpc.KEP.bot_task_config_server.SendDataSyncTaskEventRsp
	78, // 100: trpc.KEP.agent_config_server.AgentConfigApi.GetDataSyncTask:output_type -> trpc.KEP.bot_task_config_server.GetDataSyncTaskRsp
	42, // 101: trpc.KEP.agent_config_server.AgentConfigApi.ClearAgentAppResource:output_type -> trpc.KEP.agent_config_server.ClearAgentAppResourceRsp
	57, // 102: trpc.KEP.agent_config_server.AgentConfigApi.AgentMigrate:output_type -> trpc.KEP.agent_config_server.AgentMigrateRsp
	82, // [82:103] is the sub-list for method output_type
	61, // [61:82] is the sub-list for method input_type
	61, // [61:61] is the sub-list for extension type_name
	61, // [61:61] is the sub-list for extension extendee
	0,  // [0:61] is the sub-list for field type_name
}

func init() { file_agent_config_proto_init() }
func file_agent_config_proto_init() {
	if File_agent_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_agent_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EchoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EchoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentModelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Agent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentInstructionsForKbAgent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentKnowledgeSchema); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAgentConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAgentConfigRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAgentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAgentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStartingAgentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStartingAgentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyAgentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyAgentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyAgentAppReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyAgentAppRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAgentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAgentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyAgentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyAgentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyAgentHandoffListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyAgentHandoffListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAgentListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAgentListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAppAgentListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAppAgentListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAgentHasReferencedPluginToolListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAgentHasReferencedPluginToolListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentToolRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppAgentReleasePreviewReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppAgentReleasePreviewRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearAgentAppResourceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearAgentAppResourceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentPluginInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentToolInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentMCPServerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentPluginHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentToolReqParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentToolRspParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Input); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInputValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemVariable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentKnowledgeQAPlugin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeFilterDocAndAnswer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeFilterTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentMigrateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentMigrateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAgentConfigRsp_AgentModelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyAgentHandoffListReq_Agent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAgentListRsp_AppAgent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAgentListRsp_Agent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAgentListRsp_AgentPluginInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAgentListRsp_AgentModelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAppAgentListRsp_Agent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAppAgentListRsp_AgentPluginInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeAppAgentListRsp_AgentModelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppAgentReleasePreviewRsp_AppAgent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_agent_config_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeFilterTag_KnowledgeAttrLabel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_config_proto_rawDesc,
			NumEnums:      10,
			NumMessages:   60,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_agent_config_proto_goTypes,
		DependencyIndexes: file_agent_config_proto_depIdxs,
		EnumInfos:         file_agent_config_proto_enumTypes,
		MessageInfos:      file_agent_config_proto_msgTypes,
	}.Build()
	File_agent_config_proto = out.File
	file_agent_config_proto_rawDesc = nil
	file_agent_config_proto_goTypes = nil
	file_agent_config_proto_depIdxs = nil
}
