// Code generated by protoc-gen-secv. DO NOT EDIT.
// source: agent-config.proto

package agent_config_server

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	plugin_config_server "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
	_ = unicode.IsUpper
	_ = json.Valid([]byte(""))

	_ = plugin_config_server.PluginTypeEnum(0)
)

// Validate checks the field values on EchoReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EchoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EchoReq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EchoReqMultiError, or nil if none found.
func (m *EchoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *EchoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return EchoReqMultiError(errors)
	}

	return nil
}

// EchoReqMultiError is an error wrapping multiple validation errors returned
// by EchoReq.ValidateAll() if the designated constraints aren't met.
type EchoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EchoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EchoReqMultiError) AllErrors() []error { return m }

// EchoReqValidationError is the validation error returned by EchoReq.Validate
// if the designated constraints aren't met.
type EchoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EchoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EchoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EchoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EchoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EchoReqValidationError) ErrorName() string { return "EchoReqValidationError" }

// Error satisfies the builtin error interface
func (e EchoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEchoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EchoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EchoReqValidationError{}

// Validate checks the field values on EchoRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EchoRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EchoRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EchoRspMultiError, or nil if none found.
func (m *EchoRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *EchoRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return EchoRspMultiError(errors)
	}

	return nil
}

// EchoRspMultiError is an error wrapping multiple validation errors returned
// by EchoRsp.ValidateAll() if the designated constraints aren't met.
type EchoRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EchoRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EchoRspMultiError) AllErrors() []error { return m }

// EchoRspValidationError is the validation error returned by EchoRsp.Validate
// if the designated constraints aren't met.
type EchoRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EchoRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EchoRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EchoRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EchoRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EchoRspValidationError) ErrorName() string { return "EchoRspValidationError" }

// Error satisfies the builtin error interface
func (e EchoRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEchoRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EchoRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EchoRspValidationError{}

// Validate checks the field values on AgentModelInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AgentModelInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentModelInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AgentModelInfoMultiError,
// or nil if none found.
func (m *AgentModelInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentModelInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Prompt

	// no validation rules for PromptWordsLimit

	// no validation rules for HistoryLimit

	// no validation rules for HistoryWordsLimit

	// no validation rules for ModelName

	// no validation rules for ServiceName

	// no validation rules for IsEnabled

	// no validation rules for Path

	// no validation rules for Target

	// no validation rules for Type

	// no validation rules for ChatWordsLimit

	// no validation rules for TopK

	// no validation rules for Prompts

	// no validation rules for Temperature

	// no validation rules for TopP

	// no validation rules for PromptVersion

	// no validation rules for ModelContextWordsLimit

	// no validation rules for InstructionsWordsLimit

	// no validation rules for ModelAliasName

	if len(errors) > 0 {
		return AgentModelInfoMultiError(errors)
	}

	return nil
}

// AgentModelInfoMultiError is an error wrapping multiple validation errors
// returned by AgentModelInfo.ValidateAll() if the designated constraints
// aren't met.
type AgentModelInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentModelInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentModelInfoMultiError) AllErrors() []error { return m }

// AgentModelInfoValidationError is the validation error returned by
// AgentModelInfo.Validate if the designated constraints aren't met.
type AgentModelInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentModelInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentModelInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentModelInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentModelInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentModelInfoValidationError) ErrorName() string { return "AgentModelInfoValidationError" }

// Error satisfies the builtin error interface
func (e AgentModelInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentModelInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentModelInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentModelInfoValidationError{}

// Validate checks the field values on Agent with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Agent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Agent with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AgentMultiError, or nil if none found.
func (m *Agent) ValidateAll() error {
	return m.validate(true)
}

func (m *Agent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgentId

	// no validation rules for WorkflowId

	// no validation rules for Name

	// no validation rules for IconUrl

	// no validation rules for Instructions

	// no validation rules for HandoffDescription

	if all {
		switch v := interface{}(m.GetModel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentValidationError{
					field:  "Model",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentValidationError{
					field:  "Model",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetModel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentValidationError{
				field:  "Model",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTools() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentValidationError{
						field:  fmt.Sprintf("Tools[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentValidationError{
						field:  fmt.Sprintf("Tools[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentValidationError{
					field:  fmt.Sprintf("Tools[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetPlugins() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentValidationError{
						field:  fmt.Sprintf("Plugins[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentValidationError{
						field:  fmt.Sprintf("Plugins[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentValidationError{
					field:  fmt.Sprintf("Plugins[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsStartingAgent

	// no validation rules for AgentType

	if len(errors) > 0 {
		return AgentMultiError(errors)
	}

	return nil
}

// AgentMultiError is an error wrapping multiple validation errors returned by
// Agent.ValidateAll() if the designated constraints aren't met.
type AgentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentMultiError) AllErrors() []error { return m }

// AgentValidationError is the validation error returned by Agent.Validate if
// the designated constraints aren't met.
type AgentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentValidationError) ErrorName() string { return "AgentValidationError" }

// Error satisfies the builtin error interface
func (e AgentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentValidationError{}

// Validate checks the field values on AgentInstructionsForKbAgent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AgentInstructionsForKbAgent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentInstructionsForKbAgent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentInstructionsForKbAgentMultiError, or nil if none found.
func (m *AgentInstructionsForKbAgent) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentInstructionsForKbAgent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RetrieveRule

	// no validation rules for ExampleQa

	for idx, item := range m.GetKnowledgeSchemas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentInstructionsForKbAgentValidationError{
						field:  fmt.Sprintf("KnowledgeSchemas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentInstructionsForKbAgentValidationError{
						field:  fmt.Sprintf("KnowledgeSchemas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentInstructionsForKbAgentValidationError{
					field:  fmt.Sprintf("KnowledgeSchemas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AgentInstructionsForKbAgentMultiError(errors)
	}

	return nil
}

// AgentInstructionsForKbAgentMultiError is an error wrapping multiple
// validation errors returned by AgentInstructionsForKbAgent.ValidateAll() if
// the designated constraints aren't met.
type AgentInstructionsForKbAgentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentInstructionsForKbAgentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentInstructionsForKbAgentMultiError) AllErrors() []error { return m }

// AgentInstructionsForKbAgentValidationError is the validation error returned
// by AgentInstructionsForKbAgent.Validate if the designated constraints
// aren't met.
type AgentInstructionsForKbAgentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentInstructionsForKbAgentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentInstructionsForKbAgentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentInstructionsForKbAgentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentInstructionsForKbAgentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentInstructionsForKbAgentValidationError) ErrorName() string {
	return "AgentInstructionsForKbAgentValidationError"
}

// Error satisfies the builtin error interface
func (e AgentInstructionsForKbAgentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentInstructionsForKbAgent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentInstructionsForKbAgentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentInstructionsForKbAgentValidationError{}

// Validate checks the field values on AgentKnowledgeSchema with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AgentKnowledgeSchema) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentKnowledgeSchema with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentKnowledgeSchemaMultiError, or nil if none found.
func (m *AgentKnowledgeSchema) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentKnowledgeSchema) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BusinessId

	// no validation rules for Name

	// no validation rules for Summary

	if len(errors) > 0 {
		return AgentKnowledgeSchemaMultiError(errors)
	}

	return nil
}

// AgentKnowledgeSchemaMultiError is an error wrapping multiple validation
// errors returned by AgentKnowledgeSchema.ValidateAll() if the designated
// constraints aren't met.
type AgentKnowledgeSchemaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentKnowledgeSchemaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentKnowledgeSchemaMultiError) AllErrors() []error { return m }

// AgentKnowledgeSchemaValidationError is the validation error returned by
// AgentKnowledgeSchema.Validate if the designated constraints aren't met.
type AgentKnowledgeSchemaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentKnowledgeSchemaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentKnowledgeSchemaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentKnowledgeSchemaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentKnowledgeSchemaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentKnowledgeSchemaValidationError) ErrorName() string {
	return "AgentKnowledgeSchemaValidationError"
}

// Error satisfies the builtin error interface
func (e AgentKnowledgeSchemaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentKnowledgeSchema.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentKnowledgeSchemaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentKnowledgeSchemaValidationError{}

// Validate checks the field values on DescribeAgentConfigReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeAgentConfigReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeAgentConfigReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeAgentConfigReqMultiError, or nil if none found.
func (m *DescribeAgentConfigReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAgentConfigReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppBizId

	// no validation rules for ModelName

	if len(errors) > 0 {
		return DescribeAgentConfigReqMultiError(errors)
	}

	return nil
}

// DescribeAgentConfigReqMultiError is an error wrapping multiple validation
// errors returned by DescribeAgentConfigReq.ValidateAll() if the designated
// constraints aren't met.
type DescribeAgentConfigReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAgentConfigReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAgentConfigReqMultiError) AllErrors() []error { return m }

// DescribeAgentConfigReqValidationError is the validation error returned by
// DescribeAgentConfigReq.Validate if the designated constraints aren't met.
type DescribeAgentConfigReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAgentConfigReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAgentConfigReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAgentConfigReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAgentConfigReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAgentConfigReqValidationError) ErrorName() string {
	return "DescribeAgentConfigReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAgentConfigReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAgentConfigReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAgentConfigReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAgentConfigReqValidationError{}

// Validate checks the field values on DescribeAgentConfigRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeAgentConfigRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeAgentConfigRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeAgentConfigRspMultiError, or nil if none found.
func (m *DescribeAgentConfigRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAgentConfigRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetModelInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeAgentConfigRspValidationError{
						field:  fmt.Sprintf("ModelInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeAgentConfigRspValidationError{
						field:  fmt.Sprintf("ModelInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeAgentConfigRspValidationError{
					field:  fmt.Sprintf("ModelInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeAgentConfigRspMultiError(errors)
	}

	return nil
}

// DescribeAgentConfigRspMultiError is an error wrapping multiple validation
// errors returned by DescribeAgentConfigRsp.ValidateAll() if the designated
// constraints aren't met.
type DescribeAgentConfigRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAgentConfigRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAgentConfigRspMultiError) AllErrors() []error { return m }

// DescribeAgentConfigRspValidationError is the validation error returned by
// DescribeAgentConfigRsp.Validate if the designated constraints aren't met.
type DescribeAgentConfigRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAgentConfigRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAgentConfigRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAgentConfigRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAgentConfigRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAgentConfigRspValidationError) ErrorName() string {
	return "DescribeAgentConfigRspValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAgentConfigRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAgentConfigRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAgentConfigRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAgentConfigRspValidationError{}

// Validate checks the field values on CreateAgentReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateAgentReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAgentReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreateAgentReqMultiError,
// or nil if none found.
func (m *CreateAgentReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAgentReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppBizId

	if all {
		switch v := interface{}(m.GetAgent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAgentReqValidationError{
					field:  "Agent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAgentReqValidationError{
					field:  "Agent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAgent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAgentReqValidationError{
				field:  "Agent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAgentReqMultiError(errors)
	}

	return nil
}

// CreateAgentReqMultiError is an error wrapping multiple validation errors
// returned by CreateAgentReq.ValidateAll() if the designated constraints
// aren't met.
type CreateAgentReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAgentReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAgentReqMultiError) AllErrors() []error { return m }

// CreateAgentReqValidationError is the validation error returned by
// CreateAgentReq.Validate if the designated constraints aren't met.
type CreateAgentReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAgentReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAgentReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAgentReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAgentReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAgentReqValidationError) ErrorName() string { return "CreateAgentReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateAgentReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAgentReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAgentReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAgentReqValidationError{}

// Validate checks the field values on CreateAgentRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateAgentRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAgentRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreateAgentRspMultiError,
// or nil if none found.
func (m *CreateAgentRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAgentRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgentId

	if len(errors) > 0 {
		return CreateAgentRspMultiError(errors)
	}

	return nil
}

// CreateAgentRspMultiError is an error wrapping multiple validation errors
// returned by CreateAgentRsp.ValidateAll() if the designated constraints
// aren't met.
type CreateAgentRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAgentRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAgentRspMultiError) AllErrors() []error { return m }

// CreateAgentRspValidationError is the validation error returned by
// CreateAgentRsp.Validate if the designated constraints aren't met.
type CreateAgentRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAgentRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAgentRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAgentRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAgentRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAgentRspValidationError) ErrorName() string { return "CreateAgentRspValidationError" }

// Error satisfies the builtin error interface
func (e CreateAgentRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAgentRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAgentRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAgentRspValidationError{}

// Validate checks the field values on CreateStartingAgentReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateStartingAgentReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateStartingAgentReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateStartingAgentReqMultiError, or nil if none found.
func (m *CreateStartingAgentReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateStartingAgentReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppBizId

	if all {
		switch v := interface{}(m.GetAgent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateStartingAgentReqValidationError{
					field:  "Agent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateStartingAgentReqValidationError{
					field:  "Agent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAgent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateStartingAgentReqValidationError{
				field:  "Agent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateStartingAgentReqMultiError(errors)
	}

	return nil
}

// CreateStartingAgentReqMultiError is an error wrapping multiple validation
// errors returned by CreateStartingAgentReq.ValidateAll() if the designated
// constraints aren't met.
type CreateStartingAgentReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateStartingAgentReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateStartingAgentReqMultiError) AllErrors() []error { return m }

// CreateStartingAgentReqValidationError is the validation error returned by
// CreateStartingAgentReq.Validate if the designated constraints aren't met.
type CreateStartingAgentReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateStartingAgentReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateStartingAgentReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateStartingAgentReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateStartingAgentReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateStartingAgentReqValidationError) ErrorName() string {
	return "CreateStartingAgentReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateStartingAgentReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateStartingAgentReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateStartingAgentReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateStartingAgentReqValidationError{}

// Validate checks the field values on CreateStartingAgentRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateStartingAgentRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateStartingAgentRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateStartingAgentRspMultiError, or nil if none found.
func (m *CreateStartingAgentRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateStartingAgentRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgentId

	if len(errors) > 0 {
		return CreateStartingAgentRspMultiError(errors)
	}

	return nil
}

// CreateStartingAgentRspMultiError is an error wrapping multiple validation
// errors returned by CreateStartingAgentRsp.ValidateAll() if the designated
// constraints aren't met.
type CreateStartingAgentRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateStartingAgentRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateStartingAgentRspMultiError) AllErrors() []error { return m }

// CreateStartingAgentRspValidationError is the validation error returned by
// CreateStartingAgentRsp.Validate if the designated constraints aren't met.
type CreateStartingAgentRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateStartingAgentRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateStartingAgentRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateStartingAgentRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateStartingAgentRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateStartingAgentRspValidationError) ErrorName() string {
	return "CreateStartingAgentRspValidationError"
}

// Error satisfies the builtin error interface
func (e CreateStartingAgentRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateStartingAgentRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateStartingAgentRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateStartingAgentRspValidationError{}

// Validate checks the field values on CopyAgentReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CopyAgentReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CopyAgentReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CopyAgentReqMultiError, or
// nil if none found.
func (m *CopyAgentReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CopyAgentReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SourceAppBizId

	// no validation rules for SourceAgentId

	// no validation rules for SourceWorkflowId

	// no validation rules for SourceWorkflowName

	// no validation rules for TargetAppBizId

	if len(errors) > 0 {
		return CopyAgentReqMultiError(errors)
	}

	return nil
}

// CopyAgentReqMultiError is an error wrapping multiple validation errors
// returned by CopyAgentReq.ValidateAll() if the designated constraints aren't met.
type CopyAgentReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CopyAgentReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CopyAgentReqMultiError) AllErrors() []error { return m }

// CopyAgentReqValidationError is the validation error returned by
// CopyAgentReq.Validate if the designated constraints aren't met.
type CopyAgentReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CopyAgentReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CopyAgentReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CopyAgentReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CopyAgentReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CopyAgentReqValidationError) ErrorName() string { return "CopyAgentReqValidationError" }

// Error satisfies the builtin error interface
func (e CopyAgentReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCopyAgentReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CopyAgentReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CopyAgentReqValidationError{}

// Validate checks the field values on CopyAgentRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CopyAgentRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CopyAgentRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CopyAgentRspMultiError, or
// nil if none found.
func (m *CopyAgentRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *CopyAgentRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TargetAgentId

	if len(errors) > 0 {
		return CopyAgentRspMultiError(errors)
	}

	return nil
}

// CopyAgentRspMultiError is an error wrapping multiple validation errors
// returned by CopyAgentRsp.ValidateAll() if the designated constraints aren't met.
type CopyAgentRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CopyAgentRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CopyAgentRspMultiError) AllErrors() []error { return m }

// CopyAgentRspValidationError is the validation error returned by
// CopyAgentRsp.Validate if the designated constraints aren't met.
type CopyAgentRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CopyAgentRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CopyAgentRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CopyAgentRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CopyAgentRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CopyAgentRspValidationError) ErrorName() string { return "CopyAgentRspValidationError" }

// Error satisfies the builtin error interface
func (e CopyAgentRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCopyAgentRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CopyAgentRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CopyAgentRspValidationError{}

// Validate checks the field values on CopyAgentAppReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CopyAgentAppReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CopyAgentAppReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CopyAgentAppReqMultiError, or nil if none found.
func (m *CopyAgentAppReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CopyAgentAppReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SourceAppBizId

	// no validation rules for Force

	// no validation rules for TargetAppBizId

	if len(errors) > 0 {
		return CopyAgentAppReqMultiError(errors)
	}

	return nil
}

// CopyAgentAppReqMultiError is an error wrapping multiple validation errors
// returned by CopyAgentAppReq.ValidateAll() if the designated constraints
// aren't met.
type CopyAgentAppReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CopyAgentAppReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CopyAgentAppReqMultiError) AllErrors() []error { return m }

// CopyAgentAppReqValidationError is the validation error returned by
// CopyAgentAppReq.Validate if the designated constraints aren't met.
type CopyAgentAppReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CopyAgentAppReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CopyAgentAppReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CopyAgentAppReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CopyAgentAppReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CopyAgentAppReqValidationError) ErrorName() string { return "CopyAgentAppReqValidationError" }

// Error satisfies the builtin error interface
func (e CopyAgentAppReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCopyAgentAppReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CopyAgentAppReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CopyAgentAppReqValidationError{}

// Validate checks the field values on CopyAgentAppRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CopyAgentAppRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CopyAgentAppRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CopyAgentAppRspMultiError, or nil if none found.
func (m *CopyAgentAppRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *CopyAgentAppRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CopyAgentAppRspMultiError(errors)
	}

	return nil
}

// CopyAgentAppRspMultiError is an error wrapping multiple validation errors
// returned by CopyAgentAppRsp.ValidateAll() if the designated constraints
// aren't met.
type CopyAgentAppRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CopyAgentAppRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CopyAgentAppRspMultiError) AllErrors() []error { return m }

// CopyAgentAppRspValidationError is the validation error returned by
// CopyAgentAppRsp.Validate if the designated constraints aren't met.
type CopyAgentAppRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CopyAgentAppRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CopyAgentAppRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CopyAgentAppRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CopyAgentAppRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CopyAgentAppRspValidationError) ErrorName() string { return "CopyAgentAppRspValidationError" }

// Error satisfies the builtin error interface
func (e CopyAgentAppRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCopyAgentAppRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CopyAgentAppRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CopyAgentAppRspValidationError{}

// Validate checks the field values on DeleteAgentReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeleteAgentReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAgentReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeleteAgentReqMultiError,
// or nil if none found.
func (m *DeleteAgentReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAgentReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppBizId

	// no validation rules for AgentId

	if len(errors) > 0 {
		return DeleteAgentReqMultiError(errors)
	}

	return nil
}

// DeleteAgentReqMultiError is an error wrapping multiple validation errors
// returned by DeleteAgentReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteAgentReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAgentReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAgentReqMultiError) AllErrors() []error { return m }

// DeleteAgentReqValidationError is the validation error returned by
// DeleteAgentReq.Validate if the designated constraints aren't met.
type DeleteAgentReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAgentReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAgentReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAgentReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAgentReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAgentReqValidationError) ErrorName() string { return "DeleteAgentReqValidationError" }

// Error satisfies the builtin error interface
func (e DeleteAgentReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAgentReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAgentReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAgentReqValidationError{}

// Validate checks the field values on DeleteAgentRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeleteAgentRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAgentRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeleteAgentRspMultiError,
// or nil if none found.
func (m *DeleteAgentRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAgentRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgentId

	if len(errors) > 0 {
		return DeleteAgentRspMultiError(errors)
	}

	return nil
}

// DeleteAgentRspMultiError is an error wrapping multiple validation errors
// returned by DeleteAgentRsp.ValidateAll() if the designated constraints
// aren't met.
type DeleteAgentRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAgentRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAgentRspMultiError) AllErrors() []error { return m }

// DeleteAgentRspValidationError is the validation error returned by
// DeleteAgentRsp.Validate if the designated constraints aren't met.
type DeleteAgentRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAgentRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAgentRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAgentRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAgentRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAgentRspValidationError) ErrorName() string { return "DeleteAgentRspValidationError" }

// Error satisfies the builtin error interface
func (e DeleteAgentRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAgentRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAgentRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAgentRspValidationError{}

// Validate checks the field values on ModifyAgentReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ModifyAgentReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyAgentReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ModifyAgentReqMultiError,
// or nil if none found.
func (m *ModifyAgentReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyAgentReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppBizId

	if all {
		switch v := interface{}(m.GetAgent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyAgentReqValidationError{
					field:  "Agent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyAgentReqValidationError{
					field:  "Agent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAgent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyAgentReqValidationError{
				field:  "Agent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ModifyAgentReqMultiError(errors)
	}

	return nil
}

// ModifyAgentReqMultiError is an error wrapping multiple validation errors
// returned by ModifyAgentReq.ValidateAll() if the designated constraints
// aren't met.
type ModifyAgentReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyAgentReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyAgentReqMultiError) AllErrors() []error { return m }

// ModifyAgentReqValidationError is the validation error returned by
// ModifyAgentReq.Validate if the designated constraints aren't met.
type ModifyAgentReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyAgentReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyAgentReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyAgentReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyAgentReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyAgentReqValidationError) ErrorName() string { return "ModifyAgentReqValidationError" }

// Error satisfies the builtin error interface
func (e ModifyAgentReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyAgentReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyAgentReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyAgentReqValidationError{}

// Validate checks the field values on ModifyAgentRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ModifyAgentRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyAgentRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ModifyAgentRspMultiError,
// or nil if none found.
func (m *ModifyAgentRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyAgentRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ModifyAgentRspMultiError(errors)
	}

	return nil
}

// ModifyAgentRspMultiError is an error wrapping multiple validation errors
// returned by ModifyAgentRsp.ValidateAll() if the designated constraints
// aren't met.
type ModifyAgentRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyAgentRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyAgentRspMultiError) AllErrors() []error { return m }

// ModifyAgentRspValidationError is the validation error returned by
// ModifyAgentRsp.Validate if the designated constraints aren't met.
type ModifyAgentRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyAgentRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyAgentRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyAgentRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyAgentRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyAgentRspValidationError) ErrorName() string { return "ModifyAgentRspValidationError" }

// Error satisfies the builtin error interface
func (e ModifyAgentRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyAgentRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyAgentRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyAgentRspValidationError{}

// Validate checks the field values on ModifyAgentHandoffListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyAgentHandoffListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyAgentHandoffListReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyAgentHandoffListReqMultiError, or nil if none found.
func (m *ModifyAgentHandoffListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyAgentHandoffListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppBizId

	for idx, item := range m.GetAgents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ModifyAgentHandoffListReqValidationError{
						field:  fmt.Sprintf("Agents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ModifyAgentHandoffListReqValidationError{
						field:  fmt.Sprintf("Agents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ModifyAgentHandoffListReqValidationError{
					field:  fmt.Sprintf("Agents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ModifyAgentHandoffListReqMultiError(errors)
	}

	return nil
}

// ModifyAgentHandoffListReqMultiError is an error wrapping multiple validation
// errors returned by ModifyAgentHandoffListReq.ValidateAll() if the
// designated constraints aren't met.
type ModifyAgentHandoffListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyAgentHandoffListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyAgentHandoffListReqMultiError) AllErrors() []error { return m }

// ModifyAgentHandoffListReqValidationError is the validation error returned by
// ModifyAgentHandoffListReq.Validate if the designated constraints aren't met.
type ModifyAgentHandoffListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyAgentHandoffListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyAgentHandoffListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyAgentHandoffListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyAgentHandoffListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyAgentHandoffListReqValidationError) ErrorName() string {
	return "ModifyAgentHandoffListReqValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyAgentHandoffListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyAgentHandoffListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyAgentHandoffListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyAgentHandoffListReqValidationError{}

// Validate checks the field values on ModifyAgentHandoffListRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyAgentHandoffListRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyAgentHandoffListRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyAgentHandoffListRspMultiError, or nil if none found.
func (m *ModifyAgentHandoffListRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyAgentHandoffListRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ModifyAgentHandoffListRspMultiError(errors)
	}

	return nil
}

// ModifyAgentHandoffListRspMultiError is an error wrapping multiple validation
// errors returned by ModifyAgentHandoffListRsp.ValidateAll() if the
// designated constraints aren't met.
type ModifyAgentHandoffListRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyAgentHandoffListRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyAgentHandoffListRspMultiError) AllErrors() []error { return m }

// ModifyAgentHandoffListRspValidationError is the validation error returned by
// ModifyAgentHandoffListRsp.Validate if the designated constraints aren't met.
type ModifyAgentHandoffListRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyAgentHandoffListRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyAgentHandoffListRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyAgentHandoffListRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyAgentHandoffListRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyAgentHandoffListRspValidationError) ErrorName() string {
	return "ModifyAgentHandoffListRspValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyAgentHandoffListRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyAgentHandoffListRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyAgentHandoffListRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyAgentHandoffListRspValidationError{}

// Validate checks the field values on DescribeAgentListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeAgentListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeAgentListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeAgentListReqMultiError, or nil if none found.
func (m *DescribeAgentListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAgentListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppBizId

	// no validation rules for SearchWord

	// no validation rules for PageSize

	// no validation rules for PageNumber

	if len(errors) > 0 {
		return DescribeAgentListReqMultiError(errors)
	}

	return nil
}

// DescribeAgentListReqMultiError is an error wrapping multiple validation
// errors returned by DescribeAgentListReq.ValidateAll() if the designated
// constraints aren't met.
type DescribeAgentListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAgentListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAgentListReqMultiError) AllErrors() []error { return m }

// DescribeAgentListReqValidationError is the validation error returned by
// DescribeAgentListReq.Validate if the designated constraints aren't met.
type DescribeAgentListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAgentListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAgentListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAgentListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAgentListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAgentListReqValidationError) ErrorName() string {
	return "DescribeAgentListReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAgentListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAgentListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAgentListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAgentListReqValidationError{}

// Validate checks the field values on DescribeAgentListRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeAgentListRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeAgentListRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeAgentListRspMultiError, or nil if none found.
func (m *DescribeAgentListRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAgentListRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAgents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeAgentListRspValidationError{
						field:  fmt.Sprintf("Agents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeAgentListRspValidationError{
						field:  fmt.Sprintf("Agents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeAgentListRspValidationError{
					field:  fmt.Sprintf("Agents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return DescribeAgentListRspMultiError(errors)
	}

	return nil
}

// DescribeAgentListRspMultiError is an error wrapping multiple validation
// errors returned by DescribeAgentListRsp.ValidateAll() if the designated
// constraints aren't met.
type DescribeAgentListRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAgentListRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAgentListRspMultiError) AllErrors() []error { return m }

// DescribeAgentListRspValidationError is the validation error returned by
// DescribeAgentListRsp.Validate if the designated constraints aren't met.
type DescribeAgentListRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAgentListRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAgentListRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAgentListRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAgentListRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAgentListRspValidationError) ErrorName() string {
	return "DescribeAgentListRspValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAgentListRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAgentListRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAgentListRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAgentListRspValidationError{}

// Validate checks the field values on DescribeAppAgentListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeAppAgentListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeAppAgentListReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeAppAgentListReqMultiError, or nil if none found.
func (m *DescribeAppAgentListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAppAgentListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppBizId

	// no validation rules for EnvType

	if len(errors) > 0 {
		return DescribeAppAgentListReqMultiError(errors)
	}

	return nil
}

// DescribeAppAgentListReqMultiError is an error wrapping multiple validation
// errors returned by DescribeAppAgentListReq.ValidateAll() if the designated
// constraints aren't met.
type DescribeAppAgentListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAppAgentListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAppAgentListReqMultiError) AllErrors() []error { return m }

// DescribeAppAgentListReqValidationError is the validation error returned by
// DescribeAppAgentListReq.Validate if the designated constraints aren't met.
type DescribeAppAgentListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAppAgentListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAppAgentListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAppAgentListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAppAgentListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAppAgentListReqValidationError) ErrorName() string {
	return "DescribeAppAgentListReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAppAgentListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAppAgentListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAppAgentListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAppAgentListReqValidationError{}

// Validate checks the field values on DescribeAppAgentListRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeAppAgentListRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeAppAgentListRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeAppAgentListRspMultiError, or nil if none found.
func (m *DescribeAppAgentListRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAppAgentListRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StaringAgentId

	for idx, item := range m.GetAgents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeAppAgentListRspValidationError{
						field:  fmt.Sprintf("Agents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeAppAgentListRspValidationError{
						field:  fmt.Sprintf("Agents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeAppAgentListRspValidationError{
					field:  fmt.Sprintf("Agents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeAppAgentListRspMultiError(errors)
	}

	return nil
}

// DescribeAppAgentListRspMultiError is an error wrapping multiple validation
// errors returned by DescribeAppAgentListRsp.ValidateAll() if the designated
// constraints aren't met.
type DescribeAppAgentListRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAppAgentListRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAppAgentListRspMultiError) AllErrors() []error { return m }

// DescribeAppAgentListRspValidationError is the validation error returned by
// DescribeAppAgentListRsp.Validate if the designated constraints aren't met.
type DescribeAppAgentListRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAppAgentListRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAppAgentListRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAppAgentListRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAppAgentListRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAppAgentListRspValidationError) ErrorName() string {
	return "DescribeAppAgentListRspValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAppAgentListRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAppAgentListRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAppAgentListRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAppAgentListRspValidationError{}

// Validate checks the field values on
// DescribeAgentHasReferencedPluginToolListReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DescribeAgentHasReferencedPluginToolListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DescribeAgentHasReferencedPluginToolListReq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// DescribeAgentHasReferencedPluginToolListReqMultiError, or nil if none found.
func (m *DescribeAgentHasReferencedPluginToolListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAgentHasReferencedPluginToolListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PluginId

	// no validation rules for ToolId

	if len(errors) > 0 {
		return DescribeAgentHasReferencedPluginToolListReqMultiError(errors)
	}

	return nil
}

// DescribeAgentHasReferencedPluginToolListReqMultiError is an error wrapping
// multiple validation errors returned by
// DescribeAgentHasReferencedPluginToolListReq.ValidateAll() if the designated
// constraints aren't met.
type DescribeAgentHasReferencedPluginToolListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAgentHasReferencedPluginToolListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAgentHasReferencedPluginToolListReqMultiError) AllErrors() []error { return m }

// DescribeAgentHasReferencedPluginToolListReqValidationError is the validation
// error returned by DescribeAgentHasReferencedPluginToolListReq.Validate if
// the designated constraints aren't met.
type DescribeAgentHasReferencedPluginToolListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAgentHasReferencedPluginToolListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAgentHasReferencedPluginToolListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAgentHasReferencedPluginToolListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAgentHasReferencedPluginToolListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAgentHasReferencedPluginToolListReqValidationError) ErrorName() string {
	return "DescribeAgentHasReferencedPluginToolListReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAgentHasReferencedPluginToolListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAgentHasReferencedPluginToolListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAgentHasReferencedPluginToolListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAgentHasReferencedPluginToolListReqValidationError{}

// Validate checks the field values on
// DescribeAgentHasReferencedPluginToolListRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DescribeAgentHasReferencedPluginToolListRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DescribeAgentHasReferencedPluginToolListRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// DescribeAgentHasReferencedPluginToolListRspMultiError, or nil if none found.
func (m *DescribeAgentHasReferencedPluginToolListRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAgentHasReferencedPluginToolListRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetToolRefs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeAgentHasReferencedPluginToolListRspValidationError{
						field:  fmt.Sprintf("ToolRefs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeAgentHasReferencedPluginToolListRspValidationError{
						field:  fmt.Sprintf("ToolRefs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeAgentHasReferencedPluginToolListRspValidationError{
					field:  fmt.Sprintf("ToolRefs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DescribeAgentHasReferencedPluginToolListRspMultiError(errors)
	}

	return nil
}

// DescribeAgentHasReferencedPluginToolListRspMultiError is an error wrapping
// multiple validation errors returned by
// DescribeAgentHasReferencedPluginToolListRsp.ValidateAll() if the designated
// constraints aren't met.
type DescribeAgentHasReferencedPluginToolListRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAgentHasReferencedPluginToolListRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAgentHasReferencedPluginToolListRspMultiError) AllErrors() []error { return m }

// DescribeAgentHasReferencedPluginToolListRspValidationError is the validation
// error returned by DescribeAgentHasReferencedPluginToolListRsp.Validate if
// the designated constraints aren't met.
type DescribeAgentHasReferencedPluginToolListRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAgentHasReferencedPluginToolListRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAgentHasReferencedPluginToolListRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAgentHasReferencedPluginToolListRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAgentHasReferencedPluginToolListRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAgentHasReferencedPluginToolListRspValidationError) ErrorName() string {
	return "DescribeAgentHasReferencedPluginToolListRspValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAgentHasReferencedPluginToolListRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAgentHasReferencedPluginToolListRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAgentHasReferencedPluginToolListRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAgentHasReferencedPluginToolListRspValidationError{}

// Validate checks the field values on AgentToolRef with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AgentToolRef) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentToolRef with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AgentToolRefMultiError, or
// nil if none found.
func (m *AgentToolRef) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentToolRef) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppBizId

	// no validation rules for ToolId

	// no validation rules for PluginId

	if len(errors) > 0 {
		return AgentToolRefMultiError(errors)
	}

	return nil
}

// AgentToolRefMultiError is an error wrapping multiple validation errors
// returned by AgentToolRef.ValidateAll() if the designated constraints aren't met.
type AgentToolRefMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentToolRefMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentToolRefMultiError) AllErrors() []error { return m }

// AgentToolRefValidationError is the validation error returned by
// AgentToolRef.Validate if the designated constraints aren't met.
type AgentToolRefValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentToolRefValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentToolRefValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentToolRefValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentToolRefValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentToolRefValidationError) ErrorName() string { return "AgentToolRefValidationError" }

// Error satisfies the builtin error interface
func (e AgentToolRefValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentToolRef.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentToolRefValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentToolRefValidationError{}

// Validate checks the field values on ListAppAgentReleasePreviewReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAppAgentReleasePreviewReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAppAgentReleasePreviewReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListAppAgentReleasePreviewReqMultiError, or nil if none found.
func (m *ListAppAgentReleasePreviewReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAppAgentReleasePreviewReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BotBizId

	// no validation rules for Query

	// no validation rules for StartTime

	// no validation rules for EndTime

	// no validation rules for PageNumber

	// no validation rules for PageSize

	// no validation rules for ReleaseBizId

	if len(errors) > 0 {
		return ListAppAgentReleasePreviewReqMultiError(errors)
	}

	return nil
}

// ListAppAgentReleasePreviewReqMultiError is an error wrapping multiple
// validation errors returned by ListAppAgentReleasePreviewReq.ValidateAll()
// if the designated constraints aren't met.
type ListAppAgentReleasePreviewReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAppAgentReleasePreviewReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAppAgentReleasePreviewReqMultiError) AllErrors() []error { return m }

// ListAppAgentReleasePreviewReqValidationError is the validation error
// returned by ListAppAgentReleasePreviewReq.Validate if the designated
// constraints aren't met.
type ListAppAgentReleasePreviewReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAppAgentReleasePreviewReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAppAgentReleasePreviewReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAppAgentReleasePreviewReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAppAgentReleasePreviewReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAppAgentReleasePreviewReqValidationError) ErrorName() string {
	return "ListAppAgentReleasePreviewReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListAppAgentReleasePreviewReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAppAgentReleasePreviewReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAppAgentReleasePreviewReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAppAgentReleasePreviewReqValidationError{}

// Validate checks the field values on ListAppAgentReleasePreviewRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAppAgentReleasePreviewRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAppAgentReleasePreviewRsp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListAppAgentReleasePreviewRspMultiError, or nil if none found.
func (m *ListAppAgentReleasePreviewRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAppAgentReleasePreviewRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAppAgentReleasePreviewRspValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAppAgentReleasePreviewRspValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAppAgentReleasePreviewRspValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListAppAgentReleasePreviewRspMultiError(errors)
	}

	return nil
}

// ListAppAgentReleasePreviewRspMultiError is an error wrapping multiple
// validation errors returned by ListAppAgentReleasePreviewRsp.ValidateAll()
// if the designated constraints aren't met.
type ListAppAgentReleasePreviewRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAppAgentReleasePreviewRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAppAgentReleasePreviewRspMultiError) AllErrors() []error { return m }

// ListAppAgentReleasePreviewRspValidationError is the validation error
// returned by ListAppAgentReleasePreviewRsp.Validate if the designated
// constraints aren't met.
type ListAppAgentReleasePreviewRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAppAgentReleasePreviewRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAppAgentReleasePreviewRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAppAgentReleasePreviewRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAppAgentReleasePreviewRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAppAgentReleasePreviewRspValidationError) ErrorName() string {
	return "ListAppAgentReleasePreviewRspValidationError"
}

// Error satisfies the builtin error interface
func (e ListAppAgentReleasePreviewRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAppAgentReleasePreviewRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAppAgentReleasePreviewRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAppAgentReleasePreviewRspValidationError{}

// Validate checks the field values on ClearAgentAppResourceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClearAgentAppResourceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClearAgentAppResourceReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClearAgentAppResourceReqMultiError, or nil if none found.
func (m *ClearAgentAppResourceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ClearAgentAppResourceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppBizId

	// no validation rules for TaskId

	if len(errors) > 0 {
		return ClearAgentAppResourceReqMultiError(errors)
	}

	return nil
}

// ClearAgentAppResourceReqMultiError is an error wrapping multiple validation
// errors returned by ClearAgentAppResourceReq.ValidateAll() if the designated
// constraints aren't met.
type ClearAgentAppResourceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClearAgentAppResourceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClearAgentAppResourceReqMultiError) AllErrors() []error { return m }

// ClearAgentAppResourceReqValidationError is the validation error returned by
// ClearAgentAppResourceReq.Validate if the designated constraints aren't met.
type ClearAgentAppResourceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClearAgentAppResourceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClearAgentAppResourceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClearAgentAppResourceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClearAgentAppResourceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClearAgentAppResourceReqValidationError) ErrorName() string {
	return "ClearAgentAppResourceReqValidationError"
}

// Error satisfies the builtin error interface
func (e ClearAgentAppResourceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClearAgentAppResourceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClearAgentAppResourceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClearAgentAppResourceReqValidationError{}

// Validate checks the field values on ClearAgentAppResourceRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClearAgentAppResourceRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClearAgentAppResourceRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClearAgentAppResourceRspMultiError, or nil if none found.
func (m *ClearAgentAppResourceRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *ClearAgentAppResourceRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ClearAgentAppResourceRspMultiError(errors)
	}

	return nil
}

// ClearAgentAppResourceRspMultiError is an error wrapping multiple validation
// errors returned by ClearAgentAppResourceRsp.ValidateAll() if the designated
// constraints aren't met.
type ClearAgentAppResourceRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClearAgentAppResourceRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClearAgentAppResourceRspMultiError) AllErrors() []error { return m }

// ClearAgentAppResourceRspValidationError is the validation error returned by
// ClearAgentAppResourceRsp.Validate if the designated constraints aren't met.
type ClearAgentAppResourceRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClearAgentAppResourceRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClearAgentAppResourceRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClearAgentAppResourceRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClearAgentAppResourceRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClearAgentAppResourceRspValidationError) ErrorName() string {
	return "ClearAgentAppResourceRspValidationError"
}

// Error satisfies the builtin error interface
func (e ClearAgentAppResourceRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClearAgentAppResourceRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClearAgentAppResourceRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClearAgentAppResourceRspValidationError{}

// Validate checks the field values on AgentPluginInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AgentPluginInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentPluginInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentPluginInfoMultiError, or nil if none found.
func (m *AgentPluginInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentPluginInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PluginId

	for idx, item := range m.GetHeaders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentPluginInfoValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentPluginInfoValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentPluginInfoValidationError{
					field:  fmt.Sprintf("Headers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetModel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentPluginInfoValidationError{
					field:  "Model",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentPluginInfoValidationError{
					field:  "Model",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetModel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentPluginInfoValidationError{
				field:  "Model",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PluginInfoType

	if all {
		switch v := interface{}(m.GetKnowledgeQa()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentPluginInfoValidationError{
					field:  "KnowledgeQa",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentPluginInfoValidationError{
					field:  "KnowledgeQa",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKnowledgeQa()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentPluginInfoValidationError{
				field:  "KnowledgeQa",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AgentPluginInfoMultiError(errors)
	}

	return nil
}

// AgentPluginInfoMultiError is an error wrapping multiple validation errors
// returned by AgentPluginInfo.ValidateAll() if the designated constraints
// aren't met.
type AgentPluginInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentPluginInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentPluginInfoMultiError) AllErrors() []error { return m }

// AgentPluginInfoValidationError is the validation error returned by
// AgentPluginInfo.Validate if the designated constraints aren't met.
type AgentPluginInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentPluginInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentPluginInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentPluginInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentPluginInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentPluginInfoValidationError) ErrorName() string { return "AgentPluginInfoValidationError" }

// Error satisfies the builtin error interface
func (e AgentPluginInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentPluginInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentPluginInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentPluginInfoValidationError{}

// Validate checks the field values on AgentToolInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AgentToolInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentToolInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AgentToolInfoMultiError, or
// nil if none found.
func (m *AgentToolInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentToolInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PluginId

	// no validation rules for PluginName

	// no validation rules for IconUrl

	// no validation rules for PluginType

	// no validation rules for ToolId

	// no validation rules for ToolName

	// no validation rules for ToolDesc

	for idx, item := range m.GetInputs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentToolInfoValidationError{
						field:  fmt.Sprintf("Inputs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentToolInfoValidationError{
						field:  fmt.Sprintf("Inputs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentToolInfoValidationError{
					field:  fmt.Sprintf("Inputs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetOutputs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentToolInfoValidationError{
						field:  fmt.Sprintf("Outputs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentToolInfoValidationError{
						field:  fmt.Sprintf("Outputs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentToolInfoValidationError{
					field:  fmt.Sprintf("Outputs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CreateType

	if all {
		switch v := interface{}(m.GetMcpServer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentToolInfoValidationError{
					field:  "McpServer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentToolInfoValidationError{
					field:  "McpServer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMcpServer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentToolInfoValidationError{
				field:  "McpServer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsBindingKnowledge

	// no validation rules for Status

	// no validation rules for CallingMethod

	for idx, item := range m.GetHeaders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentToolInfoValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentToolInfoValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentToolInfoValidationError{
					field:  fmt.Sprintf("Headers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AgentToolInfoMultiError(errors)
	}

	return nil
}

// AgentToolInfoMultiError is an error wrapping multiple validation errors
// returned by AgentToolInfo.ValidateAll() if the designated constraints
// aren't met.
type AgentToolInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentToolInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentToolInfoMultiError) AllErrors() []error { return m }

// AgentToolInfoValidationError is the validation error returned by
// AgentToolInfo.Validate if the designated constraints aren't met.
type AgentToolInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentToolInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentToolInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentToolInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentToolInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentToolInfoValidationError) ErrorName() string { return "AgentToolInfoValidationError" }

// Error satisfies the builtin error interface
func (e AgentToolInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentToolInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentToolInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentToolInfoValidationError{}

// Validate checks the field values on AgentMCPServerInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AgentMCPServerInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentMCPServerInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentMCPServerInfoMultiError, or nil if none found.
func (m *AgentMCPServerInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentMCPServerInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for McpServerUrl

	for idx, item := range m.GetHeaders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentMCPServerInfoValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentMCPServerInfoValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentMCPServerInfoValidationError{
					field:  fmt.Sprintf("Headers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Timeout

	// no validation rules for SseReadTimeout

	if len(errors) > 0 {
		return AgentMCPServerInfoMultiError(errors)
	}

	return nil
}

// AgentMCPServerInfoMultiError is an error wrapping multiple validation errors
// returned by AgentMCPServerInfo.ValidateAll() if the designated constraints
// aren't met.
type AgentMCPServerInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentMCPServerInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentMCPServerInfoMultiError) AllErrors() []error { return m }

// AgentMCPServerInfoValidationError is the validation error returned by
// AgentMCPServerInfo.Validate if the designated constraints aren't met.
type AgentMCPServerInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentMCPServerInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentMCPServerInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentMCPServerInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentMCPServerInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentMCPServerInfoValidationError) ErrorName() string {
	return "AgentMCPServerInfoValidationError"
}

// Error satisfies the builtin error interface
func (e AgentMCPServerInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentMCPServerInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentMCPServerInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentMCPServerInfoValidationError{}

// Validate checks the field values on AgentPluginHeader with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AgentPluginHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentPluginHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentPluginHeaderMultiError, or nil if none found.
func (m *AgentPluginHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentPluginHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ParamName

	// no validation rules for ParamValue

	// no validation rules for GlobalHidden

	// no validation rules for IsRequired

	if all {
		switch v := interface{}(m.GetInput()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentPluginHeaderValidationError{
					field:  "Input",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentPluginHeaderValidationError{
					field:  "Input",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInput()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentPluginHeaderValidationError{
				field:  "Input",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AgentPluginHeaderMultiError(errors)
	}

	return nil
}

// AgentPluginHeaderMultiError is an error wrapping multiple validation errors
// returned by AgentPluginHeader.ValidateAll() if the designated constraints
// aren't met.
type AgentPluginHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentPluginHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentPluginHeaderMultiError) AllErrors() []error { return m }

// AgentPluginHeaderValidationError is the validation error returned by
// AgentPluginHeader.Validate if the designated constraints aren't met.
type AgentPluginHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentPluginHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentPluginHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentPluginHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentPluginHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentPluginHeaderValidationError) ErrorName() string {
	return "AgentPluginHeaderValidationError"
}

// Error satisfies the builtin error interface
func (e AgentPluginHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentPluginHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentPluginHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentPluginHeaderValidationError{}

// Validate checks the field values on AgentToolReqParam with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AgentToolReqParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentToolReqParam with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentToolReqParamMultiError, or nil if none found.
func (m *AgentToolReqParam) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentToolReqParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Desc

	// no validation rules for Type

	// no validation rules for IsRequired

	// no validation rules for DefaultValue

	for idx, item := range m.GetSubParams() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentToolReqParamValidationError{
						field:  fmt.Sprintf("SubParams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentToolReqParamValidationError{
						field:  fmt.Sprintf("SubParams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentToolReqParamValidationError{
					field:  fmt.Sprintf("SubParams[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AgentHidden

	for idx, item := range m.GetOneOf() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentToolReqParamValidationError{
						field:  fmt.Sprintf("OneOf[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentToolReqParamValidationError{
						field:  fmt.Sprintf("OneOf[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentToolReqParamValidationError{
					field:  fmt.Sprintf("OneOf[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAnyOf() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentToolReqParamValidationError{
						field:  fmt.Sprintf("AnyOf[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentToolReqParamValidationError{
						field:  fmt.Sprintf("AnyOf[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentToolReqParamValidationError{
					field:  fmt.Sprintf("AnyOf[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetInput()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentToolReqParamValidationError{
					field:  "Input",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentToolReqParamValidationError{
					field:  "Input",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInput()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentToolReqParamValidationError{
				field:  "Input",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AgentToolReqParamMultiError(errors)
	}

	return nil
}

// AgentToolReqParamMultiError is an error wrapping multiple validation errors
// returned by AgentToolReqParam.ValidateAll() if the designated constraints
// aren't met.
type AgentToolReqParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentToolReqParamMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentToolReqParamMultiError) AllErrors() []error { return m }

// AgentToolReqParamValidationError is the validation error returned by
// AgentToolReqParam.Validate if the designated constraints aren't met.
type AgentToolReqParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentToolReqParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentToolReqParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentToolReqParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentToolReqParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentToolReqParamValidationError) ErrorName() string {
	return "AgentToolReqParamValidationError"
}

// Error satisfies the builtin error interface
func (e AgentToolReqParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentToolReqParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentToolReqParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentToolReqParamValidationError{}

// Validate checks the field values on AgentToolRspParam with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AgentToolRspParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentToolRspParam with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentToolRspParamMultiError, or nil if none found.
func (m *AgentToolRspParam) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentToolRspParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Desc

	// no validation rules for Type

	for idx, item := range m.GetSubParams() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentToolRspParamValidationError{
						field:  fmt.Sprintf("SubParams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentToolRspParamValidationError{
						field:  fmt.Sprintf("SubParams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentToolRspParamValidationError{
					field:  fmt.Sprintf("SubParams[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AgentHidden

	// no validation rules for AnalysisMethod

	if len(errors) > 0 {
		return AgentToolRspParamMultiError(errors)
	}

	return nil
}

// AgentToolRspParamMultiError is an error wrapping multiple validation errors
// returned by AgentToolRspParam.ValidateAll() if the designated constraints
// aren't met.
type AgentToolRspParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentToolRspParamMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentToolRspParamMultiError) AllErrors() []error { return m }

// AgentToolRspParamValidationError is the validation error returned by
// AgentToolRspParam.Validate if the designated constraints aren't met.
type AgentToolRspParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentToolRspParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentToolRspParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentToolRspParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentToolRspParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentToolRspParamValidationError) ErrorName() string {
	return "AgentToolRspParamValidationError"
}

// Error satisfies the builtin error interface
func (e AgentToolRspParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentToolRspParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentToolRspParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentToolRspParamValidationError{}

// Validate checks the field values on Input with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Input) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Input with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in InputMultiError, or nil if none found.
func (m *Input) ValidateAll() error {
	return m.validate(true)
}

func (m *Input) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InputType

	if all {
		switch v := interface{}(m.GetUserInputValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InputValidationError{
					field:  "UserInputValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InputValidationError{
					field:  "UserInputValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserInputValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InputValidationError{
				field:  "UserInputValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomVarId

	if len(errors) > 0 {
		return InputMultiError(errors)
	}

	return nil
}

// InputMultiError is an error wrapping multiple validation errors returned by
// Input.ValidateAll() if the designated constraints aren't met.
type InputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InputMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InputMultiError) AllErrors() []error { return m }

// InputValidationError is the validation error returned by Input.Validate if
// the designated constraints aren't met.
type InputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InputValidationError) ErrorName() string { return "InputValidationError" }

// Error satisfies the builtin error interface
func (e InputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InputValidationError{}

// Validate checks the field values on UserInputValue with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserInputValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserInputValue with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserInputValueMultiError,
// or nil if none found.
func (m *UserInputValue) ValidateAll() error {
	return m.validate(true)
}

func (m *UserInputValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UserInputValueMultiError(errors)
	}

	return nil
}

// UserInputValueMultiError is an error wrapping multiple validation errors
// returned by UserInputValue.ValidateAll() if the designated constraints
// aren't met.
type UserInputValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserInputValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserInputValueMultiError) AllErrors() []error { return m }

// UserInputValueValidationError is the validation error returned by
// UserInputValue.Validate if the designated constraints aren't met.
type UserInputValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserInputValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserInputValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserInputValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserInputValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserInputValueValidationError) ErrorName() string { return "UserInputValueValidationError" }

// Error satisfies the builtin error interface
func (e UserInputValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserInputValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserInputValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserInputValueValidationError{}

// Validate checks the field values on SystemVariable with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SystemVariable) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SystemVariable with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SystemVariableMultiError,
// or nil if none found.
func (m *SystemVariable) ValidateAll() error {
	return m.validate(true)
}

func (m *SystemVariable) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for DialogHistoryLimit

	if len(errors) > 0 {
		return SystemVariableMultiError(errors)
	}

	return nil
}

// SystemVariableMultiError is an error wrapping multiple validation errors
// returned by SystemVariable.ValidateAll() if the designated constraints
// aren't met.
type SystemVariableMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SystemVariableMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SystemVariableMultiError) AllErrors() []error { return m }

// SystemVariableValidationError is the validation error returned by
// SystemVariable.Validate if the designated constraints aren't met.
type SystemVariableValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SystemVariableValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SystemVariableValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SystemVariableValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SystemVariableValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SystemVariableValidationError) ErrorName() string { return "SystemVariableValidationError" }

// Error satisfies the builtin error interface
func (e SystemVariableValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSystemVariable.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SystemVariableValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SystemVariableValidationError{}

// Validate checks the field values on AgentKnowledgeQAPlugin with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AgentKnowledgeQAPlugin) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentKnowledgeQAPlugin with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentKnowledgeQAPluginMultiError, or nil if none found.
func (m *AgentKnowledgeQAPlugin) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentKnowledgeQAPlugin) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentKnowledgeQAPluginValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentKnowledgeQAPluginValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentKnowledgeQAPluginValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AgentKnowledgeQAPluginMultiError(errors)
	}

	return nil
}

// AgentKnowledgeQAPluginMultiError is an error wrapping multiple validation
// errors returned by AgentKnowledgeQAPlugin.ValidateAll() if the designated
// constraints aren't met.
type AgentKnowledgeQAPluginMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentKnowledgeQAPluginMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentKnowledgeQAPluginMultiError) AllErrors() []error { return m }

// AgentKnowledgeQAPluginValidationError is the validation error returned by
// AgentKnowledgeQAPlugin.Validate if the designated constraints aren't met.
type AgentKnowledgeQAPluginValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentKnowledgeQAPluginValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentKnowledgeQAPluginValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentKnowledgeQAPluginValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentKnowledgeQAPluginValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentKnowledgeQAPluginValidationError) ErrorName() string {
	return "AgentKnowledgeQAPluginValidationError"
}

// Error satisfies the builtin error interface
func (e AgentKnowledgeQAPluginValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentKnowledgeQAPlugin.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentKnowledgeQAPluginValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentKnowledgeQAPluginValidationError{}

// Validate checks the field values on KnowledgeFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *KnowledgeFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KnowledgeFilterMultiError, or nil if none found.
func (m *KnowledgeFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilterType

	if all {
		switch v := interface{}(m.GetDocAndAnswer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KnowledgeFilterValidationError{
					field:  "DocAndAnswer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KnowledgeFilterValidationError{
					field:  "DocAndAnswer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDocAndAnswer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KnowledgeFilterValidationError{
				field:  "DocAndAnswer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KnowledgeFilterValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KnowledgeFilterValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KnowledgeFilterValidationError{
				field:  "Tag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KnowledgeFilterMultiError(errors)
	}

	return nil
}

// KnowledgeFilterMultiError is an error wrapping multiple validation errors
// returned by KnowledgeFilter.ValidateAll() if the designated constraints
// aren't met.
type KnowledgeFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeFilterMultiError) AllErrors() []error { return m }

// KnowledgeFilterValidationError is the validation error returned by
// KnowledgeFilter.Validate if the designated constraints aren't met.
type KnowledgeFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeFilterValidationError) ErrorName() string { return "KnowledgeFilterValidationError" }

// Error satisfies the builtin error interface
func (e KnowledgeFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeFilterValidationError{}

// Validate checks the field values on KnowledgeFilterDocAndAnswer with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KnowledgeFilterDocAndAnswer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeFilterDocAndAnswer with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KnowledgeFilterDocAndAnswerMultiError, or nil if none found.
func (m *KnowledgeFilterDocAndAnswer) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeFilterDocAndAnswer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AllQa

	if len(errors) > 0 {
		return KnowledgeFilterDocAndAnswerMultiError(errors)
	}

	return nil
}

// KnowledgeFilterDocAndAnswerMultiError is an error wrapping multiple
// validation errors returned by KnowledgeFilterDocAndAnswer.ValidateAll() if
// the designated constraints aren't met.
type KnowledgeFilterDocAndAnswerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeFilterDocAndAnswerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeFilterDocAndAnswerMultiError) AllErrors() []error { return m }

// KnowledgeFilterDocAndAnswerValidationError is the validation error returned
// by KnowledgeFilterDocAndAnswer.Validate if the designated constraints
// aren't met.
type KnowledgeFilterDocAndAnswerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeFilterDocAndAnswerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeFilterDocAndAnswerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeFilterDocAndAnswerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeFilterDocAndAnswerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeFilterDocAndAnswerValidationError) ErrorName() string {
	return "KnowledgeFilterDocAndAnswerValidationError"
}

// Error satisfies the builtin error interface
func (e KnowledgeFilterDocAndAnswerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeFilterDocAndAnswer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeFilterDocAndAnswerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeFilterDocAndAnswerValidationError{}

// Validate checks the field values on KnowledgeFilterTag with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KnowledgeFilterTag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeFilterTag with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KnowledgeFilterTagMultiError, or nil if none found.
func (m *KnowledgeFilterTag) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeFilterTag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Operator

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, KnowledgeFilterTagValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, KnowledgeFilterTagValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return KnowledgeFilterTagValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return KnowledgeFilterTagMultiError(errors)
	}

	return nil
}

// KnowledgeFilterTagMultiError is an error wrapping multiple validation errors
// returned by KnowledgeFilterTag.ValidateAll() if the designated constraints
// aren't met.
type KnowledgeFilterTagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeFilterTagMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeFilterTagMultiError) AllErrors() []error { return m }

// KnowledgeFilterTagValidationError is the validation error returned by
// KnowledgeFilterTag.Validate if the designated constraints aren't met.
type KnowledgeFilterTagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeFilterTagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeFilterTagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeFilterTagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeFilterTagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeFilterTagValidationError) ErrorName() string {
	return "KnowledgeFilterTagValidationError"
}

// Error satisfies the builtin error interface
func (e KnowledgeFilterTagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeFilterTag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeFilterTagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeFilterTagValidationError{}

// Validate checks the field values on AgentMigrateReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AgentMigrateReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentMigrateReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentMigrateReqMultiError, or nil if none found.
func (m *AgentMigrateReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentMigrateReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StartRobotId

	// no validation rules for MigrateLimit

	if len(errors) > 0 {
		return AgentMigrateReqMultiError(errors)
	}

	return nil
}

// AgentMigrateReqMultiError is an error wrapping multiple validation errors
// returned by AgentMigrateReq.ValidateAll() if the designated constraints
// aren't met.
type AgentMigrateReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentMigrateReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentMigrateReqMultiError) AllErrors() []error { return m }

// AgentMigrateReqValidationError is the validation error returned by
// AgentMigrateReq.Validate if the designated constraints aren't met.
type AgentMigrateReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentMigrateReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentMigrateReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentMigrateReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentMigrateReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentMigrateReqValidationError) ErrorName() string { return "AgentMigrateReqValidationError" }

// Error satisfies the builtin error interface
func (e AgentMigrateReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentMigrateReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentMigrateReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentMigrateReqValidationError{}

// Validate checks the field values on AgentMigrateRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AgentMigrateRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentMigrateRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentMigrateRspMultiError, or nil if none found.
func (m *AgentMigrateRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentMigrateRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LastRobotId

	if len(errors) > 0 {
		return AgentMigrateRspMultiError(errors)
	}

	return nil
}

// AgentMigrateRspMultiError is an error wrapping multiple validation errors
// returned by AgentMigrateRsp.ValidateAll() if the designated constraints
// aren't met.
type AgentMigrateRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentMigrateRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentMigrateRspMultiError) AllErrors() []error { return m }

// AgentMigrateRspValidationError is the validation error returned by
// AgentMigrateRsp.Validate if the designated constraints aren't met.
type AgentMigrateRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentMigrateRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentMigrateRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentMigrateRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentMigrateRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentMigrateRspValidationError) ErrorName() string { return "AgentMigrateRspValidationError" }

// Error satisfies the builtin error interface
func (e AgentMigrateRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentMigrateRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentMigrateRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentMigrateRspValidationError{}

// Validate checks the field values on DescribeAgentConfigRsp_AgentModelInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DescribeAgentConfigRsp_AgentModelInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeAgentConfigRsp_AgentModelInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DescribeAgentConfigRsp_AgentModelInfoMultiError, or nil if none found.
func (m *DescribeAgentConfigRsp_AgentModelInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAgentConfigRsp_AgentModelInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModelName

	// no validation rules for InstructionsWordsLimit

	if len(errors) > 0 {
		return DescribeAgentConfigRsp_AgentModelInfoMultiError(errors)
	}

	return nil
}

// DescribeAgentConfigRsp_AgentModelInfoMultiError is an error wrapping
// multiple validation errors returned by
// DescribeAgentConfigRsp_AgentModelInfo.ValidateAll() if the designated
// constraints aren't met.
type DescribeAgentConfigRsp_AgentModelInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAgentConfigRsp_AgentModelInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAgentConfigRsp_AgentModelInfoMultiError) AllErrors() []error { return m }

// DescribeAgentConfigRsp_AgentModelInfoValidationError is the validation error
// returned by DescribeAgentConfigRsp_AgentModelInfo.Validate if the
// designated constraints aren't met.
type DescribeAgentConfigRsp_AgentModelInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAgentConfigRsp_AgentModelInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAgentConfigRsp_AgentModelInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAgentConfigRsp_AgentModelInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAgentConfigRsp_AgentModelInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAgentConfigRsp_AgentModelInfoValidationError) ErrorName() string {
	return "DescribeAgentConfigRsp_AgentModelInfoValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAgentConfigRsp_AgentModelInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAgentConfigRsp_AgentModelInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAgentConfigRsp_AgentModelInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAgentConfigRsp_AgentModelInfoValidationError{}

// Validate checks the field values on ModifyAgentHandoffListReq_Agent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyAgentHandoffListReq_Agent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyAgentHandoffListReq_Agent with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ModifyAgentHandoffListReq_AgentMultiError, or nil if none found.
func (m *ModifyAgentHandoffListReq_Agent) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyAgentHandoffListReq_Agent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgentId

	if len(errors) > 0 {
		return ModifyAgentHandoffListReq_AgentMultiError(errors)
	}

	return nil
}

// ModifyAgentHandoffListReq_AgentMultiError is an error wrapping multiple
// validation errors returned by ModifyAgentHandoffListReq_Agent.ValidateAll()
// if the designated constraints aren't met.
type ModifyAgentHandoffListReq_AgentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyAgentHandoffListReq_AgentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyAgentHandoffListReq_AgentMultiError) AllErrors() []error { return m }

// ModifyAgentHandoffListReq_AgentValidationError is the validation error
// returned by ModifyAgentHandoffListReq_Agent.Validate if the designated
// constraints aren't met.
type ModifyAgentHandoffListReq_AgentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyAgentHandoffListReq_AgentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyAgentHandoffListReq_AgentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyAgentHandoffListReq_AgentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyAgentHandoffListReq_AgentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyAgentHandoffListReq_AgentValidationError) ErrorName() string {
	return "ModifyAgentHandoffListReq_AgentValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyAgentHandoffListReq_AgentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyAgentHandoffListReq_Agent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyAgentHandoffListReq_AgentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyAgentHandoffListReq_AgentValidationError{}

// Validate checks the field values on DescribeAgentListRsp_AppAgent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeAgentListRsp_AppAgent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeAgentListRsp_AppAgent with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DescribeAgentListRsp_AppAgentMultiError, or nil if none found.
func (m *DescribeAgentListRsp_AppAgent) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAgentListRsp_AppAgent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAgent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeAgentListRsp_AppAgentValidationError{
					field:  "Agent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeAgentListRsp_AppAgentValidationError{
					field:  "Agent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAgent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeAgentListRsp_AppAgentValidationError{
				field:  "Agent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AppBizId

	// no validation rules for AppName

	// no validation rules for From

	// no validation rules for IsWorkflow

	// no validation rules for Developer

	if len(errors) > 0 {
		return DescribeAgentListRsp_AppAgentMultiError(errors)
	}

	return nil
}

// DescribeAgentListRsp_AppAgentMultiError is an error wrapping multiple
// validation errors returned by DescribeAgentListRsp_AppAgent.ValidateAll()
// if the designated constraints aren't met.
type DescribeAgentListRsp_AppAgentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAgentListRsp_AppAgentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAgentListRsp_AppAgentMultiError) AllErrors() []error { return m }

// DescribeAgentListRsp_AppAgentValidationError is the validation error
// returned by DescribeAgentListRsp_AppAgent.Validate if the designated
// constraints aren't met.
type DescribeAgentListRsp_AppAgentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAgentListRsp_AppAgentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAgentListRsp_AppAgentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAgentListRsp_AppAgentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAgentListRsp_AppAgentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAgentListRsp_AppAgentValidationError) ErrorName() string {
	return "DescribeAgentListRsp_AppAgentValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAgentListRsp_AppAgentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAgentListRsp_AppAgent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAgentListRsp_AppAgentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAgentListRsp_AppAgentValidationError{}

// Validate checks the field values on DescribeAgentListRsp_Agent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeAgentListRsp_Agent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeAgentListRsp_Agent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeAgentListRsp_AgentMultiError, or nil if none found.
func (m *DescribeAgentListRsp_Agent) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAgentListRsp_Agent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgentId

	// no validation rules for WorkflowId

	// no validation rules for Name

	// no validation rules for IconUrl

	// no validation rules for Instructions

	// no validation rules for HandoffDescription

	if all {
		switch v := interface{}(m.GetModel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeAgentListRsp_AgentValidationError{
					field:  "Model",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeAgentListRsp_AgentValidationError{
					field:  "Model",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetModel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeAgentListRsp_AgentValidationError{
				field:  "Model",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTools() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeAgentListRsp_AgentValidationError{
						field:  fmt.Sprintf("Tools[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeAgentListRsp_AgentValidationError{
						field:  fmt.Sprintf("Tools[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeAgentListRsp_AgentValidationError{
					field:  fmt.Sprintf("Tools[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetPlugins() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeAgentListRsp_AgentValidationError{
						field:  fmt.Sprintf("Plugins[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeAgentListRsp_AgentValidationError{
						field:  fmt.Sprintf("Plugins[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeAgentListRsp_AgentValidationError{
					field:  fmt.Sprintf("Plugins[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsStartingAgent

	// no validation rules for AgentType

	if len(errors) > 0 {
		return DescribeAgentListRsp_AgentMultiError(errors)
	}

	return nil
}

// DescribeAgentListRsp_AgentMultiError is an error wrapping multiple
// validation errors returned by DescribeAgentListRsp_Agent.ValidateAll() if
// the designated constraints aren't met.
type DescribeAgentListRsp_AgentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAgentListRsp_AgentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAgentListRsp_AgentMultiError) AllErrors() []error { return m }

// DescribeAgentListRsp_AgentValidationError is the validation error returned
// by DescribeAgentListRsp_Agent.Validate if the designated constraints aren't met.
type DescribeAgentListRsp_AgentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAgentListRsp_AgentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAgentListRsp_AgentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAgentListRsp_AgentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAgentListRsp_AgentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAgentListRsp_AgentValidationError) ErrorName() string {
	return "DescribeAgentListRsp_AgentValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAgentListRsp_AgentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAgentListRsp_Agent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAgentListRsp_AgentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAgentListRsp_AgentValidationError{}

// Validate checks the field values on DescribeAgentListRsp_AgentPluginInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DescribeAgentListRsp_AgentPluginInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeAgentListRsp_AgentPluginInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DescribeAgentListRsp_AgentPluginInfoMultiError, or nil if none found.
func (m *DescribeAgentListRsp_AgentPluginInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAgentListRsp_AgentPluginInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PluginId

	for idx, item := range m.GetHeaders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeAgentListRsp_AgentPluginInfoValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeAgentListRsp_AgentPluginInfoValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeAgentListRsp_AgentPluginInfoValidationError{
					field:  fmt.Sprintf("Headers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetModel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeAgentListRsp_AgentPluginInfoValidationError{
					field:  "Model",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeAgentListRsp_AgentPluginInfoValidationError{
					field:  "Model",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetModel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeAgentListRsp_AgentPluginInfoValidationError{
				field:  "Model",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PluginInfoType

	if all {
		switch v := interface{}(m.GetKnowledgeQa()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeAgentListRsp_AgentPluginInfoValidationError{
					field:  "KnowledgeQa",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeAgentListRsp_AgentPluginInfoValidationError{
					field:  "KnowledgeQa",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKnowledgeQa()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeAgentListRsp_AgentPluginInfoValidationError{
				field:  "KnowledgeQa",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DescribeAgentListRsp_AgentPluginInfoMultiError(errors)
	}

	return nil
}

// DescribeAgentListRsp_AgentPluginInfoMultiError is an error wrapping multiple
// validation errors returned by
// DescribeAgentListRsp_AgentPluginInfo.ValidateAll() if the designated
// constraints aren't met.
type DescribeAgentListRsp_AgentPluginInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAgentListRsp_AgentPluginInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAgentListRsp_AgentPluginInfoMultiError) AllErrors() []error { return m }

// DescribeAgentListRsp_AgentPluginInfoValidationError is the validation error
// returned by DescribeAgentListRsp_AgentPluginInfo.Validate if the designated
// constraints aren't met.
type DescribeAgentListRsp_AgentPluginInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAgentListRsp_AgentPluginInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAgentListRsp_AgentPluginInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAgentListRsp_AgentPluginInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAgentListRsp_AgentPluginInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAgentListRsp_AgentPluginInfoValidationError) ErrorName() string {
	return "DescribeAgentListRsp_AgentPluginInfoValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAgentListRsp_AgentPluginInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAgentListRsp_AgentPluginInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAgentListRsp_AgentPluginInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAgentListRsp_AgentPluginInfoValidationError{}

// Validate checks the field values on DescribeAgentListRsp_AgentModelInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DescribeAgentListRsp_AgentModelInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeAgentListRsp_AgentModelInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DescribeAgentListRsp_AgentModelInfoMultiError, or nil if none found.
func (m *DescribeAgentListRsp_AgentModelInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAgentListRsp_AgentModelInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HistoryLimit

	// no validation rules for ModelName

	// no validation rules for IsEnabled

	// no validation rules for Temperature

	// no validation rules for TopP

	// no validation rules for ModelContextWordsLimit

	// no validation rules for InstructionsWordsLimit

	// no validation rules for ModelAliasName

	if len(errors) > 0 {
		return DescribeAgentListRsp_AgentModelInfoMultiError(errors)
	}

	return nil
}

// DescribeAgentListRsp_AgentModelInfoMultiError is an error wrapping multiple
// validation errors returned by
// DescribeAgentListRsp_AgentModelInfo.ValidateAll() if the designated
// constraints aren't met.
type DescribeAgentListRsp_AgentModelInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAgentListRsp_AgentModelInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAgentListRsp_AgentModelInfoMultiError) AllErrors() []error { return m }

// DescribeAgentListRsp_AgentModelInfoValidationError is the validation error
// returned by DescribeAgentListRsp_AgentModelInfo.Validate if the designated
// constraints aren't met.
type DescribeAgentListRsp_AgentModelInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAgentListRsp_AgentModelInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAgentListRsp_AgentModelInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAgentListRsp_AgentModelInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAgentListRsp_AgentModelInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAgentListRsp_AgentModelInfoValidationError) ErrorName() string {
	return "DescribeAgentListRsp_AgentModelInfoValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAgentListRsp_AgentModelInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAgentListRsp_AgentModelInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAgentListRsp_AgentModelInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAgentListRsp_AgentModelInfoValidationError{}

// Validate checks the field values on DescribeAppAgentListRsp_Agent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeAppAgentListRsp_Agent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeAppAgentListRsp_Agent with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DescribeAppAgentListRsp_AgentMultiError, or nil if none found.
func (m *DescribeAppAgentListRsp_Agent) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAppAgentListRsp_Agent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgentId

	// no validation rules for WorkflowId

	// no validation rules for Name

	// no validation rules for IconUrl

	// no validation rules for Instructions

	// no validation rules for HandoffDescription

	if all {
		switch v := interface{}(m.GetModel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeAppAgentListRsp_AgentValidationError{
					field:  "Model",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeAppAgentListRsp_AgentValidationError{
					field:  "Model",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetModel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeAppAgentListRsp_AgentValidationError{
				field:  "Model",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTools() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeAppAgentListRsp_AgentValidationError{
						field:  fmt.Sprintf("Tools[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeAppAgentListRsp_AgentValidationError{
						field:  fmt.Sprintf("Tools[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeAppAgentListRsp_AgentValidationError{
					field:  fmt.Sprintf("Tools[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetPlugins() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeAppAgentListRsp_AgentValidationError{
						field:  fmt.Sprintf("Plugins[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeAppAgentListRsp_AgentValidationError{
						field:  fmt.Sprintf("Plugins[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeAppAgentListRsp_AgentValidationError{
					field:  fmt.Sprintf("Plugins[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsStartingAgent

	// no validation rules for AgentType

	if len(errors) > 0 {
		return DescribeAppAgentListRsp_AgentMultiError(errors)
	}

	return nil
}

// DescribeAppAgentListRsp_AgentMultiError is an error wrapping multiple
// validation errors returned by DescribeAppAgentListRsp_Agent.ValidateAll()
// if the designated constraints aren't met.
type DescribeAppAgentListRsp_AgentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAppAgentListRsp_AgentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAppAgentListRsp_AgentMultiError) AllErrors() []error { return m }

// DescribeAppAgentListRsp_AgentValidationError is the validation error
// returned by DescribeAppAgentListRsp_Agent.Validate if the designated
// constraints aren't met.
type DescribeAppAgentListRsp_AgentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAppAgentListRsp_AgentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAppAgentListRsp_AgentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAppAgentListRsp_AgentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAppAgentListRsp_AgentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAppAgentListRsp_AgentValidationError) ErrorName() string {
	return "DescribeAppAgentListRsp_AgentValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAppAgentListRsp_AgentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAppAgentListRsp_Agent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAppAgentListRsp_AgentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAppAgentListRsp_AgentValidationError{}

// Validate checks the field values on DescribeAppAgentListRsp_AgentPluginInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DescribeAppAgentListRsp_AgentPluginInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DescribeAppAgentListRsp_AgentPluginInfo with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// DescribeAppAgentListRsp_AgentPluginInfoMultiError, or nil if none found.
func (m *DescribeAppAgentListRsp_AgentPluginInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAppAgentListRsp_AgentPluginInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PluginId

	for idx, item := range m.GetHeaders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DescribeAppAgentListRsp_AgentPluginInfoValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DescribeAppAgentListRsp_AgentPluginInfoValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DescribeAppAgentListRsp_AgentPluginInfoValidationError{
					field:  fmt.Sprintf("Headers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetModel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeAppAgentListRsp_AgentPluginInfoValidationError{
					field:  "Model",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeAppAgentListRsp_AgentPluginInfoValidationError{
					field:  "Model",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetModel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeAppAgentListRsp_AgentPluginInfoValidationError{
				field:  "Model",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PluginInfoType

	if all {
		switch v := interface{}(m.GetKnowledgeQa()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeAppAgentListRsp_AgentPluginInfoValidationError{
					field:  "KnowledgeQa",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeAppAgentListRsp_AgentPluginInfoValidationError{
					field:  "KnowledgeQa",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKnowledgeQa()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeAppAgentListRsp_AgentPluginInfoValidationError{
				field:  "KnowledgeQa",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DescribeAppAgentListRsp_AgentPluginInfoMultiError(errors)
	}

	return nil
}

// DescribeAppAgentListRsp_AgentPluginInfoMultiError is an error wrapping
// multiple validation errors returned by
// DescribeAppAgentListRsp_AgentPluginInfo.ValidateAll() if the designated
// constraints aren't met.
type DescribeAppAgentListRsp_AgentPluginInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAppAgentListRsp_AgentPluginInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAppAgentListRsp_AgentPluginInfoMultiError) AllErrors() []error { return m }

// DescribeAppAgentListRsp_AgentPluginInfoValidationError is the validation
// error returned by DescribeAppAgentListRsp_AgentPluginInfo.Validate if the
// designated constraints aren't met.
type DescribeAppAgentListRsp_AgentPluginInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAppAgentListRsp_AgentPluginInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAppAgentListRsp_AgentPluginInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAppAgentListRsp_AgentPluginInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAppAgentListRsp_AgentPluginInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAppAgentListRsp_AgentPluginInfoValidationError) ErrorName() string {
	return "DescribeAppAgentListRsp_AgentPluginInfoValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAppAgentListRsp_AgentPluginInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAppAgentListRsp_AgentPluginInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAppAgentListRsp_AgentPluginInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAppAgentListRsp_AgentPluginInfoValidationError{}

// Validate checks the field values on DescribeAppAgentListRsp_AgentModelInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DescribeAppAgentListRsp_AgentModelInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DescribeAppAgentListRsp_AgentModelInfo with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// DescribeAppAgentListRsp_AgentModelInfoMultiError, or nil if none found.
func (m *DescribeAppAgentListRsp_AgentModelInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeAppAgentListRsp_AgentModelInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HistoryLimit

	// no validation rules for ModelName

	// no validation rules for IsEnabled

	// no validation rules for Temperature

	// no validation rules for TopP

	// no validation rules for ModelContextWordsLimit

	// no validation rules for InstructionsWordsLimit

	// no validation rules for ModelAliasName

	if len(errors) > 0 {
		return DescribeAppAgentListRsp_AgentModelInfoMultiError(errors)
	}

	return nil
}

// DescribeAppAgentListRsp_AgentModelInfoMultiError is an error wrapping
// multiple validation errors returned by
// DescribeAppAgentListRsp_AgentModelInfo.ValidateAll() if the designated
// constraints aren't met.
type DescribeAppAgentListRsp_AgentModelInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeAppAgentListRsp_AgentModelInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeAppAgentListRsp_AgentModelInfoMultiError) AllErrors() []error { return m }

// DescribeAppAgentListRsp_AgentModelInfoValidationError is the validation
// error returned by DescribeAppAgentListRsp_AgentModelInfo.Validate if the
// designated constraints aren't met.
type DescribeAppAgentListRsp_AgentModelInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeAppAgentListRsp_AgentModelInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeAppAgentListRsp_AgentModelInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeAppAgentListRsp_AgentModelInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeAppAgentListRsp_AgentModelInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeAppAgentListRsp_AgentModelInfoValidationError) ErrorName() string {
	return "DescribeAppAgentListRsp_AgentModelInfoValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeAppAgentListRsp_AgentModelInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeAppAgentListRsp_AgentModelInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeAppAgentListRsp_AgentModelInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeAppAgentListRsp_AgentModelInfoValidationError{}

// Validate checks the field values on ListAppAgentReleasePreviewRsp_AppAgent
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListAppAgentReleasePreviewRsp_AppAgent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListAppAgentReleasePreviewRsp_AppAgent with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ListAppAgentReleasePreviewRsp_AppAgentMultiError, or nil if none found.
func (m *ListAppAgentReleasePreviewRsp_AppAgent) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAppAgentReleasePreviewRsp_AppAgent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgentId

	// no validation rules for AgentName

	// no validation rules for UpdateTime

	// no validation rules for Action

	// no validation rules for ActionDesc

	// no validation rules for Message

	if len(errors) > 0 {
		return ListAppAgentReleasePreviewRsp_AppAgentMultiError(errors)
	}

	return nil
}

// ListAppAgentReleasePreviewRsp_AppAgentMultiError is an error wrapping
// multiple validation errors returned by
// ListAppAgentReleasePreviewRsp_AppAgent.ValidateAll() if the designated
// constraints aren't met.
type ListAppAgentReleasePreviewRsp_AppAgentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAppAgentReleasePreviewRsp_AppAgentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAppAgentReleasePreviewRsp_AppAgentMultiError) AllErrors() []error { return m }

// ListAppAgentReleasePreviewRsp_AppAgentValidationError is the validation
// error returned by ListAppAgentReleasePreviewRsp_AppAgent.Validate if the
// designated constraints aren't met.
type ListAppAgentReleasePreviewRsp_AppAgentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAppAgentReleasePreviewRsp_AppAgentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAppAgentReleasePreviewRsp_AppAgentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAppAgentReleasePreviewRsp_AppAgentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAppAgentReleasePreviewRsp_AppAgentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAppAgentReleasePreviewRsp_AppAgentValidationError) ErrorName() string {
	return "ListAppAgentReleasePreviewRsp_AppAgentValidationError"
}

// Error satisfies the builtin error interface
func (e ListAppAgentReleasePreviewRsp_AppAgentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAppAgentReleasePreviewRsp_AppAgent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAppAgentReleasePreviewRsp_AppAgentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAppAgentReleasePreviewRsp_AppAgentValidationError{}

// Validate checks the field values on KnowledgeFilterTag_KnowledgeAttrLabel
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *KnowledgeFilterTag_KnowledgeAttrLabel) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeFilterTag_KnowledgeAttrLabel
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// KnowledgeFilterTag_KnowledgeAttrLabelMultiError, or nil if none found.
func (m *KnowledgeFilterTag_KnowledgeAttrLabel) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeFilterTag_KnowledgeAttrLabel) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttributeBizId

	for idx, item := range m.GetInputs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, KnowledgeFilterTag_KnowledgeAttrLabelValidationError{
						field:  fmt.Sprintf("Inputs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, KnowledgeFilterTag_KnowledgeAttrLabelValidationError{
						field:  fmt.Sprintf("Inputs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return KnowledgeFilterTag_KnowledgeAttrLabelValidationError{
					field:  fmt.Sprintf("Inputs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return KnowledgeFilterTag_KnowledgeAttrLabelMultiError(errors)
	}

	return nil
}

// KnowledgeFilterTag_KnowledgeAttrLabelMultiError is an error wrapping
// multiple validation errors returned by
// KnowledgeFilterTag_KnowledgeAttrLabel.ValidateAll() if the designated
// constraints aren't met.
type KnowledgeFilterTag_KnowledgeAttrLabelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeFilterTag_KnowledgeAttrLabelMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeFilterTag_KnowledgeAttrLabelMultiError) AllErrors() []error { return m }

// KnowledgeFilterTag_KnowledgeAttrLabelValidationError is the validation error
// returned by KnowledgeFilterTag_KnowledgeAttrLabel.Validate if the
// designated constraints aren't met.
type KnowledgeFilterTag_KnowledgeAttrLabelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeFilterTag_KnowledgeAttrLabelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeFilterTag_KnowledgeAttrLabelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeFilterTag_KnowledgeAttrLabelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeFilterTag_KnowledgeAttrLabelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeFilterTag_KnowledgeAttrLabelValidationError) ErrorName() string {
	return "KnowledgeFilterTag_KnowledgeAttrLabelValidationError"
}

// Error satisfies the builtin error interface
func (e KnowledgeFilterTag_KnowledgeAttrLabelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeFilterTag_KnowledgeAttrLabel.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeFilterTag_KnowledgeAttrLabelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeFilterTag_KnowledgeAttrLabelValidationError{}
