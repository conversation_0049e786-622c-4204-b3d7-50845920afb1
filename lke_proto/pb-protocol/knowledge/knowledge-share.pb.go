// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.1
// source: knowledge-share.proto

package knowledge

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type KnowledgeUpdateField int32

const (
	KnowledgeUpdateField_KnowledgeInvalidField        KnowledgeUpdateField = 0
	KnowledgeUpdateField_KnowledgeNameField           KnowledgeUpdateField = 1
	KnowledgeUpdateField_KnowledgeDescriptionField    KnowledgeUpdateField = 2
	KnowledgeUpdateField_KnowledgeEmbeddingModelField KnowledgeUpdateField = 3
	KnowledgeUpdateField_KnowledgeQAExtractModelField KnowledgeUpdateField = 4
)

// Enum value maps for KnowledgeUpdateField.
var (
	KnowledgeUpdateField_name = map[int32]string{
		0: "KnowledgeInvalidField",
		1: "KnowledgeNameField",
		2: "KnowledgeDescriptionField",
		3: "KnowledgeEmbeddingModelField",
		4: "KnowledgeQAExtractModelField",
	}
	KnowledgeUpdateField_value = map[string]int32{
		"KnowledgeInvalidField":        0,
		"KnowledgeNameField":           1,
		"KnowledgeDescriptionField":    2,
		"KnowledgeEmbeddingModelField": 3,
		"KnowledgeQAExtractModelField": 4,
	}
)

func (x KnowledgeUpdateField) Enum() *KnowledgeUpdateField {
	p := new(KnowledgeUpdateField)
	*p = x
	return p
}

func (x KnowledgeUpdateField) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeUpdateField) Descriptor() protoreflect.EnumDescriptor {
	return file_knowledge_share_proto_enumTypes[0].Descriptor()
}

func (KnowledgeUpdateField) Type() protoreflect.EnumType {
	return &file_knowledge_share_proto_enumTypes[0]
}

func (x KnowledgeUpdateField) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeUpdateField.Descriptor instead.
func (KnowledgeUpdateField) EnumDescriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{0}
}

// 共享知识库创建请求
type CreateSharedKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 共享知识库名称, 非空
	KnowledgeName string `protobuf:"bytes,2,opt,name=knowledge_name,json=knowledgeName,proto3" json:"knowledge_name,omitempty"`
	// 共享知识库描述
	KnowledgeDescription string `protobuf:"bytes,3,opt,name=knowledge_description,json=knowledgeDescription,proto3" json:"knowledge_description,omitempty"`
	// Embedding模型
	EmbeddingModel string `protobuf:"bytes,4,opt,name=embedding_model,json=embeddingModel,proto3" json:"embedding_model,omitempty"` // string qa_extract_model = 5;
}

func (x *CreateSharedKnowledgeReq) Reset() {
	*x = CreateSharedKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSharedKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSharedKnowledgeReq) ProtoMessage() {}

func (x *CreateSharedKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSharedKnowledgeReq.ProtoReflect.Descriptor instead.
func (*CreateSharedKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{0}
}

func (x *CreateSharedKnowledgeReq) GetKnowledgeName() string {
	if x != nil {
		return x.KnowledgeName
	}
	return ""
}

func (x *CreateSharedKnowledgeReq) GetKnowledgeDescription() string {
	if x != nil {
		return x.KnowledgeDescription
	}
	return ""
}

func (x *CreateSharedKnowledgeReq) GetEmbeddingModel() string {
	if x != nil {
		return x.EmbeddingModel
	}
	return ""
}

// 共享知识库创建响应
type CreateSharedKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 共享知识库业务ID
	KnowledgeBizId uint64 `protobuf:"varint,1,opt,name=knowledge_biz_id,json=knowledgeBizId,proto3" json:"knowledge_biz_id,omitempty"`
}

func (x *CreateSharedKnowledgeRsp) Reset() {
	*x = CreateSharedKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSharedKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSharedKnowledgeRsp) ProtoMessage() {}

func (x *CreateSharedKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSharedKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*CreateSharedKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{1}
}

func (x *CreateSharedKnowledgeRsp) GetKnowledgeBizId() uint64 {
	if x != nil {
		return x.KnowledgeBizId
	}
	return 0
}

// 共享知识库删除请求
type DeleteSharedKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 共享知识库业务ID
	KnowledgeBizId uint64 `protobuf:"varint,1,opt,name=knowledge_biz_id,json=knowledgeBizId,proto3" json:"knowledge_biz_id,omitempty"`
}

func (x *DeleteSharedKnowledgeReq) Reset() {
	*x = DeleteSharedKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSharedKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSharedKnowledgeReq) ProtoMessage() {}

func (x *DeleteSharedKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSharedKnowledgeReq.ProtoReflect.Descriptor instead.
func (*DeleteSharedKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteSharedKnowledgeReq) GetKnowledgeBizId() uint64 {
	if x != nil {
		return x.KnowledgeBizId
	}
	return 0
}

// 共享知识库删除响应
type DeleteSharedKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 共享知识库业务ID
	KnowledgeBizId uint64 `protobuf:"varint,1,opt,name=knowledge_biz_id,json=knowledgeBizId,proto3" json:"knowledge_biz_id,omitempty"`
}

func (x *DeleteSharedKnowledgeRsp) Reset() {
	*x = DeleteSharedKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSharedKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSharedKnowledgeRsp) ProtoMessage() {}

func (x *DeleteSharedKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSharedKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*DeleteSharedKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteSharedKnowledgeRsp) GetKnowledgeBizId() uint64 {
	if x != nil {
		return x.KnowledgeBizId
	}
	return 0
}

// 共享知识库列举请求
type ListSharedKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分页序号, 编码从1开始
	PageNumber uint32 `protobuf:"varint,1,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty"`
	// 分页大小, 有效范围[1,200]
	PageSize uint32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 搜索关键字: 知识库名称名称/修改人
	Keyword string `protobuf:"bytes,3,opt,name=keyword,proto3" json:"keyword,omitempty"`
}

func (x *ListSharedKnowledgeReq) Reset() {
	*x = ListSharedKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSharedKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSharedKnowledgeReq) ProtoMessage() {}

func (x *ListSharedKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSharedKnowledgeReq.ProtoReflect.Descriptor instead.
func (*ListSharedKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{4}
}

func (x *ListSharedKnowledgeReq) GetPageNumber() uint32 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

func (x *ListSharedKnowledgeReq) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListSharedKnowledgeReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

// 共享知识库查询响应
type KnowledgeBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 共享知识库业务ID
	KnowledgeBizId uint64 `protobuf:"varint,1,opt,name=knowledge_biz_id,json=knowledgeBizId,proto3" json:"knowledge_biz_id,omitempty"`
	// 共享知识库名称
	KnowledgeName string `protobuf:"bytes,2,opt,name=knowledge_name,json=knowledgeName,proto3" json:"knowledge_name,omitempty"`
	// 共享知识库描述
	KnowledgeDescription string `protobuf:"bytes,3,opt,name=knowledge_description,json=knowledgeDescription,proto3" json:"knowledge_description,omitempty"`
	// Embedding模型
	EmbeddingModel string `protobuf:"bytes,4,opt,name=embedding_model,json=embeddingModel,proto3" json:"embedding_model,omitempty"`
	// QaExtract模型
	QaExtractModel string `protobuf:"bytes,5,opt,name=qa_extract_model,json=qaExtractModel,proto3" json:"qa_extract_model,omitempty"`
	// 更新时间
	UpdateTime int64 `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *KnowledgeBaseInfo) Reset() {
	*x = KnowledgeBaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeBaseInfo) ProtoMessage() {}

func (x *KnowledgeBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeBaseInfo.ProtoReflect.Descriptor instead.
func (*KnowledgeBaseInfo) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{5}
}

func (x *KnowledgeBaseInfo) GetKnowledgeBizId() uint64 {
	if x != nil {
		return x.KnowledgeBizId
	}
	return 0
}

func (x *KnowledgeBaseInfo) GetKnowledgeName() string {
	if x != nil {
		return x.KnowledgeName
	}
	return ""
}

func (x *KnowledgeBaseInfo) GetKnowledgeDescription() string {
	if x != nil {
		return x.KnowledgeDescription
	}
	return ""
}

func (x *KnowledgeBaseInfo) GetEmbeddingModel() string {
	if x != nil {
		return x.EmbeddingModel
	}
	return ""
}

func (x *KnowledgeBaseInfo) GetQaExtractModel() string {
	if x != nil {
		return x.QaExtractModel
	}
	return ""
}

func (x *KnowledgeBaseInfo) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

// 应用基础信息
type AppBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用ID
	AppBizId uint64 `protobuf:"varint,1,opt,name=app_biz_id,json=appBizId,proto3" json:"app_biz_id,omitempty"`
	// 应用名称
	AppName string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
}

func (x *AppBaseInfo) Reset() {
	*x = AppBaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppBaseInfo) ProtoMessage() {}

func (x *AppBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppBaseInfo.ProtoReflect.Descriptor instead.
func (*AppBaseInfo) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{6}
}

func (x *AppBaseInfo) GetAppBizId() uint64 {
	if x != nil {
		return x.AppBizId
	}
	return 0
}

func (x *AppBaseInfo) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

// 用户基础信息
type UserBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	UserBizId uint64 `protobuf:"varint,1,opt,name=user_biz_id,json=userBizId,proto3" json:"user_biz_id,omitempty"`
	// 用户名称
	UserName string `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
}

func (x *UserBaseInfo) Reset() {
	*x = UserBaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBaseInfo) ProtoMessage() {}

func (x *UserBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBaseInfo.ProtoReflect.Descriptor instead.
func (*UserBaseInfo) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{7}
}

func (x *UserBaseInfo) GetUserBizId() uint64 {
	if x != nil {
		return x.UserBizId
	}
	return 0
}

func (x *UserBaseInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

// 共享知识库详情信息
type KnowledgeDetailInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Knowledge *KnowledgeBaseInfo `protobuf:"bytes,1,opt,name=knowledge,proto3" json:"knowledge,omitempty"`
	AppList   []*AppBaseInfo     `protobuf:"bytes,2,rep,name=app_list,json=appList,proto3" json:"app_list,omitempty"`
	User      *UserBaseInfo      `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *KnowledgeDetailInfo) Reset() {
	*x = KnowledgeDetailInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeDetailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeDetailInfo) ProtoMessage() {}

func (x *KnowledgeDetailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeDetailInfo.ProtoReflect.Descriptor instead.
func (*KnowledgeDetailInfo) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{8}
}

func (x *KnowledgeDetailInfo) GetKnowledge() *KnowledgeBaseInfo {
	if x != nil {
		return x.Knowledge
	}
	return nil
}

func (x *KnowledgeDetailInfo) GetAppList() []*AppBaseInfo {
	if x != nil {
		return x.AppList
	}
	return nil
}

func (x *KnowledgeDetailInfo) GetUser() *UserBaseInfo {
	if x != nil {
		return x.User
	}
	return nil
}

// 共享知识库列举响应
type ListSharedKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KnowledgeList []*KnowledgeDetailInfo `protobuf:"bytes,1,rep,name=knowledge_list,json=knowledgeList,proto3" json:"knowledge_list,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListSharedKnowledgeRsp) Reset() {
	*x = ListSharedKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSharedKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSharedKnowledgeRsp) ProtoMessage() {}

func (x *ListSharedKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSharedKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*ListSharedKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{9}
}

func (x *ListSharedKnowledgeRsp) GetKnowledgeList() []*KnowledgeDetailInfo {
	if x != nil {
		return x.KnowledgeList
	}
	return nil
}

func (x *ListSharedKnowledgeRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 共享知识库查询请求
type DescribeSharedKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 共享知识库业务ID
	KnowledgeBizId uint64 `protobuf:"varint,1,opt,name=knowledge_biz_id,json=knowledgeBizId,proto3" json:"knowledge_biz_id,omitempty"`
}

func (x *DescribeSharedKnowledgeReq) Reset() {
	*x = DescribeSharedKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSharedKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSharedKnowledgeReq) ProtoMessage() {}

func (x *DescribeSharedKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSharedKnowledgeReq.ProtoReflect.Descriptor instead.
func (*DescribeSharedKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{10}
}

func (x *DescribeSharedKnowledgeReq) GetKnowledgeBizId() uint64 {
	if x != nil {
		return x.KnowledgeBizId
	}
	return 0
}

// 共享知识库查询响应
type DescribeSharedKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info *KnowledgeDetailInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *DescribeSharedKnowledgeRsp) Reset() {
	*x = DescribeSharedKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSharedKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSharedKnowledgeRsp) ProtoMessage() {}

func (x *DescribeSharedKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSharedKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*DescribeSharedKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{11}
}

func (x *DescribeSharedKnowledgeRsp) GetInfo() *KnowledgeDetailInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type KnowledgeUpdateInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 共享知识库名称, 非空
	KnowledgeName        string `protobuf:"bytes,2,opt,name=knowledge_name,json=knowledgeName,proto3" json:"knowledge_name,omitempty"`
	KnowledgeDescription string `protobuf:"bytes,3,opt,name=knowledge_description,json=knowledgeDescription,proto3" json:"knowledge_description,omitempty"`
	EmbeddingModel       string `protobuf:"bytes,4,opt,name=embedding_model,json=embeddingModel,proto3" json:"embedding_model,omitempty"`
	QaExtractModel       string `protobuf:"bytes,5,opt,name=qa_extract_model,json=qaExtractModel,proto3" json:"qa_extract_model,omitempty"`
}

func (x *KnowledgeUpdateInfo) Reset() {
	*x = KnowledgeUpdateInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeUpdateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeUpdateInfo) ProtoMessage() {}

func (x *KnowledgeUpdateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeUpdateInfo.ProtoReflect.Descriptor instead.
func (*KnowledgeUpdateInfo) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{12}
}

func (x *KnowledgeUpdateInfo) GetKnowledgeName() string {
	if x != nil {
		return x.KnowledgeName
	}
	return ""
}

func (x *KnowledgeUpdateInfo) GetKnowledgeDescription() string {
	if x != nil {
		return x.KnowledgeDescription
	}
	return ""
}

func (x *KnowledgeUpdateInfo) GetEmbeddingModel() string {
	if x != nil {
		return x.EmbeddingModel
	}
	return ""
}

func (x *KnowledgeUpdateInfo) GetQaExtractModel() string {
	if x != nil {
		return x.QaExtractModel
	}
	return ""
}

// 共享知识库更新请求
type UpdateSharedKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 共享知识库业务ID
	KnowledgeBizId uint64               `protobuf:"varint,1,opt,name=knowledge_biz_id,json=knowledgeBizId,proto3" json:"knowledge_biz_id,omitempty"`
	Info           *KnowledgeUpdateInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"` // repeated KnowledgeUpdateField field_list = 3;
}

func (x *UpdateSharedKnowledgeReq) Reset() {
	*x = UpdateSharedKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSharedKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSharedKnowledgeReq) ProtoMessage() {}

func (x *UpdateSharedKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSharedKnowledgeReq.ProtoReflect.Descriptor instead.
func (*UpdateSharedKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateSharedKnowledgeReq) GetKnowledgeBizId() uint64 {
	if x != nil {
		return x.KnowledgeBizId
	}
	return 0
}

func (x *UpdateSharedKnowledgeReq) GetInfo() *KnowledgeUpdateInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

// 共享知识库更新响应
type UpdateSharedKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 共享知识库业务ID
	KnowledgeBizId uint64 `protobuf:"varint,1,opt,name=knowledge_biz_id,json=knowledgeBizId,proto3" json:"knowledge_biz_id,omitempty"`
}

func (x *UpdateSharedKnowledgeRsp) Reset() {
	*x = UpdateSharedKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSharedKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSharedKnowledgeRsp) ProtoMessage() {}

func (x *UpdateSharedKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSharedKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*UpdateSharedKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateSharedKnowledgeRsp) GetKnowledgeBizId() uint64 {
	if x != nil {
		return x.KnowledgeBizId
	}
	return 0
}

// 引用共享知识库请求
type ReferSharedKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId       uint64   `protobuf:"varint,1,opt,name=app_biz_id,json=appBizId,proto3" json:"app_biz_id,omitempty"`                          // 应用业务ID
	KnowledgeBizId []uint64 `protobuf:"varint,2,rep,packed,name=knowledge_biz_id,json=knowledgeBizId,proto3" json:"knowledge_biz_id,omitempty"` // 共享知识库业务ID
}

func (x *ReferSharedKnowledgeReq) Reset() {
	*x = ReferSharedKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReferSharedKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReferSharedKnowledgeReq) ProtoMessage() {}

func (x *ReferSharedKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReferSharedKnowledgeReq.ProtoReflect.Descriptor instead.
func (*ReferSharedKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{15}
}

func (x *ReferSharedKnowledgeReq) GetAppBizId() uint64 {
	if x != nil {
		return x.AppBizId
	}
	return 0
}

func (x *ReferSharedKnowledgeReq) GetKnowledgeBizId() []uint64 {
	if x != nil {
		return x.KnowledgeBizId
	}
	return nil
}

// 引用共享知识库响应
type ReferSharedKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReferSharedKnowledgeRsp) Reset() {
	*x = ReferSharedKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReferSharedKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReferSharedKnowledgeRsp) ProtoMessage() {}

func (x *ReferSharedKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReferSharedKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*ReferSharedKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{16}
}

// 查看引用共享知识库列表请求
type ListReferSharedKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId uint64 `protobuf:"varint,1,opt,name=app_biz_id,json=appBizId,proto3" json:"app_biz_id,omitempty"` // 应用业务ID
}

func (x *ListReferSharedKnowledgeReq) Reset() {
	*x = ListReferSharedKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReferSharedKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReferSharedKnowledgeReq) ProtoMessage() {}

func (x *ListReferSharedKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReferSharedKnowledgeReq.ProtoReflect.Descriptor instead.
func (*ListReferSharedKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{17}
}

func (x *ListReferSharedKnowledgeReq) GetAppBizId() uint64 {
	if x != nil {
		return x.AppBizId
	}
	return 0
}

// 查看引用共享知识库列表响应
type ListReferSharedKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*KnowledgeBaseInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"` // 共享知识库业务ID
}

func (x *ListReferSharedKnowledgeRsp) Reset() {
	*x = ListReferSharedKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReferSharedKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReferSharedKnowledgeRsp) ProtoMessage() {}

func (x *ListReferSharedKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReferSharedKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*ListReferSharedKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{18}
}

func (x *ListReferSharedKnowledgeRsp) GetList() []*KnowledgeBaseInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 共享知识库批量获取请求
type BatchGetSharedKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 企业业务ID
	CorpBizId uint64 `protobuf:"varint,1,opt,name=corp_biz_id,json=corpBizId,proto3" json:"corp_biz_id,omitempty"`
	// 共享知识库业务ID列表
	KnowledgeBizIdList []uint64 `protobuf:"varint,2,rep,packed,name=knowledge_biz_id_list,json=knowledgeBizIdList,proto3" json:"knowledge_biz_id_list,omitempty"`
}

func (x *BatchGetSharedKnowledgeReq) Reset() {
	*x = BatchGetSharedKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetSharedKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetSharedKnowledgeReq) ProtoMessage() {}

func (x *BatchGetSharedKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetSharedKnowledgeReq.ProtoReflect.Descriptor instead.
func (*BatchGetSharedKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{19}
}

func (x *BatchGetSharedKnowledgeReq) GetCorpBizId() uint64 {
	if x != nil {
		return x.CorpBizId
	}
	return 0
}

func (x *BatchGetSharedKnowledgeReq) GetKnowledgeBizIdList() []uint64 {
	if x != nil {
		return x.KnowledgeBizIdList
	}
	return nil
}

// 共享知识库批量获取响应
type BatchGetSharedKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 共享知识库基本信息列表
	InfoList []*KnowledgeBaseInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
}

func (x *BatchGetSharedKnowledgeRsp) Reset() {
	*x = BatchGetSharedKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_share_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetSharedKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetSharedKnowledgeRsp) ProtoMessage() {}

func (x *BatchGetSharedKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_share_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetSharedKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*BatchGetSharedKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_knowledge_share_proto_rawDescGZIP(), []int{20}
}

func (x *BatchGetSharedKnowledgeRsp) GetInfoList() []*KnowledgeBaseInfo {
	if x != nil {
		return x.InfoList
	}
	return nil
}

var File_knowledge_share_proto protoreflect.FileDescriptor

var file_knowledge_share_proto_rawDesc = []byte{
	0x0a, 0x15, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2d, 0x73, 0x68, 0x61, 0x72,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x1a, 0x0e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x9f, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x68,
	0x61, 0x72, 0x65, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x25, 0x0a, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x15, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f,
	0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x44, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x68, 0x61, 0x72, 0x65, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x28, 0x0a, 0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62,
	0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x44, 0x0a, 0x18, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x28, 0x0a, 0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x69, 0x7a, 0x49,
	0x64, 0x22, 0x44, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x28, 0x0a,
	0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x70, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x68, 0x61, 0x72, 0x65, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x8d, 0x02, 0x0a, 0x11, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x28, 0x0a, 0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x69, 0x7a,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x33, 0x0a, 0x15, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x28,
	0x0a, 0x10, 0x71, 0x61, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x71, 0x61, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x46, 0x0a, 0x0b, 0x41, 0x70, 0x70,
	0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f,
	0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x61, 0x70,
	0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x4b, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1e, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x42, 0x69, 0x7a, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xcc,
	0x01, 0x0a, 0x13, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x43, 0x0a, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x61,
	0x70, 0x70, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07,
	0x61, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x42,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x22, 0x7e, 0x0a,
	0x16, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x46, 0x0a,
	0x1a, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x28, 0x0a, 0x10, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x59, 0x0a, 0x1a, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f,
	0x22, 0xc4, 0x01, 0x0a, 0x13, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a, 0x0e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x33, 0x0a, 0x15, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65,
	0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x28, 0x0a,
	0x10, 0x71, 0x61, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x71, 0x61, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x81, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x28, 0x0a, 0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x3b,
	0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x44, 0x0a, 0x18, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x28, 0x0a, 0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x69, 0x7a, 0x49,
	0x64, 0x22, 0x61, 0x0a, 0x17, 0x52, 0x65, 0x66, 0x65, 0x72, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x0a,
	0x61, 0x70, 0x70, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x61, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x04, 0x52, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x69, 0x7a, 0x49, 0x64, 0x22, 0x19, 0x0a, 0x17, 0x52, 0x65, 0x66, 0x65, 0x72, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x22,
	0x3b, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x53, 0x68, 0x61, 0x72,
	0x65, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1c,
	0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x61, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x58, 0x0a, 0x1b,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x6f, 0x0a, 0x1a, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47,
	0x65, 0x74, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x62, 0x69, 0x7a,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x6f, 0x72, 0x70, 0x42,
	0x69, 0x7a, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x15, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x04, 0x52, 0x12, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x69,
	0x7a, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x60, 0x0a, 0x1a, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x65, 0x74, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x42, 0x0a, 0x09, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x08, 0x69, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x2a, 0xac, 0x01, 0x0a, 0x14, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x12, 0x19, 0x0a, 0x15, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x49,
	0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x10, 0x00, 0x12, 0x16, 0x0a,
	0x12, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x51, 0x41, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x10, 0x04, 0x42, 0x3f, 0x5a, 0x3d, 0x67, 0x69, 0x74, 0x2e,
	0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65,
	0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_knowledge_share_proto_rawDescOnce sync.Once
	file_knowledge_share_proto_rawDescData = file_knowledge_share_proto_rawDesc
)

func file_knowledge_share_proto_rawDescGZIP() []byte {
	file_knowledge_share_proto_rawDescOnce.Do(func() {
		file_knowledge_share_proto_rawDescData = protoimpl.X.CompressGZIP(file_knowledge_share_proto_rawDescData)
	})
	return file_knowledge_share_proto_rawDescData
}

var file_knowledge_share_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_knowledge_share_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_knowledge_share_proto_goTypes = []interface{}{
	(KnowledgeUpdateField)(0),           // 0: trpc.KEP.knowledge.KnowledgeUpdateField
	(*CreateSharedKnowledgeReq)(nil),    // 1: trpc.KEP.knowledge.CreateSharedKnowledgeReq
	(*CreateSharedKnowledgeRsp)(nil),    // 2: trpc.KEP.knowledge.CreateSharedKnowledgeRsp
	(*DeleteSharedKnowledgeReq)(nil),    // 3: trpc.KEP.knowledge.DeleteSharedKnowledgeReq
	(*DeleteSharedKnowledgeRsp)(nil),    // 4: trpc.KEP.knowledge.DeleteSharedKnowledgeRsp
	(*ListSharedKnowledgeReq)(nil),      // 5: trpc.KEP.knowledge.ListSharedKnowledgeReq
	(*KnowledgeBaseInfo)(nil),           // 6: trpc.KEP.knowledge.KnowledgeBaseInfo
	(*AppBaseInfo)(nil),                 // 7: trpc.KEP.knowledge.AppBaseInfo
	(*UserBaseInfo)(nil),                // 8: trpc.KEP.knowledge.UserBaseInfo
	(*KnowledgeDetailInfo)(nil),         // 9: trpc.KEP.knowledge.KnowledgeDetailInfo
	(*ListSharedKnowledgeRsp)(nil),      // 10: trpc.KEP.knowledge.ListSharedKnowledgeRsp
	(*DescribeSharedKnowledgeReq)(nil),  // 11: trpc.KEP.knowledge.DescribeSharedKnowledgeReq
	(*DescribeSharedKnowledgeRsp)(nil),  // 12: trpc.KEP.knowledge.DescribeSharedKnowledgeRsp
	(*KnowledgeUpdateInfo)(nil),         // 13: trpc.KEP.knowledge.KnowledgeUpdateInfo
	(*UpdateSharedKnowledgeReq)(nil),    // 14: trpc.KEP.knowledge.UpdateSharedKnowledgeReq
	(*UpdateSharedKnowledgeRsp)(nil),    // 15: trpc.KEP.knowledge.UpdateSharedKnowledgeRsp
	(*ReferSharedKnowledgeReq)(nil),     // 16: trpc.KEP.knowledge.ReferSharedKnowledgeReq
	(*ReferSharedKnowledgeRsp)(nil),     // 17: trpc.KEP.knowledge.ReferSharedKnowledgeRsp
	(*ListReferSharedKnowledgeReq)(nil), // 18: trpc.KEP.knowledge.ListReferSharedKnowledgeReq
	(*ListReferSharedKnowledgeRsp)(nil), // 19: trpc.KEP.knowledge.ListReferSharedKnowledgeRsp
	(*BatchGetSharedKnowledgeReq)(nil),  // 20: trpc.KEP.knowledge.BatchGetSharedKnowledgeReq
	(*BatchGetSharedKnowledgeRsp)(nil),  // 21: trpc.KEP.knowledge.BatchGetSharedKnowledgeRsp
}
var file_knowledge_share_proto_depIdxs = []int32{
	6,  // 0: trpc.KEP.knowledge.KnowledgeDetailInfo.knowledge:type_name -> trpc.KEP.knowledge.KnowledgeBaseInfo
	7,  // 1: trpc.KEP.knowledge.KnowledgeDetailInfo.app_list:type_name -> trpc.KEP.knowledge.AppBaseInfo
	8,  // 2: trpc.KEP.knowledge.KnowledgeDetailInfo.user:type_name -> trpc.KEP.knowledge.UserBaseInfo
	9,  // 3: trpc.KEP.knowledge.ListSharedKnowledgeRsp.knowledge_list:type_name -> trpc.KEP.knowledge.KnowledgeDetailInfo
	9,  // 4: trpc.KEP.knowledge.DescribeSharedKnowledgeRsp.info:type_name -> trpc.KEP.knowledge.KnowledgeDetailInfo
	13, // 5: trpc.KEP.knowledge.UpdateSharedKnowledgeReq.info:type_name -> trpc.KEP.knowledge.KnowledgeUpdateInfo
	6,  // 6: trpc.KEP.knowledge.ListReferSharedKnowledgeRsp.list:type_name -> trpc.KEP.knowledge.KnowledgeBaseInfo
	6,  // 7: trpc.KEP.knowledge.BatchGetSharedKnowledgeRsp.info_list:type_name -> trpc.KEP.knowledge.KnowledgeBaseInfo
	8,  // [8:8] is the sub-list for method output_type
	8,  // [8:8] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_knowledge_share_proto_init() }
func file_knowledge_share_proto_init() {
	if File_knowledge_share_proto != nil {
		return
	}
	file_knowledge_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_knowledge_share_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSharedKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSharedKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSharedKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSharedKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSharedKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeBaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppBaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserBaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeDetailInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSharedKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSharedKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSharedKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeUpdateInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSharedKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSharedKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReferSharedKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReferSharedKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReferSharedKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReferSharedKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetSharedKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_share_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetSharedKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_knowledge_share_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_knowledge_share_proto_goTypes,
		DependencyIndexes: file_knowledge_share_proto_depIdxs,
		EnumInfos:         file_knowledge_share_proto_enumTypes,
		MessageInfos:      file_knowledge_share_proto_msgTypes,
	}.Build()
	File_knowledge_share_proto = out.File
	file_knowledge_share_proto_rawDesc = nil
	file_knowledge_share_proto_goTypes = nil
	file_knowledge_share_proto_depIdxs = nil
}
