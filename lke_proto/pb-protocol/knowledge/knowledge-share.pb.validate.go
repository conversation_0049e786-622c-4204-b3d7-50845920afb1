// Code generated by protoc-gen-secv. DO NOT EDIT.
// source: knowledge-share.proto

package knowledge

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
)

// Validate checks the field values on CreateSharedKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSharedKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSharedKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSharedKnowledgeReqMultiError, or nil if none found.
func (m *CreateSharedKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSharedKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KnowledgeName

	// no validation rules for KnowledgeDescription

	// no validation rules for EmbeddingModel

	if len(errors) > 0 {
		return CreateSharedKnowledgeReqMultiError(errors)
	}
	return nil
}

// CreateSharedKnowledgeReqMultiError is an error wrapping multiple validation
// errors returned by CreateSharedKnowledgeReq.ValidateAll() if the designated
// constraints aren't met.
type CreateSharedKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSharedKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSharedKnowledgeReqMultiError) AllErrors() []error { return m }

// CreateSharedKnowledgeReqValidationError is the validation error returned by
// CreateSharedKnowledgeReq.Validate if the designated constraints aren't met.
type CreateSharedKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSharedKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSharedKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSharedKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSharedKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSharedKnowledgeReqValidationError) ErrorName() string {
	return "CreateSharedKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSharedKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSharedKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSharedKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSharedKnowledgeReqValidationError{}

// Validate checks the field values on CreateSharedKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSharedKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSharedKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSharedKnowledgeRspMultiError, or nil if none found.
func (m *CreateSharedKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSharedKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KnowledgeBizId

	if len(errors) > 0 {
		return CreateSharedKnowledgeRspMultiError(errors)
	}
	return nil
}

// CreateSharedKnowledgeRspMultiError is an error wrapping multiple validation
// errors returned by CreateSharedKnowledgeRsp.ValidateAll() if the designated
// constraints aren't met.
type CreateSharedKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSharedKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSharedKnowledgeRspMultiError) AllErrors() []error { return m }

// CreateSharedKnowledgeRspValidationError is the validation error returned by
// CreateSharedKnowledgeRsp.Validate if the designated constraints aren't met.
type CreateSharedKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSharedKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSharedKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSharedKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSharedKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSharedKnowledgeRspValidationError) ErrorName() string {
	return "CreateSharedKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSharedKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSharedKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSharedKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSharedKnowledgeRspValidationError{}

// Validate checks the field values on DeleteSharedKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteSharedKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSharedKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSharedKnowledgeReqMultiError, or nil if none found.
func (m *DeleteSharedKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSharedKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KnowledgeBizId

	if len(errors) > 0 {
		return DeleteSharedKnowledgeReqMultiError(errors)
	}
	return nil
}

// DeleteSharedKnowledgeReqMultiError is an error wrapping multiple validation
// errors returned by DeleteSharedKnowledgeReq.ValidateAll() if the designated
// constraints aren't met.
type DeleteSharedKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSharedKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSharedKnowledgeReqMultiError) AllErrors() []error { return m }

// DeleteSharedKnowledgeReqValidationError is the validation error returned by
// DeleteSharedKnowledgeReq.Validate if the designated constraints aren't met.
type DeleteSharedKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSharedKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSharedKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSharedKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSharedKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSharedKnowledgeReqValidationError) ErrorName() string {
	return "DeleteSharedKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSharedKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSharedKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSharedKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSharedKnowledgeReqValidationError{}

// Validate checks the field values on DeleteSharedKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteSharedKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSharedKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSharedKnowledgeRspMultiError, or nil if none found.
func (m *DeleteSharedKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSharedKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KnowledgeBizId

	if len(errors) > 0 {
		return DeleteSharedKnowledgeRspMultiError(errors)
	}
	return nil
}

// DeleteSharedKnowledgeRspMultiError is an error wrapping multiple validation
// errors returned by DeleteSharedKnowledgeRsp.ValidateAll() if the designated
// constraints aren't met.
type DeleteSharedKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSharedKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSharedKnowledgeRspMultiError) AllErrors() []error { return m }

// DeleteSharedKnowledgeRspValidationError is the validation error returned by
// DeleteSharedKnowledgeRsp.Validate if the designated constraints aren't met.
type DeleteSharedKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSharedKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSharedKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSharedKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSharedKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSharedKnowledgeRspValidationError) ErrorName() string {
	return "DeleteSharedKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSharedKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSharedKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSharedKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSharedKnowledgeRspValidationError{}

// Validate checks the field values on ListSharedKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSharedKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSharedKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSharedKnowledgeReqMultiError, or nil if none found.
func (m *ListSharedKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSharedKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageNumber

	// no validation rules for PageSize

	// no validation rules for Keyword

	if len(errors) > 0 {
		return ListSharedKnowledgeReqMultiError(errors)
	}
	return nil
}

// ListSharedKnowledgeReqMultiError is an error wrapping multiple validation
// errors returned by ListSharedKnowledgeReq.ValidateAll() if the designated
// constraints aren't met.
type ListSharedKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSharedKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSharedKnowledgeReqMultiError) AllErrors() []error { return m }

// ListSharedKnowledgeReqValidationError is the validation error returned by
// ListSharedKnowledgeReq.Validate if the designated constraints aren't met.
type ListSharedKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSharedKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSharedKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSharedKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSharedKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSharedKnowledgeReqValidationError) ErrorName() string {
	return "ListSharedKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListSharedKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSharedKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSharedKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSharedKnowledgeReqValidationError{}

// Validate checks the field values on KnowledgeBaseInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *KnowledgeBaseInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeBaseInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KnowledgeBaseInfoMultiError, or nil if none found.
func (m *KnowledgeBaseInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeBaseInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KnowledgeBizId

	// no validation rules for KnowledgeName

	// no validation rules for KnowledgeDescription

	// no validation rules for EmbeddingModel

	// no validation rules for QaExtractModel

	// no validation rules for UpdateTime

	if len(errors) > 0 {
		return KnowledgeBaseInfoMultiError(errors)
	}
	return nil
}

// KnowledgeBaseInfoMultiError is an error wrapping multiple validation errors
// returned by KnowledgeBaseInfo.ValidateAll() if the designated constraints
// aren't met.
type KnowledgeBaseInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeBaseInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeBaseInfoMultiError) AllErrors() []error { return m }

// KnowledgeBaseInfoValidationError is the validation error returned by
// KnowledgeBaseInfo.Validate if the designated constraints aren't met.
type KnowledgeBaseInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeBaseInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeBaseInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeBaseInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeBaseInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeBaseInfoValidationError) ErrorName() string {
	return "KnowledgeBaseInfoValidationError"
}

// Error satisfies the builtin error interface
func (e KnowledgeBaseInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeBaseInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeBaseInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeBaseInfoValidationError{}

// Validate checks the field values on AppBaseInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AppBaseInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppBaseInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AppBaseInfoMultiError, or
// nil if none found.
func (m *AppBaseInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AppBaseInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppBizId

	// no validation rules for AppName

	if len(errors) > 0 {
		return AppBaseInfoMultiError(errors)
	}
	return nil
}

// AppBaseInfoMultiError is an error wrapping multiple validation errors
// returned by AppBaseInfo.ValidateAll() if the designated constraints aren't met.
type AppBaseInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppBaseInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppBaseInfoMultiError) AllErrors() []error { return m }

// AppBaseInfoValidationError is the validation error returned by
// AppBaseInfo.Validate if the designated constraints aren't met.
type AppBaseInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppBaseInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppBaseInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppBaseInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppBaseInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppBaseInfoValidationError) ErrorName() string { return "AppBaseInfoValidationError" }

// Error satisfies the builtin error interface
func (e AppBaseInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppBaseInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppBaseInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppBaseInfoValidationError{}

// Validate checks the field values on UserBaseInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserBaseInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserBaseInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserBaseInfoMultiError, or
// nil if none found.
func (m *UserBaseInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UserBaseInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserBizId

	// no validation rules for UserName

	if len(errors) > 0 {
		return UserBaseInfoMultiError(errors)
	}
	return nil
}

// UserBaseInfoMultiError is an error wrapping multiple validation errors
// returned by UserBaseInfo.ValidateAll() if the designated constraints aren't met.
type UserBaseInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserBaseInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserBaseInfoMultiError) AllErrors() []error { return m }

// UserBaseInfoValidationError is the validation error returned by
// UserBaseInfo.Validate if the designated constraints aren't met.
type UserBaseInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserBaseInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserBaseInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserBaseInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserBaseInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserBaseInfoValidationError) ErrorName() string { return "UserBaseInfoValidationError" }

// Error satisfies the builtin error interface
func (e UserBaseInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserBaseInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserBaseInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserBaseInfoValidationError{}

// Validate checks the field values on KnowledgeDetailInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KnowledgeDetailInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeDetailInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KnowledgeDetailInfoMultiError, or nil if none found.
func (m *KnowledgeDetailInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeDetailInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetKnowledge()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KnowledgeDetailInfoValidationError{
					field:  "Knowledge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KnowledgeDetailInfoValidationError{
					field:  "Knowledge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKnowledge()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KnowledgeDetailInfoValidationError{
				field:  "Knowledge",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAppList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, KnowledgeDetailInfoValidationError{
						field:  fmt.Sprintf("AppList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, KnowledgeDetailInfoValidationError{
						field:  fmt.Sprintf("AppList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return KnowledgeDetailInfoValidationError{
					field:  fmt.Sprintf("AppList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KnowledgeDetailInfoValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KnowledgeDetailInfoValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KnowledgeDetailInfoValidationError{
				field:  "User",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KnowledgeDetailInfoMultiError(errors)
	}
	return nil
}

// KnowledgeDetailInfoMultiError is an error wrapping multiple validation
// errors returned by KnowledgeDetailInfo.ValidateAll() if the designated
// constraints aren't met.
type KnowledgeDetailInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeDetailInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeDetailInfoMultiError) AllErrors() []error { return m }

// KnowledgeDetailInfoValidationError is the validation error returned by
// KnowledgeDetailInfo.Validate if the designated constraints aren't met.
type KnowledgeDetailInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeDetailInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeDetailInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeDetailInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeDetailInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeDetailInfoValidationError) ErrorName() string {
	return "KnowledgeDetailInfoValidationError"
}

// Error satisfies the builtin error interface
func (e KnowledgeDetailInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeDetailInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeDetailInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeDetailInfoValidationError{}

// Validate checks the field values on ListSharedKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSharedKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSharedKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSharedKnowledgeRspMultiError, or nil if none found.
func (m *ListSharedKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSharedKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetKnowledgeList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListSharedKnowledgeRspValidationError{
						field:  fmt.Sprintf("KnowledgeList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListSharedKnowledgeRspValidationError{
						field:  fmt.Sprintf("KnowledgeList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListSharedKnowledgeRspValidationError{
					field:  fmt.Sprintf("KnowledgeList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListSharedKnowledgeRspMultiError(errors)
	}
	return nil
}

// ListSharedKnowledgeRspMultiError is an error wrapping multiple validation
// errors returned by ListSharedKnowledgeRsp.ValidateAll() if the designated
// constraints aren't met.
type ListSharedKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSharedKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSharedKnowledgeRspMultiError) AllErrors() []error { return m }

// ListSharedKnowledgeRspValidationError is the validation error returned by
// ListSharedKnowledgeRsp.Validate if the designated constraints aren't met.
type ListSharedKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSharedKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSharedKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSharedKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSharedKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSharedKnowledgeRspValidationError) ErrorName() string {
	return "ListSharedKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e ListSharedKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSharedKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSharedKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSharedKnowledgeRspValidationError{}

// Validate checks the field values on DescribeSharedKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeSharedKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeSharedKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeSharedKnowledgeReqMultiError, or nil if none found.
func (m *DescribeSharedKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeSharedKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KnowledgeBizId

	if len(errors) > 0 {
		return DescribeSharedKnowledgeReqMultiError(errors)
	}
	return nil
}

// DescribeSharedKnowledgeReqMultiError is an error wrapping multiple
// validation errors returned by DescribeSharedKnowledgeReq.ValidateAll() if
// the designated constraints aren't met.
type DescribeSharedKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeSharedKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeSharedKnowledgeReqMultiError) AllErrors() []error { return m }

// DescribeSharedKnowledgeReqValidationError is the validation error returned
// by DescribeSharedKnowledgeReq.Validate if the designated constraints aren't met.
type DescribeSharedKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeSharedKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeSharedKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeSharedKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeSharedKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeSharedKnowledgeReqValidationError) ErrorName() string {
	return "DescribeSharedKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeSharedKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeSharedKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeSharedKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeSharedKnowledgeReqValidationError{}

// Validate checks the field values on DescribeSharedKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DescribeSharedKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DescribeSharedKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DescribeSharedKnowledgeRspMultiError, or nil if none found.
func (m *DescribeSharedKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DescribeSharedKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DescribeSharedKnowledgeRspValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DescribeSharedKnowledgeRspValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DescribeSharedKnowledgeRspValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DescribeSharedKnowledgeRspMultiError(errors)
	}
	return nil
}

// DescribeSharedKnowledgeRspMultiError is an error wrapping multiple
// validation errors returned by DescribeSharedKnowledgeRsp.ValidateAll() if
// the designated constraints aren't met.
type DescribeSharedKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescribeSharedKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescribeSharedKnowledgeRspMultiError) AllErrors() []error { return m }

// DescribeSharedKnowledgeRspValidationError is the validation error returned
// by DescribeSharedKnowledgeRsp.Validate if the designated constraints aren't met.
type DescribeSharedKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescribeSharedKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescribeSharedKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescribeSharedKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescribeSharedKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescribeSharedKnowledgeRspValidationError) ErrorName() string {
	return "DescribeSharedKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e DescribeSharedKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescribeSharedKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescribeSharedKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescribeSharedKnowledgeRspValidationError{}

// Validate checks the field values on KnowledgeUpdateInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KnowledgeUpdateInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeUpdateInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KnowledgeUpdateInfoMultiError, or nil if none found.
func (m *KnowledgeUpdateInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeUpdateInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KnowledgeName

	// no validation rules for KnowledgeDescription

	// no validation rules for EmbeddingModel

	// no validation rules for QaExtractModel

	if len(errors) > 0 {
		return KnowledgeUpdateInfoMultiError(errors)
	}
	return nil
}

// KnowledgeUpdateInfoMultiError is an error wrapping multiple validation
// errors returned by KnowledgeUpdateInfo.ValidateAll() if the designated
// constraints aren't met.
type KnowledgeUpdateInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeUpdateInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeUpdateInfoMultiError) AllErrors() []error { return m }

// KnowledgeUpdateInfoValidationError is the validation error returned by
// KnowledgeUpdateInfo.Validate if the designated constraints aren't met.
type KnowledgeUpdateInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeUpdateInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeUpdateInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeUpdateInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeUpdateInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeUpdateInfoValidationError) ErrorName() string {
	return "KnowledgeUpdateInfoValidationError"
}

// Error satisfies the builtin error interface
func (e KnowledgeUpdateInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeUpdateInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeUpdateInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeUpdateInfoValidationError{}

// Validate checks the field values on UpdateSharedKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateSharedKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSharedKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateSharedKnowledgeReqMultiError, or nil if none found.
func (m *UpdateSharedKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSharedKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KnowledgeBizId

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSharedKnowledgeReqValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSharedKnowledgeReqValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSharedKnowledgeReqValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateSharedKnowledgeReqMultiError(errors)
	}
	return nil
}

// UpdateSharedKnowledgeReqMultiError is an error wrapping multiple validation
// errors returned by UpdateSharedKnowledgeReq.ValidateAll() if the designated
// constraints aren't met.
type UpdateSharedKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSharedKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSharedKnowledgeReqMultiError) AllErrors() []error { return m }

// UpdateSharedKnowledgeReqValidationError is the validation error returned by
// UpdateSharedKnowledgeReq.Validate if the designated constraints aren't met.
type UpdateSharedKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSharedKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSharedKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSharedKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSharedKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSharedKnowledgeReqValidationError) ErrorName() string {
	return "UpdateSharedKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSharedKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSharedKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSharedKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSharedKnowledgeReqValidationError{}

// Validate checks the field values on UpdateSharedKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateSharedKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSharedKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateSharedKnowledgeRspMultiError, or nil if none found.
func (m *UpdateSharedKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSharedKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KnowledgeBizId

	if len(errors) > 0 {
		return UpdateSharedKnowledgeRspMultiError(errors)
	}
	return nil
}

// UpdateSharedKnowledgeRspMultiError is an error wrapping multiple validation
// errors returned by UpdateSharedKnowledgeRsp.ValidateAll() if the designated
// constraints aren't met.
type UpdateSharedKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSharedKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSharedKnowledgeRspMultiError) AllErrors() []error { return m }

// UpdateSharedKnowledgeRspValidationError is the validation error returned by
// UpdateSharedKnowledgeRsp.Validate if the designated constraints aren't met.
type UpdateSharedKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSharedKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSharedKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSharedKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSharedKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSharedKnowledgeRspValidationError) ErrorName() string {
	return "UpdateSharedKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSharedKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSharedKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSharedKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSharedKnowledgeRspValidationError{}

// Validate checks the field values on ReferSharedKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReferSharedKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReferSharedKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReferSharedKnowledgeReqMultiError, or nil if none found.
func (m *ReferSharedKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReferSharedKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppBizId

	if len(errors) > 0 {
		return ReferSharedKnowledgeReqMultiError(errors)
	}
	return nil
}

// ReferSharedKnowledgeReqMultiError is an error wrapping multiple validation
// errors returned by ReferSharedKnowledgeReq.ValidateAll() if the designated
// constraints aren't met.
type ReferSharedKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReferSharedKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReferSharedKnowledgeReqMultiError) AllErrors() []error { return m }

// ReferSharedKnowledgeReqValidationError is the validation error returned by
// ReferSharedKnowledgeReq.Validate if the designated constraints aren't met.
type ReferSharedKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReferSharedKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReferSharedKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReferSharedKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReferSharedKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReferSharedKnowledgeReqValidationError) ErrorName() string {
	return "ReferSharedKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e ReferSharedKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReferSharedKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReferSharedKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReferSharedKnowledgeReqValidationError{}

// Validate checks the field values on ReferSharedKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReferSharedKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReferSharedKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReferSharedKnowledgeRspMultiError, or nil if none found.
func (m *ReferSharedKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *ReferSharedKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ReferSharedKnowledgeRspMultiError(errors)
	}
	return nil
}

// ReferSharedKnowledgeRspMultiError is an error wrapping multiple validation
// errors returned by ReferSharedKnowledgeRsp.ValidateAll() if the designated
// constraints aren't met.
type ReferSharedKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReferSharedKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReferSharedKnowledgeRspMultiError) AllErrors() []error { return m }

// ReferSharedKnowledgeRspValidationError is the validation error returned by
// ReferSharedKnowledgeRsp.Validate if the designated constraints aren't met.
type ReferSharedKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReferSharedKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReferSharedKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReferSharedKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReferSharedKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReferSharedKnowledgeRspValidationError) ErrorName() string {
	return "ReferSharedKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e ReferSharedKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReferSharedKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReferSharedKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReferSharedKnowledgeRspValidationError{}

// Validate checks the field values on ListReferSharedKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListReferSharedKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListReferSharedKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListReferSharedKnowledgeReqMultiError, or nil if none found.
func (m *ListReferSharedKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListReferSharedKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppBizId

	if len(errors) > 0 {
		return ListReferSharedKnowledgeReqMultiError(errors)
	}
	return nil
}

// ListReferSharedKnowledgeReqMultiError is an error wrapping multiple
// validation errors returned by ListReferSharedKnowledgeReq.ValidateAll() if
// the designated constraints aren't met.
type ListReferSharedKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListReferSharedKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListReferSharedKnowledgeReqMultiError) AllErrors() []error { return m }

// ListReferSharedKnowledgeReqValidationError is the validation error returned
// by ListReferSharedKnowledgeReq.Validate if the designated constraints
// aren't met.
type ListReferSharedKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListReferSharedKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListReferSharedKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListReferSharedKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListReferSharedKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListReferSharedKnowledgeReqValidationError) ErrorName() string {
	return "ListReferSharedKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListReferSharedKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListReferSharedKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListReferSharedKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListReferSharedKnowledgeReqValidationError{}

// Validate checks the field values on ListReferSharedKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListReferSharedKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListReferSharedKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListReferSharedKnowledgeRspMultiError, or nil if none found.
func (m *ListReferSharedKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListReferSharedKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListReferSharedKnowledgeRspValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListReferSharedKnowledgeRspValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListReferSharedKnowledgeRspValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListReferSharedKnowledgeRspMultiError(errors)
	}
	return nil
}

// ListReferSharedKnowledgeRspMultiError is an error wrapping multiple
// validation errors returned by ListReferSharedKnowledgeRsp.ValidateAll() if
// the designated constraints aren't met.
type ListReferSharedKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListReferSharedKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListReferSharedKnowledgeRspMultiError) AllErrors() []error { return m }

// ListReferSharedKnowledgeRspValidationError is the validation error returned
// by ListReferSharedKnowledgeRsp.Validate if the designated constraints
// aren't met.
type ListReferSharedKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListReferSharedKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListReferSharedKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListReferSharedKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListReferSharedKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListReferSharedKnowledgeRspValidationError) ErrorName() string {
	return "ListReferSharedKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e ListReferSharedKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListReferSharedKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListReferSharedKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListReferSharedKnowledgeRspValidationError{}

// Validate checks the field values on BatchGetSharedKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchGetSharedKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchGetSharedKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchGetSharedKnowledgeReqMultiError, or nil if none found.
func (m *BatchGetSharedKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchGetSharedKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CorpBizId

	if len(errors) > 0 {
		return BatchGetSharedKnowledgeReqMultiError(errors)
	}
	return nil
}

// BatchGetSharedKnowledgeReqMultiError is an error wrapping multiple
// validation errors returned by BatchGetSharedKnowledgeReq.ValidateAll() if
// the designated constraints aren't met.
type BatchGetSharedKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchGetSharedKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchGetSharedKnowledgeReqMultiError) AllErrors() []error { return m }

// BatchGetSharedKnowledgeReqValidationError is the validation error returned
// by BatchGetSharedKnowledgeReq.Validate if the designated constraints aren't met.
type BatchGetSharedKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchGetSharedKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchGetSharedKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchGetSharedKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchGetSharedKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchGetSharedKnowledgeReqValidationError) ErrorName() string {
	return "BatchGetSharedKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e BatchGetSharedKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchGetSharedKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchGetSharedKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchGetSharedKnowledgeReqValidationError{}

// Validate checks the field values on BatchGetSharedKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchGetSharedKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchGetSharedKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchGetSharedKnowledgeRspMultiError, or nil if none found.
func (m *BatchGetSharedKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchGetSharedKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchGetSharedKnowledgeRspValidationError{
						field:  fmt.Sprintf("InfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchGetSharedKnowledgeRspValidationError{
						field:  fmt.Sprintf("InfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchGetSharedKnowledgeRspValidationError{
					field:  fmt.Sprintf("InfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchGetSharedKnowledgeRspMultiError(errors)
	}
	return nil
}

// BatchGetSharedKnowledgeRspMultiError is an error wrapping multiple
// validation errors returned by BatchGetSharedKnowledgeRsp.ValidateAll() if
// the designated constraints aren't met.
type BatchGetSharedKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchGetSharedKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchGetSharedKnowledgeRspMultiError) AllErrors() []error { return m }

// BatchGetSharedKnowledgeRspValidationError is the validation error returned
// by BatchGetSharedKnowledgeRsp.Validate if the designated constraints aren't met.
type BatchGetSharedKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchGetSharedKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchGetSharedKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchGetSharedKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchGetSharedKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchGetSharedKnowledgeRspValidationError) ErrorName() string {
	return "BatchGetSharedKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e BatchGetSharedKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchGetSharedKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchGetSharedKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchGetSharedKnowledgeRspValidationError{}
