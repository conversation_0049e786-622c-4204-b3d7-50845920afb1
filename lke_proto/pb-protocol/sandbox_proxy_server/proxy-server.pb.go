// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        v3.6.1
// source: proxy-server.proto

package sandbox_proxy_server

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

type EchoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EchoReq) Reset() {
	*x = EchoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proxy_server_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EchoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoReq) ProtoMessage() {}

func (x *EchoReq) ProtoReflect() protoreflect.Message {
	mi := &file_proxy_server_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoReq.ProtoReflect.Descriptor instead.
func (*EchoReq) Descriptor() ([]byte, []int) {
	return file_proxy_server_proto_rawDescGZIP(), []int{0}
}

type EchoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EchoRsp) Reset() {
	*x = EchoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proxy_server_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EchoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoRsp) ProtoMessage() {}

func (x *EchoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proxy_server_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoRsp.ProtoReflect.Descriptor instead.
func (*EchoRsp) Descriptor() ([]byte, []int) {
	return file_proxy_server_proto_rawDescGZIP(), []int{1}
}

type GetSandboxViewAddressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppBizId  string `protobuf:"bytes,1,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"`
	SessionId string `protobuf:"bytes,2,opt,name=SessionId,proto3" json:"SessionId,omitempty"`
	Uin       string `protobuf:"bytes,3,opt,name=Uin,proto3" json:"Uin,omitempty"`
}

func (x *GetSandboxViewAddressReq) Reset() {
	*x = GetSandboxViewAddressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proxy_server_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSandboxViewAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSandboxViewAddressReq) ProtoMessage() {}

func (x *GetSandboxViewAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_proxy_server_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSandboxViewAddressReq.ProtoReflect.Descriptor instead.
func (*GetSandboxViewAddressReq) Descriptor() ([]byte, []int) {
	return file_proxy_server_proto_rawDescGZIP(), []int{2}
}

func (x *GetSandboxViewAddressReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *GetSandboxViewAddressReq) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *GetSandboxViewAddressReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

type GetSandboxViewAddressRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ViewAddress  string `protobuf:"bytes,1,opt,name=ViewAddress,proto3" json:"ViewAddress,omitempty"`
	IsNewAddress bool   `protobuf:"varint,2,opt,name=IsNewAddress,proto3" json:"IsNewAddress,omitempty"`
}

func (x *GetSandboxViewAddressRsp) Reset() {
	*x = GetSandboxViewAddressRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proxy_server_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSandboxViewAddressRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSandboxViewAddressRsp) ProtoMessage() {}

func (x *GetSandboxViewAddressRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proxy_server_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSandboxViewAddressRsp.ProtoReflect.Descriptor instead.
func (*GetSandboxViewAddressRsp) Descriptor() ([]byte, []int) {
	return file_proxy_server_proto_rawDescGZIP(), []int{3}
}

func (x *GetSandboxViewAddressRsp) GetViewAddress() string {
	if x != nil {
		return x.ViewAddress
	}
	return ""
}

func (x *GetSandboxViewAddressRsp) GetIsNewAddress() bool {
	if x != nil {
		return x.IsNewAddress
	}
	return false
}

var File_proxy_server_proto protoreflect.FileDescriptor

var file_proxy_server_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x73,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x22, 0x09, 0x0a, 0x07, 0x45, 0x63, 0x68, 0x6f, 0x52, 0x65, 0x71, 0x22, 0x09,
	0x0a, 0x07, 0x45, 0x63, 0x68, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x66, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x56, 0x69, 0x65, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x55, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x69,
	0x6e, 0x22, 0x60, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x56,
	0x69, 0x65, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a,
	0x0b, 0x56, 0x69, 0x65, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x56, 0x69, 0x65, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x22, 0x0a, 0x0c, 0x49, 0x73, 0x4e, 0x65, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x49, 0x73, 0x4e, 0x65, 0x77, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x32, 0xf5, 0x01, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x12, 0x58, 0x0a, 0x04, 0x45, 0x63, 0x68, 0x6f, 0x12, 0x26, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x5f, 0x70,
	0x72, 0x6f, 0x78, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x63, 0x68, 0x6f,
	0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x73,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x45, 0x63, 0x68, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x8b, 0x01,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x56, 0x69, 0x65, 0x77,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x61, 0x6e, 0x64, 0x62,
	0x6f, 0x78, 0x56, 0x69, 0x65, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x73, 0x61, 0x6e, 0x64,
	0x62, 0x6f, 0x78, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x56, 0x69, 0x65, 0x77, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x4a, 0x5a, 0x48, 0x67,
	0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f,
	0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2f, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proxy_server_proto_rawDescOnce sync.Once
	file_proxy_server_proto_rawDescData = file_proxy_server_proto_rawDesc
)

func file_proxy_server_proto_rawDescGZIP() []byte {
	file_proxy_server_proto_rawDescOnce.Do(func() {
		file_proxy_server_proto_rawDescData = protoimpl.X.CompressGZIP(file_proxy_server_proto_rawDescData)
	})
	return file_proxy_server_proto_rawDescData
}

var file_proxy_server_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_proxy_server_proto_goTypes = []interface{}{
	(*EchoReq)(nil),                  // 0: trpc.KEP.sandbox_proxy_server.EchoReq
	(*EchoRsp)(nil),                  // 1: trpc.KEP.sandbox_proxy_server.EchoRsp
	(*GetSandboxViewAddressReq)(nil), // 2: trpc.KEP.sandbox_proxy_server.GetSandboxViewAddressReq
	(*GetSandboxViewAddressRsp)(nil), // 3: trpc.KEP.sandbox_proxy_server.GetSandboxViewAddressRsp
}
var file_proxy_server_proto_depIdxs = []int32{
	0, // 0: trpc.KEP.sandbox_proxy_server.ProxyServer.Echo:input_type -> trpc.KEP.sandbox_proxy_server.EchoReq
	2, // 1: trpc.KEP.sandbox_proxy_server.ProxyServer.GetSandboxViewAddress:input_type -> trpc.KEP.sandbox_proxy_server.GetSandboxViewAddressReq
	1, // 2: trpc.KEP.sandbox_proxy_server.ProxyServer.Echo:output_type -> trpc.KEP.sandbox_proxy_server.EchoRsp
	3, // 3: trpc.KEP.sandbox_proxy_server.ProxyServer.GetSandboxViewAddress:output_type -> trpc.KEP.sandbox_proxy_server.GetSandboxViewAddressRsp
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proxy_server_proto_init() }
func file_proxy_server_proto_init() {
	if File_proxy_server_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proxy_server_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EchoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proxy_server_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EchoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proxy_server_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSandboxViewAddressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proxy_server_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSandboxViewAddressRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proxy_server_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxy_server_proto_goTypes,
		DependencyIndexes: file_proxy_server_proto_depIdxs,
		MessageInfos:      file_proxy_server_proto_msgTypes,
	}.Build()
	File_proxy_server_proto = out.File
	file_proxy_server_proto_rawDesc = nil
	file_proxy_server_proto_goTypes = nil
	file_proxy_server_proto_depIdxs = nil
}
