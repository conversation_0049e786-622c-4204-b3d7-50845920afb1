// Code generated by trpc-go/trpc-go-cmdline v2.8.31. DO NOT EDIT.
// source: proxy-server.proto

package sandbox_proxy_server

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// START ======================================= Server Service Definition ======================================= START

// ProxyServerService defines service.
type ProxyServerService interface {
	// Echo Echo 方法用于测试服务是否部署成功
	//  @alias=/Echo
	Echo(ctx context.Context, req *EchoReq) (*EchoRsp, error)
	// GetSandboxViewAddress @alias=/GetSandboxViewAddress 获取链接
	GetSandboxViewAddress(ctx context.Context, req *GetSandboxViewAddressReq) (*GetSandboxViewAddressRsp, error)
}

func ProxyServerService_Echo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &EchoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ProxyServerService).Echo(ctx, reqbody.(*EchoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ProxyServerService_GetSandboxViewAddress_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetSandboxViewAddressReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ProxyServerService).GetSandboxViewAddress(ctx, reqbody.(*GetSandboxViewAddressReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// ProxyServerServer_ServiceDesc descriptor for server.RegisterService.
var ProxyServerServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.sandbox_proxy_server.ProxyServer",
	HandlerType: ((*ProxyServerService)(nil)),
	Methods: []server.Method{
		{
			Name: "/Echo",
			Func: ProxyServerService_Echo_Handler,
		},
		{
			Name: "/GetSandboxViewAddress",
			Func: ProxyServerService_GetSandboxViewAddress_Handler,
		},
		{
			Name: "/trpc.KEP.sandbox_proxy_server.ProxyServer/Echo",
			Func: ProxyServerService_Echo_Handler,
		},
		{
			Name: "/trpc.KEP.sandbox_proxy_server.ProxyServer/GetSandboxViewAddress",
			Func: ProxyServerService_GetSandboxViewAddress_Handler,
		},
	},
}

// RegisterProxyServerService registers service.
func RegisterProxyServerService(s server.Service, svr ProxyServerService) {
	if err := s.Register(&ProxyServerServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("ProxyServer register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedProxyServer struct{}

// Echo Echo 方法用于测试服务是否部署成功
//
//	@alias=/Echo
func (s *UnimplementedProxyServer) Echo(ctx context.Context, req *EchoReq) (*EchoRsp, error) {
	return nil, errors.New("rpc Echo of service ProxyServer is not implemented")
}

// GetSandboxViewAddress @alias=/GetSandboxViewAddress 获取链接
func (s *UnimplementedProxyServer) GetSandboxViewAddress(ctx context.Context, req *GetSandboxViewAddressReq) (*GetSandboxViewAddressRsp, error) {
	return nil, errors.New("rpc GetSandboxViewAddress of service ProxyServer is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// ProxyServerClientProxy defines service client proxy
type ProxyServerClientProxy interface {
	// Echo Echo 方法用于测试服务是否部署成功
	//  @alias=/Echo
	Echo(ctx context.Context, req *EchoReq, opts ...client.Option) (rsp *EchoRsp, err error)

	// GetSandboxViewAddress @alias=/GetSandboxViewAddress 获取链接
	GetSandboxViewAddress(ctx context.Context, req *GetSandboxViewAddressReq, opts ...client.Option) (rsp *GetSandboxViewAddressRsp, err error)
}

type ProxyServerClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewProxyServerClientProxy = func(opts ...client.Option) ProxyServerClientProxy {
	return &ProxyServerClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *ProxyServerClientProxyImpl) Echo(ctx context.Context, req *EchoReq, opts ...client.Option) (*EchoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/Echo")
	msg.WithCalleeServiceName(ProxyServerServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("sandbox_proxy_server")
	msg.WithCalleeService("ProxyServer")
	msg.WithCalleeMethod("Echo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &EchoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ProxyServerClientProxyImpl) GetSandboxViewAddress(ctx context.Context, req *GetSandboxViewAddressReq, opts ...client.Option) (*GetSandboxViewAddressRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetSandboxViewAddress")
	msg.WithCalleeServiceName(ProxyServerServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("sandbox_proxy_server")
	msg.WithCalleeService("ProxyServer")
	msg.WithCalleeMethod("GetSandboxViewAddress")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetSandboxViewAddressRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
