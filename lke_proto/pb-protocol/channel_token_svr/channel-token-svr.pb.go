// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.13.0
// source: channel-token-svr.proto

package channel_token_svr

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChannelType int32

const (
	ChannelType_Channel_Type_UNKNOW ChannelType = 0
	ChannelType_Channel_Type_WX_PUB ChannelType = 10000
	ChannelType_Channel_Type_WX_APP ChannelType = 10001
	ChannelType_Channel_Type_Wecom  ChannelType = 10002
)

// Enum value maps for ChannelType.
var (
	ChannelType_name = map[int32]string{
		0:     "Channel_Type_UNKNOW",
		10000: "Channel_Type_WX_PUB",
		10001: "Channel_Type_WX_APP",
		10002: "Channel_Type_Wecom",
	}
	ChannelType_value = map[string]int32{
		"Channel_Type_UNKNOW": 0,
		"Channel_Type_WX_PUB": 10000,
		"Channel_Type_WX_APP": 10001,
		"Channel_Type_Wecom":  10002,
	}
)

func (x ChannelType) Enum() *ChannelType {
	p := new(ChannelType)
	*p = x
	return p
}

func (x ChannelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChannelType) Descriptor() protoreflect.EnumDescriptor {
	return file_channel_token_svr_proto_enumTypes[0].Descriptor()
}

func (ChannelType) Type() protoreflect.EnumType {
	return &file_channel_token_svr_proto_enumTypes[0]
}

func (x ChannelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChannelType.Descriptor instead.
func (ChannelType) EnumDescriptor() ([]byte, []int) {
	return file_channel_token_svr_proto_rawDescGZIP(), []int{0}
}

type GetChannelTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//渠道类型
	ChannelType uint64 `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	// 渠道应用id
	ChannelAppId string `protobuf:"bytes,2,opt,name=channel_app_id,json=channelAppId,proto3" json:"channel_app_id,omitempty"`
	// 渠道企业id
	ChannelCorpId string `protobuf:"bytes,3,opt,name=channel_corp_id,json=channelCorpId,proto3" json:"channel_corp_id,omitempty"`
	// 应用ID
	AppBizId uint64 `protobuf:"varint,4,opt,name=app_biz_id,json=appBizId,proto3" json:"app_biz_id,omitempty"`
}

func (x *GetChannelTokenReq) Reset() {
	*x = GetChannelTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_token_svr_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelTokenReq) ProtoMessage() {}

func (x *GetChannelTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_channel_token_svr_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelTokenReq.ProtoReflect.Descriptor instead.
func (*GetChannelTokenReq) Descriptor() ([]byte, []int) {
	return file_channel_token_svr_proto_rawDescGZIP(), []int{0}
}

func (x *GetChannelTokenReq) GetChannelType() uint64 {
	if x != nil {
		return x.ChannelType
	}
	return 0
}

func (x *GetChannelTokenReq) GetChannelAppId() string {
	if x != nil {
		return x.ChannelAppId
	}
	return ""
}

func (x *GetChannelTokenReq) GetChannelCorpId() string {
	if x != nil {
		return x.ChannelCorpId
	}
	return ""
}

func (x *GetChannelTokenReq) GetAppBizId() uint64 {
	if x != nil {
		return x.AppBizId
	}
	return 0
}

type GetChannelTokenRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 企业ID
	CorpBizId uint64 `protobuf:"varint,1,opt,name=corp_biz_id,json=corpBizId,proto3" json:"corp_biz_id,omitempty"`
	// 应用ID
	AppBizId uint64 `protobuf:"varint,2,opt,name=app_biz_id,json=appBizId,proto3" json:"app_biz_id,omitempty"`
	// token
	AccessToken string `protobuf:"bytes,3,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	// 过期时间
	ExpireTime uint64 `protobuf:"varint,4,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
}

func (x *GetChannelTokenRsp) Reset() {
	*x = GetChannelTokenRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_token_svr_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelTokenRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelTokenRsp) ProtoMessage() {}

func (x *GetChannelTokenRsp) ProtoReflect() protoreflect.Message {
	mi := &file_channel_token_svr_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelTokenRsp.ProtoReflect.Descriptor instead.
func (*GetChannelTokenRsp) Descriptor() ([]byte, []int) {
	return file_channel_token_svr_proto_rawDescGZIP(), []int{1}
}

func (x *GetChannelTokenRsp) GetCorpBizId() uint64 {
	if x != nil {
		return x.CorpBizId
	}
	return 0
}

func (x *GetChannelTokenRsp) GetAppBizId() uint64 {
	if x != nil {
		return x.AppBizId
	}
	return 0
}

func (x *GetChannelTokenRsp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *GetChannelTokenRsp) GetExpireTime() uint64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

type RefreshChannelTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道类型
	ChannelType uint64 `protobuf:"varint,1,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	// 企业ID
	CorpBizId uint64 `protobuf:"varint,2,opt,name=corp_biz_id,json=corpBizId,proto3" json:"corp_biz_id,omitempty"`
	// 应用ID
	AppBizId   uint64      `protobuf:"varint,3,opt,name=app_biz_id,json=appBizId,proto3" json:"app_biz_id,omitempty"`
	WechatInfo *WechatInfo `protobuf:"bytes,4,opt,name=wechat_info,json=wechatInfo,proto3" json:"wechat_info,omitempty"`
	WecomInfo  *WecomInfo  `protobuf:"bytes,5,opt,name=wecom_info,json=wecomInfo,proto3" json:"wecom_info,omitempty"`
}

func (x *RefreshChannelTokenReq) Reset() {
	*x = RefreshChannelTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_token_svr_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshChannelTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshChannelTokenReq) ProtoMessage() {}

func (x *RefreshChannelTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_channel_token_svr_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshChannelTokenReq.ProtoReflect.Descriptor instead.
func (*RefreshChannelTokenReq) Descriptor() ([]byte, []int) {
	return file_channel_token_svr_proto_rawDescGZIP(), []int{2}
}

func (x *RefreshChannelTokenReq) GetChannelType() uint64 {
	if x != nil {
		return x.ChannelType
	}
	return 0
}

func (x *RefreshChannelTokenReq) GetCorpBizId() uint64 {
	if x != nil {
		return x.CorpBizId
	}
	return 0
}

func (x *RefreshChannelTokenReq) GetAppBizId() uint64 {
	if x != nil {
		return x.AppBizId
	}
	return 0
}

func (x *RefreshChannelTokenReq) GetWechatInfo() *WechatInfo {
	if x != nil {
		return x.WechatInfo
	}
	return nil
}

func (x *RefreshChannelTokenReq) GetWecomInfo() *WecomInfo {
	if x != nil {
		return x.WecomInfo
	}
	return nil
}

type WechatInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefreshToken string `protobuf:"bytes,1,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"` // 刷新token
	AppId        string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                      // 公众号id
}

func (x *WechatInfo) Reset() {
	*x = WechatInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_token_svr_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatInfo) ProtoMessage() {}

func (x *WechatInfo) ProtoReflect() protoreflect.Message {
	mi := &file_channel_token_svr_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatInfo.ProtoReflect.Descriptor instead.
func (*WechatInfo) Descriptor() ([]byte, []int) {
	return file_channel_token_svr_proto_rawDescGZIP(), []int{3}
}

func (x *WechatInfo) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *WechatInfo) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

type WecomInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CorpId         string `protobuf:"bytes,1,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`                           // 企业id
	AgentId        uint64 `protobuf:"varint,2,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`                       // 企微应用id
	AgentSecret    string `protobuf:"bytes,3,opt,name=agent_secret,json=agentSecret,proto3" json:"agent_secret,omitempty"`            // 企业微信应用secret
	CallbackToken  string `protobuf:"bytes,4,opt,name=callback_token,json=callbackToken,proto3" json:"callback_token,omitempty"`      // 企微回调token
	CallbackAesKey string `protobuf:"bytes,5,opt,name=callback_aes_key,json=callbackAesKey,proto3" json:"callback_aes_key,omitempty"` // 企微回调secret
}

func (x *WecomInfo) Reset() {
	*x = WecomInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_token_svr_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WecomInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WecomInfo) ProtoMessage() {}

func (x *WecomInfo) ProtoReflect() protoreflect.Message {
	mi := &file_channel_token_svr_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WecomInfo.ProtoReflect.Descriptor instead.
func (*WecomInfo) Descriptor() ([]byte, []int) {
	return file_channel_token_svr_proto_rawDescGZIP(), []int{4}
}

func (x *WecomInfo) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *WecomInfo) GetAgentId() uint64 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *WecomInfo) GetAgentSecret() string {
	if x != nil {
		return x.AgentSecret
	}
	return ""
}

func (x *WecomInfo) GetCallbackToken() string {
	if x != nil {
		return x.CallbackToken
	}
	return ""
}

func (x *WecomInfo) GetCallbackAesKey() string {
	if x != nil {
		return x.CallbackAesKey
	}
	return ""
}

type RefreshChannelTokenRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RefreshChannelTokenRsp) Reset() {
	*x = RefreshChannelTokenRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_token_svr_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshChannelTokenRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshChannelTokenRsp) ProtoMessage() {}

func (x *RefreshChannelTokenRsp) ProtoReflect() protoreflect.Message {
	mi := &file_channel_token_svr_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshChannelTokenRsp.ProtoReflect.Descriptor instead.
func (*RefreshChannelTokenRsp) Descriptor() ([]byte, []int) {
	return file_channel_token_svr_proto_rawDescGZIP(), []int{5}
}

type GetWechatPlatformInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetWechatPlatformInfoReq) Reset() {
	*x = GetWechatPlatformInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_token_svr_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWechatPlatformInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWechatPlatformInfoReq) ProtoMessage() {}

func (x *GetWechatPlatformInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_channel_token_svr_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWechatPlatformInfoReq.ProtoReflect.Descriptor instead.
func (*GetWechatPlatformInfoReq) Descriptor() ([]byte, []int) {
	return file_channel_token_svr_proto_rawDescGZIP(), []int{6}
}

type GetWechatPlatformInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// token
	AccessToken string `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	// 过期时间
	ExpireTime uint64 `protobuf:"varint,2,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	// 微信平台id
	ComponentAppId string `protobuf:"bytes,3,opt,name=component_app_id,json=componentAppId,proto3" json:"component_app_id,omitempty"`
	ComponentToken string `protobuf:"bytes,4,opt,name=component_token,json=componentToken,proto3" json:"component_token,omitempty"`
	EncodingAesKey string `protobuf:"bytes,5,opt,name=encoding_aes_key,json=encodingAesKey,proto3" json:"encoding_aes_key,omitempty"`
}

func (x *GetWechatPlatformInfoRsp) Reset() {
	*x = GetWechatPlatformInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_token_svr_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWechatPlatformInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWechatPlatformInfoRsp) ProtoMessage() {}

func (x *GetWechatPlatformInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_channel_token_svr_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWechatPlatformInfoRsp.ProtoReflect.Descriptor instead.
func (*GetWechatPlatformInfoRsp) Descriptor() ([]byte, []int) {
	return file_channel_token_svr_proto_rawDescGZIP(), []int{7}
}

func (x *GetWechatPlatformInfoRsp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *GetWechatPlatformInfoRsp) GetExpireTime() uint64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *GetWechatPlatformInfoRsp) GetComponentAppId() string {
	if x != nil {
		return x.ComponentAppId
	}
	return ""
}

func (x *GetWechatPlatformInfoRsp) GetComponentToken() string {
	if x != nil {
		return x.ComponentToken
	}
	return ""
}

func (x *GetWechatPlatformInfoRsp) GetEncodingAesKey() string {
	if x != nil {
		return x.EncodingAesKey
	}
	return ""
}

var File_channel_token_svr_proto protoreflect.FileDescriptor

var file_channel_token_svr_proto_rawDesc = []byte{
	0x0a, 0x17, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2d, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x2d,
	0x73, 0x76, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x73, 0x76, 0x72, 0x22, 0xa3, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x24, 0x0a, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x72, 0x70, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x0a, 0x61, 0x70, 0x70, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x61, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x96, 0x01, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x6f, 0x72, 0x70, 0x42, 0x69, 0x7a,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x61, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0x88, 0x02, 0x0a, 0x16, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x6f, 0x72, 0x70, 0x42, 0x69, 0x7a,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x61, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64,
	0x12, 0x47, 0x0a, 0x0b, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73,
	0x76, 0x72, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x77,
	0x65, 0x63, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x44, 0x0a, 0x0a, 0x77, 0x65, 0x63,
	0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x57, 0x65, 0x63, 0x6f, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x77, 0x65, 0x63, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x48, 0x0a, 0x0a, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x22, 0xb3, 0x01, 0x0a, 0x09, 0x57, 0x65,
	0x63, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x5f, 0x61, 0x65, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x41, 0x65, 0x73, 0x4b, 0x65, 0x79, 0x22,
	0x18, 0x0a, 0x16, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x1a, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x22, 0xdb, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x57, 0x65, 0x63,
	0x68, 0x61, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x73, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x6e, 0x63,
	0x6f, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x65, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x65, 0x73,
	0x4b, 0x65, 0x79, 0x2a, 0x73, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x13, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x57, 0x58, 0x5f, 0x50,
	0x55, 0x42, 0x10, 0x90, 0x4e, 0x12, 0x18, 0x0a, 0x13, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x57, 0x58, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x91, 0x4e, 0x12,
	0x17, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x57, 0x65, 0x63, 0x6f, 0x6d, 0x10, 0x92, 0x4e, 0x32, 0x93, 0x03, 0x0a, 0x13, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x73, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x76, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x76, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x85, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x57, 0x65, 0x63,
	0x68, 0x61, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73,
	0x76, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x7f, 0x0a,
	0x13, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x76,
	0x72, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x47,
	0x5a, 0x45, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69,
	0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x76, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_channel_token_svr_proto_rawDescOnce sync.Once
	file_channel_token_svr_proto_rawDescData = file_channel_token_svr_proto_rawDesc
)

func file_channel_token_svr_proto_rawDescGZIP() []byte {
	file_channel_token_svr_proto_rawDescOnce.Do(func() {
		file_channel_token_svr_proto_rawDescData = protoimpl.X.CompressGZIP(file_channel_token_svr_proto_rawDescData)
	})
	return file_channel_token_svr_proto_rawDescData
}

var file_channel_token_svr_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_channel_token_svr_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_channel_token_svr_proto_goTypes = []interface{}{
	(ChannelType)(0),                 // 0: trpc.KEP.channel_token_svr.ChannelType
	(*GetChannelTokenReq)(nil),       // 1: trpc.KEP.channel_token_svr.GetChannelTokenReq
	(*GetChannelTokenRsp)(nil),       // 2: trpc.KEP.channel_token_svr.GetChannelTokenRsp
	(*RefreshChannelTokenReq)(nil),   // 3: trpc.KEP.channel_token_svr.RefreshChannelTokenReq
	(*WechatInfo)(nil),               // 4: trpc.KEP.channel_token_svr.WechatInfo
	(*WecomInfo)(nil),                // 5: trpc.KEP.channel_token_svr.WecomInfo
	(*RefreshChannelTokenRsp)(nil),   // 6: trpc.KEP.channel_token_svr.RefreshChannelTokenRsp
	(*GetWechatPlatformInfoReq)(nil), // 7: trpc.KEP.channel_token_svr.GetWechatPlatformInfoReq
	(*GetWechatPlatformInfoRsp)(nil), // 8: trpc.KEP.channel_token_svr.GetWechatPlatformInfoRsp
}
var file_channel_token_svr_proto_depIdxs = []int32{
	4, // 0: trpc.KEP.channel_token_svr.RefreshChannelTokenReq.wechat_info:type_name -> trpc.KEP.channel_token_svr.WechatInfo
	5, // 1: trpc.KEP.channel_token_svr.RefreshChannelTokenReq.wecom_info:type_name -> trpc.KEP.channel_token_svr.WecomInfo
	1, // 2: trpc.KEP.channel_token_svr.ChannelTokenService.GetChannelToken:input_type -> trpc.KEP.channel_token_svr.GetChannelTokenReq
	7, // 3: trpc.KEP.channel_token_svr.ChannelTokenService.GetWechatPlatformInfo:input_type -> trpc.KEP.channel_token_svr.GetWechatPlatformInfoReq
	3, // 4: trpc.KEP.channel_token_svr.ChannelTokenService.RefreshChannelToken:input_type -> trpc.KEP.channel_token_svr.RefreshChannelTokenReq
	2, // 5: trpc.KEP.channel_token_svr.ChannelTokenService.GetChannelToken:output_type -> trpc.KEP.channel_token_svr.GetChannelTokenRsp
	8, // 6: trpc.KEP.channel_token_svr.ChannelTokenService.GetWechatPlatformInfo:output_type -> trpc.KEP.channel_token_svr.GetWechatPlatformInfoRsp
	6, // 7: trpc.KEP.channel_token_svr.ChannelTokenService.RefreshChannelToken:output_type -> trpc.KEP.channel_token_svr.RefreshChannelTokenRsp
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_channel_token_svr_proto_init() }
func file_channel_token_svr_proto_init() {
	if File_channel_token_svr_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_channel_token_svr_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_token_svr_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelTokenRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_token_svr_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshChannelTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_token_svr_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_token_svr_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WecomInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_token_svr_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshChannelTokenRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_token_svr_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWechatPlatformInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_token_svr_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWechatPlatformInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_channel_token_svr_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_channel_token_svr_proto_goTypes,
		DependencyIndexes: file_channel_token_svr_proto_depIdxs,
		EnumInfos:         file_channel_token_svr_proto_enumTypes,
		MessageInfos:      file_channel_token_svr_proto_msgTypes,
	}.Build()
	File_channel_token_svr_proto = out.File
	file_channel_token_svr_proto_rawDesc = nil
	file_channel_token_svr_proto_goTypes = nil
	file_channel_token_svr_proto_depIdxs = nil
}
