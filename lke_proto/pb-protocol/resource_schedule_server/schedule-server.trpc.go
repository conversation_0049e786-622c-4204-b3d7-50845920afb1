// Code generated by trpc-go/trpc-go-cmdline v2.8.5. DO NOT EDIT.
// source: schedule-server.proto

package resource_schedule_server

import (
	"context"
	"errors"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/server"

	_ "git.code.oa.com/trpc-go/trpc-go"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
)

// START ======================================= Server Service Definition ======================================= START

// ScheduleServerService defines service.
type ScheduleServerService interface {
	// Echo Echo 方法用于测试服务是否部署成功
	//  @alias=/Echo
	Echo(ctx context.Context, req *EchoReq) (*EchoRsp, error)
	// SchedulePod SchedulePod 调度pod资源
	//  @alias=/SchedulePod
	SchedulePod(ctx context.Context, req *SchedulePodReq) (*SchedulePodRsp, error)
	// CancelPod CancelPod 释放pod资源
	//  @alias=/CancelPod
	CancelPod(ctx context.Context, req *CancelPodReq) (*CancelPodRsp, error)
	// GetScheduledPod GetScheduledPod 获取已调度的pod资源
	//  @alias=/GetScheduledPod
	GetScheduledPod(ctx context.Context, req *GetScheduledPodReq) (*GetScheduledPodRsp, error)
}

func ScheduleServerService_Echo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &EchoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ScheduleServerService).Echo(ctx, reqbody.(*EchoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ScheduleServerService_SchedulePod_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SchedulePodReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ScheduleServerService).SchedulePod(ctx, reqbody.(*SchedulePodReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ScheduleServerService_CancelPod_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CancelPodReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ScheduleServerService).CancelPod(ctx, reqbody.(*CancelPodReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ScheduleServerService_GetScheduledPod_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetScheduledPodReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ScheduleServerService).GetScheduledPod(ctx, reqbody.(*GetScheduledPodReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// ScheduleServerServer_ServiceDesc descriptor for server.RegisterService.
var ScheduleServerServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.resource_schedule_server.ScheduleServer",
	HandlerType: ((*ScheduleServerService)(nil)),
	Methods: []server.Method{
		{
			Name: "/Echo",
			Func: ScheduleServerService_Echo_Handler,
		},
		{
			Name: "/SchedulePod",
			Func: ScheduleServerService_SchedulePod_Handler,
		},
		{
			Name: "/CancelPod",
			Func: ScheduleServerService_CancelPod_Handler,
		},
		{
			Name: "/GetScheduledPod",
			Func: ScheduleServerService_GetScheduledPod_Handler,
		},
		{
			Name: "/trpc.KEP.resource_schedule_server.ScheduleServer/Echo",
			Func: ScheduleServerService_Echo_Handler,
		},
		{
			Name: "/trpc.KEP.resource_schedule_server.ScheduleServer/SchedulePod",
			Func: ScheduleServerService_SchedulePod_Handler,
		},
		{
			Name: "/trpc.KEP.resource_schedule_server.ScheduleServer/CancelPod",
			Func: ScheduleServerService_CancelPod_Handler,
		},
		{
			Name: "/trpc.KEP.resource_schedule_server.ScheduleServer/GetScheduledPod",
			Func: ScheduleServerService_GetScheduledPod_Handler,
		},
	},
}

// RegisterScheduleServerService registers service.
func RegisterScheduleServerService(s server.Service, svr ScheduleServerService) {
	if err := s.Register(&ScheduleServerServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("ScheduleServer register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedScheduleServer struct{}

// Echo Echo 方法用于测试服务是否部署成功
//
//	@alias=/Echo
func (s *UnimplementedScheduleServer) Echo(ctx context.Context, req *EchoReq) (*EchoRsp, error) {
	return nil, errors.New("rpc Echo of service ScheduleServer is not implemented")
}

// SchedulePod SchedulePod 调度pod资源
//
//	@alias=/SchedulePod
func (s *UnimplementedScheduleServer) SchedulePod(ctx context.Context, req *SchedulePodReq) (*SchedulePodRsp, error) {
	return nil, errors.New("rpc SchedulePod of service ScheduleServer is not implemented")
}

// CancelPod CancelPod 释放pod资源
//
//	@alias=/CancelPod
func (s *UnimplementedScheduleServer) CancelPod(ctx context.Context, req *CancelPodReq) (*CancelPodRsp, error) {
	return nil, errors.New("rpc CancelPod of service ScheduleServer is not implemented")
}

// GetScheduledPod GetScheduledPod 获取已调度的pod资源
//
//	@alias=/GetScheduledPod
func (s *UnimplementedScheduleServer) GetScheduledPod(ctx context.Context, req *GetScheduledPodReq) (*GetScheduledPodRsp, error) {
	return nil, errors.New("rpc GetScheduledPod of service ScheduleServer is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// ScheduleServerClientProxy defines service client proxy
type ScheduleServerClientProxy interface {
	// Echo Echo 方法用于测试服务是否部署成功
	//  @alias=/Echo
	Echo(ctx context.Context, req *EchoReq, opts ...client.Option) (rsp *EchoRsp, err error)

	// SchedulePod SchedulePod 调度pod资源
	//  @alias=/SchedulePod
	SchedulePod(ctx context.Context, req *SchedulePodReq, opts ...client.Option) (rsp *SchedulePodRsp, err error)

	// CancelPod CancelPod 释放pod资源
	//  @alias=/CancelPod
	CancelPod(ctx context.Context, req *CancelPodReq, opts ...client.Option) (rsp *CancelPodRsp, err error)

	// GetScheduledPod GetScheduledPod 获取已调度的pod资源
	//  @alias=/GetScheduledPod
	GetScheduledPod(ctx context.Context, req *GetScheduledPodReq, opts ...client.Option) (rsp *GetScheduledPodRsp, err error)
}

type ScheduleServerClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewScheduleServerClientProxy = func(opts ...client.Option) ScheduleServerClientProxy {
	return &ScheduleServerClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *ScheduleServerClientProxyImpl) Echo(ctx context.Context, req *EchoReq, opts ...client.Option) (*EchoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/Echo")
	msg.WithCalleeServiceName(ScheduleServerServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("resource_schedule_server")
	msg.WithCalleeService("ScheduleServer")
	msg.WithCalleeMethod("Echo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &EchoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ScheduleServerClientProxyImpl) SchedulePod(ctx context.Context, req *SchedulePodReq, opts ...client.Option) (*SchedulePodRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SchedulePod")
	msg.WithCalleeServiceName(ScheduleServerServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("resource_schedule_server")
	msg.WithCalleeService("ScheduleServer")
	msg.WithCalleeMethod("SchedulePod")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SchedulePodRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ScheduleServerClientProxyImpl) CancelPod(ctx context.Context, req *CancelPodReq, opts ...client.Option) (*CancelPodRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CancelPod")
	msg.WithCalleeServiceName(ScheduleServerServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("resource_schedule_server")
	msg.WithCalleeService("ScheduleServer")
	msg.WithCalleeMethod("CancelPod")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CancelPodRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ScheduleServerClientProxyImpl) GetScheduledPod(ctx context.Context, req *GetScheduledPodReq, opts ...client.Option) (*GetScheduledPodRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetScheduledPod")
	msg.WithCalleeServiceName(ScheduleServerServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("resource_schedule_server")
	msg.WithCalleeService("ScheduleServer")
	msg.WithCalleeMethod("GetScheduledPod")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetScheduledPodRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
