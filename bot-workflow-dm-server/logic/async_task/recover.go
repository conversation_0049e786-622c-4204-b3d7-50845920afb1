package async_task

import "time"

// runRecoveryLoop 运行任务恢复循环
func (s *Server) runRecoveryLoop() {
	LogAsyncTask().Infof("runRecoveryLoop start ...")
	// 启动的时候检查一次，然后隔 RecoverCheckInterval 检查，获取全部没有锁。
	s.runRecovery()

	ticker := time.NewTicker(s.cfg.RecoverCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			LogAsyncTask().Infof("runRecoveryLoop end")
			return
		case <-ticker.C:
			LogAsyncTask().Infof("runRecovery start ...")
			s.runRecovery()
		}
	}
}

// runRecovery Loop 运行任务恢复循环
func (s *Server) runRecovery() {
	// 获取失效的任务
	expiredTasks, err := s.store.ListLeaseExpired(s.ctx, time.Now(), getQueueNames(s.cfg.PriorityQueues))
	if err != nil {
		LogAsyncTask().Errorf("invoke ListLeaseExpired failed, err: %v", err)
		return
	}

	// 重新入队失效的任务。重新入队后再进行任务的执行 or 取消。
	for _, task := range expiredTasks {
		LogAsyncTask().Errorf("in runRecovery, Requeueing, taskID: %v", task.TaskID)
		if err := s.store.Requeue(s.ctx, task); err != nil {
			LogAsyncTask().Errorf("Requeue failed, task: %v, err: %v", task, err)
			return
		}
	}
}
