// Package async_task 异步任务
package async_task

import (
	"context"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/logger"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/workflow"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
)

var (
	server *Server
	// LogAsyncTask 异步任务日志
	LogAsyncTask = logger.LogAsyncTask
)

// Init 初始化
func Init() {
	cfg := Config{
		Concurrency:          config.GetMainConfig().AsyncTask.Concurrency,
		TaskCheckInterval:    time.Duration(config.GetMainConfig().AsyncTask.TaskCheckInterval) * time.Second,
		RecoverCheckInterval: time.Duration(config.GetMainConfig().AsyncTask.RecoverCheckInterval) * time.Second,
		PriorityQueues:       map[string]int{entity.DefaultQueue: 10},
		ExecFuncMap: map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{
			entity.TaskTypeWorkflowRun: workflow.DealWorkflowRunTask,
		},
		CancelFuncMap: map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{
			entity.TaskTypeWorkflowRun: workflow.CancelWorkflowRunTask,
		},
	}
	var err error
	server, err = NewServer(cfg)
	if err != nil {
		LogAsyncTask().Fatal(err)
		panic(err)
	}
	LogAsyncTask().Infof("async task server start")
	server.Run()
}

// StartTask 启动任务
func StartTask(ctx context.Context, task entity.TaskMessage) error {
	LogAsyncTask(ctx).Infof("start task: %v", task)
	return server.StartTask(ctx, task)
}

// StopTask 停止任务
func StopTask(ctx context.Context, taskID string) error {
	LogAsyncTask(ctx).Infof("stop task: %v", taskID)
	return server.StopTask(ctx, taskID)
}
