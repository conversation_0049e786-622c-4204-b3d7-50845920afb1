package async_task

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao/task_store"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
)

// Server 任务服务器
type Server struct {
	store          task_store.TaskStore
	cfg            Config
	mu             sync.RWMutex
	taskIDToCancel map[string]func()
	taskIDToTask   map[string]*entity.TaskMessage
	ctx            context.Context
	cancel         context.CancelFunc
}

// Config specifies the server's background-task processing behavior.
type Config struct {
	// Concurrency 并发数，即workers 的数量。
	Concurrency int

	// TaskCheckInterval 没有任务的时候，隔多长时间去检查是否有新的任务过来
	TaskCheckInterval time.Duration

	// RecoverCheckInterval 任务恢复检查间隔，隔多长时间检查一次是否有任务需要恢复
	RecoverCheckInterval time.Duration

	// PriorityQueues 优先队列，Key是队列名称，Value是优先级值。Key不存在或者值为0的时候，表示不处理该队列的任务。
	// 可用来排优先级，也可以过滤某些服务节点来执行任务。
	PriorityQueues map[string]int

	// ExecFuncMap 执行函数Map，Key是任务类型，Value是执行函数。
	ExecFuncMap map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult

	// CancelFuncMap 取消函数的Map，Key是任务类型，Value是取消函数。
	CancelFuncMap map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult
}

// NewServer 新建任务服务
func NewServer(cfg Config) (*Server, error) {
	if cfg.Concurrency <= 0 {
		return nil, fmt.Errorf("concurrency must be greater than 0")
	}
	// 判断时间是否合法
	if cfg.TaskCheckInterval <= 0 {
		return nil, fmt.Errorf("TaskCheckInterval must be greater than 0")
	}
	if cfg.RecoverCheckInterval <= 0 {
		return nil, fmt.Errorf("RecoverCheckInterval must be greater than 0")
	}
	if len(cfg.PriorityQueues) == 0 {
		return nil, fmt.Errorf("PriorityQueues cannot be empty")
	}

	store, err := task_store.NewStore()
	if err != nil {
		return nil, err
	}
	ctx, cancel := context.WithCancel(context.Background())
	return &Server{
		store:          store,
		cfg:            cfg,
		mu:             sync.RWMutex{},
		taskIDToCancel: make(map[string]func()),
		taskIDToTask:   make(map[string]*entity.TaskMessage),
		ctx:            ctx,
		cancel:         cancel,
	}, nil
}

// NewServerWithStore 新建任务服务
func NewServerWithStore(cfg Config, store task_store.TaskStore) (*Server, error) {
	ctx, cancel := context.WithCancel(context.Background())
	return &Server{
		store:          store,
		cfg:            cfg,
		mu:             sync.RWMutex{},
		taskIDToCancel: make(map[string]func()),
		taskIDToTask:   make(map[string]*entity.TaskMessage),
		ctx:            ctx,
		cancel:         cancel,
	}, nil
}

// Run 运行任务服务
func (s *Server) Run() {
	// 启动任务恢复函数
	go s.runRecoveryLoop()

	// 监听取消任务的队列
	go s.runCancellationListener()

	// 启动workers来处理任务
	for i := 0; i < s.cfg.Concurrency; i++ {
		go s.startWorker()
	}
}

// StartTask 启动任务
func (s *Server) StartTask(ctx context.Context, task entity.TaskMessage) error {
	existed, err := s.store.IsTaskExisted(ctx, task.TaskID)
	if err != nil {
		return err
	}
	if existed {
		return fmt.Errorf("task(%v) is existed", task.TaskID)
	}

	// 保存任务，把任务ID写入到Pending队列中。
	return s.store.Enqueue(ctx, &task)
}

// StopTask 停止任务
func (s *Server) StopTask(ctx context.Context, taskID string) error {
	// 兼容任务ID不存在的情况
	existed, err := s.store.IsTaskExisted(ctx, taskID)
	if err != nil {
		return err
	}
	if !existed {
		// 执行取消任务函数
		task := &entity.TaskMessage{
			TaskID:  taskID,
			Type:    entity.TaskTypeWorkflowRun,
			TraceID: fmt.Sprintf("not-found-task.%v", taskID),
		}
		if cancelFunc, ok := s.getCancelFunc(entity.TaskTypeWorkflowRun); ok {
			taskResult := cancelFunc(ctx, *task)
			if taskResult.Status != entity.TaskStatusSuccess {
				LogAsyncTask(ctx).Errorf("invoke cancelFunc failed, err: %v", taskResult.Message)
				return fmt.Errorf("%v", taskResult.Message)
			}
		}
		return nil
	}

	taskAction, err := s.store.GetTaskAction(ctx, taskID)
	if err != nil {
		return err
	}
	if taskAction == entity.TaskActionCancel {
		LogAsyncTask(ctx).Warnf("task(%v) is canceled", taskID)
		return nil
	}

	// 更新任务状态，把任务发布到订阅队列。
	err = s.store.SetTaskAction(ctx, taskID, entity.TaskActionCancel)
	if err != nil {
		LogAsyncTask(ctx).Errorf("SetTaskAction failed, taskID: %v, err: %v", taskID, err)
		return err
	}
	err = s.store.PublishCancellation(ctx, taskID)
	if err != nil {
		LogAsyncTask(ctx).Errorf("PublishCancellation failed, taskID: %v, err: %v", taskID, err)
		return err
	}
	return nil
}

// Shutdown 关闭服务器
func (s *Server) Shutdown() {
	s.cancel()
	// TODO 把所有执行中的任务重新入队。
}

// setTaskCancelFunc 设置任务的取消函数
func (s *Server) setTaskCancelFunc(taskID string, cancelFunc func()) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.taskIDToCancel[taskID] = cancelFunc
}

// getTaskCancelFunc 获取任务的取消函数
func (s *Server) getTaskCancelFunc(taskID string) (func(), bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	cancelFunc, exists := s.taskIDToCancel[taskID]
	return cancelFunc, exists
}

// removeTaskCancelFunc 移除任务的取消函数
func (s *Server) removeTaskCancelFunc(taskID string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	delete(s.taskIDToCancel, taskID)
}

// setTaskMessage 设置任务消息
func (s *Server) setTaskMessage(taskID string, taskMsg *entity.TaskMessage) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.taskIDToTask[taskID] = taskMsg
}

// getTaskMessage 获取任务消息
func (s *Server) getTaskMessage(taskID string) (*entity.TaskMessage, bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	taskMsg, exists := s.taskIDToTask[taskID]
	return taskMsg, exists
}

// removeTaskMessage 移除任务消息
func (s *Server) removeTaskMessage(taskID string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	delete(s.taskIDToTask, taskID)
}

// getExecFunc 获取任务的执行函数
func (s *Server) getExecFunc(taskType entity.TaskType) (func(context.Context, entity.TaskMessage) *entity.TaskResult,
	bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	execFunc, exists := s.cfg.ExecFuncMap[taskType]
	return execFunc, exists
}

// getCancelFunc 获取任务的取消函数
func (s *Server) getCancelFunc(taskType entity.TaskType) (func(context.Context, entity.TaskMessage) *entity.TaskResult,
	bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	cancelFunc, exists := s.cfg.CancelFuncMap[taskType]
	return cancelFunc, exists
}

func (s *Server) clearTask(queue string, taskID string) {
	s.removeTaskCancelFunc(taskID)
	s.removeTaskMessage(taskID)
	err := s.store.Done(s.ctx, queue, taskID)
	if err != nil {
		LogAsyncTask().Errorf("clear task failed, queue: %v, taskID: %v, err: %v", queue, taskID, err)
	}
}
