package async_task

import (
	"context"
	"sync"
	"testing"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockStore 模拟任务存储
type MockStore struct {
	mock.Mock
}

func (m *MockStore) GetUinTaskCount(ctx context.Context, uin string) (int32, error) {
	panic("implement me")
}

func (m *MockStore) GetAllUinTaskCounts(ctx context.Context) (map[string]int32, error) {
	panic("implement me")
}

func (m *MockStore) Enqueue(ctx context.Context, msg *entity.TaskMessage) error {
	args := m.Called(ctx, msg)
	return args.Error(0)
}

func (m *MockStore) Dequeue(ctx context.Context, qNames []string) (*entity.TaskMessage, error) {
	args := m.Called(ctx, qNames)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.TaskMessage), args.Error(1)
}

func (m *MockStore) Requeue(ctx context.Context, msg *entity.TaskMessage) error {
	args := m.Called(ctx, msg)
	return args.Error(0)
}

func (m *MockStore) ListLeaseExpired(ctx context.Context, endTime time.Time, qNames []string) ([]*entity.TaskMessage, error) {
	args := m.Called(ctx, endTime, qNames)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*entity.TaskMessage), args.Error(1)
}

func (m *MockStore) ExtendLease(ctx context.Context, qName string, taskIDs []string) error {
	args := m.Called(ctx, qName, taskIDs)
	return args.Error(0)
}

func (m *MockStore) CancellationPubSub(ctx context.Context) (*redis.PubSub, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*redis.PubSub), args.Error(1)
}

func (m *MockStore) PublishCancellation(ctx context.Context, taskID string) error {
	args := m.Called(ctx, taskID)
	return args.Error(0)
}

func (m *MockStore) IsTaskExpired(ctx context.Context, queue string, taskID string) (bool, error) {
	args := m.Called(ctx, queue, taskID)
	return args.Bool(0), args.Error(1)
}

func (m *MockStore) SetTaskAction(ctx context.Context, taskID string, action entity.TaskAction) error {
	args := m.Called(ctx, taskID, action)
	return args.Error(0)
}

func (m *MockStore) GetTaskAction(ctx context.Context, taskID string) (entity.TaskAction, error) {
	args := m.Called(ctx, taskID)
	return args.Get(0).(entity.TaskAction), args.Error(1)
}

func (m *MockStore) Done(ctx context.Context, queue string, taskID string) error {
	args := m.Called(ctx, queue, taskID)
	return args.Error(0)
}

func (m *MockStore) IsTaskExisted(ctx context.Context, taskID string) (bool, error) {
	args := m.Called(ctx, taskID)
	return args.Bool(0), args.Error(1)
}

// MockPubSub 模拟 Redis PubSub
type MockPubSub struct {
	mock.Mock
	ch chan *redis.Message
}

func NewMockPubSub() *MockPubSub {
	return &MockPubSub{
		ch: make(chan *redis.Message, 10),
	}
}

func (m *MockPubSub) ReceiveMessage(ctx context.Context) (*redis.Message, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*redis.Message), args.Error(1)
}

func (m *MockPubSub) Close() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockPubSub) Channel() <-chan *redis.Message {
	return m.ch
}

func TestNewServer(t *testing.T) {
	tests := []struct {
		name    string
		cfg     Config
		wantErr bool
	}{
		{
			name: "valid config",
			cfg: Config{
				Concurrency:          5,
				TaskCheckInterval:    time.Second,
				RecoverCheckInterval: time.Second,
				PriorityQueues:       map[string]int{"queue1": 1},
				ExecFuncMap:          map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
				CancelFuncMap:        map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
			},
			wantErr: false,
		},
		{
			name: "invalid concurrency",
			cfg: Config{
				Concurrency:          0,
				TaskCheckInterval:    time.Second,
				RecoverCheckInterval: time.Second,
				PriorityQueues:       map[string]int{"queue1": 1},
				ExecFuncMap:          map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
				CancelFuncMap:        map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
			},
			wantErr: true,
		},
		{
			name: "invalid intervals",
			cfg: Config{
				Concurrency:          5,
				TaskCheckInterval:    0,
				RecoverCheckInterval: 0,
				PriorityQueues:       map[string]int{"queue1": 1},
				ExecFuncMap:          map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
				CancelFuncMap:        map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server, err := NewServer(tt.cfg)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, server)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, server)
				assert.Equal(t, tt.cfg, server.cfg)
			}
		})
	}
}

func TestStartTask(t *testing.T) {
	mockStore := new(MockStore)
	server := &Server{
		store: mockStore,
		cfg: Config{
			Concurrency:          5,
			TaskCheckInterval:    time.Second,
			RecoverCheckInterval: time.Second,
			PriorityQueues:       map[string]int{"queue1": 1},
			ExecFuncMap:          map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
			CancelFuncMap:        map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
		},
		ctx: context.Background(),
	}

	task := entity.TaskMessage{
		TaskID: "test-task",
		Queue:  "queue1",
		Type:   entity.TaskTypeWorkflowRun,
	}

	mockStore.On("Enqueue", server.ctx, &task).Return(nil).Once()

	err := server.StartTask(server.ctx, task)
	assert.NoError(t, err)
	mockStore.AssertExpectations(t)
}

func TestStopTask(t *testing.T) {
	mockStore := new(MockStore)
	server := &Server{
		store: mockStore,
		cfg: Config{
			Concurrency:          5,
			TaskCheckInterval:    time.Second,
			RecoverCheckInterval: time.Second,
			PriorityQueues:       map[string]int{"queue1": 1},
			ExecFuncMap:          map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
			CancelFuncMap:        map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
		},
		ctx: context.Background(),
	}

	taskID := "test-task"

	mockStore.On("SetTaskAction", server.ctx, taskID, entity.TaskActionCancel).Return(nil).Once()
	mockStore.On("PublishCancellation", server.ctx, taskID).Return(nil).Once()

	err := server.StopTask(server.ctx, taskID)
	assert.NoError(t, err)
	mockStore.AssertExpectations(t)
}

func TestRunCancellationListener(t *testing.T) {
	mockStore := new(MockStore)
	mockPubSub := NewMockPubSub()
	ctx, cancel := context.WithCancel(context.Background())
	server := &Server{
		store: mockStore,
		cfg: Config{
			Concurrency:          5,
			TaskCheckInterval:    time.Second,
			RecoverCheckInterval: time.Second,
			PriorityQueues:       map[string]int{"queue1": 1},
			ExecFuncMap:          map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
			CancelFuncMap:        map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
		},
		ctx:            ctx,
		cancel:         cancel,
		taskIDToCancel: make(map[string]func()),
		taskIDToTask:   make(map[string]*entity.TaskMessage),
	}

	taskID := "test-task"
	task := &entity.TaskMessage{
		TaskID: taskID,
		Queue:  "queue1",
		Type:   entity.TaskTypeWorkflowRun,
	}

	// 设置任务取消函数和任务消息
	cancelCalled := false
	cancelFunc := func() {
		cancelCalled = true
	}
	server.setTaskCancelFunc(taskID, cancelFunc)
	server.setTaskMessage(taskID, task)

	// 设置取消函数
	cancelTaskFunc := func(ctx context.Context, msg entity.TaskMessage) *entity.TaskResult {
		return nil
	}
	server.cfg.CancelFuncMap[entity.TaskTypeWorkflowRun] = cancelTaskFunc

	// 模拟 PubSub 接收消息
	msg := &redis.Message{
		Payload: taskID,
	}
	mockPubSub.On("ReceiveMessage", server.ctx).Return(msg, nil).Once()
	mockPubSub.On("Close").Return(nil).Once()

	mockStore.On("CancellationPubSub", server.ctx).Return(&redis.PubSub{}, nil).Once()
	mockStore.On("Done", server.ctx, task.Queue, taskID).Return(nil).Once()

	// 运行取消监听器
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		server.runCancellationListener()
	}()

	// 等待一段时间后取消上下文
	time.Sleep(100 * time.Millisecond)
	server.cancel()
	wg.Wait()

	// 验证取消函数被调用
	assert.True(t, cancelCalled)
	mockStore.AssertExpectations(t)
	mockPubSub.AssertExpectations(t)
}

func TestStartWorker(t *testing.T) {
	mockStore := new(MockStore)
	server := &Server{
		store: mockStore,
		cfg: Config{
			Concurrency:          5,
			TaskCheckInterval:    time.Second,
			RecoverCheckInterval: time.Second,
			PriorityQueues:       map[string]int{"queue1": 1},
			ExecFuncMap:          map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
			CancelFuncMap:        map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
		},
		ctx:            context.Background(),
		taskIDToCancel: make(map[string]func()),
		taskIDToTask:   make(map[string]*entity.TaskMessage),
	}

	task := &entity.TaskMessage{
		TaskID: "test-task",
		Queue:  "queue1",
		Type:   entity.TaskTypeWorkflowRun,
	}

	// 设置执行函数
	execFuncCalled := false
	execFunc := func(ctx context.Context, msg entity.TaskMessage) *entity.TaskResult {
		execFuncCalled = true
		return nil
	}
	server.cfg.ExecFuncMap[entity.TaskTypeWorkflowRun] = execFunc

	// 模拟任务出队
	mockStore.On("Dequeue", server.ctx, []string{"queue1"}).Return(task, nil).Once()
	mockStore.On("GetTaskAction", server.ctx, task.TaskID).Return(entity.TaskActionNone, nil).Once()
	mockStore.On("Done", server.ctx, task.Queue, task.TaskID).Return(nil).Once()

	// 运行 worker
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		server.startWorker()
	}()

	// 等待一段时间后取消上下文
	time.Sleep(100 * time.Millisecond)
	server.cancel()
	wg.Wait()

	// 验证执行函数被调用
	assert.True(t, execFuncCalled)
	mockStore.AssertExpectations(t)
}

func TestRunRecovery(t *testing.T) {
	mockStore := new(MockStore)
	server := &Server{
		store: mockStore,
		cfg: Config{
			Concurrency:          5,
			TaskCheckInterval:    time.Second,
			RecoverCheckInterval: time.Second,
			PriorityQueues:       map[string]int{"queue1": 1},
			ExecFuncMap:          map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
			CancelFuncMap:        map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
		},
		ctx: context.Background(),
	}

	expiredTasks := []*entity.TaskMessage{
		{
			TaskID: "expired-task-1",
			Queue:  "queue1",
			Type:   entity.TaskTypeWorkflowRun,
		},
		{
			TaskID: "expired-task-2",
			Queue:  "queue1",
			Type:   entity.TaskTypeWorkflowRun,
		},
	}

	mockStore.On("ListLeaseExpired", server.ctx, mock.Anything, []string{"queue1"}).Return(expiredTasks, nil).Once()
	mockStore.On("Requeue", server.ctx, expiredTasks[0]).Return(nil).Once()
	mockStore.On("Requeue", server.ctx, expiredTasks[1]).Return(nil).Once()

	server.runRecovery()
	mockStore.AssertExpectations(t)
}

func TestRunRecoveryLoop(t *testing.T) {
	mockStore := new(MockStore)
	server := &Server{
		store: mockStore,
		cfg: Config{
			Concurrency:          5,
			TaskCheckInterval:    time.Second,
			RecoverCheckInterval: 100 * time.Millisecond,
			PriorityQueues:       map[string]int{"queue1": 1},
			ExecFuncMap:          map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
			CancelFuncMap:        map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{},
		},
		ctx: context.Background(),
	}

	// 模拟任务恢复
	mockStore.On("ListLeaseExpired", server.ctx, mock.Anything, []string{"queue1"}).Return([]*entity.TaskMessage{}, nil).Times(3)

	// 运行恢复循环
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		server.runRecoveryLoop()
	}()

	// 等待一段时间后取消上下文
	time.Sleep(300 * time.Millisecond)
	// server.cancel()
	wg.Wait()

	mockStore.AssertExpectations(t)
}

func TestGetExecFunc(t *testing.T) {
	server := &Server{
		cfg: Config{
			ExecFuncMap: map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{
				entity.TaskTypeWorkflowRun: func(ctx context.Context, msg entity.TaskMessage) *entity.TaskResult {
					return nil
				},
			},
		},
	}

	// 测试存在的任务类型
	execFunc, exists := server.getExecFunc(entity.TaskTypeWorkflowRun)
	assert.True(t, exists)
	assert.NotNil(t, execFunc)

	// 测试不存在的任务类型
	execFunc, exists = server.getExecFunc("non-existent-type")
	assert.False(t, exists)
	assert.Nil(t, execFunc)
}

func TestGetCancelFunc(t *testing.T) {
	server := &Server{
		cfg: Config{
			CancelFuncMap: map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{
				entity.TaskTypeWorkflowRun: func(ctx context.Context, msg entity.TaskMessage) *entity.TaskResult {
					return nil
				},
			},
		},
	}

	// 测试存在的任务类型
	cancelFunc, exists := server.getCancelFunc(entity.TaskTypeWorkflowRun)
	assert.True(t, exists)
	assert.NotNil(t, cancelFunc)

	// 测试不存在的任务类型
	cancelFunc, exists = server.getCancelFunc("non-existent-type")
	assert.False(t, exists)
	assert.Nil(t, cancelFunc)
}

func TestTaskMessageManagement(t *testing.T) {
	server := &Server{
		taskIDToTask: make(map[string]*entity.TaskMessage),
	}

	taskID := "test-task"
	task := &entity.TaskMessage{
		TaskID: taskID,
		Queue:  "queue1",
		Type:   entity.TaskTypeWorkflowRun,
	}

	// 测试设置任务消息
	server.setTaskMessage(taskID, task)
	assert.Equal(t, task, server.taskIDToTask[taskID])

	// 测试获取任务消息
	retrievedTask, exists := server.getTaskMessage(taskID)
	assert.True(t, exists)
	assert.Equal(t, task, retrievedTask)

	// 测试获取不存在的任务消息
	retrievedTask, exists = server.getTaskMessage("non-existent-task")
	assert.False(t, exists)
	assert.Nil(t, retrievedTask)

	// 测试移除任务消息
	server.removeTaskMessage(taskID)
	_, exists = server.getTaskMessage(taskID)
	assert.False(t, exists)
}

func TestTaskCancelFuncManagement(t *testing.T) {
	server := &Server{
		taskIDToCancel: make(map[string]func()),
	}

	taskID := "test-task"
	cancelCalled := false
	cancelFunc := func() {
		cancelCalled = true
	}

	// 测试设置取消函数
	server.setTaskCancelFunc(taskID, cancelFunc)
	assert.NotNil(t, server.taskIDToCancel[taskID])

	// 测试获取取消函数
	retrievedCancelFunc, exists := server.getTaskCancelFunc(taskID)
	assert.True(t, exists)
	assert.NotNil(t, retrievedCancelFunc)

	// 测试获取不存在的取消函数
	retrievedCancelFunc, exists = server.getTaskCancelFunc("non-existent-task")
	assert.False(t, exists)
	assert.Nil(t, retrievedCancelFunc)

	// 测试移除取消函数
	server.removeTaskCancelFunc(taskID)
	_, exists = server.getTaskCancelFunc(taskID)
	assert.False(t, exists)

	cancelFunc()
	assert.True(t, cancelCalled)
}
