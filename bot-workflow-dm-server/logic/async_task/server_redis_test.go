package async_task

import (
	"context"
	"testing"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao/task_store"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/workflow"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/encoding/protojson"
)

// 创建一个自定义的 Redis 客户端，使用指定的地址
func createTestRedisClient() redis.UniversalClient {
	return redis.NewClient(&redis.Options{
		Addr:     "9.134.166.84:8081",
		DB:       0,
		Password: "shabianquan",
	})
}

// TestStartTaskWithRedis 使用真实 Redis 测试 StartTask 函数
func TestStartTaskWithRedis(t *testing.T) {
	// 创建 Redis 客户端
	client := createTestRedisClient()
	defer func() {
		if err := client.Close(); err != nil {
			t.Logf("Error closing Redis client: %v", err)
		}
	}()

	// 创建测试用的 Store
	store := task_store.NewStoreByClient(client)

	// 创建测试用的 Server
	cfg := Config{
		Concurrency:          3,
		TaskCheckInterval:    1 * time.Second,
		RecoverCheckInterval: 30 * time.Second,
		PriorityQueues:       map[string]int{entity.DefaultQueue: 10},
		ExecFuncMap: map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{
			entity.TaskTypeWorkflowRun: workflow.DealWorkflowRunTask,
		},
		CancelFuncMap: map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult{
			entity.TaskTypeWorkflowRun: workflow.CancelWorkflowRunTask,
		},
	}

	server, err := NewServerWithStore(cfg, store)
	assert.NoError(t, err)

	// // 测试兜底处理（先启动执行工作流，未运行完成的时候退出）
	// server.Run()
	// time.Sleep(time.Second * 100)

	ctx := context.Background()
	// 创建测试用的任务
	req := &KEP_WF_DM.StartWorkflowRunRequest{
		WorkflowRunID: "test-task-" + time.Now().Format("20060102150405"),
	}
	taskPayload, _ := protojson.Marshal(req)
	task := entity.TaskMessage{
		TaskID:  req.WorkflowRunID,
		Type:    entity.TaskTypeWorkflowRun,
		Queue:   entity.DefaultQueue,
		TraceID: "trace_id_XXXXXXXXXXX",
		Payload: string(taskPayload),
	}

	// 测试 StartTask 函数
	err = server.StartTask(ctx, task)
	assert.NoError(t, err)
	t.Log(task)

	// 验证任务是否已存在
	exists, err := store.IsTaskExisted(ctx, task.TaskID)
	assert.NoError(t, err)
	assert.True(t, exists)

	// 再次尝试启动同一个任务，应该返回错误
	err = server.StartTask(ctx, task)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "task("+task.TaskID+") is existed")

	server.Run()

	// 停止任务的两个时机： 1. 任务未开始执行； 2. 任务开始后才去停止。
	time.Sleep(time.Second * 10)

	// 测试 StopTask 函数
	err = server.StopTask(ctx, task.TaskID)
	assert.NoError(t, err)

	time.Sleep(time.Second)
}

// TestStopTaskWithRedis 使用真实 Redis 测试 StopTask 函数
func TestStopTaskWithRedis(t *testing.T) {
	// 创建 Redis 客户端
	client := createTestRedisClient()
	defer func() {
		if err := client.Close(); err != nil {
			t.Logf("Error closing Redis client: %v", err)
		}
	}()

	// 创建测试用的 Store
	store := task_store.NewStoreByClient(client)

	// 创建测试用的 Server
	cfg := Config{
		Concurrency:          5,
		TaskCheckInterval:    time.Second,
		RecoverCheckInterval: time.Second,
		PriorityQueues:       map[string]int{entity.DefaultQueue: 10},
		ExecFuncMap:          make(map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult),
		CancelFuncMap:        make(map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult),
	}

	server, err := NewServerWithStore(cfg, store)
	assert.NoError(t, err)

	// 创建测试用的任务
	task := entity.TaskMessage{
		TaskID:  "test-task-" + time.Now().Format("20060102150405"),
		Type:    entity.TaskTypeWorkflowRun,
		Queue:   entity.DefaultQueue,
		Payload: "test payload",
	}

	// 先启动任务
	ctx := context.Background()
	err = server.StartTask(ctx, task)
	assert.NoError(t, err)

	// 测试 StopTask 函数
	err = server.StopTask(ctx, task.TaskID)
	assert.NoError(t, err)

	// 验证任务操作是否已设置为 Cancel
	action, err := store.GetTaskAction(ctx, task.TaskID)
	assert.NoError(t, err)
	assert.Equal(t, entity.TaskActionCancel, action)

	// 再次尝试停止同一个任务，应该返回 nil
	err = server.StopTask(ctx, task.TaskID)
	assert.NoError(t, err)

	// 测试停止不存在的任务
	err = server.StopTask(ctx, "non-existent-task")
	assert.Error(t, err)
}

// TestStartAndStopTaskWithRedis 使用真实 Redis 测试 StartTask 和 StopTask 函数的组合
func TestStartAndStopTaskWithRedis(t *testing.T) {
	// 创建 Redis 客户端
	client := createTestRedisClient()
	defer func() {
		if err := client.Close(); err != nil {
			t.Logf("Error closing Redis client: %v", err)
		}
	}()

	// 创建测试用的 Store
	store := task_store.NewStoreByClient(client)

	// 创建测试用的 Server
	cfg := Config{
		Concurrency:          5,
		TaskCheckInterval:    time.Second,
		RecoverCheckInterval: time.Second,
		PriorityQueues:       map[string]int{entity.DefaultQueue: 10},
		ExecFuncMap:          make(map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult),
		CancelFuncMap:        make(map[entity.TaskType]func(ctx context.Context, message entity.TaskMessage) *entity.TaskResult),
	}

	server, err := NewServerWithStore(cfg, store)
	assert.NoError(t, err)

	// 创建多个测试用的任务
	tasks := []entity.TaskMessage{
		{
			TaskID:  "test-task-1-" + time.Now().Format("20060102150405"),
			Type:    entity.TaskTypeWorkflowRun,
			Queue:   entity.DefaultQueue,
			Payload: "test payload 1",
		},
		{
			TaskID:  "test-task-2-" + time.Now().Format("20060102150405"),
			Type:    entity.TaskTypeWorkflowRun,
			Queue:   entity.DefaultQueue,
			Payload: "test payload 2",
		},
		{
			TaskID:  "test-task-3-" + time.Now().Format("20060102150405"),
			Type:    entity.TaskTypeWorkflowRun,
			Queue:   entity.DefaultQueue,
			Payload: "test payload 3",
		},
	}

	// 启动所有任务
	ctx := context.Background()
	for _, task := range tasks {
		err = server.StartTask(ctx, task)
		assert.NoError(t, err)
	}

	// 停止部分任务
	err = server.StopTask(ctx, tasks[0].TaskID)
	assert.NoError(t, err)
	err = server.StopTask(ctx, tasks[2].TaskID)
	assert.NoError(t, err)

	// 验证任务操作是否已正确设置
	action, err := store.GetTaskAction(ctx, tasks[0].TaskID)
	assert.NoError(t, err)
	assert.Equal(t, entity.TaskActionCancel, action)

	action, err = store.GetTaskAction(ctx, tasks[1].TaskID)
	assert.NoError(t, err)
	assert.Equal(t, entity.TaskActionNone, action)

	action, err = store.GetTaskAction(ctx, tasks[2].TaskID)
	assert.NoError(t, err)
	assert.Equal(t, entity.TaskActionCancel, action)
}
