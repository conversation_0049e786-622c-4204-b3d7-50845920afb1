package async_task

import (
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"github.com/go-redis/redis/v8"
)

// runCancellationListener 运行取消任务监听器
func (s *Server) runCancellationListener() {
	LogAsyncTask().Infof("runCancellationListener start ...")
	defer LogAsyncTask().Infof("runCancellationListener Done")
	// 监听删除任务的队列，获取到对应的任务ID，更新任务状态并调用对应的cancel函数来取消任务。

	var pubSub *redis.PubSub
	var err error

	// Try until successfully connect to Redis.
	for {
		pubSub, err = s.store.CancellationPubSub(s.ctx)
		if err != nil {
			LogAsyncTask().Errorf("get CancellationPubSub failed, err: %v", err)
			select {
			case <-time.After(time.Second):
				continue
			case <-s.ctx.Done():
				return
			}
		}
		break
	}
	defer func() {
		_ = pubSub.Close()
	}()
	cancelCh := pubSub.Channel()
	// TODO redis断开连接后恢复，需要保证运行还正常。
	for {
		select {
		case <-s.ctx.Done():
			return
		case msg := <-cancelCh:
			if msg == nil {
				break
			}
			taskID := msg.Payload
			cancel, ok := s.getTaskCancelFunc(taskID)
			task, ok2 := s.getTaskMessage(taskID)
			if !ok || !ok2 {
				break
			}

			ctx := util.SetTraceID(s.ctx, task.TraceID)
			LogAsyncTask(ctx).Infof("task existed in this server, canceling task: %v", task)

			// 取消任务的context
			cancel()
			// 执行取消任务函数
			if cancelFunc, ok := s.getCancelFunc(task.Type); ok {
				taskResult := cancelFunc(ctx, *task)
				if taskResult.Status != entity.TaskStatusSuccess {
					LogAsyncTask(ctx).Errorf("invoke cancelFunc failed, err: %v", err)
					break
				}
			}
			s.clearTask(task.Queue, taskID)
			LogAsyncTask(ctx).Infof("task is canceled, taskID: %v", task.TaskID)
		}
	}
}
