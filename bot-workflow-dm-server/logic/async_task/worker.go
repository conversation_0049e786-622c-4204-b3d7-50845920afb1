package async_task

import (
	"context"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao/task_store"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
)

// startWorker 启动worker处理任务
func (s *Server) startWorker() {
	LogAsyncTask().Infof("worker start ...")
	// 从任务Pending队列中获取任务，并且更新任务状态并执行。如果没有任务，则休眠 TaskCheckInterval 再从队列中读取。

	// 执行任务流程：
	// 1. 如果任务已经被别的worker执行，那么就直接退出；
	// 2. 如果任务已经标志为Stopping，就更新工作流运行实例的状态，更新对应的redis存储；
	// 3. 设置cancel函数到taskIDToCancel中，如果cancel被调用，先停止正在执行的任务，更新工作流运行实例状态，更新对应的redis存储；
	// 4. 执行任务，同时定期地给正在进行任务续期Lease；
	// 5. 如果任务执行完成（成功或者失败），更新工作流运行实例状态，更新对应的redis存储，清除对应的存储。
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			qNames := getQueueNames(s.cfg.PriorityQueues)
			// 从队列中获取任务
			task, err := s.store.Dequeue(s.ctx, qNames)
			if err != nil {
				LogAsyncTask().Errorf("Dequeue failed, qNames: %v, err: %v", qNames, err)
				time.Sleep(s.cfg.TaskCheckInterval)
				break
			}
			if task == nil {
				time.Sleep(s.cfg.TaskCheckInterval)
				break
			}

			taskAction, err := s.store.GetTaskAction(s.ctx, task.TaskID)
			if err != nil {
				// 获取任务动作失败，只能丢给兜底处理
				LogAsyncTask().Errorf("GetTaskAction failed, taskID: %v, err: %v", task.TaskID, err)
				break
			}

			ctx := util.SetTraceID(s.ctx, task.TraceID)
			LogAsyncTask(ctx).Infof("get task from queue, taskAction: %v; task: %v", taskAction, task)

			// 检查任务是否已经被取消
			if taskAction == entity.TaskActionCancel {
				LogAsyncTask(ctx).Infof("canceling task: %v", task)

				// 执行取消任务
				if cancelFunc, ok := s.getCancelFunc(task.Type); ok {
					taskResult := cancelFunc(ctx, *task)
					if taskResult.Status != entity.TaskStatusSuccess {
						LogAsyncTask(ctx).Errorf("invoke cancelFunc failed, taskID: %v, err: %v", task.TaskID, err)
						break
					}
				}
				s.clearTask(task.Queue, task.TaskID)
				LogAsyncTask(ctx).Infof("canceled taskID: %v", task.TaskID)
				break
			}

			// 创建任务上下文
			LogAsyncTask(ctx).Infof("executing task: %v", task)
			taskCtx, taskCancel := context.WithCancel(ctx)
			s.setTaskCancelFunc(task.TaskID, taskCancel)
			s.setTaskMessage(task.TaskID, task)

			// 启动租约续期
			go s.extendLeaseLoop(taskCtx, task.Queue, task.TaskID)

			// 执行任务
			var taskResult *entity.TaskResult
			if execFunc, ok := s.getExecFunc(task.Type); ok {
				taskResult = execFunc(taskCtx, *task)
				if taskResult.Status != entity.TaskStatusSuccess && taskResult.Status != entity.TaskStatusCancelled {
					LogAsyncTask(ctx).Errorf("invoke execFunc failed, exit, taskID: %v, taskResult: %v", task.TaskID, taskResult)
					// 暂时不入队，避免死循环 // 任务执行失败，重新入队
					// err2 := s.store.Requeue(s.ctx, task)
					// if err2 != nil {
					//	LogAsyncTask().Errorf("Requeue failed, taskID: %v, err: %v", task.TaskID, err)
					// }
					taskCancel()
					break
				}
			}

			taskCancel()
			// 清理任务
			if taskResult.Status == entity.TaskStatusSuccess {
				s.clearTask(task.Queue, task.TaskID)
			}
			LogAsyncTask(ctx).Infof("task completed, taskID: %v", task.TaskID)
		}
	}
}

func getQueueNames(queues map[string]int) []string {
	qNames := make([]string, 0, len(queues))
	for qName := range queues {
		qNames = append(qNames, qName)
	}
	return qNames
}

// extendLeaseLoop 续期任务租约
func (s *Server) extendLeaseLoop(ctx context.Context, queue string, taskID string) {
	if err := s.store.ExtendLease(ctx, queue, []string{taskID}); err != nil {
		LogAsyncTask(ctx).Errorf("ExtendLease failed, taskID: %v, err: %v", taskID, err)
	}
	ticker := time.NewTicker(task_store.LeaseDuration / 2)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			if err := s.store.ExtendLease(ctx, queue, []string{taskID}); err != nil {
				LogAsyncTask(ctx).Errorf("ExtendLease failed, taskID: %v, err: %v", taskID, err)
			}
		}
	}
}
