package workflow

import (
	"context"
	"fmt"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// executeLLMNode 执行大模型节点，输出结构{"output":"string"}，模型返回结果
var executeLLMNode = func(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	start := time.Now()
	var statistic []*KEP_WF_DM.StatisticInfo
	// 节点状态初始化
	nodeResult := entity.NodeResult{
		BelongNodeID: nodeTask.BelongNodeID,
		NodeID:       node.GetNodeID(),
	}
	defer func() {
		nodeResult.CostMilliSeconds = time.Since(start).Milliseconds()
		nodeResult.StatisticInfo = statistic
		nodeResultQueue <- nodeResult
	}()
	// 参数初始化
	data := node.GetLLMNodeData()
	if data == nil {
		LogWorkflow(ctx).Errorf("invalid node data")
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.ErrMissingParam.Msg
		nodeResult.ErrorCode = entity.ErrMissingParam.Code
		return
	}
	// 解析prompt中的参数引用
	prompt, inputResult, _, err := fillContent(session, nodeTask.BelongNodeID, data.GetPrompt(), node.GetInputs())
	if err != nil {
		LogWorkflow(ctx).Errorf("parseReference error: %v", err)
	}
	nodeResult.Input = util.ToJsonString(inputResult)
	// 请求大模型
	req := newLLMRequest(ctx, session.AppID, prompt, data.GetModelName(),
		convertLLMParams(data.GetTopP(), data.GetTemperature(), data.GetMaxTokens()))
	daoClient := dao.Default()
	if session.IsAsync {
		daoClient = dao.InAsyncDao()
	}
	ch, err := daoClient.Chat(ctx, session, req, trace.StepKeyDirect)
	if err != nil {
		LogWorkflow(ctx).Warnf("Chat error: %v", err)
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.GetNodeErrMsg(err)
		nodeResult.ErrorCode = entity.GetNodeErrCode(err)
		return
	}

	for {
		select {
		case <-ctx.Done():
			LogWorkflow(ctx).Warnf("LLMNode, ctx done")
			nodeResult.Status = entity.NodeStatusFailed
			nodeResult.FailMessage = entity.ErrLLMTimeout.Msg
			nodeResult.ErrorCode = entity.ErrLLMTimeout.Code
			return
		case rsp, ok := <-ch:
			if !ok {
				LogWorkflow(ctx).Errorf("LLMNode, ch unexpected closed")
				nodeResult.Status = entity.NodeStatusFailed
				nodeResult.FailMessage = entity.ErrLLMFail.Msg
				nodeResult.ErrorCode = entity.ErrLLMFail.Code
				return
			}
			if rsp.GetCode() != 0 {
				LogWorkflow(ctx).Errorf("LLMNode, rsp code: %d, message: %v", rsp.GetCode(), rsp.GetErrMsg())
				nodeResult.Status = entity.NodeStatusFailed
				nodeResult.FailMessage = entity.ErrLLMFail.Msg + ": " + rsp.GetErrMsg()
				nodeResult.ErrorCode = entity.ErrLLMFail.Code
				return
			}
			nodeResult.Status = entity.NodeStatusRunning
			nodeResult.Thought = rsp.GetMessage().GetReasoningContent()
			if nodeResult.ThoughtStartTime == 0 && len(rsp.GetMessage().GetReasoningContent()) > 0 {
				nodeResult.ThoughtStartTime = time.Now().UnixMilli()
			}
			if nodeResult.ThoughtEndTime == 0 && nodeResult.ThoughtStartTime != 0 &&
				(len(rsp.GetMessage().GetContent()) > 0 || rsp.GetFinished()) {
				nodeResult.ThoughtEndTime = time.Now().UnixMilli()
			}
			nodeResult.Output = makeLLMNodeOutput(rsp.GetMessage().GetReasoningContent(), rsp.GetMessage().GetContent())
			statistic = []*KEP_WF_DM.StatisticInfo{convertStatisticInfo(data.ModelName, rsp.GetStatisticInfo())}
			if rsp.GetFinished() { // 大模型结束
				LogWorkflow(ctx).Infof("LLMNode, rsp: %s", rsp)
				nodeResult.Status = entity.NodeStatusSuccess
				return
			}
			nodeResultQueue <- nodeResult
		}
	}
}

func makeLLMNodeOutput(thought, content string) string {
	return util.ToJsonString(entity.LLMNodeOutput{
		Thought: thought,
		Content: content,
	})
}

func convertLLMParams(topP float32, temperature float32, maxTokens int32) string {
	return fmt.Sprintf(`{"top_p":%f,"temperature":%f,"max_tokens":%d}`, topP, temperature, maxTokens)
}
