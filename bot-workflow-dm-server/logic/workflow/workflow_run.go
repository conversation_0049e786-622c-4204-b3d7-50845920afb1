package workflow

import (
	"context"
	"fmt"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao/store"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/model"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/logger"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/metrics"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"google.golang.org/protobuf/encoding/protojson"
)

// DealWorkflowRunTask 处理工作流运行任务
func DealWorkflowRunTask(ctx context.Context, task entity.TaskMessage) *entity.TaskResult {
	LogWorkflow(ctx).Infof("DealWorkflowRunTask start, task: %v", task)
	// 1. 解析任务数据
	req := &KEP_WF_DM.StartWorkflowRunRequest{}
	if err := protojson.Unmarshal([]byte(task.Payload), req); err != nil {
		return &entity.TaskResult{
			Status:  entity.TaskStatusFailed,
			Message: fmt.Sprintf("unmarshal task payload failed: %v", err),
		}
	}

	// 2. 创建工作流运行上下文。 sessionID设置为workflowRunID； traceID为调用（每次运行）的ID
	sessionID := req.WorkflowRunID
	ctx = initCtx(ctx, req.RunEnv, sessionID, req.AppID, req.MainModelName)
	workflowRunRecord, err := store.Default().GetWorkflowRunByRunID(ctx, req.WorkflowRunID)
	if err != nil {
		return &entity.TaskResult{
			Status:  entity.TaskStatusFailed,
			Message: fmt.Sprintf("get workflowRun failed: %v", err),
		}
	}
	nodeRunRecords, err := store.Default().GetNodeRunsByWorkflowRunID(ctx, workflowRunRecord.WorkflowRunID)
	if err != nil {
		return &entity.TaskResult{
			Status:  entity.TaskStatusFailed,
			Message: fmt.Sprintf("get NodeRuns failed: %v", err),
		}
	}

	if workflowRunRecord.State == model.WorkflowRunStatePending {
		now := time.Now()
		// workflowRunRecord.WorkflowJSON = "" // TODO 配置端设置，配置端可能会跟运行时不一致
		// workflowRunRecord.ProtoVersion = 1  // TODO 配置端设置
		workflowRunRecord.State = model.WorkflowRunStateRunning
		workflowRunRecord.StartTime = &now
		workflowRunRecord.UpdateTime = now
		err = store.Default().UpdateWorkflowRun(ctx, workflowRunRecord)
		if err != nil {
			return &entity.TaskResult{
				Status:  entity.TaskStatusFailed,
				Message: fmt.Sprintf("UpdateWorkflowRun failed: %v", err),
			}
		}
	}

	// 异步工作流最大运行时间12小时（2.9.0版本），节点也是。所以每个节点都需要放开超时限制，但有些节点超过一定时间是没有意义的，比如检索节点。
	// 需要超时时间的节点主要为外部依赖的，包括： LLM、代码、插件、知识问答、工具节点。
	limitTime := workflowRunRecord.StartTime.Add(time.Second * time.Duration(
		config.GetMainConfig().Workflow.AsyncExecuteTimeLimit))
	var cancel context.CancelFunc
	ctx, cancel = context.WithCancel(ctx)
	defer cancel()
	go func() {
		select {
		case <-time.After(time.Until(limitTime)):
			LogWorkflow(ctx).Warnf("DealWorkflowRunTask, timeout, workflowRunID: %v", task.TaskID)
			err = store.Default().SetWorkflowRunResult(ctx, workflowRunRecord, model.WorkflowRunStateFailed, "运行超时")
			if err != nil {
				LogWorkflow(ctx).Errorf("UpdateWorkflowRun failed, error: %v", err)
			}
			cancel()
		case <-ctx.Done():
			return
		}
	}()

	// 获取session
	session, err := store.Default().GetSession(ctx, req.RunEnv, sessionID, req.AppID, store.AllWorkflows)
	if err != nil {
		LogWorkflow(ctx).Errorf("RunWorkflow get session failed, error: %v", err)
		failMessage := fmt.Sprintf("get session failed, error: %v", err)
		err = store.Default().SetWorkflowRunResult(ctx, workflowRunRecord, model.WorkflowRunStateFailed, failMessage)
		if err != nil {
			return &entity.TaskResult{
				Status:  entity.TaskStatusFailed,
				Message: fmt.Sprintf("UpdateWorkflowRun failed: %v", err),
			}
		}
		return &entity.TaskResult{Status: entity.TaskStatusSuccess}
	}
	session.AsyncStart(req, workflowRunRecord, nodeRunRecords)
	sessionScene := dao.SceneProduct
	if session.RunEnv == KEP_WF_DM.RunEnvType_SANDBOX {
		sessionScene = dao.SceneSandbox
	}
	appInfo, err := dao.Default().GetAppInfo(ctx, util.ConvertStringToUint64(session.AppID), uint32(sessionScene))
	if err != nil {
		LogWorkflow(ctx).Errorf("DealWorkflowRunTask GetAppInfo failed, error: %v", err)
		return &entity.TaskResult{
			Status:  entity.TaskStatusFailed,
			Message: fmt.Sprintf("GetAppInfo failed: %v", err),
		}
	}
	session.AppType = appInfo.AppType
	uin, sid, err := dao.Default().GetCorpInfo(ctx, appInfo.CorpId)
	if err != nil {
		LogWorkflow(ctx).Errorf("DealWorkflowRunTask GetCorpInfo failed, error: %v", err)
		return &entity.TaskResult{
			Status:  entity.TaskStatusFailed,
			Message: fmt.Sprintf("GetCorpInfo failed: %v", err),
		}
	}
	session.Uin = uin
	session.SID = sid

	ctx = context.WithValue(ctx, tconst.SessionKey, session)

	// 3. 执行工作流
	replyCh, err := ExecuteWorkflow(ctx, session, req.WorkflowID)
	if err != nil {
		LogWorkflow(ctx).Warnf("ExecuteWorkflow failed, error: %v", err)
		failMessage := fmt.Sprintf("ExecuteWorkflow failed, error: %v", err)
		err = store.Default().SetWorkflowRunResult(ctx, workflowRunRecord, model.WorkflowRunStateFailed, failMessage)
		if err != nil {
			return &entity.TaskResult{
				Status:  entity.TaskStatusFailed,
				Message: fmt.Sprintf("UpdateWorkflowRun failed: %v", err),
			}
		}
		return &entity.TaskResult{Status: entity.TaskStatusSuccess}
	}
	for {
		select {
		case <-ctx.Done():
			LogWorkflow().Infof("DealWorkflowRunTask, task is canceled, taskID: %v", task.TaskID)
			return &entity.TaskResult{Status: entity.TaskStatusCancelled}
		case <-replyCh:
			LogWorkflow().Infof("DealWorkflowRunTask, task is success, taskID: %v", task.TaskID)
			return &entity.TaskResult{Status: entity.TaskStatusSuccess}
		}
	}
}

// CancelWorkflowRunTask 取消工作流运行任务
func CancelWorkflowRunTask(ctx context.Context, task entity.TaskMessage) *entity.TaskResult {
	LogWorkflow(ctx).Infof("CancelWorkflowRunTask start, task: %v", util.ToJsonString(task))
	// Pending状态的WorkflowRun，直接取消
	workflowRunID := task.TaskID
	workflowRunRecord, err := store.Default().GetWorkflowRunByRunID(ctx, workflowRunID)
	if err != nil {
		LogWorkflow(ctx).Errorf("CancelWorkflowRunTask, GetWorkflowRunByRunID failed, error: %v", err)
		return &entity.TaskResult{
			Status:  entity.TaskStatusFailed,
			Message: fmt.Sprintf("get workflowRun failed: %v", err),
		}
	}
	if workflowRunRecord.IsFinished() {
		LogWorkflow(ctx).Info("CancelWorkflowRunTask, workflow is finished, return")
		return &entity.TaskResult{
			Status:  entity.TaskStatusSuccess,
			Message: "工作流已经停止",
		}
	}
	// 更新workflowRun的状态
	err = store.Default().SetWorkflowRunResult(ctx, workflowRunRecord, model.WorkflowRunStateCanceled, "工作流已取消")
	if err != nil {
		LogWorkflow(ctx).Errorf("CancelWorkflowRunTask, SetWorkflowRunResult failed, error: %v", err)
		return &entity.TaskResult{
			Status:  entity.TaskStatusFailed,
			Message: fmt.Sprintf("UpdateWorkflowRun failed: %v", err),
		}
	}
	LogWorkflow(ctx).Infof("CancelWorkflowRunTask done, task: %v", task)
	return &entity.TaskResult{Status: entity.TaskStatusSuccess}
}

func initCtx(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, sessionID, appID, mainModel string) context.Context {
	ctx = logger.SetCtxSessionIDLogger(ctx, sessionID)
	ctx = metrics.WithPublicDimensions(ctx, runEnv, appID, mainModel)
	util.SetAppKey(ctx, appID)
	return ctx
}
