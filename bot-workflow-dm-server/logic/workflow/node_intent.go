package workflow

import (
	"context"
	"strconv"
	"strings"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// IntentNodeOutput 意图节点的Output
type IntentNodeOutput struct {
	IntentIndex int
	IntentName  string
}

func executeIntentNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	LogWorkflow(ctx).Infof("executeIntentNode start, nodeName: %v, nodeID: %v", node.NodeName, node.NodeID)
	start := time.Now()
	// 参数初始化
	nodeData := node.GetIntentRecognitionNodeData()
	if nodeData == nil || len(nodeData.GetIntents()) == 0 {
		LogWorkflow(ctx).Errorf("invalid node nodeData")
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.ErrMissingParam)
		return
	}
	// 解析prompt中的参数引用，err需要使用entity中的NodeErr，避免错误码、错误信息为空
	userInput, inputResult, _, err := fillContent(session, nodeTask.BelongNodeID, nodeData.GetPrompt(), node.GetInputs())
	if err != nil {
		LogWorkflow(ctx).Errorf("parseReference error: %v", err)
	}
	intentIndex, statisticInfo, err := llmIntentJudge(ctx, session, nodeData, userInput)
	if err != nil {
		LogWorkflow(ctx).Warnf("llmIntentJudge failed, error: %v", err)
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, err)
		return
	}
	if intentIndex <= 0 || intentIndex >= len(nodeData.GetIntents()) {
		intentIndex = len(nodeData.GetIntents())
	}
	output := &IntentNodeOutput{
		IntentIndex: intentIndex,
		IntentName:  nodeData.GetIntents()[intentIndex-1].GetName(),
	}
	if intentIndex == len(nodeData.GetIntents()) {
		output.IntentName = "Other"
	}
	nodeResult := entity.NodeResult{
		BelongNodeID:     nodeTask.BelongNodeID,
		NodeID:           node.GetNodeID(),
		Status:           entity.NodeStatusSuccess,
		Input:            util.ToJsonString(inputResult),
		Output:           util.ToJsonString(output),
		CostMilliSeconds: time.Since(start).Milliseconds(),
		StatisticInfo:    statisticInfo,
	}
	nodeResultQueue <- nodeResult

	LogWorkflow(ctx).Infof("executeIntentNode done, nodeName: %v", node.NodeName)
}

func llmIntentJudge(ctx context.Context, session *entity.Session, nodeData *KEP_WF.IntentRecognitionNodeData,
	userInput string) (intentIndex int, statisticInfos []*KEP_WF_DM.StatisticInfo, err error) {
	intents := nodeData.GetIntents()
	env := map[string]interface{}{
		"Intents":   intents,
		"UserInput": userInput,
	}
	prompt, err := util.ParseTemplate(config.GetMainConfig().Prompts.Intent, env)
	if err != nil {
		LogWorkflow(ctx).Errorf("ParseTemplate failed, error: %v", err)
		return -1, nil, entity.WrapNodeErr(entity.ErrSystemError, err.Error())
	}

	// 请求大模型
	llmParams := convertLLMParams(nodeData.GetTopP(), nodeData.GetTemperature(), nodeData.GetMaxTokens())
	req := newLLMRequest(ctx, session.AppID, prompt, nodeData.GetModelName(), llmParams)
	rsp, err := dao.Default().SimpleChat(ctx, session, req, trace.StepKeyNodeIntent)
	if err != nil {
		LogWorkflow(ctx).Warnf("request LLM failed, error: %v", err)
		// 这里已经wrap过一层，可直接透传，无需 entity.WrapNodeErr(entity.ErrLLMFail, err.Error())
		return -1, nil, err
	}
	if rsp.GetCode() != 0 {
		LogWorkflow(ctx).Errorf("LLMNode, rsp code: %d, message: %v", rsp.GetCode(), rsp.GetErrMsg())
		return -1, nil, entity.WrapNodeErr(entity.ErrLLMResultError, rsp.GetErrMsg())
	}

	llmResultContent := strings.Trim(rsp.GetMessage().GetContent(), "\t\n ")
	if len(llmResultContent) < 1 {
		LogWorkflow(ctx).Warnf("invalid LLM result: %v", llmResultContent)
		return -1, nil, entity.WrapNodeErr(entity.ErrLLMResultError, llmResultContent)
	}
	if llmResultContent == "其他" {
		intentIndex = len(intents)
	} else {
		intentIndex, err = strconv.Atoi(llmResultContent)
		if err != nil {
			LogWorkflow(ctx).Warnf("invalid LLM result: %v", llmResultContent)
			return -1, nil, entity.WrapNodeErr(entity.ErrLLMResultError, llmResultContent)
		}
	}
	statisticInfos = []*KEP_WF_DM.StatisticInfo{convertStatisticInfo(nodeData.GetModelName(), rsp.GetStatisticInfo())}
	return intentIndex, statisticInfos, nil
}
