package workflow

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config/custom"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// OptionCardNodeOutput 选项卡节点的结果结构
type OptionCardNodeOutput struct {
	OptionIndex   int    // 下标从1开始
	OptionContent string // 命中的选项名称
}

type llmOption struct {
	Content string
	Num     string
}

func executeOptionCardNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	LogWorkflow(ctx).Infof("executeOptionCardNode start, nodeName: %v, nodeID: %v", node.NodeName, node.NodeID)
	startTime := time.Now()
	nodeData := node.GetOptionCardNodeData()
	if nodeData == nil {
		LogWorkflow(ctx).Errorf("invalid node data")
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.ErrMissingParam)
		return
	}

	// err需要使用entity中的NodeErr，避免错误码、错误信息为空
	convertedContent, inputResult, _, err := fillContent(session, nodeTask.BelongNodeID, nodeData.GetQuestion(),
		node.GetInputs())
	if err != nil {
		LogWorkflow(ctx).Errorf("executeAnswerNode error: %v", err)
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, err)
		return
	}
	if len(convertedContent) == 0 {
		convertedContent = config.GetMainConfig().Workflow.OptionCardDefaultQuestion
	}

	// 1. 动态的： 没问过则返回询问+选项卡的内容； 问过则根据选项卡判断结果；
	// 2. 配置的： 没问过则返回询问+选项卡的内容； 问过则根据选项卡判断结果；
	optionContents := make([]string, 0)
	if nodeData.GetCardFrom() == KEP_WF.OptionCardNodeData_DYNAMIC {
		optionArrayI := inputResult[nodeData.GetDynamicOptionsRefInputName()]
		optionArray, err := util.ConvertValue(optionArrayI, KEP_WF.TypeEnum_ARRAY_STRING)
		if err == nil {
			optionContents = optionArray.([]string)
		}
		optionContents = append(optionContents, "") // 最后的“其他”
	} else {
		options := nodeData.GetOptions()
		for i := range options {
			optionContent := options[i].GetContent()
			if len(optionContent) > 0 {
				_, optionContent = util.ReplacePlaceholders(options[i].GetContent(), inputResult)
			}
			optionContents = append(optionContents, optionContent)
		}
	}
	optionContents = limitOptions(optionContents)

	workflowRun := session.GetCurWorkflowRun()
	nodeRun := workflowRun.GetNodeRun(nodeTask.BelongNodeID, node.NodeID)
	// 首次到选项卡节点时：  下发回复和选项卡的内容；选项卡节点状态设置为“等待回复中”。 如果是切换意图再回来，重新提问
	if nodeRun.Reply == "" || !isContinuous(session.QueryHistory, nodeRun.Reply) {
		result := entity.NodeResult{
			BelongNodeID:   nodeTask.BelongNodeID,
			NodeID:         node.NodeID,
			Status:         entity.NodeStatusWaitingReply,
			Input:          util.ToJsonString(inputResult),
			Reply:          convertedContent,
			OptionContents: optionContents[0 : len(optionContents)-1], // 最后的“其他”不显示
		}
		// 如果选项卡内容只有一个，则直接命中其他，节点完成
		if len(optionContents) == 1 {
			result.Status = entity.NodeStatusSuccess
			result.Reply = ""
			outputObj := &OptionCardNodeOutput{
				OptionIndex:   2,
				OptionContent: "其他",
			}
			result.Output = util.ToJsonString(outputObj)
		}
		nodeResultQueue <- result
	} else {
		// 工程先匹配，如果不相等，则调用LLM来判断
		optionIndex := -1
		statisticInfos := make([]*KEP_WF_DM.StatisticInfo, 0)
		for i, optionContent := range optionContents {
			if optionContent == session.Query {
				optionIndex = i + 1
				break
			}
		}
		if optionIndex < 0 {
			// 返回的err需要使用entity中的NodeErr，避免错误码、错误信息为空
			optionIndex, statisticInfos, err = llmOptionCard(ctx, session, nodeRun.Reply, session.Query, optionContents)
			if err != nil {
				LogWorkflow(ctx).Warnf("llmOptionCard failed, error: %v", err)
				sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, err)
				return
			}
		}
		// 二次到选项卡节点时： 拼装Prompt，调用LLM得到选项卡的识别结果，设置Output。命中其他的时候，取值为query的值
		hitOptionContent := ""
		if optionIndex <= 0 || optionIndex >= len(optionContents) {
			optionIndex = len(optionContents)
			hitOptionContent = session.Query
			if nodeData.GetCardFrom() == KEP_WF.OptionCardNodeData_DYNAMIC {
				optionIndex = 2
			}
		} else {
			session.SetHitOptionCardIndex(int32(optionIndex))
			hitOptionContent = optionContents[optionIndex-1]
			if nodeData.GetCardFrom() == KEP_WF.OptionCardNodeData_DYNAMIC {
				optionIndex = 1
			}
		}
		outputObj := &OptionCardNodeOutput{
			OptionIndex:   optionIndex,
			OptionContent: hitOptionContent,
		}
		result := entity.NodeResult{
			BelongNodeID:     nodeTask.BelongNodeID,
			NodeID:           node.NodeID,
			Status:           entity.NodeStatusSuccess,
			Input:            nodeRun.Input,
			Output:           util.ToJsonString(outputObj),
			StatisticInfo:    statisticInfos,
			CostMilliSeconds: time.Since(startTime).Milliseconds(),
		}
		nodeResultQueue <- result
	}

	LogWorkflow(ctx).Infof("executeOptionCardNode done, nodeName: %v", node.NodeName)
}

func limitOptions(options []string) []string {
	maxSize := config.GetMainConfig().OptionCard.MaxSize
	oneMaxLength := config.GetMainConfig().OptionCard.OneMaxLength
	limitedOptions := make([]string, 0)
	for i, option := range options {
		if i > maxSize {
			break
		}
		optionRunes := []rune(option)
		if oneMaxLength > 0 && len(optionRunes) > oneMaxLength {
			option = string(optionRunes[0:oneMaxLength])
		}
		limitedOptions = append(limitedOptions, option)
	}
	return limitedOptions
}

func isContinuous(history []*KEP_WF_DM.Message, reply string) bool {
	length := len(history)
	if length == 0 {
		return false
	}
	return strings.Contains(history[length-1].GetContent(), reply)
}

func llmOptionCard(ctx context.Context, session *entity.Session, question, answer string,
	options []string) (optionIndex int, statisticInfos []*KEP_WF_DM.StatisticInfo, err error) {
	llmOptions := make([]*llmOption, 0)
	for i, option := range options {
		llmOptions = append(llmOptions, &llmOption{
			Content: option,
			Num:     fmt.Sprintf("%c", 'A'+i),
		})
	}
	env := map[string]interface{}{
		"Question": question,
		"Answer":   answer,
		"Options":  llmOptions,
	}
	prompt, err := util.ParseTemplate(config.GetMainConfig().Prompts.OptionCard, env)
	if err != nil {
		LogWorkflow(ctx).Errorf("ParseTemplate failed, error: %v", err)
		return -1, nil, entity.WrapNodeErr(entity.ErrSystemError, err.Error())
	}
	modelName := custom.GetOptionCardModelName(ctx, session.MainModelName)
	req := newLLMRequest(ctx, session.AppID, prompt, modelName, defaultParams(modelName))
	rsp, err := dao.Default().SimpleChat(ctx, session, req, trace.StepKeyOptionCard)
	if err != nil {
		LogWorkflow(ctx).Warnf("request LLM failed, error: %v", err)
		// 这里已经wrap过一层，可直接透传，无需 entity.WrapNodeErr(entity.ErrLLMFail, err.Error())
		return -1, nil, err
	}

	if rsp.GetCode() != 0 {
		LogWorkflow(ctx).Errorf("LLM failed, rsp code: %d, message: %v", rsp.GetCode(), rsp.GetErrMsg())
		return -1, nil, entity.WrapNodeErr(entity.ErrLLMResultError, rsp.GetErrMsg())
	}
	llmResultContent := strings.Trim(rsp.GetMessage().GetContent(), "\t\n ")
	if len(llmResultContent) < 1 {
		LogWorkflow(ctx).Warnf("invalid LLM result: %v", llmResultContent)
		return -1, nil, entity.WrapNodeErr(entity.ErrLLMResultError, llmResultContent)
	}
	if llmResultContent == "其他" {
		optionIndex = len(llmOptions)
	} else {
		optionIndex = int(llmResultContent[0]-'A') + 1
		if optionIndex < 1 || optionIndex > 26 {
			LogWorkflow(ctx).Warnf("invalid LLM result: %v", llmResultContent)
			return -1, nil, entity.WrapNodeErr(entity.ErrLLMResultError, llmResultContent)
		}
	}
	statisticInfos = []*KEP_WF_DM.StatisticInfo{convertStatisticInfo(session.MainModelName, rsp.GetStatisticInfo())}
	return optionIndex, statisticInfos, nil
}
