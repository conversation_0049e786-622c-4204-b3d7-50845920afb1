package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"github.com/IBM/sarama"
	"github.com/IBM/sarama/mocks"
	"github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
)

// Test_makeMQNodeOutput tests the makeMQNodeOutput function
func Test_makeMQNodeOutput(t *testing.T) {
	Convey("Test makeMQNodeOutput function", t, func() {
		Convey("When message is sent successfully", func() {
			msgSent := true
			msgSize := 100
			msgSentTime := time.Unix(1609459200, 0) // 2021-01-01 00:00:00
			var err error = nil

			output := makeMQNodeOutput(msgSent, msgSize, msgSentTime, err)

			// Parse the JSON output
			outputMap := make(map[string]interface{})
			err = json.Unmarshal([]byte(output), &outputMap)
			So(err, ShouldBeNil)

			// Verify the output
			So(outputMap[mqNodeSendStatusResultKey], ShouldEqual, 1)
			So(outputMap[mqNodeTimestampResultKey], ShouldEqual, msgSentTime.Unix())
			So(outputMap[mqNodeErrorMsgResultKey], ShouldEqual, "")
			So(outputMap[mqNodeMessageSizeResultKey], ShouldEqual, float64(msgSize))
		})

		Convey("When message sending fails", func() {
			msgSent := false
			msgSize := 100
			msgSentTime := time.Time{}
			err := fmt.Errorf("test error")

			output := makeMQNodeOutput(msgSent, msgSize, msgSentTime, err)

			// Parse the JSON output
			outputMap := make(map[string]interface{})
			err2 := json.Unmarshal([]byte(output), &outputMap)
			So(err2, ShouldBeNil)

			// Verify the output
			So(outputMap[mqNodeSendStatusResultKey], ShouldEqual, 0)
			So(outputMap[mqNodeErrorMsgResultKey], ShouldEqual, err.Error())
			So(outputMap[mqNodeMessageSizeResultKey], ShouldEqual, float64(msgSize))
			// Timestamp should not be present
			_, exists := outputMap[mqNodeTimestampResultKey]
			So(exists, ShouldBeFalse)
		})
	})
}

// Test_executeMQNode tests the executeMQNode function
func Test_executeMQNode(t *testing.T) {
	// Create stubs for the sendMessage2Kafka and sendMessage2RocketMQ functions
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	Convey("Test executeMQNode function", t, func() {
		// Setup common test data
		ctx := context.Background()
		nodeResultQueue := make(chan entity.NodeResult, 10)

		Convey("When using Kafka and message is sent successfully", func() {
			// Mock the sendMessage2Kafka function
			patches.ApplyFunc(sendMessage2Kafka, func(ctx context.Context, session *entity.Session, belongNodeID string,
				options *KEP_WF.MQNodeData_KafkaOption, message string) (bool, time.Time, error) {
				return true, time.Now(), nil
			})

			// Create a session and node task for Kafka
			session := createTestSession()
			nodeTask := createKafkaNodeTask()

			// Execute the MQ node
			executeMQNode(ctx, session, nodeTask, nodeResultQueue)

			// Get the result
			result := <-nodeResultQueue

			// Verify the result
			So(result.Status, ShouldEqual, entity.NodeStatusSuccess)
			So(result.BelongNodeID, ShouldEqual, nodeTask.BelongNodeID)
			So(result.NodeID, ShouldEqual, nodeTask.Node.NodeID)

			// Parse the output
			outputMap := make(map[string]interface{})
			err := json.Unmarshal([]byte(result.Output), &outputMap)
			So(err, ShouldBeNil)
			So(outputMap[mqNodeSendStatusResultKey], ShouldEqual, float64(1))
		})

		Convey("When using RocketMQ and message is sent successfully", func() {
			// Mock the sendMessage2RocketMQ function
			patches.ApplyFunc(sendMessage2RocketMQ, func(ctx context.Context, session *entity.Session, belongNodeID string,
				options *KEP_WF.MQNodeData_RocketMQOption, message string) (bool, time.Time, error) {
				return true, time.Now(), nil
			})

			// Create a session and node task for RocketMQ
			session := createTestSession()
			nodeTask := createRocketMQNodeTask()

			// Execute the MQ node
			executeMQNode(ctx, session, nodeTask, nodeResultQueue)

			// Get the result
			result := <-nodeResultQueue

			// Verify the result
			So(result.Status, ShouldEqual, entity.NodeStatusSuccess)
			So(result.BelongNodeID, ShouldEqual, nodeTask.BelongNodeID)
			So(result.NodeID, ShouldEqual, nodeTask.Node.NodeID)

			// Parse the output
			outputMap := make(map[string]interface{})
			err := json.Unmarshal([]byte(result.Output), &outputMap)
			So(err, ShouldBeNil)
			So(outputMap[mqNodeSendStatusResultKey], ShouldEqual, float64(1))
		})

		Convey("When using an unsupported MQ type", func() {
			// Create a session and node task with an unsupported MQ type
			session := createTestSession()
			nodeTask := createUnsupportedMQNodeTask()

			// Execute the MQ node
			executeMQNode(ctx, session, nodeTask, nodeResultQueue)

			// Get the result
			result := <-nodeResultQueue

			// Verify the result
			So(result.Status, ShouldEqual, entity.NodeStatusFailed)
			So(strings.HasPrefix(result.FailMessage, entity.ErrMQTypeUnsupported.Msg), ShouldBeTrue)
		})

		Convey("When fillContent returns an error", func() {
			// Mock the fillContent function to return an error
			patches.ApplyFunc(fillContent, func(session *entity.Session, belongNodeID string,
				content string, inputs []*KEP_WF.InputParam) (string, map[string]interface{},
				[]*KEP_WF_DM.Reference, error) {
				return "", nil, nil, fmt.Errorf("fill content error")
			})

			// Create a session and node task
			session := createTestSession()
			nodeTask := createKafkaNodeTask()

			// Execute the MQ node
			executeMQNode(ctx, session, nodeTask, nodeResultQueue)

			// Get the result
			result := <-nodeResultQueue

			// Verify the result
			So(result.Status, ShouldEqual, entity.NodeStatusFailed)
			So(strings.HasPrefix(result.FailMessage, entity.ErrInvalidParamValue.Msg), ShouldBeTrue)
		})

		Convey("When Kafka message sending fails", func() {
			// Mock the sendMessage2Kafka function to return an error
			patches.ApplyFunc(sendMessage2Kafka, func(ctx context.Context, session *entity.Session, belongNodeID string,
				options *KEP_WF.MQNodeData_KafkaOption, message string) (bool, time.Time, error) {
				return false, time.Time{}, fmt.Errorf("kafka error")
			})

			// Mock the fillContent function to return success
			patches.ApplyFunc(fillContent, func(session *entity.Session, belongNodeID string,
				content string, inputs []*KEP_WF.InputParam) (string, map[string]interface{},
				[]*KEP_WF_DM.Reference, error) {
				return "test message", nil, nil, nil
			})

			// Create a session and node task
			session := createTestSession()
			nodeTask := createKafkaNodeTask()

			// Execute the MQ node
			executeMQNode(ctx, session, nodeTask, nodeResultQueue)

			// Get the result
			result := <-nodeResultQueue

			// Verify the result
			So(result.Status, ShouldEqual, entity.NodeStatusFailed)
			So(strings.HasPrefix(result.FailMessage, entity.ErrMQSendMessageFailed.Msg), ShouldBeTrue)
		})

		Convey("When RocketMQ message sending fails", func() {
			// Mock the sendMessage2RocketMQ function to return an error
			patches.ApplyFunc(sendMessage2RocketMQ, func(ctx context.Context, session *entity.Session, belongNodeID string,
				options *KEP_WF.MQNodeData_RocketMQOption, message string) (bool, time.Time, error) {
				return false, time.Time{}, fmt.Errorf("rocketmq error")
			})

			// Mock the fillContent function to return success
			patches.ApplyFunc(fillContent, func(session *entity.Session, belongNodeID string,
				content string, inputs []*KEP_WF.InputParam) (string, map[string]interface{},
				[]*KEP_WF_DM.Reference, error) {
				return "test message", nil, nil, nil
			})

			// Create a session and node task
			session := createTestSession()
			nodeTask := createRocketMQNodeTask()

			// Execute the MQ node
			executeMQNode(ctx, session, nodeTask, nodeResultQueue)

			// Get the result
			result := <-nodeResultQueue

			// Verify the result
			So(result.Status, ShouldEqual, entity.NodeStatusFailed)
			So(strings.HasPrefix(result.FailMessage, entity.ErrMQSendMessageFailed.Msg), ShouldBeTrue)
		})
	})
}

// Helper functions to create test data

func createTestSession() *entity.Session {
	return &entity.Session{
		Query: "test query",
		QueryHistory: []*KEP_WF_DM.Message{
			{
				Role:    0,
				Content: "test message",
			},
		},
		RWMutex: sync.RWMutex{},
		App: &entity.App{
			Workflows: map[string]*KEP_WF.Workflow{},
		},
	}
}

func createKafkaNodeTask() *entity.NodeTask {
	return &entity.NodeTask{
		BelongNodeID: "parent_node_001",
		Node: &KEP_WF.WorkflowNode{
			NodeID:   "node_001",
			NodeName: "Kafka MQ Node",
			NodeType: KEP_WF.NodeType_MQ,
			NodeData: &KEP_WF.WorkflowNode_MQNodeData{
				MQNodeData: &KEP_WF.MQNodeData{
					MQType:       KEP_WF.MQNodeData_KAFKA,
					MQActionType: KEP_WF.MQNodeData_SEND,
					Message:      "test message",
					Option: &KEP_WF.MQNodeData_KafkaOptions{
						KafkaOptions: &KEP_WF.MQNodeData_KafkaOption{
							Protocol:  KEP_WF.MQNodeData_KafkaOption_SASL_PLAINTEXT,
							Mechanism: KEP_WF.MQNodeData_KafkaOption_PLAIN,
							Endpoints: &KEP_WF.Input{
								InputType: KEP_WF.InputSourceEnum_USER_INPUT,
								Source: &KEP_WF.Input_UserInputValue{
									UserInputValue: &KEP_WF.UserInputContent{
										Values: []string{"ckafka-xxxxxxxx.ap-guangzhou.ckafka.tencentcloudmq.com:50001"},
									},
								},
							},
							Topic: &KEP_WF.Input{
								InputType: KEP_WF.InputSourceEnum_USER_INPUT,
								Source: &KEP_WF.Input_UserInputValue{
									UserInputValue: &KEP_WF.UserInputContent{
										Values: []string{"test"},
									},
								},
							},
							UserName: &KEP_WF.Input{
								InputType: KEP_WF.InputSourceEnum_USER_INPUT,
								Source: &KEP_WF.Input_UserInputValue{
									UserInputValue: &KEP_WF.UserInputContent{
										Values: []string{"ckafka-xxxxxxxx#xxxx"},
									},
								},
							},
							Password: &KEP_WF.Input{
								InputType: KEP_WF.InputSourceEnum_USER_INPUT,
								Source: &KEP_WF.Input_UserInputValue{
									UserInputValue: &KEP_WF.UserInputContent{
										Values: []string{"xxxxxxxx"},
									},
								},
							},
						},
					},
				},
			},
			Inputs: []*KEP_WF.InputParam{},
		},
	}
}

func createRocketMQNodeTask() *entity.NodeTask {
	return &entity.NodeTask{
		BelongNodeID: "parent_node_001",
		Node: &KEP_WF.WorkflowNode{
			NodeID:   "node_001",
			NodeName: "RocketMQ Node",
			NodeType: KEP_WF.NodeType_MQ,
			NodeData: &KEP_WF.WorkflowNode_MQNodeData{
				MQNodeData: &KEP_WF.MQNodeData{
					MQType:       KEP_WF.MQNodeData_ROCKETMQ,
					MQActionType: KEP_WF.MQNodeData_SEND,
					Message:      "test message",
					Option: &KEP_WF.MQNodeData_RocketMQOptions{
						RocketMQOptions: &KEP_WF.MQNodeData_RocketMQOption{
							Version: KEP_WF.MQNodeData_RocketMQOption_V5,
							Endpoints: &KEP_WF.Input{
								InputType: KEP_WF.InputSourceEnum_USER_INPUT,
								Source: &KEP_WF.Input_UserInputValue{
									UserInputValue: &KEP_WF.UserInputContent{
										Values: []string{"rmq-xxxxxxxxx.rocketmq.gz.public.tencenttdmq.com:8080"},
									},
								},
							},
							Topic: &KEP_WF.Input{
								InputType: KEP_WF.InputSourceEnum_USER_INPUT,
								Source: &KEP_WF.Input_UserInputValue{
									UserInputValue: &KEP_WF.UserInputContent{
										Values: []string{"test"},
									},
								},
							},
							AccessKey: &KEP_WF.Input{
								InputType: KEP_WF.InputSourceEnum_USER_INPUT,
								Source: &KEP_WF.Input_UserInputValue{
									UserInputValue: &KEP_WF.UserInputContent{
										Values: []string{"akxxxxxxxx"},
									},
								},
							},
							SecretKey: &KEP_WF.Input{
								InputType: KEP_WF.InputSourceEnum_USER_INPUT,
								Source: &KEP_WF.Input_UserInputValue{
									UserInputValue: &KEP_WF.UserInputContent{
										Values: []string{"skxxxxxxxx"},
									},
								},
							},
						},
					},
				},
			},
			Inputs: []*KEP_WF.InputParam{},
		},
	}
}

func createUnsupportedMQNodeTask() *entity.NodeTask {
	return &entity.NodeTask{
		BelongNodeID: "parent_node_001",
		Node: &KEP_WF.WorkflowNode{
			NodeID:   "node_001",
			NodeName: "Unsupported MQ Node",
			NodeType: KEP_WF.NodeType_MQ,
			NodeData: &KEP_WF.WorkflowNode_MQNodeData{
				MQNodeData: &KEP_WF.MQNodeData{
					MQType:       999, // Unsupported MQ type
					MQActionType: KEP_WF.MQNodeData_SEND,
					Message:      "test message",
				},
			},
			Inputs: []*KEP_WF.InputParam{},
		},
	}
}

// Test_sendMessage2Kafka tests the sendMessage2Kafka function with IBM/sarama
func Test_sendMessage2Kafka(t *testing.T) {
	Convey("Test sendMessage2Kafka function with IBM/sarama", t, func() {
		ctx := context.Background()
		session := createTestSession()
		belongNodeID := "test_node"
		message := "test message"

		Convey("When Kafka options are nil", func() {
			msgSent, sendTime, err := sendMessage2Kafka(ctx, session, belongNodeID, nil, message)
			So(msgSent, ShouldBeFalse)
			So(sendTime.IsZero(), ShouldBeTrue)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "kafka option is empty")
		})

		Convey("When Kafka message is sent successfully", func() {
			// Create a mock Kafka producer
			config := sarama.NewConfig()
			config.Producer.Return.Successes = true
			mockProducer := mocks.NewSyncProducer(t, config)

			// Set up expectations
			mockProducer.ExpectSendMessageAndSucceed()

			// Create a patch to replace the sarama.NewSyncProducer function
			patches := gomonkey.NewPatches()
			defer patches.Reset()
			patches.ApplyFunc(sarama.NewSyncProducer, func(addrs []string, conf *sarama.Config) (sarama.SyncProducer, error) {
				return mockProducer, nil
			})

			// Create Kafka options
			options := &KEP_WF.MQNodeData_KafkaOption{
				Endpoints: &KEP_WF.Input{
					InputType: KEP_WF.InputSourceEnum_USER_INPUT,
					Source: &KEP_WF.Input_UserInputValue{
						UserInputValue: &KEP_WF.UserInputContent{
							Values: []string{"localhost:9092"},
						},
					},
				},
				Topic: &KEP_WF.Input{
					InputType: KEP_WF.InputSourceEnum_USER_INPUT,
					Source: &KEP_WF.Input_UserInputValue{
						UserInputValue: &KEP_WF.UserInputContent{
							Values: []string{"test-topic"},
						},
					},
				},
				Protocol: KEP_WF.MQNodeData_KafkaOption_PLAINTEXT,
			}

			// Call the function
			msgSent, sendTime, err := sendMessage2Kafka(ctx, session, belongNodeID, options, message)

			// Verify the results
			So(msgSent, ShouldBeTrue)
			So(sendTime.IsZero(), ShouldBeFalse)
			So(err, ShouldBeNil)
		})

		Convey("When Kafka producer creation fails", func() {
			// Create a patch to replace the sarama.NewSyncProducer function
			patches := gomonkey.NewPatches()
			defer patches.Reset()
			patches.ApplyFunc(sarama.NewSyncProducer, func(addrs []string, conf *sarama.Config) (sarama.SyncProducer, error) {
				return nil, fmt.Errorf("producer creation error")
			})

			// Create Kafka options
			options := &KEP_WF.MQNodeData_KafkaOption{
				Endpoints: &KEP_WF.Input{
					InputType: KEP_WF.InputSourceEnum_USER_INPUT,
					Source: &KEP_WF.Input_UserInputValue{
						UserInputValue: &KEP_WF.UserInputContent{
							Values: []string{"localhost:9092"},
						},
					},
				},
				Topic: &KEP_WF.Input{
					InputType: KEP_WF.InputSourceEnum_USER_INPUT,
					Source: &KEP_WF.Input_UserInputValue{
						UserInputValue: &KEP_WF.UserInputContent{
							Values: []string{"test-topic"},
						},
					},
				},
			}

			// Call the function
			msgSent, sendTime, err := sendMessage2Kafka(ctx, session, belongNodeID, options, message)

			// Verify the results
			So(msgSent, ShouldBeFalse)
			So(sendTime.IsZero(), ShouldBeTrue)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "producer creation error")
		})

		Convey("When Kafka message sending fails", func() {
			// Create a mock Kafka producer
			config := sarama.NewConfig()
			config.Producer.Return.Successes = true
			mockProducer := mocks.NewSyncProducer(t, config)

			// Set up expectations
			mockProducer.ExpectSendMessageAndFail(fmt.Errorf("send message error"))

			// Create a patch to replace the sarama.NewSyncProducer function
			patches := gomonkey.NewPatches()
			defer patches.Reset()
			patches.ApplyFunc(sarama.NewSyncProducer, func(addrs []string, conf *sarama.Config) (sarama.SyncProducer, error) {
				return mockProducer, nil
			})

			// Create Kafka options
			options := &KEP_WF.MQNodeData_KafkaOption{
				Endpoints: &KEP_WF.Input{
					InputType: KEP_WF.InputSourceEnum_USER_INPUT,
					Source: &KEP_WF.Input_UserInputValue{
						UserInputValue: &KEP_WF.UserInputContent{
							Values: []string{"localhost:9092"},
						},
					},
				},
				Topic: &KEP_WF.Input{
					InputType: KEP_WF.InputSourceEnum_USER_INPUT,
					Source: &KEP_WF.Input_UserInputValue{
						UserInputValue: &KEP_WF.UserInputContent{
							Values: []string{"test-topic"},
						},
					},
				},
			}

			// Call the function
			msgSent, sendTime, err := sendMessage2Kafka(ctx, session, belongNodeID, options, message)

			// Verify the results
			So(msgSent, ShouldBeFalse)
			So(sendTime.IsZero(), ShouldBeFalse)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "send message error")
		})

		Convey("When using SASL authentication", func() {
			// Create a mock Kafka producer
			config := sarama.NewConfig()
			config.Producer.Return.Successes = true
			mockProducer := mocks.NewSyncProducer(t, config)

			// Set up expectations
			mockProducer.ExpectSendMessageAndSucceed()

			// Create a patch to replace the sarama.NewSyncProducer function
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			// 直接修改 sendMessage2Kafka 函数，跳过 AES 解密
			var capturedConfig *sarama.Config
			patches.ApplyFunc(sarama.NewSyncProducer, func(addrs []string, conf *sarama.Config) (sarama.SyncProducer, error) {
				// 在这里直接设置密码，而不是依赖于 AES 解密
				conf.Net.SASL.Password = "test-password"
				capturedConfig = conf
				return mockProducer, nil
			})

			// Create Kafka options with SASL
			options := &KEP_WF.MQNodeData_KafkaOption{
				Endpoints: &KEP_WF.Input{
					InputType: KEP_WF.InputSourceEnum_USER_INPUT,
					Source: &KEP_WF.Input_UserInputValue{
						UserInputValue: &KEP_WF.UserInputContent{
							Values: []string{"localhost:9092"},
						},
					},
				},
				Topic: &KEP_WF.Input{
					InputType: KEP_WF.InputSourceEnum_USER_INPUT,
					Source: &KEP_WF.Input_UserInputValue{
						UserInputValue: &KEP_WF.UserInputContent{
							Values: []string{"test-topic"},
						},
					},
				},
				Protocol:  KEP_WF.MQNodeData_KafkaOption_SASL_PLAINTEXT,
				Mechanism: KEP_WF.MQNodeData_KafkaOption_PLAIN,
				UserName: &KEP_WF.Input{
					InputType: KEP_WF.InputSourceEnum_USER_INPUT,
					Source: &KEP_WF.Input_UserInputValue{
						UserInputValue: &KEP_WF.UserInputContent{
							Values: []string{"test-user"},
						},
					},
				},
				Password: &KEP_WF.Input{
					InputType: KEP_WF.InputSourceEnum_USER_INPUT,
					Source: &KEP_WF.Input_UserInputValue{
						UserInputValue: &KEP_WF.UserInputContent{
							Values: []string{"test-password"},
						},
					},
				},
			}

			// Call the function
			msgSent, sendTime, err := sendMessage2Kafka(ctx, session, belongNodeID, options, message)

			// Verify the results
			So(msgSent, ShouldBeTrue)
			So(sendTime.IsZero(), ShouldBeFalse)
			So(err, ShouldBeNil)

			// Verify SASL configuration
			So(capturedConfig.Net.SASL.Enable, ShouldBeTrue)
			So(string(capturedConfig.Net.SASL.Mechanism), ShouldEqual, "PLAIN")
			So(capturedConfig.Net.SASL.User, ShouldEqual, "test-user")
			So(capturedConfig.Net.SASL.Password, ShouldEqual, "test-password")
		})
	})
}

// Test_getValue tests the getValue function
func Test_getValue(t *testing.T) {
	Convey("Test getValue function", t, func() {
		ctx := context.Background()
		session := createTestSession()

		Convey("When input is nil", func() {
			value := getValue(ctx, session, "node_001", nil)
			So(value, ShouldBeNil)
		})

		Convey("When input has a constant value", func() {
			input := &KEP_WF.Input{
				InputType: KEP_WF.InputSourceEnum_USER_INPUT,
				Source: &KEP_WF.Input_UserInputValue{
					UserInputValue: &KEP_WF.UserInputContent{
						Values: []string{"test value"},
					},
				},
			}

			value := getValue(ctx, session, "node_001", input)
			So(value, ShouldEqual, "test value")
		})

		Convey("When input has a system variable", func() {
			input := &KEP_WF.Input{
				InputType: KEP_WF.InputSourceEnum_SYSTEM_VARIABLE,
				Source: &KEP_WF.Input_SystemVariable{
					SystemVariable: &KEP_WF.SystemVariable{
						Name:               "SYS.UserQuery",
						DialogHistoryLimit: 15,
					},
				},
			}

			value := getValue(ctx, session, "node_001", input)
			So(value, ShouldEqual, "test query")
		})
	})
}
