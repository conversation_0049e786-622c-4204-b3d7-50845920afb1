package workflow

import (
	"context"
	"reflect"
	"testing"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
)

func Test_DebugNotDialogNodeLLM(t *testing.T) {

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	Convey("Test_DebugNotDialogNode", t, func() {
		daoM := dao.NewMockDao(ctrl)
		daoM.EXPECT().Chat(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, req *llmm.Request, _ trace.StepKey) (chan *llmm.Response, error) {
				ch := make(chan *llmm.Response, 1)
				ch <- &llmm.Response{
					Finished: true,
					Message: &llmm.Message{
						Content: "hello",
					},
					StatisticInfo: &llmm.StatisticInfo{
						TotalTokens:  100,
						InputTokens:  20,
						OutputTokens: 80,
					},
				}
				return ch, nil
			}).AnyTimes()

		session := &entity.Session{}
		ch := make(chan struct{}, 3)
		p := gomonkey.ApplyMethod(reflect.TypeOf(session), "ConsumeLLMResource",
			func(_ *entity.Session, _ context.Context) {
				ch <- struct{}{}
			}).ApplyMethod(reflect.TypeOf(session), "ReleaseLLMResource",
			func(_ *entity.Session, _ context.Context) {
				<-ch
			}).ApplyFunc(dao.Default, func() dao.Dao {
			return daoM
		})
		defer p.Reset()

		req := &KEP_WF_DM.DebugWorkflowNodeRequest{
			AppID: "app_id",
			Inputs: map[string]string{
				"node_start_array_object": `[{"aa": 1}, {"aa": 2}]`,
				"node_start_bool":         `1`,
			},
		}
		node := &KEP_WF.WorkflowNode{
			NodeID:   "llm_1",
			NodeName: "llm_1",
			NodeType: KEP_WF.NodeType_LLM,
			NodeData: &KEP_WF.WorkflowNode_LLMNodeData{
				LLMNodeData: &KEP_WF.LLMNodeData{
					Prompt: "hello",
				},
			},
			NextNodeIDs: []string{"answer_2"},
		}
		reply, err := DebugNotDialogNode(context.Background(), req, node)
		t.Log(reply, err)

		node = &KEP_WF.WorkflowNode{
			NodeID:      "node_002",
			NodeName:    "node_002_name",
			NodeType:    KEP_WF.NodeType_ANSWER,
			NextNodeIDs: nil,
			NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
				Answer: "你好呀",
			}},
			Outputs: []*KEP_WF.OutputParam{
				{
					Title:      "node_002_output_01",
					Type:       0,
					Required:   nil,
					Properties: nil,
					Desc:       "",
					Value: &KEP_WF.Input{
						InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
						Source: &KEP_WF.Input_Reference{
							Reference: &KEP_WF.ReferenceFromNode{
								NodeID:   "node_001",
								JsonPath: "Output.node_start_array_object",
							},
						},
					},
				},
				{
					Title:      "node_002_output_02",
					Type:       0,
					Required:   nil,
					Properties: nil,
					Desc:       "",
					Value: &KEP_WF.Input{
						InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
						Source: &KEP_WF.Input_Reference{
							Reference: &KEP_WF.ReferenceFromNode{
								NodeID:   "node_001",
								JsonPath: "Output.node_start_bool",
							},
						},
					},
				},
			},
		}
		reply, err = DebugNotDialogNode(context.Background(), req, node)
		t.Log(reply, err)
	})
}

func Test_DebugNotDialogNodeAnswer(t *testing.T) {

	req := &KEP_WF_DM.DebugWorkflowNodeRequest{
		AppID: "app_id",
		Inputs: map[string]string{
			"node_start_array_object": `[{"aa": 1}, {"aa": 2}]`,
			"node_start_bool":         `1`,
			"node_start_int":          `33`,
			"node_start_str":          `33`,
		},
	}
	Convey("答案节点", t, func() {
		node := &KEP_WF.WorkflowNode{
			NodeID:      "node_002",
			NodeName:    "node_002_name",
			NodeType:    KEP_WF.NodeType_ANSWER,
			NextNodeIDs: nil,
			NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{
				AnswerNodeData: &KEP_WF.AnswerNodeData{
					Answer: "{{node_start_int}}你好呀",
				},
			},
			Inputs: []*KEP_WF.InputParam{
				{
					Name:       "node_start_int",
					Type:       KEP_WF.TypeEnum_INT,
					Input:      nil,
					Desc:       "",
					IsRequired: false,
				},
			},
			Outputs: []*KEP_WF.OutputParam{
				{
					Title:      "node_002_output_01",
					Type:       0,
					Required:   nil,
					Properties: nil,
					Desc:       "",
					Value: &KEP_WF.Input{
						InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
						Source: &KEP_WF.Input_Reference{
							Reference: &KEP_WF.ReferenceFromNode{
								NodeID:   "node_001",
								JsonPath: "Output.node_start_array_object",
							},
						},
					},
				},
				{
					Title:      "node_002_output_02",
					Type:       0,
					Required:   nil,
					Properties: nil,
					Desc:       "",
					Value: &KEP_WF.Input{
						InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
						Source: &KEP_WF.Input_Reference{
							Reference: &KEP_WF.ReferenceFromNode{
								NodeID:   "node_001",
								JsonPath: "Output.node_start_bool",
							},
						},
					},
				},
			},
		}
		reply, err := DebugNotDialogNode(context.Background(), req, node)
		t.Log(reply, err)
	})
}
