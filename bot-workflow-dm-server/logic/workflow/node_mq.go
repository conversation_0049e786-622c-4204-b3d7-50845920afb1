package workflow

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/go-comm/security"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"github.com/IBM/sarama"
	rmqv4 "github.com/apache/rocketmq-client-go/v2"
	rmqv4primitive "github.com/apache/rocketmq-client-go/v2/primitive"
	rmqv4producer "github.com/apache/rocketmq-client-go/v2/producer"
	rmqv5 "github.com/apache/rocketmq-clients/golang/v5"
	rmqv5cred "github.com/apache/rocketmq-clients/golang/v5/credentials"
)

const (
	mqNodeMessageKey           = "Message"
	mqNodeSendStatusResultKey  = "SendStatus"  // 发送状态，表示消息发送是否成功，1-成功、0-失败等
	mqNodeErrorMsgResultKey    = "ErrorMsg"    // 错误信息，如果发送失败，返回具体的错误原因
	mqNodeTimestampResultKey   = "Timestamp"   // 时间戳，消息的发送时间
	mqNodeMessageSizeResultKey = "MessageSize" // 消息大小，发送消息的大小，单位为字节
)

// executeMQNode 执行消息队列节点
func executeMQNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	LogWorkflow(ctx).Infof("executeMQNode start, nodeID: %v", node.NodeID)
	start := time.Now()
	nodeData := node.GetMQNodeData()
	if nodeData == nil {
		LogWorkflow(ctx).Errorf("invalid node data")
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.ErrMissingParam)
		return
	}
	convertedContent, inputResult, references, err :=
		fillContent(session, nodeTask.BelongNodeID, nodeData.Message, node.GetInputs())
	if err != nil {
		LogWorkflow(ctx).Errorf("executeMQNode error: %v", err)
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
			entity.WrapNodeErr(entity.ErrInvalidParamValue, err.Error()))
		return
	}
	var msgSent bool
	var sendTime time.Time
	switch nodeData.GetMQType() {
	case KEP_WF.MQNodeData_KAFKA:
		msgSent, sendTime, err = sendMessage2Kafka(ctx, session, nodeTask.BelongNodeID,
			nodeData.GetKafkaOptions(), convertedContent)
	case KEP_WF.MQNodeData_ROCKETMQ:
		msgSent, sendTime, err = sendMessage2RocketMQ(ctx, session, nodeTask.BelongNodeID,
			nodeData.GetRocketMQOptions(), convertedContent)
	default:
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.ErrMQTypeUnsupported)
		return
	}
	if err != nil {
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
			entity.WrapNodeErr(entity.ErrMQSendMessageFailed, err.Error()))
		return
	}
	referencesMap := map[string][]*KEP_WF_DM.Reference{mqNodeMessageKey: references}
	outputResultStr := makeMQNodeOutput(msgSent, len([]byte(convertedContent)), sendTime, err)
	result := entity.NodeResult{
		BelongNodeID:     nodeTask.BelongNodeID,
		NodeID:           node.NodeID,
		Status:           entity.NodeStatusSuccess,
		Reply:            convertedContent,
		Input:            util.ToJsonString(inputResult),
		Output:           outputResultStr,
		TaskOutput:       "",
		ReferencesMap:    referencesMap,
		FailMessage:      "",
		ErrorCode:        "",
		StatisticInfo:    nil,
		CostMilliSeconds: time.Since(start).Milliseconds(),
	}
	nodeResultQueue <- result
	LogWorkflow(ctx).Infof("executeMQNode done, nodeID: %v", node.NodeID)
}

func makeMQNodeOutput(msgSent bool, msgSize int, msgSentTime time.Time, err error) string {
	outputResult := make(map[string]any)
	outputResult[mqNodeSendStatusResultKey] = 0
	if msgSent {
		outputResult[mqNodeSendStatusResultKey] = 1
		outputResult[mqNodeTimestampResultKey] = msgSentTime.Unix()
	}
	outputResult[mqNodeErrorMsgResultKey] = ""
	if err != nil {
		outputResult[mqNodeErrorMsgResultKey] = err.Error()
	}
	outputResult[mqNodeMessageSizeResultKey] = msgSize
	return util.ToJsonString(outputResult)
}

func getValue(ctx context.Context, session *entity.Session, belongNodeID string, input *KEP_WF.Input) any {
	if input == nil {
		LogWorkflow(ctx).Debugf("input is nil")
		return nil
	}
	value := getInputValue(session, belongNodeID, input, nil)
	return value
}

func sendMessage2Kafka(ctx context.Context, session *entity.Session, belongNodeID string,
	options *KEP_WF.MQNodeData_KafkaOption, message string) (bool, time.Time, error) {
	if options == nil {
		return false, time.Time{}, fmt.Errorf("kafka option is empty")
	}
	endpoints := fmt.Sprintf("%v", getValue(ctx, session, belongNodeID, options.GetEndpoints()))
	brokers := strings.Split(endpoints, ",")
	var saslMechanism string
	switch options.GetMechanism() {
	case KEP_WF.MQNodeData_KafkaOption_SCRAM_SHA_256:
		saslMechanism = sarama.SASLTypeSCRAMSHA256
	case KEP_WF.MQNodeData_KafkaOption_SCRAM_SHA_512:
		saslMechanism = sarama.SASLTypeSCRAMSHA512
	default:
		saslMechanism = sarama.SASLTypePlaintext
	}
	var protocol string
	switch options.GetProtocol() {
	case KEP_WF.MQNodeData_KafkaOption_SASL_PLAINTEXT:
		protocol = "SASL_PLAINTEXT"
	case KEP_WF.MQNodeData_KafkaOption_SASL_SSL:
		protocol = "SASL_SSL"
	default:
		protocol = "PLAINTEXT"
	}
	userName := fmt.Sprintf("%v", getValue(ctx, session, belongNodeID, options.GetUserName()))
	password := fmt.Sprintf("%v", getValue(ctx, session, belongNodeID, options.GetPassword()))
	decryptedPassword := password
	if options.GetPassword().GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT && len(password) > 0 {
		decryptedPassword, _ = security.AESDecryptBase64StringDefault(password)
		// 单节点调试传入的为非加密内容，因此解码会失败，降级使用原文
		if decryptedPassword == "" {
			decryptedPassword = password
		}
	}
	topic := fmt.Sprintf("%v", getValue(ctx, session, belongNodeID, options.GetTopic()))
	// 创建 sarama 配置
	config := sarama.NewConfig()
	// 设置基本配置
	config.Producer.Return.Successes = true // 必须设置为true才能使用SyncProducer
	config.Producer.Return.Errors = true
	// 设置ACK级别
	config.Producer.RequiredAcks = sarama.WaitForLocal // 相当于acks=1
	// 设置重试
	config.Producer.Retry.Max = 3
	config.Producer.Retry.Backoff = 100 * time.Millisecond
	// 设置超时
	config.Net.DialTimeout = 6 * time.Second
	config.Net.ReadTimeout = 6 * time.Second
	config.Net.WriteTimeout = 6 * time.Second
	// 设置SASL认证
	if protocol != "PLAINTEXT" {
		config.Net.SASL.Enable = true
		config.Net.SASL.Mechanism = sarama.SASLMechanism(saslMechanism)
		config.Net.SASL.User = userName
		config.Net.SASL.Password = decryptedPassword
		config.Net.SASL.Handshake = true
		if protocol == "SASL_SSL" {
			config.Net.TLS.Enable = true
			config.Net.TLS.Config.InsecureSkipVerify = true
		}
	}
	// 创建生产者
	producer, err := sarama.NewSyncProducer(brokers, config)
	if err != nil {
		LogWorkflow(ctx).Warnf("kafka new producer failed, node: %s, brokers: %s, config: %+v, err:%v",
			belongNodeID, brokers, *config, err.Error())
		return false, time.Time{}, err
	}
	defer func() {
		if err := producer.Close(); err != nil {
			LogWorkflow(ctx).Warnf("kafka producer close failed, node: %s, brokers: %s, topic: %s, err:%v",
				belongNodeID, brokers, topic, err.Error())
		}
	}()
	// 创建消息
	msg := &sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.StringEncoder(message),
	}
	// 发送消息
	sendTime := time.Now()
	partition, offset, err := producer.SendMessage(msg)
	if err != nil {
		LogWorkflow(ctx).Warnf("kafka delivery failed, node: %s, topic: %s, err: %w",
			belongNodeID, topic, err)
		return false, sendTime, err
	}

	LogWorkflow(ctx).Infof("kafka delivered message, node: %s, topic: %s, partition: %d, offset: %d",
		belongNodeID, topic, partition, offset)

	return true, sendTime, nil
}

func sendMessage2RocketMQ(ctx context.Context, session *entity.Session, belongNodeID string,
	options *KEP_WF.MQNodeData_RocketMQOption, message string) (bool, time.Time, error) {
	if options == nil {
		return false, time.Time{}, fmt.Errorf("rocketmq option is empty")
	}
	endpoint := fmt.Sprintf("%v", getValue(ctx, session, belongNodeID, options.GetEndpoints()))
	endpoints := strings.Split(endpoint, ",")
	accessKey := fmt.Sprintf("%v", getValue(ctx, session, belongNodeID, options.GetAccessKey()))
	secretKey := fmt.Sprintf("%v", getValue(ctx, session, belongNodeID, options.GetSecretKey()))
	decryptedSecretKey := secretKey
	if options.GetSecretKey().GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT && len(secretKey) > 0 {
		decryptedSecretKey, _ = security.AESDecryptBase64StringDefault(secretKey)
		// 单节点调试传入的为非加密内容，因此解码会失败，降级使用原文
		if decryptedSecretKey == "" {
			decryptedSecretKey = secretKey
		}
	}
	topic := fmt.Sprintf("%v", getValue(ctx, session, belongNodeID, options.GetTopic()))
	switch options.GetVersion() {
	case KEP_WF.MQNodeData_RocketMQOption_V4:
		return sendMessage2RocketMQV4(ctx, belongNodeID, []rmqv4producer.Option{
			rmqv4producer.WithNsResolver(rmqv4primitive.NewPassthroughResolver(endpoints)),
			rmqv4producer.WithCredentials(rmqv4primitive.Credentials{
				SecretKey: decryptedSecretKey,
				AccessKey: accessKey,
			}),
		}, topic, message)
	case KEP_WF.MQNodeData_RocketMQOption_V5:
		return sendMessage2RocketMQV5(ctx, belongNodeID, &rmqv5.Config{
			Endpoint: endpoint,
			Credentials: &rmqv5cred.SessionCredentials{
				AccessKey:    accessKey,
				AccessSecret: decryptedSecretKey,
			},
		}, topic, message)
	default:
		return false, time.Time{}, fmt.Errorf("unsupported rocket mq version")
	}
}

func sendMessage2RocketMQV5(ctx context.Context, belongNodeID string,
	options *rmqv5.Config, topic string, message string) (bool, time.Time, error) {
	rmqv5.ResetLogger()
	p, err := rmqv5.NewProducer(options, rmqv5.WithTopics(topic))
	if err != nil {
		LogWorkflow(ctx).Warnf("rocketmq[v5] new producer failed, node: %s, topic: %s, options: %v err: %w",
			belongNodeID, topic, util.ToJsonString(options), err)
		return false, time.Time{}, err
	}
	err = p.Start()
	if err != nil {
		LogWorkflow(ctx).Warnf("rocketmq[v5] start producer failed, node: %s, topic: %s, err: %w",
			belongNodeID, topic, err)
		return false, time.Time{}, err
	}
	defer func() {
		if err := p.GracefulStop(); err != nil {
			LogWorkflow(ctx).Warnf("rocketmq[v5] graceful stop failed, node: %s, topic: %s, err: %w",
				belongNodeID, topic, err)
		}
	}()
	// 同步发送消息
	msg := &rmqv5.Message{
		Topic: topic,
		Body:  []byte(message),
	}
	sendTime := time.Now()
	resp, err := p.Send(ctx, msg)
	if err != nil {
		LogWorkflow(ctx).Warnf("rocketmq[v5] delivery failed, node: %s, topic: %s, err: %w",
			belongNodeID, topic, err)
		return false, sendTime, err
	}
	LogWorkflow(ctx).Infof("rocketmq[v5] delivered message, node: %s, topic: %s, resp:%+v",
		belongNodeID, topic, util.ToJsonString(resp))
	return true, sendTime, nil
}

func sendMessage2RocketMQV4(ctx context.Context, belongNodeID string,
	options []rmqv4producer.Option, topic string, message string) (bool, time.Time, error) {
	p, err := rmqv4.NewProducer(options...)
	if err != nil {
		LogWorkflow(ctx).Warnf("rocketmq[v4] new producer failed, node: %s, topic: %s, options: %v err: %w",
			belongNodeID, topic, util.ToJsonString(options), err)
		return false, time.Time{}, err
	}
	err = p.Start()
	if err != nil {
		LogWorkflow(ctx).Warnf("rocketmq[v4] start producer failed, node: %s, topic: %s, err: %w",
			belongNodeID, topic, err)
		return false, time.Time{}, err
	}
	defer func() {
		if err := p.Shutdown(); err != nil {
			LogWorkflow(ctx).Warnf("rocketmq[v4] shutdown failed, node: %s, topic: %s, err: %w",
				belongNodeID, topic, err)
		}
	}()
	// 同步发送消息
	msg := &rmqv4primitive.Message{
		Topic: topic,
		Body:  []byte(message),
	}
	sendTime := time.Now()
	resp, err := p.SendSync(ctx, msg)
	if err != nil {
		LogWorkflow(ctx).Warnf("rocketmq[v4] delivery failed, node: %s, topic: %s, err: %w",
			belongNodeID, topic, err)
		return false, sendTime, err
	}
	LogWorkflow(ctx).Infof("rocketmq[v4] delivered message, node: %s, topic: %s, resp: %+v",
		belongNodeID, topic, util.ToJsonString(resp))
	return true, sendTime, nil
}
