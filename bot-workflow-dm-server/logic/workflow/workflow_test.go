package workflow

import (
	"context"
	"sync"
	"testing"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"github.com/prashantv/gostub"
	. "github.com/smartystreets/goconvey/convey"
)

const failedReply = "这个是错误回复"

func getLLMStub() *gostub.Stubs {
	return gostub.Stub(&executeLLMNode, func(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
		nodeResultQueue chan entity.NodeResult) {
		node := nodeTask.Node
		LogWorkflow(ctx).Infof("executeLLMNode start, nodeID: %v", node.NodeID)

		session.ConsumeLLMResource(ctx)
		defer session.ReleaseLLMResource(ctx)

		// 输出参数
		nodeResult := entity.NodeResult{
			BelongNodeID:     nodeTask.BelongNodeID,
			NodeID:           node.NodeID,
			Status:           entity.NodeStatusRunning,
			Input:            "",
			Output:           `{"Content": "这个是前半部分"}`,
			TaskOutput:       "",
			Reply:            "",
			FailMessage:      "",
			ReferencesMap:    nil,
			CostMilliSeconds: 20,
			StatisticInfo: []*KEP_WF_DM.StatisticInfo{{
				ModelName:      "ModelName____001",
				FirstTokenCost: 10,
				TotalCost:      20,
				InputTokens:    40,
				OutputTokens:   50,
				TotalTokens:    60,
			}},
		}
		nodeResultQueue <- nodeResult

		time.Sleep(time.Second)
		nodeResult.Status = entity.NodeStatusSuccess
		nodeResult.CostMilliSeconds = 30
		nodeResult.Output = `{"Content": "这个是前半部分，这个是后半部分"}`
		nodeResultQueue <- nodeResult
		LogWorkflow(ctx).Infof("executeLLMNode done, nodeID: %v", node.NodeID)
	})
}
func getParameterStub() *gostub.Stubs {
	return gostub.Stub(&executeParameterNode, func(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
		nodeResultQueue chan entity.NodeResult) {
		node := nodeTask.Node
		LogWorkflow(ctx).Infof("executeParameterNode start, nodeID: %v", node.NodeID)

		session.ConsumeLLMResource(ctx)
		defer session.ReleaseLLMResource(ctx)

		// 输出参数
		nodeResult := entity.NodeResult{
			BelongNodeID:     nodeTask.BelongNodeID,
			NodeID:           node.NodeID,
			Status:           entity.NodeStatusRunning,
			Input:            "",
			Output:           `{"Content": "这个是前半部分"}`,
			TaskOutput:       "",
			Reply:            "",
			FailMessage:      "",
			ReferencesMap:    nil,
			CostMilliSeconds: 20,
			StatisticInfo: []*KEP_WF_DM.StatisticInfo{{
				ModelName:      "ModelName____001",
				FirstTokenCost: 10,
				TotalCost:      20,
				InputTokens:    40,
				OutputTokens:   50,
				TotalTokens:    60,
			}},
		}
		nodeResultQueue <- nodeResult

		time.Sleep(time.Second)
		nodeResult.Status = entity.NodeStatusWaitingReply
		nodeResult.CostMilliSeconds = 30
		nodeResult.Output = `{"Content": "这个是前半部分，这个是后半部分"}`
		nodeResultQueue <- nodeResult
		LogWorkflow(ctx).Infof("executeParameterNode done, nodeID: %v", node.NodeID)
	})
}

func getFailedLLMStub() *gostub.Stubs {
	return gostub.Stub(&executeLLMNode, func(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
		nodeResultQueue chan entity.NodeResult) {
		node := nodeTask.Node
		LogWorkflow(ctx).Infof("executeLLMNode start, nodeID: %v", node.NodeID)

		// 输出参数
		nodeResult := entity.NodeResult{
			BelongNodeID: nodeTask.BelongNodeID,
			NodeID:       node.NodeID,
			Status:       entity.NodeStatusFailed,
			Input:        "",
			Output:       `{"Content": "这个是前半部分"}`,
			FailMessage:  "xxxxxxxx",
		}
		nodeResultQueue <- nodeResult

		LogWorkflow(ctx).Infof("executeLLMNode done, nodeID: %v", node.NodeID)
	})
}

func getPanicLLMStub() *gostub.Stubs {
	return gostub.Stub(&executeLLMNode, func(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
		nodeResultQueue chan entity.NodeResult) {
		node := nodeTask.Node
		LogWorkflow(ctx).Infof("executeLLMNode start, nodeID: %v", node.NodeID)

		aa := 0
		LogWorkflow(ctx).Infof("%v", 2/aa)

		LogWorkflow(ctx).Infof("executeLLMNode done, nodeID: %v", node.NodeID)
	})
}

func initConfig() {
	config.GetMainConfig = func() *config.MainConfig {
		return &config.MainConfig{Workflow: config.Workflow{
			FailedReply: failedReply,
		}}
	}
}

func Test_RunWorkflow(t *testing.T) {
	stubs := getLLMStub()
	defer stubs.Reset()
	Convey("开始、回复节点，变量替换正常", t, func() {
		session := &entity.Session{
			Query: "query_001",
			QueryHistory: []*KEP_WF_DM.Message{
				{
					Role:    0,
					Content: "今天天气怎么样？",
				},
				{
					Role:    1,
					Content: "挺好的",
				},
			},
			RWMutex: sync.RWMutex{},

			App: &entity.App{
				Workflows: map[string]*KEP_WF.Workflow{"wf_001": {
					ProtoVersion: 0,
					WorkflowID:   "",
					WorkflowName: "工作流名称",
					WorkflowDesc: "",
					Nodes: []*KEP_WF.WorkflowNode{
						{
							NodeID:      "node_001",
							NodeName:    "node_001_name",
							NodeType:    KEP_WF.NodeType_START,
							NodeData:    nil,
							NextNodeIDs: []string{"node_002"},
						}, {
							NodeID:   "node_002",
							NodeName: "node_002_name",
							Inputs: []*KEP_WF.InputParam{
								{
									Name: "sysTime",
									Type: 0,
									Input: &KEP_WF.Input{
										InputType: KEP_WF.InputSourceEnum_SYSTEM_VARIABLE,
										Source: &KEP_WF.Input_SystemVariable{
											SystemVariable: &KEP_WF.SystemVariable{
												Name:               entity.SystemVariableTime,
												DialogHistoryLimit: 0,
											},
										},
									},
								},
								{
									Name: "sysQuery",
									Type: 0,
									Input: &KEP_WF.Input{
										InputType: KEP_WF.InputSourceEnum_SYSTEM_VARIABLE,
										Source: &KEP_WF.Input_SystemVariable{
											SystemVariable: &KEP_WF.SystemVariable{
												Name:               entity.SystemVariableQuery,
												DialogHistoryLimit: 0,
											},
										},
									},
								},
								{
									Name: "sysHistory1",
									Type: 0,
									Input: &KEP_WF.Input{
										InputType: KEP_WF.InputSourceEnum_SYSTEM_VARIABLE,
										Source: &KEP_WF.Input_SystemVariable{
											SystemVariable: &KEP_WF.SystemVariable{
												Name:               entity.SystemVariableHistory,
												DialogHistoryLimit: 1,
											},
										},
									},
								},
								{
									Name: "sysHistory2",
									Type: 0,
									Input: &KEP_WF.Input{
										InputType: KEP_WF.InputSourceEnum_SYSTEM_VARIABLE,
										Source: &KEP_WF.Input_SystemVariable{
											SystemVariable: &KEP_WF.SystemVariable{
												Name:               entity.SystemVariableHistory,
												DialogHistoryLimit: 2,
											},
										},
									},
								},
							},
							NodeType: KEP_WF.NodeType_ANSWER,
							NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
								Answer: `node_002_output： 
时间： {{sysTime}}；
query： {{sysQuery}}；
历史1轮： {{sysHistory1}}；
历史2轮： {{sysHistory2}}；`,
							}},
							NextNodeIDs: nil,
						},
					},
					Edge: "",
				}},
				Parameters: nil,
			},
		}
		ctx, cancel := context.WithCancel(context.Background())
		defer func() {
			cancel()
			// 确认所有协程是否退出
			time.Sleep(time.Millisecond)
		}()
		replyCh, err := ExecuteWorkflow(ctx, session, "wf_001")
		if err != nil {
			t.Errorf("failed, error: %v", err)
		} else {
			for reply := range replyCh {
				t.Logf("Output: ----- %v", reply)
			}
		}
	})
}

func Test_RunWorkflowLLM(t *testing.T) {
	stubs := getLLMStub()
	defer stubs.Reset()
	Convey("开始+LLM+回复节点，流式回复", t, func() {
		session := &entity.Session{
			Query: "query_001",
			QueryHistory: []*KEP_WF_DM.Message{
				{
					Role:    0,
					Content: "今天天气怎么样？",
				},
				{
					Role:    0,
					Content: "挺好的",
				},
			},
			RWMutex:          sync.RWMutex{},
			LLMResourceLimit: make(chan struct{}, 3),

			App: &entity.App{
				Workflows: map[string]*KEP_WF.Workflow{"wf_001": {
					ProtoVersion: 0,
					WorkflowID:   "",
					WorkflowName: "工作流名称",
					WorkflowDesc: "",
					Nodes: []*KEP_WF.WorkflowNode{
						{
							NodeID:      "node_001",
							NodeName:    "node_001_name",
							NodeType:    KEP_WF.NodeType_START,
							NodeData:    nil,
							NextNodeIDs: []string{"node_002"},
						}, {
							NodeID:   "node_002",
							NodeName: "node_002_name",
							NodeType: KEP_WF.NodeType_LLM,
							NodeData: &KEP_WF.WorkflowNode_LLMNodeData{
								LLMNodeData: &KEP_WF.LLMNodeData{
									ModelName:   "",
									Temperature: 0,
									TopP:        0,
									MaxTokens:   0,
									Prompt:      "",
								},
							},
							NextNodeIDs: []string{"node_003"},
						}, {
							NodeID:   "node_003",
							NodeName: "node_003_name",
							Inputs: []*KEP_WF.InputParam{
								{
									Name: "aa",
									Type: 0,
									Input: &KEP_WF.Input{
										InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
										Source: &KEP_WF.Input_Reference{
											Reference: &KEP_WF.ReferenceFromNode{
												NodeID:   "node_002",
												JsonPath: "Output.Content",
											},
										},
									},
									Desc: "",
								},
							},
							NodeType: KEP_WF.NodeType_ANSWER,
							NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
								Answer: `node_003_output： LLM： {{aa}} ，LLM后面的输入（后面才显示）`,
							}},
							NextNodeIDs: nil,
						},
					},
				}},
				Parameters: nil,
			},
		}
		ctx, cancel := context.WithCancel(context.Background())
		defer func() {
			cancel()
			// 确认所有协程是否退出
			time.Sleep(time.Millisecond)
		}()
		replyCh, err := ExecuteWorkflow(ctx, session, "wf_001")
		if err != nil {
			t.Errorf("failed, error: %v", err)
		} else {
			replyContents := []string{
				"node_003_output： LLM： 这个是前半部分",
				"node_003_output： LLM： 这个是前半部分，这个是后半部分 ，LLM后面的输入（后面才显示）",
			}
			i := 0
			for reply := range replyCh {
				t.Logf("Output: ----- %v", reply)
				So(reply.Respond.Content, ShouldEqual, replyContents[i])
				i++
			}
		}
	})
}

func Test_RunWorkflowTowAnswer(t *testing.T) {
	stubs := getLLMStub()
	defer stubs.Reset()
	Convey("开始+LLM+两个回复节点，流式回复", t, func() {
		session := &entity.Session{
			Query: "query_001",
			QueryHistory: []*KEP_WF_DM.Message{
				{
					Role:    0,
					Content: "今天天气怎么样？",
				},
				{
					Role:    0,
					Content: "挺好的",
				},
			},
			RWMutex:          sync.RWMutex{},
			LLMResourceLimit: make(chan struct{}, 3),

			App: &entity.App{
				Workflows: map[string]*KEP_WF.Workflow{"wf_001": {
					ProtoVersion: 0,
					WorkflowID:   "wf_001",
					WorkflowName: "工作流名称",
					WorkflowDesc: "",
					Nodes: []*KEP_WF.WorkflowNode{
						{
							NodeID:      "node_001",
							NodeName:    "node_001_name",
							NodeType:    KEP_WF.NodeType_START,
							NodeData:    nil,
							NextNodeIDs: []string{"node_002"},
						}, {
							NodeID:   "node_002",
							NodeName: "node_002_name",
							NodeType: KEP_WF.NodeType_LLM,
							NodeData: &KEP_WF.WorkflowNode_LLMNodeData{
								LLMNodeData: &KEP_WF.LLMNodeData{
									ModelName:   "",
									Temperature: 0,
									TopP:        0,
									MaxTokens:   0,
									Prompt:      "",
								},
							},
							NextNodeIDs: []string{"node_003", "node_004"},
						}, {
							NodeID:   "node_003",
							NodeName: "node_003_name",
							Inputs: []*KEP_WF.InputParam{
								{
									Name: "aa",
									Type: 0,
									Input: &KEP_WF.Input{
										InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
										Source: &KEP_WF.Input_Reference{
											Reference: &KEP_WF.ReferenceFromNode{
												NodeID:   "node_002",
												JsonPath: "Output.Content",
											},
										},
									},
									Desc: "",
								},
							},
							NodeType: KEP_WF.NodeType_ANSWER,
							NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
								Answer: `这个是回复节点33333333333333： LLM： {{aa}} ，LLM后面的输入（后面才显示）`,
							}},
							NextNodeIDs: nil,
						}, {
							NodeID:   "node_004",
							NodeName: "node_004_name",
							Inputs: []*KEP_WF.InputParam{
								{
									Name: "aa",
									Type: 0,
									Input: &KEP_WF.Input{
										InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
										Source: &KEP_WF.Input_Reference{
											Reference: &KEP_WF.ReferenceFromNode{
												NodeID:   "node_002",
												JsonPath: "Output.Content",
											},
										},
									},
									Desc: "",
								},
							},
							NodeType: KEP_WF.NodeType_ANSWER,
							NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
								Answer: `这个是回复节点4444444444： LLM： {{aa}} ，LLM后面的输入（后面才显示），空内容： {{NotFound}}`,
							}},
							NextNodeIDs: nil,
						},
					},
				}},
				Parameters: nil,
			},
		}
		ctx, cancel := context.WithCancel(context.Background())
		defer func() {
			cancel()
			// 确认所有协程是否退出
			time.Sleep(time.Millisecond)
		}()
		replyCh, err := ExecuteWorkflow(ctx, session, "wf_001")
		if err != nil {
			t.Errorf("failed, error: %v", err)
		} else {
			expectReplies := []struct {
				Content        string
				WorkflowStatus KEP_WF_DM.WorkflowStatus
				WorkflowID     string
				WorkflowName   string
				NodeLength     int
			}{
				{
					Content:        "这个是回复节点33333333333333： LLM： 这个是前半部分",
					WorkflowStatus: KEP_WF_DM.WorkflowStatus_RUNNING,
					WorkflowID:     "wf_001",
					WorkflowName:   "工作流名称",
					NodeLength:     3,
				},
				{
					Content:        "这个是回复节点33333333333333： LLM： 这个是前半部分，这个是后半部分 ，LLM后面的输入（后面才显示）这个是回复节点4444444444： LLM： 这个是前半部分，这个是后半部分 ，LLM后面的输入（后面才显示），空内容： ",
					WorkflowStatus: KEP_WF_DM.WorkflowStatus_SUCCESS,
					WorkflowID:     "wf_001",
					WorkflowName:   "工作流名称",
					NodeLength:     4,
				},
			}
			i := 0
			for reply := range replyCh {
				t.Logf("Output: ----- %v", reply)
				So(reply.Respond.Content, ShouldEqual, expectReplies[i].Content)
				So(reply.Respond.WorkflowStatus, ShouldEqual, expectReplies[i].WorkflowStatus)
				So(reply.Respond.WorkflowID, ShouldEqual, expectReplies[i].WorkflowID)
				So(reply.Respond.WorkflowName, ShouldEqual, expectReplies[i].WorkflowName)
				So(len(reply.Respond.RunNodes), ShouldEqual, expectReplies[i].NodeLength)
				i++
			}
		}
	})
}

func Test_RunWorkflowTowAnswerFailed(t *testing.T) {
	stubs := getFailedLLMStub()
	defer stubs.Reset()
	Convey("开始+LLM+两个回复节点，流式回复，LLM失败", t, func() {
		session := &entity.Session{
			Query: "query_001",
			QueryHistory: []*KEP_WF_DM.Message{
				{
					Role:    0,
					Content: "今天天气怎么样？",
				},
				{
					Role:    0,
					Content: "挺好的",
				},
			},
			RWMutex:          sync.RWMutex{},
			LLMResourceLimit: make(chan struct{}, 3),

			App: &entity.App{
				Workflows: map[string]*KEP_WF.Workflow{"wf_001": {
					ProtoVersion: 0,
					WorkflowID:   "wf_001",
					WorkflowName: "工作流名称",
					WorkflowDesc: "",
					Nodes: []*KEP_WF.WorkflowNode{
						{
							NodeID:      "node_001",
							NodeName:    "node_001_name",
							NodeType:    KEP_WF.NodeType_START,
							NodeData:    nil,
							NextNodeIDs: []string{"node_002"},
						}, {
							NodeID:   "node_002",
							NodeName: "node_002_name",
							NodeType: KEP_WF.NodeType_LLM,
							NodeData: &KEP_WF.WorkflowNode_LLMNodeData{
								LLMNodeData: &KEP_WF.LLMNodeData{
									ModelName:   "",
									Temperature: 0,
									TopP:        0,
									MaxTokens:   0,
									Prompt:      "",
								},
							},
							NextNodeIDs: []string{"node_003", "node_004"},
						}, {
							NodeID:   "node_003",
							NodeName: "node_003_name",
							Inputs: []*KEP_WF.InputParam{
								{
									Name: "aa",
									Type: 0,
									Input: &KEP_WF.Input{
										InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
										Source: &KEP_WF.Input_Reference{
											Reference: &KEP_WF.ReferenceFromNode{
												NodeID:   "node_002",
												JsonPath: "Output.Content",
											},
										},
									},
									Desc: "",
								},
							},
							NodeType: KEP_WF.NodeType_ANSWER,
							NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
								Answer: `这个是回复节点33333333333333： LLM： {{aa}} ，LLM后面的输入（后面才显示）`,
							}},
							NextNodeIDs: nil,
						}, {
							NodeID:   "node_004",
							NodeName: "node_004_name",
							Inputs: []*KEP_WF.InputParam{
								{
									Name: "aa",
									Type: 0,
									Input: &KEP_WF.Input{
										InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
										Source: &KEP_WF.Input_Reference{
											Reference: &KEP_WF.ReferenceFromNode{
												NodeID:   "node_002",
												JsonPath: "Output.Content",
											},
										},
									},
									Desc: "",
								},
							},
							NodeType: KEP_WF.NodeType_ANSWER,
							NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
								Answer: `这个是回复节点4444444444： LLM： {{aa}} ，LLM后面的输入（后面才显示），空内容： {{NotFound}}`,
							}},
							NextNodeIDs: nil,
						},
					},
				}},
				Parameters: nil,
			},
		}
		ctx, cancel := context.WithCancel(context.Background())
		defer func() {
			cancel()
			// 确认所有协程是否退出
			time.Sleep(time.Millisecond)
		}()
		initConfig()
		replyCh, err := ExecuteWorkflow(ctx, session, "wf_001")
		if err != nil {
			t.Errorf("failed, error: %v", err)
		} else {
			for reply := range replyCh {
				t.Logf("Output: ----- %v", reply)
				So(reply.Respond.Content, ShouldEqual, failedReply)
				So(reply.Respond.WorkflowStatus, ShouldEqual, KEP_WF_DM.WorkflowStatus_FAILED)
				So(reply.Respond.WorkflowID, ShouldEqual, "wf_001")
				So(reply.Respond.WorkflowName, ShouldEqual, "工作流名称")
				So(len(reply.Respond.RunNodes), ShouldEqual, 2)
			}
		}
	})
}

func Test_RunWorkflowTowAnswerPanic(t *testing.T) {
	stubs := getPanicLLMStub()
	defer stubs.Reset()
	Convey("开始+LLM+两个回复节点，流式回复，LLM失败", t, func() {
		session := &entity.Session{
			Query: "query_001",
			QueryHistory: []*KEP_WF_DM.Message{
				{
					Role:    0,
					Content: "今天天气怎么样？",
				},
				{
					Role:    0,
					Content: "挺好的",
				},
			},
			RWMutex:          sync.RWMutex{},
			LLMResourceLimit: make(chan struct{}, 3),

			App: &entity.App{
				Workflows: map[string]*KEP_WF.Workflow{"wf_001": {
					ProtoVersion: 0,
					WorkflowID:   "wf_001",
					WorkflowName: "工作流名称",
					WorkflowDesc: "",
					Nodes: []*KEP_WF.WorkflowNode{
						{
							NodeID:      "node_001",
							NodeName:    "node_001_name",
							NodeType:    KEP_WF.NodeType_START,
							NodeData:    nil,
							NextNodeIDs: []string{"node_002"},
						}, {
							NodeID:   "node_002",
							NodeName: "node_002_name",
							NodeType: KEP_WF.NodeType_LLM,
							NodeData: &KEP_WF.WorkflowNode_LLMNodeData{
								LLMNodeData: &KEP_WF.LLMNodeData{
									ModelName:   "",
									Temperature: 0,
									TopP:        0,
									MaxTokens:   0,
									Prompt:      "",
								},
							},
							NextNodeIDs: []string{"node_003", "node_004"},
						}, {
							NodeID:   "node_003",
							NodeName: "node_003_name",
							Inputs: []*KEP_WF.InputParam{
								{
									Name: "aa",
									Type: 0,
									Input: &KEP_WF.Input{
										InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
										Source: &KEP_WF.Input_Reference{
											Reference: &KEP_WF.ReferenceFromNode{
												NodeID:   "node_002",
												JsonPath: "Output.Content",
											},
										},
									},
									Desc: "",
								},
							},
							NodeType: KEP_WF.NodeType_ANSWER,
							NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
								Answer: `这个是回复节点33333333333333： LLM： {{aa}} ，LLM后面的输入（后面才显示）`,
							}},
							NextNodeIDs: nil,
						}, {
							NodeID:   "node_004",
							NodeName: "node_004_name",
							Inputs: []*KEP_WF.InputParam{
								{
									Name: "aa",
									Type: 0,
									Input: &KEP_WF.Input{
										InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
										Source: &KEP_WF.Input_Reference{
											Reference: &KEP_WF.ReferenceFromNode{
												NodeID:   "node_002",
												JsonPath: "Output.Content",
											},
										},
									},
									Desc: "",
								},
							},
							NodeType: KEP_WF.NodeType_ANSWER,
							NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
								Answer: `这个是回复节点4444444444： LLM： {{aa}} ，LLM后面的输入（后面才显示），空内容： {{NotFound}}`,
							}},
							NextNodeIDs: nil,
						},
					},
				}},
				Parameters: nil,
			},
		}
		ctx, cancel := context.WithCancel(context.Background())
		defer func() {
			cancel()
			// 确认所有协程是否退出
			time.Sleep(time.Millisecond)
		}()
		initConfig()
		replyCh, err := ExecuteWorkflow(ctx, session, "wf_001")
		if err != nil {
			t.Errorf("failed, error: %v", err)
		} else {
			for reply := range replyCh {
				t.Logf("Output: ----- %v", reply)
				So(reply.Respond.Content, ShouldEqual, failedReply)
				So(reply.Respond.WorkflowStatus, ShouldEqual, KEP_WF_DM.WorkflowStatus_FAILED)
				So(reply.Respond.WorkflowID, ShouldEqual, "wf_001")
				So(reply.Respond.WorkflowName, ShouldEqual, "工作流名称")
				So(len(reply.Respond.RunNodes), ShouldEqual, 2)
			}
		}
	})
}

// func Test_RunWorkflowWorkflow(t *testing.T) {
//	stubs := getLLMStub()
//	defer stubs.Reset()
//	Convey("开始+工具+回复节点； 子工作流：开始+LLM+回复节点", t, func() {
//		session := &entity.Session{
//			Query: "query_001",
//			QueryHistory: []*KEP_WF_DM.Message{
//				{
//					Role:    0,
//					Content: "今天天气怎么样？",
//				},
//				{
//					Role:    0,
//					Content: "挺好的",
//				},
//			},
//			RWMutex:          sync.RWMutex{},
//			LLMResourceLimit: make(chan struct{}, 3),
//
//			App: &entity.App{
//				Workflows: map[string]*KEP_WF.Workflow{
//					"wf_001": {
//						ProtoVersion: 0,
//						WorkflowID:   "",
//						WorkflowName: "工作流名称",
//						WorkflowDesc: "",
//						Nodes: []*KEP_WF.WorkflowNode{
//							{
//								NodeID:      "wf1_node_001",
//								NodeName:    "node_001_name",
//								NodeType:    KEP_WF.NodeType_START,
//								NodeData:    nil,
//								NextNodeIDs: []string{"wf1_node_002"},
//							}, {
//								NodeID:   "wf1_node_002",
//								NodeName: "node_002_name",
//								NodeType: KEP_WF.NodeType_TOOL,
//								NodeData: &KEP_WF.WorkflowNode_ToolNodeData{
//									ToolNodeData: &KEP_WF.ToolNodeData{
//										API: &KEP_WF.ToolNodeData_APIInfo{
//											ConfigType: KEP_WF.ToolNodeData_CALL_WORKFLOW,
//											WorkFlowID: "wf_002",
//										},
//									},
//								},
//								NextNodeIDs: []string{"wf1_node_003"},
//							}, {
//								NodeID:   "wf1_node_003",
//								NodeName: "node_003_name",
//								NodeType: KEP_WF.NodeType_ANSWER,
//								NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
//									Answer: `node_003_output：
// LLM： <span data-type="REFERENCE_OUTPUT" data-value="%7B%22InputType%22%3A%20%22REFERENCE_OUTPUT%22%2C%20%22Reference%22%3A%7B%22NodeID%22%3A%20%22wf1_node_002%22%2C%20%22ReferenceInfo%22%3A%20%22output%22%7D%7D" data-info="xxxx" >@TOOL</span>；
// `,
//								}},
//								NextNodeIDs: nil,
//							},
//						},
//					},
//					"wf_002": {
//						ProtoVersion: 0,
//						WorkflowID:   "",
//						WorkflowName: "工作流名称",
//						WorkflowDesc: "",
//						Nodes: []*KEP_WF.WorkflowNode{
//							{
//								NodeID:      "wf2_node_001",
//								NodeName:    "node_001_name",
//								NodeType:    KEP_WF.NodeType_START,
//								NodeData:    nil,
//								NextNodeIDs: []string{"wf2_node_002"},
//							}, {
//								NodeID:   "wf2_node_002",
//								NodeName: "node_002_name",
//								NodeType: KEP_WF.NodeType_LLM,
//								NodeData: &KEP_WF.WorkflowNode_LLMNodeData{
//									LLMNodeData: &KEP_WF.LLMNodeData{
//										ModelName:          "",
//										Temperature:        0,
//										TopP:               0,
//										MaxTokens:          0,
//										Prompt:             "",
//									},
//								},
//								NextNodeIDs: []string{"wf2_node_003"},
//							}, {
//								NodeID:   "wf2_node_003",
//								NodeName: "node_003_name",
//								NodeType: KEP_WF.NodeType_ANSWER,
//								NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
//									Answer: `node_003_output：
// LLM： <span data-type="REFERENCE_OUTPUT" data-value="%7B%22InputType%22%3A%20%22REFERENCE_OUTPUT%22%2C%20%22Reference%22%3A%7B%22NodeID%22%3A%20%22wf2_node_002%22%2C%20%22ReferenceInfo%22%3A%20%22output%22%7D%7D" data-info="xxxx" >@LLM</span>；
// `,
//								}},
//								NextNodeIDs: nil,
//							},
//						},
//					}},
//				Parameters: nil,
//			},
//		}
//		ctx, cancel := context.WithCancel(context.Background())
//		defer func() {
//			cancel()
//			// 确认所有协程是否退出
//			time.Sleep(time.Millisecond)
//		}()
//		replyCh, err := ExecuteWorkflow(ctx, session, "wf_001")
//		if err != nil {
//			t.Errorf("failed, error: %v", err)
//		} else {
//			for reply := range replyCh {
//				t.Logf("Output: ----- %v", reply)
//			}
//		}
//	})
// }
//
// func Test_RunWorkflow3Workflow(t *testing.T) {
//	Convey("两层嵌套： 开始+工具+回复节点； 子工作流：开始+工具+回复节点； 孙子工作流：开始+LLM+回复节点", t, func() {
//		session := &entity.Session{
//			Query: "query_001",
//			QueryHistory: []*KEP_WF_DM.Message{
//				{
//					Role:    0,
//					Content: "今天天气怎么样？",
//				},
//				{
//					Role:    0,
//					Content: "挺好的",
//				},
//			},
//			RWMutex:          sync.RWMutex{},
//			LLMResourceLimit: make(chan struct{}, 3),
//
//			App: &entity.App{
//				Workflows: map[string]*KEP_WF.Workflow{
//					"wf_001": {
//						ProtoVersion: 0,
//						WorkflowID:   "",
//						WorkflowName: "工作流名称",
//						WorkflowDesc: "",
//						Nodes: []*KEP_WF.WorkflowNode{
//							{
//								NodeID:      "wf1_node_001",
//								NodeName:    "node_001_name",
//								NodeType:    KEP_WF.NodeType_START,
//								NodeData:    nil,
//								NextNodeIDs: []string{"wf1_node_002"},
//							}, {
//								NodeID:   "wf1_node_002",
//								NodeName: "node_002_name",
//								NodeType: KEP_WF.NodeType_TOOL,
//								NodeData: &KEP_WF.WorkflowNode_ToolNodeData{
//									ToolNodeData: &KEP_WF.ToolNodeData{
//										API: &KEP_WF.ToolNodeData_APIInfo{
//											ConfigType: KEP_WF.ToolNodeData_CALL_WORKFLOW,
//											WorkFlowID: "wf_002",
//										},
//									},
//								},
//								NextNodeIDs: []string{"wf1_node_003"},
//							}, {
//								NodeID:   "wf1_node_003",
//								NodeName: "node_003_name",
//								NodeType: KEP_WF.NodeType_ANSWER,
//								NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
//									Answer: `node_003_output：
// LLM： <span data-type="REFERENCE_OUTPUT" data-value="%7B%22InputType%22%3A%20%22REFERENCE_OUTPUT%22%2C%20%22Reference%22%3A%7B%22NodeID%22%3A%20%22wf1_node_002%22%2C%20%22ReferenceInfo%22%3A%20%22output%22%7D%7D" data-info="xxxx" >@TOOL</span>；
// `,
//								}},
//								NextNodeIDs: nil,
//							},
//						},
//					},
//					"wf_002": {
//						ProtoVersion: 0,
//						WorkflowID:   "",
//						WorkflowName: "工作流名称",
//						WorkflowDesc: "",
//						Nodes: []*KEP_WF.WorkflowNode{
//							{
//								NodeID:      "wf2_node_001",
//								NodeName:    "node_001_name",
//								NodeType:    KEP_WF.NodeType_START,
//								NodeData:    nil,
//								NextNodeIDs: []string{"wf2_node_002"},
//							}, {
//								NodeID:   "wf2_node_002",
//								NodeName: "node_002_name",
//								NodeType: KEP_WF.NodeType_TOOL,
//								NodeData: &KEP_WF.WorkflowNode_ToolNodeData{
//									ToolNodeData: &KEP_WF.ToolNodeData{
//										API: &KEP_WF.ToolNodeData_APIInfo{
//											ConfigType: KEP_WF.ToolNodeData_CALL_WORKFLOW,
//											WorkFlowID: "wf_003",
//										},
//									},
//								},
//								NextNodeIDs: []string{"wf2_node_003"},
//							}, {
//								NodeID:   "wf2_node_003",
//								NodeName: "node_003_name",
//								NodeType: KEP_WF.NodeType_ANSWER,
//								NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
//									Answer: `node_003_output：
// LLM： <span data-type="REFERENCE_OUTPUT" data-value="%7B%22InputType%22%3A%20%22REFERENCE_OUTPUT%22%2C%20%22Reference%22%3A%7B%22NodeID%22%3A%20%22wf2_node_002%22%2C%20%22ReferenceInfo%22%3A%20%22output%22%7D%7D" data-info="xxxx" >@LLM</span>；
// `,
//								}},
//								NextNodeIDs: nil,
//							},
//						},
//					},
//					"wf_003": {
//						ProtoVersion: 0,
//						WorkflowID:   "",
//						WorkflowName: "工作流名称",
//						WorkflowDesc: "",
//						Nodes: []*KEP_WF.WorkflowNode{
//							{
//								NodeID:      "wf3_node_001",
//								NodeName:    "node_001_name",
//								NodeType:    KEP_WF.NodeType_START,
//								NodeData:    nil,
//								NextNodeIDs: []string{"wf3_node_002"},
//							}, {
//								NodeID:   "wf3_node_002",
//								NodeName: "node_002_name",
//								NodeType: KEP_WF.NodeType_LLM,
//								NodeData: &KEP_WF.WorkflowNode_LLMNodeData{
//									LLMNodeData: &KEP_WF.LLMNodeData{
//										ModelName:          "",
//										Temperature:        0,
//										TopP:               0,
//										MaxTokens:          0,
//										Prompt:             "",
//									},
//								},
//								NextNodeIDs: []string{"wf3_node_003"},
//							}, {
//								NodeID:   "wf3_node_003",
//								NodeName: "node_003_name",
//								NodeType: KEP_WF.NodeType_ANSWER,
//								NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
//									Answer: `wf_node_003_output：
// LLM： <span data-type="REFERENCE_OUTPUT" data-value="%7B%22InputType%22%3A%20%22REFERENCE_OUTPUT%22%2C%20%22Reference%22%3A%7B%22NodeID%22%3A%20%22wf3_node_002%22%2C%20%22ReferenceInfo%22%3A%20%22output%22%7D%7D" data-info="xxxx" >@LLM</span>；
// `,
//								}},
//								NextNodeIDs: nil,
//							},
//						},
//					},
//				},
//				Parameters: nil,
//			},
//		}
//		ctx, cancel := context.WithCancel(context.Background())
//		defer func() {
//			cancel()
//			// 确认所有协程是否退出
//			time.Sleep(time.Millisecond)
//		}()
//		replyCh, err := ExecuteWorkflow(ctx, session, "wf_001")
//		if err != nil {
//			t.Errorf("failed, error: %v", err)
//		} else {
//			for reply := range replyCh {
//				t.Logf("Output: ----- %v", reply)
//			}
//		}
//	})
// }
