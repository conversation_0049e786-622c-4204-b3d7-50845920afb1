package workflow

import (
	"context"
	"errors"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	report "git.woa.com/dialogue-platform/go-comm/plugin"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	plugin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	pluginRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_exec_server"
	"github.com/mark3labs/mcp-go/mcp"
)

// MCPToolResult MCP工具调用结果
type MCPToolResult struct {
	Result *mcp.CallToolResult `json:"result"`
	Error  MCPError            `json:"error"`
}

// MCPError 错误信息
type MCPError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func executePluginNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	LogWorkflow(ctx).Infof("executePluginNode start, nodeName: %v, nodeID: %v", node.NodeName, node.NodeID)
	start := time.Now()
	// 参数初始化
	nodeData := node.GetPluginNodeData()
	if nodeData == nil {
		LogWorkflow(ctx).Errorf("invalid node nodeData")
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.ErrMissingParam)
		return
	}
	// api调用
	headerValues, queryValues, bodyValues, err := parseToolParams(session, nodeTask.BelongNodeID, nodeData.GetToolInputs())
	if err != nil {
		LogWorkflow(ctx).Warnf("invalid node nodeData: %v", err)
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.WrapNodeErr(entity.ErrInvalidParam,
			err.Error()))
		return
	}
	nodeResult := entity.NodeResult{
		BelongNodeID: nodeTask.BelongNodeID,
		NodeID:       node.GetNodeID(),
		Input:        constructToolInput(headerValues, queryValues, bodyValues),
		Status:       entity.NodeStatusRunning,
	}
	nodeResultQueue <- nodeResult

	// 获取MCP的信息
	toolReq := &plugin.DescribeToolReq{
		PluginId: nodeData.PluginID,
		ToolId:   nodeData.ToolID,
	}
	toolRsp, err := dao.Default().DescribeTool(ctx, toolReq)
	if err != nil {
		LogWorkflow(ctx).Warnf("DescribeTool fialed, error: %v", err)
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.WrapNodeErr(
			entity.ErrGetPluginToolInfoFailed, err.Error()))
		return
	}
	reportData, startTime := getReportData(session, toolRsp), time.Now()
	if toolRsp.CallingMethod == "NON_STREAMING" {
		// MCP的处理
		if nodeData.PluginCreateType == KEP_WF.PluginNodeData_MCP {
			mcpToolResult, err := dealMCPNode(ctx, toolRsp, headerValues, bodyValues)
			doReportData(reportData, startTime, err)
			nodeResult.Output = util.ToJsonString(mcpToolResult)
		} else {
			// 普通插件
			result, statisticInfos, err := dealNotStreamTool(ctx, session, nodeData, headerValues, queryValues, bodyValues)
			doReportData(reportData, startTime, err)
			if err != nil {
				LogWorkflow(ctx).Warnf("invalid node nodeData: %v", err)
				sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
					entity.WrapNodeErr(entity.ErrCallPluginToolFailed, err.Error()))
				return
			}
			nodeResult.Output = util.ToJsonString(result)
			nodeResult.StatisticInfo = statisticInfos
		}
		nodeResult.Status = entity.NodeStatusSuccess
		nodeResult.CostMilliSeconds = time.Since(start).Milliseconds()
		nodeResultQueue <- nodeResult

		LogWorkflow(ctx).Infof("executePluginNode done, nodeName: %v", node.NodeName)
		return
	}

	req := &pluginRun.StreamRunToolReq{
		Type: pluginRun.StreamRunToolReq_RUN,
		Req: &pluginRun.RunToolReq{
			AppId:       session.AppID,
			AppScene:    dao.RunEnvToScene(session.RunEnv),
			PluginId:    nodeData.PluginID,
			ToolId:      nodeData.ToolID,
			HeaderValue: util.ToJsonString(headerValues),
			QueryValue:  util.ToJsonString(queryValues),
			BodyValue:   util.ToJsonString(bodyValues),
			ExtraInfo: &pluginRun.ToolExtraInfo{
				SessionId: session.AppID + ":" + session.SessionID,
			},
		},
	}
	daoClient := dao.Default()
	if session.IsAsync {
		daoClient = dao.InAsyncDao()
	}
	// err需要使用entity中的NodeErr，避免错误码、错误信息为空
	ch, err := daoClient.StreamRunTool(ctx, req)
	if err != nil {
		doReportData(reportData, startTime, err)
		LogWorkflow(ctx).Warnf("invalid node nodeData: %v", err)
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
			entity.WrapNodeErr(entity.ErrCallPluginToolFailed, err.Error()))
		return
	}

	var statistic []*KEP_WF_DM.StatisticInfo
	defer func() {
		doReportData(reportData, startTime, err)
		LogWorkflow(ctx).Infof("executePluginNode done, nodeName: %v", node.NodeName)
		nodeResult.CostMilliSeconds = time.Since(start).Milliseconds()
		nodeResult.StatisticInfo = statistic
		nodeResultQueue <- nodeResult
	}()
	for {
		select {
		case <-ctx.Done():
			var reportErr error
			if nodeResult.Status != entity.NodeStatusFailed {
				reportErr = entity.ErrPluginToolResultError
			}
			doReportData(reportData, startTime, reportErr)
			LogWorkflow(ctx).Warnf("executePluginNode, ctx done")
			nodeResult.Status = entity.NodeStatusFailed
			nodeResult.FailMessage = entity.ErrCallPluginToolFailed.Msg
			nodeResult.ErrorCode = entity.ErrCallPluginToolFailed.Code
			return
		case rsp, ok := <-ch:
			if !ok {
				LogWorkflow(ctx).Errorf("executePluginNode, ch unexpected closed")
				nodeResult.Status = entity.NodeStatusFailed
				nodeResult.FailMessage = entity.ErrCallPluginToolFailed.Msg
				nodeResult.ErrorCode = entity.ErrCallPluginToolFailed.Code
				return
			}
			if rsp.GetCode() != 0 {
				LogWorkflow(ctx).Warnf("executePluginNode, StreamRunTool failed, rsp code: %d, message: %v",
					rsp.GetCode(), rsp.GetErrMsg())
				nodeResult.Status = entity.NodeStatusFailed
				nodeResult.FailMessage = entity.ErrCallPluginToolFailed.Msg + ": " + rsp.GetErrMsg()
				nodeResult.ErrorCode = entity.ErrCallPluginToolFailed.Code
				return
			}
			nodeResult.Output = rsp.GetRsp().GetResult()
			for _, statisticInfo := range rsp.GetRsp().StatisticInfos {
				statistic = append(nodeResult.StatisticInfo, &KEP_WF_DM.StatisticInfo{
					ModelName:      statisticInfo.GetModelName(),
					FirstTokenCost: statisticInfo.GetFirstTokenCost(),
					TotalCost:      statisticInfo.GetTotalCost(),
					InputTokens:    statisticInfo.GetInputTokens(),
					OutputTokens:   statisticInfo.GetOutputTokens(),
					TotalTokens:    statisticInfo.GetTotalTokens(),
				})
			}
			if rsp.GetIsFinal() {
				nodeResult.Status = entity.NodeStatusSuccess
				return
			}
			nodeResultQueue <- nodeResult
		}
	}
}

func getReportData(session *entity.Session, toolRsp *plugin.DescribeToolRsp) *report.ReportData {
	return &report.ReportData{
		Dimension: report.Dimension{
			Uin:        fmt.Sprint(session.Uin),
			AppID:      session.AppID,
			PluginID:   toolRsp.PluginId,
			PluginName: toolRsp.PluginName,
			ToolID:     toolRsp.ToolId,
			ToolName:   toolRsp.Name,
			PluginType: int(toolRsp.PluginType),
			CreateType: int(toolRsp.CreateType),
			ErrCode:    0,
		},
		Cost:      0,     // 耗时，单位毫秒
		IsSuccess: false, // 是否成功
	}
}

func doReportData(reportData *report.ReportData, startTime time.Time, err error) {
	reportData.Cost = time.Since(startTime).Milliseconds()
	reportData.IsSuccess = true
	if err != nil {
		reportData.IsSuccess = false
	}
	var errV *errs.Error
	if errors.As(err, &errV) {
		reportData.Dimension.ErrCode = int(errV.Code)
	}
	report.Report(reportData)
}

func dealMCPNode(ctx context.Context, toolRsp *plugin.DescribeToolRsp, headerValues map[string]any,
	bodyValues map[string]any) (*MCPToolResult, error) {
	mcpToolResult := &MCPToolResult{}

	// 调用MCP工具
	toolResult, err := dao.Default().MCPCallTool(ctx, toolRsp.Name, toolRsp.MCPServer, headerValues, bodyValues)
	if err != nil {
		mcpToolResult.Error.Code = -1
		mcpToolResult.Error.Message = err.Error()
	} else {
		mcpToolResult.Result = toolResult
	}
	return mcpToolResult, err
}

func dealNotStreamTool(ctx context.Context, session *entity.Session, nodeData *KEP_WF.PluginNodeData,
	headerM map[string]any, queryM map[string]any, bodyM map[string]any) (string,
	[]*KEP_WF_DM.StatisticInfo, error) {
	req := &pluginRun.RunToolReq{
		AppId:       session.AppID,
		AppScene:    dao.RunEnvToScene(session.RunEnv),
		PluginId:    nodeData.PluginID,
		ToolId:      nodeData.ToolID,
		HeaderValue: util.ToJsonString(headerM),
		QueryValue:  util.ToJsonString(queryM),
		BodyValue:   util.ToJsonString(bodyM),
		ExtraInfo: &pluginRun.ToolExtraInfo{
			SessionId: session.AppID + ":" + session.SessionID,
		},
	}

	daoClient := dao.Default()
	if session.IsAsync {
		daoClient = dao.InAsyncDao()
	}
	rsp, err := daoClient.RunTool(ctx, req)
	if err != nil {
		LogWorkflow(ctx).Warnf("invalid node nodeData: %v", err)
		return "", nil, err
	}

	statisticInfos := make([]*KEP_WF_DM.StatisticInfo, 0)
	for _, statisticInfo := range rsp.StatisticInfos {
		statisticInfos = append(statisticInfos, &KEP_WF_DM.StatisticInfo{
			ModelName:      statisticInfo.GetModelName(),
			FirstTokenCost: statisticInfo.GetFirstTokenCost(),
			TotalCost:      statisticInfo.GetTotalCost(),
			InputTokens:    statisticInfo.GetInputTokens(),
			OutputTokens:   statisticInfo.GetOutputTokens(),
			TotalTokens:    statisticInfo.GetTotalTokens(),
		})
	}
	return rsp.Result, statisticInfos, nil
}
