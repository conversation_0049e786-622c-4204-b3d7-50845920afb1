package workflow

import (
	"context"
	"sync"
	"testing"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	. "github.com/smartystreets/goconvey/convey"
)

func initLoopMax() {
	config.GetMainConfig = func() *config.MainConfig {
		return &config.MainConfig{Workflow: config.Workflow{
			LoopMax: 10,
		}}
	}
}

func Test_RunWorkflowIterationALL(t *testing.T) {
	stubs := getLLMStub()
	defer stubs.Reset()
	initLoopMax()
	Convey("开始+循环（IterationMode： ALL）+回复节点+END； 子工作流：开始+LLM+回复节点+END", t, func() {
		session := &entity.Session{
			Query: "query_001",
			QueryHistory: []*KEP_WF_DM.Message{
				{
					Role:    0,
					Content: "今天天气怎么样？",
				},
				{
					Role:    1,
					Content: "挺好的",
				},
			},
			RWMutex:          sync.RWMutex{},
			LLMResourceLimit: make(chan struct{}, 3),

			App: &entity.App{
				Workflows: map[string]*KEP_WF.Workflow{
					"wf_001": {
						ProtoVersion: 0,
						WorkflowID:   "wf_001",
						WorkflowName: "工作流名称",
						WorkflowDesc: "",
						Nodes: []*KEP_WF.WorkflowNode{
							{
								NodeID:      "wf1_node_001",
								NodeName:    "node_001_name",
								NodeType:    KEP_WF.NodeType_START,
								NodeData:    nil,
								NextNodeIDs: []string{"wf1_node_002"},
							}, {
								NodeID:   "wf1_node_002",
								NodeName: "node_002_name",
								NodeType: KEP_WF.NodeType_ITERATION,
								Inputs: []*KEP_WF.InputParam{
									{
										Name: "Input",
										Type: 0,
										Input: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_USER_INPUT,
											Source: &KEP_WF.Input_UserInputValue{
												UserInputValue: &KEP_WF.UserInputContent{
													Values: []string{"深圳", "上海"},
												},
											},
										},
										Desc:       "",
										IsRequired: false,
									},
									{
										Name: "wf_002_start_node_input_01",
										Type: 0,
										Input: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_USER_INPUT,
											Source: &KEP_WF.Input_UserInputValue{
												UserInputValue: &KEP_WF.UserInputContent{
													Values: []string{"wf_002_start_node_input_01_____Value"},
												},
											},
										},
										Desc:       "",
										IsRequired: false,
									},
								},
								NodeData: &KEP_WF.WorkflowNode_IterationNodeData{
									IterationNodeData: &KEP_WF.IterationNodeData{
										WorkflowID: "wf_002",
										RefInputs: []*KEP_WF.InputParam{
											{
												Name: "wf_002_start_node_input_01",
												Type: 0,
												Input: &KEP_WF.Input{
													InputType: KEP_WF.InputSourceEnum_USER_INPUT,
													Source: &KEP_WF.Input_UserInputValue{
														UserInputValue: &KEP_WF.UserInputContent{
															Values: []string{"wf_002_start_node_input_01_____Value"},
														},
													},
												},
												Desc:       "",
												IsRequired: false,
											},
										},
									},
								},
								NextNodeIDs: []string{"wf1_node_003"},
							}, {
								NodeID:   "wf1_node_003",
								NodeName: "node_003_name",
								NodeType: KEP_WF.NodeType_ANSWER,
								Inputs: []*KEP_WF.InputParam{
									{
										Name: "aa",
										Type: 0,
										Input: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
											Source: &KEP_WF.Input_Reference{
												Reference: &KEP_WF.ReferenceFromNode{
													NodeID:   "wf1_node_002",
													JsonPath: "Output.wf2_node_004_output1",
												},
											},
										},
										Desc:       "",
										IsRequired: false,
									},
								},
								NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
									Answer: `wf1_node_003_output1：{{aa}}；`,
								}},
								NextNodeIDs: []string{"wf1_node_004"},
							}, {
								NodeID:      "wf1_node_004",
								NodeName:    "wf1_node_004_name",
								NodeType:    KEP_WF.NodeType_END,
								NextNodeIDs: nil,
								Outputs: []*KEP_WF.OutputParam{
									{
										Title:      "wf1_node_004_output_01",
										Type:       0,
										Required:   nil,
										Properties: nil,
										Desc:       "",
										Value: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
											Source: &KEP_WF.Input_Reference{
												Reference: &KEP_WF.ReferenceFromNode{
													NodeID:   "wf1_node_003",
													JsonPath: "Output.Answer",
												},
											},
										},
									},
									{
										Title:      "wf1_node_004_output_02_P_S_P",
										Type:       0,
										Required:   nil,
										Properties: nil,
										Desc:       "",
										Value: &KEP_WF.Input{
											// 引用工作流节点的Output(从父工作流 -> 子工作流 -> 父工作流)
											InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
											Source: &KEP_WF.Input_Reference{
												Reference: &KEP_WF.ReferenceFromNode{
													NodeID:   "wf1_node_002",
													JsonPath: "Output.wf2_node_004_output_02",
												},
											},
										},
									},
								},
							},
						},
					},
					"wf_002": {
						ProtoVersion: 0,
						WorkflowID:   "wf_002",
						WorkflowName: "工作流名称",
						WorkflowDesc: "",
						Nodes: []*KEP_WF.WorkflowNode{
							{
								NodeID:      "wf2_node_001",
								NodeName:    "node_001_name",
								NodeType:    KEP_WF.NodeType_START,
								NodeData:    nil,
								NextNodeIDs: []string{"wf2_node_002"},
								Inputs: []*KEP_WF.InputParam{
									{
										Name:       "wf_002_start_node_input_01",
										Type:       0,
										Desc:       "",
										IsRequired: false,
									},
								},
							}, {
								NodeID:   "wf2_node_002",
								NodeName: "node_002_name",
								NodeType: KEP_WF.NodeType_LLM,
								NodeData: &KEP_WF.WorkflowNode_LLMNodeData{
									LLMNodeData: &KEP_WF.LLMNodeData{
										ModelName:   "",
										Temperature: 0,
										TopP:        0,
										MaxTokens:   0,
										Prompt:      "",
									},
								},
								NextNodeIDs: []string{"wf2_node_003"},
							}, {
								NodeID:   "wf2_node_003",
								NodeName: "node_003_name",
								NodeType: KEP_WF.NodeType_ANSWER,
								Inputs: []*KEP_WF.InputParam{
									{
										Name: "aa",
										Type: 0,
										Input: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
											Source: &KEP_WF.Input_Reference{
												Reference: &KEP_WF.ReferenceFromNode{
													NodeID:   "wf2_node_002",
													JsonPath: "Output.Content",
												},
											},
										},
										Desc:       "",
										IsRequired: false,
									},
								},
								NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
									Answer: `wf2_node_003_output：{{aa}}`,
								}},
								NextNodeIDs: []string{"wf2_node_004"},
							}, {
								NodeID:      "wf2_node_004",
								NodeName:    "node_004_name",
								NodeType:    KEP_WF.NodeType_END,
								NextNodeIDs: nil,
								Outputs: []*KEP_WF.OutputParam{
									{
										Title:      "wf2_node_004_output1",
										Type:       0,
										Required:   nil,
										Properties: nil,
										Desc:       "",
										Value: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
											Source: &KEP_WF.Input_Reference{
												Reference: &KEP_WF.ReferenceFromNode{
													NodeID:   "wf2_node_003",
													JsonPath: "Output.Answer",
												},
											},
										},
									},
									{
										Title:      "wf2_node_004_output_02",
										Type:       0,
										Required:   nil,
										Properties: nil,
										Desc:       "",
										Value: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
											Source: &KEP_WF.Input_Reference{
												Reference: &KEP_WF.ReferenceFromNode{
													NodeID:   "wf2_node_001",
													JsonPath: "Output.wf_002_start_node_input_01",
												},
											},
										},
									},
								},
							},
						},
					}},
				Parameters: nil,
			},
		}
		ctx, cancel := context.WithCancel(context.Background())
		defer func() {
			cancel()
			// 确认所有协程是否退出
			time.Sleep(time.Millisecond)
		}()
		replyCh, err := ExecuteWorkflow(ctx, session, "wf_001")
		if err != nil {
			t.Errorf("failed, error: %v", err)
			return
		}
		var reply *KEP_WF_DM.RunWorkflowReply
		for reply = range replyCh {
			t.Logf("Output: ----- %v", util.ToJsonString(reply))
		}
		t.Logf("WorkflowRunStack: ----- %v", util.ToJsonString(session.WorkflowRunStack))
	})
}

func Test_RunWorkflowIterationCondition(t *testing.T) {
	stubs := getLLMStub()
	defer stubs.Reset()
	initLoopMax()
	Convey("开始+循环（IterationMode： Condition）+回复节点+END； 子工作流：开始+LLM+回复节点+END", t, func() {
		session := &entity.Session{
			Query: "query_001",
			QueryHistory: []*KEP_WF_DM.Message{
				{
					Role:    0,
					Content: "今天天气怎么样？",
				},
				{
					Role:    1,
					Content: "挺好的",
				},
			},
			RWMutex:          sync.RWMutex{},
			LLMResourceLimit: make(chan struct{}, 3),

			App: &entity.App{
				Workflows: map[string]*KEP_WF.Workflow{
					"wf_001": {
						ProtoVersion: 0,
						WorkflowID:   "wf_001",
						WorkflowName: "工作流名称",
						WorkflowDesc: "",
						Nodes: []*KEP_WF.WorkflowNode{
							{
								NodeID:      "wf1_node_001",
								NodeName:    "node_001_name",
								NodeType:    KEP_WF.NodeType_START,
								NodeData:    nil,
								NextNodeIDs: []string{"wf1_node_002"},
							}, {
								NodeID:   "wf1_node_002",
								NodeName: "node_002_name",
								NodeType: KEP_WF.NodeType_ITERATION,
								Inputs: []*KEP_WF.InputParam{
									{
										Name: "Input",
										Type: 0,
										Input: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_USER_INPUT,
											Source: &KEP_WF.Input_UserInputValue{
												UserInputValue: &KEP_WF.UserInputContent{
													Values: []string{"深圳", "上海"},
												},
											},
										},
										Desc:       "",
										IsRequired: false,
									},
									{
										Name: "wf1_node_002_input_01",
										Type: 0,
										Input: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_USER_INPUT,
											Source: &KEP_WF.Input_UserInputValue{
												UserInputValue: &KEP_WF.UserInputContent{
													Values: []string{"wf_002_start_node_input_01_____Value"},
												},
											},
										},
										Desc:       "",
										IsRequired: false,
									},
								},
								NodeData: &KEP_WF.WorkflowNode_IterationNodeData{
									IterationNodeData: &KEP_WF.IterationNodeData{
										WorkflowID: "wf_002",
										RefInputs: []*KEP_WF.InputParam{
											{
												Name: "wf_002_start_node_input_01",
												Type: 0,
												Input: &KEP_WF.Input{
													InputType: KEP_WF.InputSourceEnum_NODE_INPUT_PARAM,
													Source: &KEP_WF.Input_NodeInputParamName{
														NodeInputParamName: "Input.Item",
													},
												},
												Desc:       "",
												IsRequired: false,
											},
											{
												Name: "wf_002_start_node_input_02",
												Type: 0,
												Input: &KEP_WF.Input{
													InputType: KEP_WF.InputSourceEnum_NODE_INPUT_PARAM,
													Source: &KEP_WF.Input_NodeInputParamName{
														NodeInputParamName: "wf1_node_002_input_01",
													},
												},
												Desc:       "",
												IsRequired: false,
											},
										},
										IterationMode: KEP_WF.IterationNodeData_BY_CONDITION,
										Condition: &KEP_WF.LogicalExpression{
											LogicalOperator: 0,
											Compound:        nil,
											Comparison: &KEP_WF.LogicalExpression_ComparisonExpression{
												Left: &KEP_WF.Input{
													InputType: KEP_WF.InputSourceEnum_NODE_INPUT_PARAM,
													Source: &KEP_WF.Input_NodeInputParamName{
														NodeInputParamName: "Input.Index",
													},
												},
												LeftType: 0,
												Operator: KEP_WF.LogicalExpression_ComparisonExpression_EQ,
												Right: &KEP_WF.Input{
													InputType: KEP_WF.InputSourceEnum_USER_INPUT,
													Source: &KEP_WF.Input_UserInputValue{
														UserInputValue: &KEP_WF.UserInputContent{Values: []string{"-1"}},
													},
												},
												MatchType: KEP_WF.LogicalExpression_ComparisonExpression_PRECISE,
											},
										},
									},
								},
								NextNodeIDs: []string{"wf1_node_003"},
							}, {
								NodeID:   "wf1_node_003",
								NodeName: "node_003_name",
								NodeType: KEP_WF.NodeType_ANSWER,
								Inputs: []*KEP_WF.InputParam{
									{
										Name: "aa",
										Type: 0,
										Input: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
											Source: &KEP_WF.Input_Reference{
												Reference: &KEP_WF.ReferenceFromNode{
													NodeID:   "wf1_node_002",
													JsonPath: "Output.wf2_node_004_output1",
												},
											},
										},
										Desc:       "",
										IsRequired: false,
									},
								},
								NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
									Answer: `wf1_node_003_output1：{{aa}}；`,
								}},
								NextNodeIDs: []string{"wf1_node_004"},
							}, {
								NodeID:      "wf1_node_004",
								NodeName:    "wf1_node_004_name",
								NodeType:    KEP_WF.NodeType_END,
								NextNodeIDs: nil,
								Outputs: []*KEP_WF.OutputParam{
									{
										Title:      "wf1_node_004_output_01",
										Type:       0,
										Required:   nil,
										Properties: nil,
										Desc:       "",
										Value: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
											Source: &KEP_WF.Input_Reference{
												Reference: &KEP_WF.ReferenceFromNode{
													NodeID:   "wf1_node_003",
													JsonPath: "Output.Answer",
												},
											},
										},
									},
									{
										Title:      "wf1_node_004_output_02_P_S_P",
										Type:       0,
										Required:   nil,
										Properties: nil,
										Desc:       "",
										Value: &KEP_WF.Input{
											// 引用工作流节点的Output(从父工作流 -> 子工作流 -> 父工作流)
											InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
											Source: &KEP_WF.Input_Reference{
												Reference: &KEP_WF.ReferenceFromNode{
													NodeID:   "wf1_node_002",
													JsonPath: "Output.wf2_node_004_output_02",
												},
											},
										},
									},
								},
							},
						},
					},
					"wf_002": {
						ProtoVersion: 0,
						WorkflowID:   "wf_002",
						WorkflowName: "工作流名称",
						WorkflowDesc: "",
						Nodes: []*KEP_WF.WorkflowNode{
							{
								NodeID:      "wf2_node_001",
								NodeName:    "node_001_name",
								NodeType:    KEP_WF.NodeType_START,
								NodeData:    nil,
								NextNodeIDs: []string{"wf2_node_002"},
								Inputs: []*KEP_WF.InputParam{
									{
										Name:       "wf_002_start_node_input_01",
										Type:       0,
										Desc:       "",
										IsRequired: false,
									},
									{
										Name:       "wf_002_start_node_input_02",
										Type:       0,
										Desc:       "",
										IsRequired: false,
									},
								},
							}, {
								NodeID:   "wf2_node_002",
								NodeName: "node_002_name",
								NodeType: KEP_WF.NodeType_LLM,
								NodeData: &KEP_WF.WorkflowNode_LLMNodeData{
									LLMNodeData: &KEP_WF.LLMNodeData{
										ModelName:   "",
										Temperature: 0,
										TopP:        0,
										MaxTokens:   0,
										Prompt:      "",
									},
								},
								NextNodeIDs: []string{"wf2_node_003"},
							}, {
								NodeID:   "wf2_node_003",
								NodeName: "node_003_name",
								NodeType: KEP_WF.NodeType_ANSWER,
								Inputs: []*KEP_WF.InputParam{
									{
										Name: "aa",
										Type: 0,
										Input: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
											Source: &KEP_WF.Input_Reference{
												Reference: &KEP_WF.ReferenceFromNode{
													NodeID:   "wf2_node_002",
													JsonPath: "Output.Content",
												},
											},
										},
										Desc:       "",
										IsRequired: false,
									},
								},
								NodeData: &KEP_WF.WorkflowNode_AnswerNodeData{AnswerNodeData: &KEP_WF.AnswerNodeData{
									Answer: `wf2_node_003_output：{{aa}}`,
								}},
								NextNodeIDs: []string{"wf2_node_004"},
							}, {
								NodeID:      "wf2_node_004",
								NodeName:    "node_004_name",
								NodeType:    KEP_WF.NodeType_END,
								NextNodeIDs: nil,
								Outputs: []*KEP_WF.OutputParam{
									{
										Title:      "wf2_node_004_output1",
										Type:       0,
										Required:   nil,
										Properties: nil,
										Desc:       "",
										Value: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
											Source: &KEP_WF.Input_Reference{
												Reference: &KEP_WF.ReferenceFromNode{
													NodeID:   "wf2_node_003",
													JsonPath: "Output.Answer",
												},
											},
										},
									},
									{
										Title:      "wf2_node_004_output_02",
										Type:       0,
										Required:   nil,
										Properties: nil,
										Desc:       "",
										Value: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
											Source: &KEP_WF.Input_Reference{
												Reference: &KEP_WF.ReferenceFromNode{
													NodeID:   "wf2_node_001",
													JsonPath: "Output.wf_002_start_node_input_01",
												},
											},
										},
									},
									{
										Title:      "wf2_node_004_output_03_start",
										Type:       0,
										Required:   nil,
										Properties: nil,
										Desc:       "",
										Value: &KEP_WF.Input{
											InputType: KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
											Source: &KEP_WF.Input_Reference{
												Reference: &KEP_WF.ReferenceFromNode{
													NodeID:   "wf2_node_001",
													JsonPath: "Output.wf_002_start_node_input_02",
												},
											},
										},
									},
								},
							},
						},
					}},
				Parameters: nil,
			},
		}
		ctx, cancel := context.WithCancel(context.Background())
		defer func() {
			cancel()
			// 确认所有协程是否退出
			time.Sleep(time.Millisecond)
		}()
		replyCh, err := ExecuteWorkflow(ctx, session, "wf_001")
		if err != nil {
			t.Errorf("failed, error: %v", err)
			return
		}
		var reply *KEP_WF_DM.RunWorkflowReply
		for reply = range replyCh {
			t.Logf("Output: ----- %v", util.ToJsonString(reply))
		}
		t.Logf("WorkflowRunStack: ----- %v", util.ToJsonString(session.WorkflowRunStack))
	})
}
