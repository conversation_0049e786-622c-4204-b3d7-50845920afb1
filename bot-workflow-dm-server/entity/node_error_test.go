package entity

import (
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestNodeErr_Error 测试 NodeErr 的 Error 方法
// 验证不同情况下 Error() 方法返回的格式是否符合预期
func TestNodeErr_Error(t *testing.T) {
	tests := []struct {
		name     string
		nodeErr  error
		expected string
	}{
		{
			name:     "空错误对象",
			nodeErr:  NodeErr{},
			expected: "",
		},
		{
			name:     "只有错误码",
			nodeErr:  NodeErr{Code: "TestCode", Msg: ""},
			expected: "code:TestCode, msg:",
		},
		{
			name:     "只有错误信息",
			nodeErr:  NodeErr{Code: "", Msg: "TestMsg"},
			expected: "code:, msg:TestMsg",
		},
		{
			name:     "同时有错误码和错误信息",
			nodeErr:  NodeErr{Code: "TestCode", Msg: "TestMsg"},
			expected: "code:TestCode, msg:TestMsg",
		},
		{
			name:     "预定义错误",
			nodeErr:  ErrUnknown,
			expected: "code:NodeErr.Unknown, msg:未知错误",
		},
		{
			name:     "预定义错误的值类型",
			nodeErr:  *ErrUnknown,
			expected: "code:NodeErr.Unknown, msg:未知错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试值类型
			result := tt.nodeErr.Error()
			assert.Equal(t, tt.expected, result, "值类型测试失败")

			// 测试指针类型
			ptrResult := tt.nodeErr.Error()
			assert.Equal(t, tt.expected, ptrResult, "指针类型测试失败")
		})
	}
}

// TestGetNodeErrCode 测试从不同类型的错误中获取错误码的函数
// 验证 GetNodeErrCode 函数能否正确从各种错误类型中提取错误码
func TestGetNodeErrCode(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected string
		comment  string
	}{
		{
			name:     "空错误",
			err:      nil,
			expected: "",
			comment:  "空错误应返回空字符串",
		},
		{
			name:     "NodeErr指针类型",
			err:      &NodeErr{Code: "TestCode", Msg: "TestMsg"},
			expected: "TestCode",
			comment:  "指针类型应正确返回错误码",
		},
		{
			name:     "NodeErr值类型",
			err:      NodeErr{Code: "TestCode", Msg: "TestMsg"},
			expected: "NodeErr.Unknown",
			comment:  "值类型无法通过类型断言，返回默认错误码",
		},
		{
			name:     "标准错误",
			err:      errors.New("standard error"),
			expected: ErrUnknown.Code,
			comment:  "标准错误应返回默认错误码",
		},
		{
			name:     "包装的NodeErr",
			err:      fmt.Errorf("wrapped: %w", &NodeErr{Code: "WrappedCode", Msg: "WrappedMsg"}),
			expected: "WrappedCode",
			comment:  "通过errors.As可以提取被包装的NodeErr",
		},
		{
			name:     "预定义错误",
			err:      ErrSystemError,
			expected: "NodeErr.SystemError",
			comment:  "预定义错误应返回其错误码",
		},
		{
			name:     "多层包装的错误",
			err:      fmt.Errorf("outer: %w", fmt.Errorf("inner: %w", &NodeErr{Code: "DeepCode", Msg: "DeepMsg"})),
			expected: "DeepCode",
			comment:  "多层包装的错误也应能提取到最内层的NodeErr",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetNodeErrCode(tt.err)
			assert.Equal(t, tt.expected, result, tt.comment)
		})
	}
}

// TestGetNodeErrMsg 测试从不同类型的错误中获取错误信息的函数
// 验证 GetNodeErrMsg 函数能否正确从各种错误类型中提取错误信息
func TestGetNodeErrMsg(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected string
		comment  string
	}{
		{
			name:     "空错误",
			err:      nil,
			expected: "",
			comment:  "空错误应返回空字符串",
		},
		{
			name:     "NodeErr指针类型",
			err:      &NodeErr{Code: "TestCode", Msg: "TestMsg"},
			expected: "TestMsg",
			comment:  "指针类型应正确返回错误信息",
		},
		{
			name:     "NodeErr值类型",
			err:      NodeErr{Code: "TestCode", Msg: "TestMsg"},
			expected: "未知错误: code:TestCode, msg:TestMsg",
			comment:  "值类型被当作标准错误处理，会包含原始错误信息",
		},
		{
			name:     "标准错误",
			err:      errors.New("standard error"),
			expected: "未知错误: standard error",
			comment:  "标准错误会被添加默认错误信息前缀",
		},
		{
			name:     "包装的NodeErr",
			err:      fmt.Errorf("wrapped: %w", &NodeErr{Code: "WrappedCode", Msg: "WrappedMsg"}),
			expected: "WrappedMsg",
			comment:  "通过errors.As可以提取被包装的NodeErr的信息",
		},
		{
			name:     "预定义错误",
			err:      ErrSystemError,
			expected: "系统错误",
			comment:  "预定义错误应返回其错误信息",
		},
		{
			name:     "多层包装的错误",
			err:      fmt.Errorf("outer: %w", fmt.Errorf("inner: %w", &NodeErr{Code: "DeepCode", Msg: "DeepMsg"})),
			expected: "DeepMsg",
			comment:  "多层包装的错误也应能提取到最内层的NodeErr信息",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetNodeErrMsg(tt.err)
			assert.Equal(t, tt.expected, result, tt.comment)
		})
	}
}

// TestGetNodeErrMsgf 测试从错误中获取错误信息并添加额外信息的函数
// 验证 GetNodeErrMsgf 函数能否正确处理各种错误类型和额外信息
func TestGetNodeErrMsgf(t *testing.T) {
	tests := []struct {
		name         string
		err          error
		extraErrMsgs []any
		expected     string
		comment      string
	}{
		{
			name:         "空错误",
			err:          nil,
			extraErrMsgs: nil,
			expected:     "",
			comment:      "空错误应返回空字符串",
		},
		{
			name:         "NodeErr值类型无额外信息",
			err:          NodeErr{Code: "TestCode", Msg: "TestMsg"},
			extraErrMsgs: nil,
			expected:     "未知错误",
			comment:      "值类型无法通过类型断言，返回默认错误信息",
		},
		{
			name:         "NodeErr指针类型无额外信息",
			err:          &NodeErr{Code: "TestCode", Msg: "TestMsg"},
			extraErrMsgs: nil,
			expected:     "TestMsg",
			comment:      "指针类型应正确返回错误信息",
		},
		{
			name:         "预定义错误无额外信息",
			err:          ErrMQSendMessageFailed,
			extraErrMsgs: nil,
			expected:     "消息队列生产者发送消息失败",
			comment:      "预定义错误应返回其错误信息",
		},
		{
			name:         "标准错误无额外信息",
			err:          errors.New("standard error"),
			extraErrMsgs: nil,
			expected:     "未知错误",
			comment:      "标准错误应返回默认错误信息",
		},
		{
			name:         "预定义错误单个额外信息",
			err:          ErrMQSendMessageFailed,
			extraErrMsgs: []any{"Extra Info"},
			expected:     "消息队列生产者发送消息失败: Extra Info",
			comment:      "预定义错误应正确添加额外信息",
		},
		{
			name:         "NodeErr指针类型多个额外信息",
			err:          &NodeErr{Code: "TestCode", Msg: "TestMsg"},
			extraErrMsgs: []any{"Extra1", "Extra2", 123},
			expected:     "TestMsg: Extra1; Extra2; 123",
			comment:      "指针类型应正确添加多个额外信息，并用分号分隔",
		},
		{
			name:         "标准错误单个额外信息",
			err:          errors.New("standard error"),
			extraErrMsgs: []any{"Extra Info"},
			expected:     "未知错误: Extra Info",
			comment:      "标准错误应正确添加额外信息",
		},
		{
			name:         "包装的NodeErr带额外信息",
			err:          fmt.Errorf("wrapped: %w", &NodeErr{Code: "WrappedCode", Msg: "WrappedMsg"}),
			extraErrMsgs: []any{"Extra Wrapped"},
			expected:     "WrappedMsg: Extra Wrapped",
			comment:      "包装的NodeErr应正确提取错误信息并添加额外信息",
		},
		{
			name:         "复杂类型额外信息",
			err:          ErrSystemError,
			extraErrMsgs: []any{map[string]any{"key": "value"}, []int{1, 2, 3}},
			expected:     "系统错误: map[key:value]; [1 2 3]",
			comment:      "应能正确处理复杂类型的额外信息",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetNodeErrMsgf(tt.err, tt.extraErrMsgs...)
			assert.Equal(t, tt.expected, result, tt.comment)
		})
	}
}

// TestWrapNodeErr 测试包装错误并添加额外信息的函数
// 验证 WrapNodeErr 函数能否正确包装错误并添加额外信息
func TestWrapNodeErr(t *testing.T) {
	tests := []struct {
		name         string
		nodeErr      *NodeErr
		extraErrMsgs []any
		expectedCode string
		expectedMsg  string
		comment      string
	}{
		{
			name:         "无额外信息",
			nodeErr:      &NodeErr{Code: "TestCode", Msg: "TestMsg"},
			extraErrMsgs: nil,
			expectedCode: "TestCode",
			expectedMsg:  "TestMsg",
			comment:      "WrapNodeErr对指针类型应保留原始错误信息",
		},
		{
			name:         "单个额外信息",
			nodeErr:      &NodeErr{Code: "TestCode", Msg: "TestMsg"},
			extraErrMsgs: []any{"Extra Info"},
			expectedCode: "TestCode",
			expectedMsg:  "TestMsg: Extra Info",
			comment:      "WrapNodeErr对指针类型应保留原始错误信息并添加额外信息",
		},
		{
			name:         "多个额外信息",
			nodeErr:      &NodeErr{Code: "TestCode", Msg: "TestMsg"},
			extraErrMsgs: []any{"Extra1", "Extra2", 123},
			expectedCode: "TestCode",
			expectedMsg:  "TestMsg: Extra1; Extra2; 123",
			comment:      "WrapNodeErr对指针类型应保留原始错误信息并添加多个额外信息",
		},
		{
			name:         "预定义错误",
			nodeErr:      ErrSystemError,
			extraErrMsgs: []any{"Database connection failed"},
			expectedCode: "NodeErr.SystemError",
			expectedMsg:  "系统错误: Database connection failed",
			comment:      "预定义错误应保留其原始错误信息",
		},
		{
			name:         "空错误码",
			nodeErr:      &NodeErr{Code: "", Msg: "只有错误信息"},
			extraErrMsgs: []any{"额外信息"},
			expectedCode: "",
			expectedMsg:  "只有错误信息: 额外信息",
			comment:      "错误码为空时应保持为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := WrapNodeErr(tt.nodeErr, tt.extraErrMsgs...)

			// 检查结果类型
			nodeErr, ok := result.(*NodeErr)
			assert.True(t, ok, "结果应为*NodeErr类型")

			// 检查错误码和错误信息
			assert.Equal(t, tt.expectedCode, nodeErr.Code, "错误码不匹配: "+tt.comment)
			assert.Equal(t, tt.expectedMsg, nodeErr.Msg, "错误信息不匹配: "+tt.comment)

			// 检查Error()方法
			expectedErrorString := fmt.Sprintf("code:%s, msg:%s", tt.expectedCode, tt.expectedMsg)
			assert.Equal(t, expectedErrorString, result.Error(), "Error()方法返回值不匹配: "+tt.comment)
		})
	}
}

// TestPredefinedErrors 测试预定义错误常量
// 验证预定义的错误常量是否具有正确的错误码和错误信息
func TestPredefinedErrors(t *testing.T) {
	// 测试一组预定义错误，确保它们具有预期的值
	tests := []struct {
		name    string
		nodeErr *NodeErr
		code    string
		msg     string
		comment string
	}{
		{
			name:    "未知错误",
			nodeErr: ErrUnknown,
			code:    "NodeErr.Unknown",
			msg:     "未知错误",
			comment: "基础错误类型",
		},
		{
			name:    "系统错误",
			nodeErr: ErrSystemError,
			code:    "NodeErr.SystemError",
			msg:     "系统错误",
			comment: "系统级错误",
		},
		{
			name:    "参数缺失错误",
			nodeErr: ErrMissingParam,
			code:    "NodeErr.MissingParam",
			msg:     "获取输入变量失败",
			comment: "参数相关错误",
		},
		{
			name:    "大模型请求失败",
			nodeErr: ErrLLMFail,
			code:    "NodeErr.LLMFail",
			msg:     "大模型请求失败",
			comment: "LLM相关错误",
		},
		{
			name:    "消息队列发送失败",
			nodeErr: ErrMQSendMessageFailed,
			code:    "NodeErr.MQSendMessageFailed",
			msg:     "消息队列生产者发送消息失败",
			comment: "消息队列相关错误",
		},
		{
			name:    "工作流终止错误",
			nodeErr: ErrRuntimeWorkflowTerminated,
			code:    "NodeErr.RuntimeWorkflowTerminated",
			msg:     "工作流已运行结束",
			comment: "运行时错误",
		},
		{
			name:    "批处理并行数错误",
			nodeErr: ErrInvalidBatchSize,
			code:    "NodeErr.InvalidBatchSize",
			msg:     "批处理并行数不合法",
			comment: "批处理相关错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.code, tt.nodeErr.Code, "错误码不匹配: "+tt.comment)
			assert.Equal(t, tt.msg, tt.nodeErr.Msg, "错误信息不匹配: "+tt.comment)

			// 测试Error()方法
			expectedErrorString := fmt.Sprintf("code:%s, msg:%s", tt.code, tt.msg)
			assert.Equal(t, expectedErrorString, tt.nodeErr.Error(), "Error()方法返回值不匹配: "+tt.comment)
		})
	}
}
