package entity

// TaskMessage 任务信息
type TaskMessage struct {
	TaskID  string   // 任务ID。 如： 工作流运行实例ID1
	Uin     string   // 账号唯一ID
	Type    TaskType // 任务类型。 如：工作流运行实例
	Queue   string   // 队列名称。
	TraceID string   // 跟踪ID。
	Payload string   // 任务内容。 如： 工作流运行的启动参数
}

// TaskType 任务类型
type TaskType string

const (
	// TaskTypeWorkflowRun 工作流运行
	TaskTypeWorkflowRun TaskType = "WorkflowRun"
)

// // TaskStatus 任务状态
// type TaskStatus string
//
// const (
//	// TaskStatusPending 排队中
//	TaskStatusPending TaskStatus = "Pending"
//	// TaskStatusActive 处理中
//	TaskStatusActive TaskStatus = "Active"
//	// TaskStatusStopping 停止中
//	TaskStatusStopping TaskStatus = "Stopping"
// )

const (
	// DefaultQueue 默认的队列
	DefaultQueue = "default"
)

// TaskAction 任务操作类型
type TaskAction string

const (
	// TaskActionNone 无操作
	TaskActionNone TaskAction = "None"
	// TaskActionCancel 取消操作
	TaskActionCancel TaskAction = "Cancel"
	// TaskActionPause 暂停操作
	TaskActionPause TaskAction = "Pause"
)

// TaskResult 任务的处理结果
type TaskResult struct {
	Status  TaskStatus
	Message string
}

// TaskStatus 任务结果状态
type TaskStatus string

const (
	// TaskStatusSuccess 成功
	TaskStatusSuccess TaskStatus = "Success"
	// TaskStatusFailed 失败
	TaskStatusFailed TaskStatus = "Failed"
	// TaskStatusCancelled 取消
	TaskStatusCancelled TaskStatus = "Cancelled"
	// TaskStatusRetry 任务需要重试
	TaskStatusRetry TaskStatus = "Retry"
)
