package model

import (
	"time"
)

// WorkflowRunState 工作流运行状态
type WorkflowRunState string

const (
	// WorkflowRunStatePending 待处理
	WorkflowRunStatePending WorkflowRunState = "PENDING"
	// WorkflowRunStateRunning 运行中
	WorkflowRunStateRunning WorkflowRunState = "RUNNING"
	// WorkflowRunStateFailed 失败
	WorkflowRunStateFailed WorkflowRunState = "FAILED"
	// WorkflowRunStateSuccess 成功
	WorkflowRunStateSuccess WorkflowRunState = "SUCCESS"
	// WorkflowRunStateCanceled 已取消
	WorkflowRunStateCanceled WorkflowRunState = "CANCELED"
)

// // RunEnv 运行环境
// type RunEnv string
//
// const (
//	// RunEnvTest 测试环境
//	RunEnvTest RunEnv = "TEST"
//	// RunEnvProd 生产环境
//	RunEnvProd RunEnv = "PROD"
// )

// WorkflowRun 工作流运行记录（数据库模型）
type WorkflowRun struct {
	ID            int64  `gorm:"column:f_id;primaryKey;autoIncrement"`
	WorkflowRunID string `gorm:"column:f_workflow_run_id;uniqueIndex:uk_workflow_run_id"`
	// Uin           string `gorm:"column:f_uin"`
	// SubUin        string `gorm:"column:f_sub_uin"`
	// AppID         string `gorm:"column:f_app_id;index:idx_app_id"`
	// WorkflowID    string `gorm:"column:f_workflow_id;index:idx_workflow_id"`
	// Name          string `gorm:"column:f_name"`
	// WorkflowJSON    string           `gorm:"column:f_workflow_json;type:mediumtext"` // 这里不更新，task-config维护
	// ProtoVersion    int8             `gorm:"column:f_proto_version"`
	// RunEnv          RunEnv           `gorm:"column:f_run_env;type:enum('TEST','PROD');default:TEST;index:idx_run_env"`
	// Query           string           `gorm:"column:f_query;type:text"`
	// MainModelName   string           `gorm:"column:f_main_model_name"`
	// CustomVariables string           `gorm:"column:f_custom_variables;type:text"`
	State       WorkflowRunState `gorm:"column:f_state;type:enum('PENDING','RUNNING','FAILED','SUCCESS','CANCELED');default:PENDING;index:idx_state_create_time,priority:1"`
	FailMessage string           `gorm:"column:f_fail_message"`
	// TotalToken      uint32           `gorm:"column:f_total_token"` // 这里不更新，更新节点的时候，实时更新
	WorkflowOutput string     `gorm:"column:f_workflow_output;type:mediumtext"`
	StartTime      *time.Time `gorm:"column:f_start_time"`
	EndTime        *time.Time `gorm:"column:f_end_time"`
	// CreateTime     time.Time  `gorm:"column:f_create_time;index:idx_state_create_time,priority:2"`
	UpdateTime time.Time `gorm:"column:f_update_time;index:idx_update_time"`
}

// TableName 指定表名
func (WorkflowRun) TableName() string {
	return "t_workflow_run"
}

// IsFinished 是否已经完成
func (w *WorkflowRun) IsFinished() bool {
	if w == nil {
		return false
	}
	return w.State == WorkflowRunStateFailed || w.State == WorkflowRunStateSuccess || w.State == WorkflowRunStateCanceled
}
