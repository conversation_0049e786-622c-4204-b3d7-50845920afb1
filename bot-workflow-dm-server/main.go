// KEP.bot-workflow-dm-server
//
// @(#)main.go  December 07, 2023
// Copyright(c) 2023, boyucao@Tencent. All rights reserved.

package main

import (
	"git.code.oa.com/trpc-go/trpc-go"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao/store"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/async_task"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/logger"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/service"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/panicprinter"
	"git.woa.com/dialogue-platform/go-comm/plugin"
	"git.woa.com/dialogue-platform/go-comm/runtime0"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"gopkg.in/yaml.v3"

	_ "git.code.oa.com/trpc-go/trpc-metrics-prometheus"
	_ "git.code.oa.com/trpc-go/trpc-selector-dsn"
	_ "git.woa.com/dialogue-platform/go-comm/trpc0"
	_ "git.woa.com/galileo/trpc-go-galileo"
)

const (
	// 编译版本号, 流水线会注入相关信息
	buildVersion = "服务编译版本号, 勿动~~"
)

// Log Log
var Log = logger.Log

func main() {
	defer panicprinter.PrintPanic()
	runtime0.SetServerVersion(buildVersion)
	runtime0.PrintVersion()
	trpcServer := trpc.NewServer() // 启动一个服务实例
	// router0.Init()        // 连接类资源路由表加载

	// 配置加载
	config.Init()
	store.Init()
	dao.Init()
	async_task.Init()
	plugin.Init()

	KEP_WF_DM.RegisterWorkflowDmService(trpcServer, service.NewWorkflowDmImp())

	// // 启动成功通知
	// "git.woa.com/raven/three-eyed-raven/im"
	// im.ServerStartUpMessage()

	// log
	cfg := trpc.GlobalConfig()
	Log().Info("\n-------------------------------------------------------------------------------")
	g0, _ := yaml.Marshal(cfg.Global)
	Log().Infof("\nGlobal:\n%v", encode.String(g0))
	Log().Info("\n-------------------------------------------------------------------------------")
	s0, _ := yaml.Marshal(cfg.Server)
	Log().Infof("\nServer:\n%v", encode.String(s0))
	Log().Info("\n-------------------------------------------------------------------------------")
	c0, _ := yaml.Marshal(cfg.Client)
	Log().Infof("\nClient:\n%v", encode.String(c0))
	Log().Info("\n-------------------------------------------------------------------------------")
	p0, _ := yaml.Marshal(cfg.Plugins)
	Log().Infof("\nPlugins:\n%v", encode.String(p0))
	Log().Info("\n===============================================================================")

	if err := trpcServer.Serve(); err != nil {
		Log().Fatal(err)
	}
}
