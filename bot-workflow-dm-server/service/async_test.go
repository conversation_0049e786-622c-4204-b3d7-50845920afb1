package service

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao/store"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao/task_store"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/async_task"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestWorkflowDmImp_StartWorkflowRun(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	imp := &WorkflowDmImp{}

	t.Run("Success case", func(t *testing.T) {
		ctx := context.Background()
		req := &KEP_WF_DM.StartWorkflowRunRequest{
			WorkflowRunID: "test-run-id",
			Uin:           "test-uin",
		}

		patches := gomonkey.ApplyFunc(async_task.StartTask, func(ctx context.Context, task entity.TaskMessage) error {
			return nil
		})
		defer patches.Reset()

		reply, err := imp.StartWorkflowRun(ctx, req)
		require.NoError(t, err)
		assert.NotNil(t, reply)
	})

	t.Run("Invalid request case", func(t *testing.T) {
		ctx := context.Background()
		req := &KEP_WF_DM.StartWorkflowRunRequest{} // Empty request

		patches := gomonkey.ApplyFunc(async_task.StartTask, func(ctx context.Context, task entity.TaskMessage) error {
			return errs.New(tconst.ErrCodeInvalidParam, "err.Error()")
		})
		defer patches.Reset()

		reply, err := imp.StartWorkflowRun(ctx, req)
		require.Error(t, err)
		assert.Equal(t, tconst.ErrCodeInvalidParam, int(err.(*errs.Error).Code))
		assert.Nil(t, reply)
	})

	t.Run("StartTask error case", func(t *testing.T) {
		ctx := context.Background()
		req := &KEP_WF_DM.StartWorkflowRunRequest{
			WorkflowRunID: "test-run-id",
			Uin:           "test-uin",
		}

		patches := gomonkey.ApplyFunc(async_task.StartTask, func(ctx context.Context, task entity.TaskMessage) error {
			return errs.New(500, "internal error")
		})
		defer patches.Reset()

		reply, err := imp.StartWorkflowRun(ctx, req)
		require.Error(t, err)
		assert.Nil(t, reply)
	})
}

func TestWorkflowDmImp_StopWorkflowRun(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	imp := &WorkflowDmImp{}

	t.Run("Success case", func(t *testing.T) {
		ctx := context.Background()
		req := &KEP_WF_DM.StopWorkflowRunRequest{
			WorkflowRunID: "test-run-id",
		}

		patches := gomonkey.ApplyFunc(async_task.StopTask, func(ctx context.Context, taskID string) error {
			return nil
		})
		defer patches.Reset()

		reply, err := imp.StopWorkflowRun(ctx, req)
		require.NoError(t, err)
		assert.NotNil(t, reply)
	})

	t.Run("Invalid request case", func(t *testing.T) {
		ctx := context.Background()
		req := &KEP_WF_DM.StopWorkflowRunRequest{} // Empty request

		patches := gomonkey.ApplyFunc(async_task.StopTask, func(ctx context.Context, taskID string) error {
			return errs.New(tconst.ErrCodeInvalidParam, "err.Error()")
		})
		defer patches.Reset()

		reply, err := imp.StopWorkflowRun(ctx, req)
		require.Error(t, err)
		assert.Equal(t, tconst.ErrCodeInvalidParam, int(err.(*errs.Error).Code))
		assert.Nil(t, reply)
	})

	t.Run("StopTask error case", func(t *testing.T) {
		ctx := context.Background()
		req := &KEP_WF_DM.StopWorkflowRunRequest{
			WorkflowRunID: "test-run-id",
		}

		patches := gomonkey.ApplyFunc(async_task.StopTask, func(ctx context.Context, taskID string) error {
			return errs.New(500, "internal error")
		})
		defer patches.Reset()

		reply, err := imp.StopWorkflowRun(ctx, req)
		require.Error(t, err)
		assert.Nil(t, reply)
	})
}

func TestWorkflowDmImp_DescribeWorkflowRunNum(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockStore := store.NewMockStore(ctrl)
	imp := &WorkflowDmImp{store: mockStore}

	t.Run("Single UIN case", func(t *testing.T) {
		ctx := context.Background()
		req := &KEP_WF_DM.DescribeWorkflowRunNumRequest{
			Uin: "test-uin",
		}
		// 方法一：
		imp := &WorkflowDmImp{store: store.DMStore{TaskStore: &task_store.RedisTaskStore{}}}
		var redisTaskStore *task_store.RedisTaskStore
		patches := gomonkey.ApplyMethod(reflect.TypeOf(redisTaskStore), "GetUinTaskCount",
			func(_ *task_store.RedisTaskStore, ctx context.Context, uin string) (int, error) {
				return 5, nil
			})
		defer patches.Reset()

		// 方法二：
		// mockStore.EXPECT().GetUinTaskCount(gomock.Any(), "test-uin").Return(int32(5), nil)

		reply, err := imp.DescribeWorkflowRunNum(ctx, req)
		require.NoError(t, err)
		assert.NotNil(t, reply)
		assert.Equal(t, int32(5), reply.UinInfos["test-uin"].RunningNum)
	})

	t.Run("All UINs case", func(t *testing.T) {
		ctx := context.Background()
		req := &KEP_WF_DM.DescribeWorkflowRunNumRequest{}

		uinToNum := map[string]int32{
			"test-uin1": 3,
			"test-uin2": 7,
		}

		mockStore.EXPECT().GetAllUinTaskCounts(gomock.Any()).Return(uinToNum, nil)

		reply, err := imp.DescribeWorkflowRunNum(ctx, req)
		require.NoError(t, err)
		assert.NotNil(t, reply)
		assert.Equal(t, int32(3), reply.UinInfos["test-uin1"].RunningNum)
		assert.Equal(t, int32(7), reply.UinInfos["test-uin2"].RunningNum)
	})

	t.Run("Store error case", func(t *testing.T) {
		ctx := context.Background()
		req := &KEP_WF_DM.DescribeWorkflowRunNumRequest{
			Uin: "test-uin",
		}

		mockStore.EXPECT().GetUinTaskCount(gomock.Any(), "test-uin").Return(int32(0), errs.New(500, "internal error"))

		reply, err := imp.DescribeWorkflowRunNum(ctx, req)
		require.Error(t, err)
		assert.Nil(t, reply)
	})
}
