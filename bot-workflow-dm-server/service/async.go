package service

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/async_task"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"github.com/asaskevich/govalidator"
	"google.golang.org/protobuf/encoding/protojson"
)

// StartWorkflowRun 启动异步工作流运行实例
func (w WorkflowDmImp) StartWorkflowRun(ctx context.Context, req *KEP_WF_DM.StartWorkflowRunRequest) (
	*KEP_WF_DM.StartWorkflowRunReply, error) {
	LogDMApi(ctx).Infof("StartWorkflowRun, req: %v", util.Pb2String(req))

	if _, err := govalidator.ValidateStruct(req); err != nil {
		return nil, errs.New(tconst.ErrCodeInvalidParam, err.Error())
	}
	taskPayload, _ := protojson.Marshal(req)
	task := entity.TaskMessage{
		TaskID:  req.WorkflowRunID,
		Uin:     req.Uin,
		Type:    entity.TaskTypeWorkflowRun,
		Queue:   entity.DefaultQueue,
		TraceID: util.GetTraceID(ctx),
		Payload: string(taskPayload),
	}
	err := async_task.StartTask(ctx, task)
	if err != nil {
		LogDMApi(ctx).Errorf("StartTask failed, error: %v", err)
		return nil, err
	}

	LogDMApi(ctx).Infof("StartWorkflowRun, done")
	return &KEP_WF_DM.StartWorkflowRunReply{}, nil
}

// StopWorkflowRun 停止异步工作流运行实例
func (w WorkflowDmImp) StopWorkflowRun(ctx context.Context, req *KEP_WF_DM.StopWorkflowRunRequest) (
	*KEP_WF_DM.StopWorkflowRunReply, error) {
	LogDMApi(ctx).Infof("StopWorkflowRun, req: %v", util.Pb2String(req))

	if _, err := govalidator.ValidateStruct(req); err != nil {
		return nil, errs.New(tconst.ErrCodeInvalidParam, err.Error())
	}

	err := async_task.StopTask(ctx, req.WorkflowRunID)
	if err != nil {
		LogDMApi(ctx).Errorf("StopTask failed, error: %v", err)
		return nil, err
	}

	LogDMApi(ctx).Infof("StopWorkflowRun, done")
	return &KEP_WF_DM.StopWorkflowRunReply{}, nil
}

// DescribeWorkflowRunNum 获取异步工作流运行实例数量
func (w WorkflowDmImp) DescribeWorkflowRunNum(ctx context.Context, req *KEP_WF_DM.DescribeWorkflowRunNumRequest) (
	*KEP_WF_DM.DescribeWorkflowRunNumReply, error) {
	LogDMApi(ctx).Infof("DescribeWorkflowRunNum, req: %v", util.Pb2String(req))

	uinInfos := make(map[string]*KEP_WF_DM.UinInfo)
	if len(req.Uin) != 0 {
		num, err := w.store.GetUinTaskCount(ctx, req.Uin)
		if err != nil {
			LogDMApi(ctx).Errorf("GetUinTaskCount failed, error: %v", err)
			return nil, err
		}
		uinInfos[req.Uin] = &KEP_WF_DM.UinInfo{
			RunningNum: num,
		}
	} else {
		uinToNum, err := w.store.GetAllUinTaskCounts(ctx)
		if err != nil {
			LogDMApi(ctx).Errorf("GetAllUin failed, error: %v", err)
			return nil, err
		}
		for uin, num := range uinToNum {
			uinInfos[uin] = &KEP_WF_DM.UinInfo{
				RunningNum: num,
			}
		}
	}

	LogDMApi(ctx).Infof("DescribeWorkflowRunNum, done, result: %v", uinInfos)
	return &KEP_WF_DM.DescribeWorkflowRunNumReply{UinInfos: uinInfos}, nil
}
