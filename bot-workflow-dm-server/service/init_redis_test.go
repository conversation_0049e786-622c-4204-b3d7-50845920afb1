package service

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"testing"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao/store"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
)

const AppID = "1854548189164339200"

func InitTestRedis() func() {
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}

	client := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})

	err = initMiniRedisWithWorkflow(client, AppID, "../gotest/workflows")
	if err != nil {
		panic(err)
	}
	err = initMinRedisWithVar(client, AppID, "../gotest/variables")
	if err != nil {
		panic(err)
	}

	store.SetDefaultRedis(client)

	return func() {
		_ = client.Close()
		s.Close()
	}
}

func TestInitMiniRedisWithWorkflow(t *testing.T) {
	appID := "test_app_id"
	s, err := miniredis.Run()
	assert.NoError(t, err)
	defer s.Close()

	client := redis.NewClient(&redis.Options{
		Addr: s.Addr(),
	})
	defer func() { _ = client.Close() }()

	err = initMiniRedisWithWorkflow(client, appID, "../gotest/workflows")
	assert.NoError(t, err)

	workflowRedisKey := fmt.Sprintf(tconst.WorkflowsKeyFormat, "Sandbox", appID)
	enableWorkflowRedisKey := fmt.Sprintf(tconst.EnableWorkflowsKeyFormat, "Sandbox", appID)
	appRedisKey := fmt.Sprintf(tconst.AppKeyFormat, "Sandbox", appID)

	{
		// 1. hash结构，redis key的格式为 工作流
		workflowIDs, err := client.HKeys(context.Background(), workflowRedisKey).Result()
		assert.NoError(t, err)
		assert.Equal(t, 2, len(workflowIDs))
	}

	{
		// 2hash结构，key的格式为 TaskConfig
		enableWorkflowIDs, err := client.HKeys(context.Background(), enableWorkflowRedisKey).Result()
		assert.NoError(t, err)
		assert.Equal(t, 2, len(enableWorkflowIDs))
	}

	{
		// 3. key的格式为 WF_DM:ENV:Sandbox:AppID:<AppID>
		appStr, err := client.Get(context.Background(), appRedisKey).Result()
		assert.NoError(t, err)
		app := &entity.App{}
		err = json.Unmarshal([]byte(appStr), app)
		assert.NoError(t, err)
		assert.Equal(t, appID, app.AppID)
		assert.Equal(t, "cs-workflow-sandbox-"+appID, app.RetrievalWorkflowGroupID)
	}
}

func initMiniRedisWithWorkflow(client *redis.Client, appID string, dirPath string) error {
	workflowRedisKey := fmt.Sprintf(tconst.WorkflowsKeyFormat, "Sandbox", appID)
	enableWorkflowRedisKey := fmt.Sprintf(tconst.EnableWorkflowsKeyFormat, "Sandbox", appID)
	appRedisKey := fmt.Sprintf(tconst.AppKeyFormat, "Sandbox", appID)

	// 读取目录下所有文件
	files, err := os.ReadDir(dirPath)
	if err != nil {
		return fmt.Errorf("读取目录失败: %v", err)
	}

	for _, file := range files {
		jsonPath := filepath.Join(dirPath, file.Name())
		jsonBytes, err := os.ReadFile(jsonPath)
		if err != nil {
			return err
		}

		var m map[string]interface{}
		err = json.Unmarshal(jsonBytes, &m)
		if err != nil {
			return fmt.Errorf("Unmarshal failed, error: %v, jsonPath: %v", err, jsonPath)
		}
		workflowID, ok := m["WorkflowID"]
		if !ok {
			return fmt.Errorf("file %s not contains WorkflowID field", jsonPath)
		}
		workflowIDStr, ok := workflowID.(string)
		if !ok {
			return fmt.Errorf("WorkflowID field in file %s is not a string", jsonPath)
		}

		// 1. hash结构，redis key的格式为 工作流
		err = client.HSet(context.Background(), workflowRedisKey, workflowIDStr, string(jsonBytes)).Err()
		if err != nil {
			return err
		}

		// 2hash结构，key的格式为 TaskConfig
		err = client.HSet(context.Background(), enableWorkflowRedisKey, workflowIDStr, tconst.WorkflowEnableTrue).Err()
		if err != nil {
			return err
		}
	}

	// 3. key的格式为 WF_DM:ENV:Sandbox:AppID:<AppID>
	appStr, err := json.Marshal(&entity.App{
		AppID:                    appID,
		RetrievalWorkflowGroupID: "cs-workflow-sandbox-" + appID,
	})
	if err != nil {
		return err
	}
	err = client.Set(context.Background(), appRedisKey, appStr, 0).Err()
	if err != nil {
		return err
	}

	return nil
}

func initMinRedisWithVar(client *redis.Client, appID string, dirPath string) error {
	type KeyMeta struct {
		Type string      `json:"type"`
		Data interface{} `json:"data"`
		TTL  int64       `json:"ttl"` // 过期时间(秒)
	}
	type RedisExportData map[string]KeyMeta

	// 读取目录下所有文件
	files, err := os.ReadDir(dirPath)
	if err != nil {
		return fmt.Errorf("读取目录失败: %v", err)
	}

	ctx := context.Background()
	for _, file := range files {
		jsonPath := filepath.Join(dirPath, file.Name())
		jsonBytes, err := os.ReadFile(jsonPath)
		if err != nil {
			return err
		}

		var exportData RedisExportData
		if err := json.Unmarshal(jsonBytes, &exportData); err != nil {
			return fmt.Errorf("解析JSON失败: %s", err)
		}

		for key, meta := range exportData {
			// 使用正则将key中的AppID替换为指定的AppID。  "DM:ENV:Sandbox:RobotID:1854548189164339200:Variables"
			key = regexp.MustCompile(`(DM:ENV:Sandbox:RobotID:)[^:]+(:Variables)`).ReplaceAllString(key, "${1}"+appID+"$2")
			// 可以在这里添加额外的导入过滤条件
			if strings.HasPrefix(key, "temp:") {
				continue // 跳过临时键
			}

			switch meta.Type {
			case "string":
				client.Set(ctx, key, meta.Data.(string), 0)
			case "hash":
				hashData := convertToMap(meta.Data)
				client.HSet(ctx, key, hashData)
			case "list":
				client.Del(ctx, key)
				for _, item := range meta.Data.([]interface{}) {
					client.RPush(ctx, key, item)
				}
			}
		}
	}
	// aa, _ := client.HGet(ctx, "DM:ENV:Sandbox:RobotID:1854548189164339200:Variables", "93e7e50c-4334-4b90-8939-bc8abc0dbd3f").Result()
	// fmt.Println(aa)

	fmt.Println("initMinRedisWithVar 导入完成")
	return nil
}

// 辅助函数：将interface{}转换为map[string]interface{}
func convertToMap(data interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	slice := data.([]interface{})
	for i := 0; i < len(slice); i += 2 {
		key := slice[i].(string)
		value := slice[i+1]
		result[key] = value
	}
	return result
}
