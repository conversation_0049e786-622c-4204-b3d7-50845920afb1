package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/metrics"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/workflow"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"github.com/asaskevich/govalidator"
	"google.golang.org/protobuf/encoding/protojson"
)

// DebugWorkflowNode 非对话类的工作流节点调试
func (w WorkflowDmImp) DebugWorkflowNode(ctx context.Context, req *KEP_WF_DM.DebugWorkflowNodeRequest) (
	*KEP_WF_DM.DebugWorkflowNodeReply, error) {
	// 目前还不需要传mainModel，因为参数、选项卡节点是对话的，条件判断先不做。
	ctx = initCtx(ctx, KEP_WF_DM.RunEnvType_SANDBOX, "", req.AppID, "")
	LogDMApi(ctx).Infof("DebugWorkflowNode, req: %v", util.Pb2String(req))

	node := &KEP_WF.WorkflowNode{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(req.NodeJSON), node)
	if err != nil {
		LogDMApi(ctx).Errorf("invalid node, error: %v; NodeJSON: %v", err, req.NodeJSON)
		return nil, errs.New(tconst.ErrCodeFailed, fmt.Sprintf("invalid node, error: %v", err))
	}

	switch node.NodeType {
	case KEP_WF.NodeType_LLM, KEP_WF.NodeType_PLUGIN,
		KEP_WF.NodeType_TAG_EXTRACTOR, KEP_WF.NodeType_CODE_EXECUTOR,
		KEP_WF.NodeType_TOOL, KEP_WF.NodeType_LOGIC_EVALUATOR,
		KEP_WF.NodeType_ANSWER, KEP_WF.NodeType_INTENT_RECOGNITION,
		KEP_WF.NodeType_VAR_AGGREGATION, KEP_WF.NodeType_MQ:
	case KEP_WF.NodeType_LLM_KNOWLEDGE_QA, KEP_WF.NodeType_KNOWLEDGE_RETRIEVER:
		err = processKnowledgeNode(ctx, req.AppID, node)
		if err != nil {
			LogDMApi(ctx).Errorf("processKnowledgeNode failed, error: %v", err)
			return nil, err
		}
	default:
		LogDMApi(ctx).Errorf("invalid NodeType: %v", node.NodeType)
		return nil, errs.New(tconst.ErrCodeFailed, fmt.Sprintf("invalid NodeType: %v", node.NodeType))
	}

	result, err := workflow.DebugNotDialogNode(ctx, req, node)
	if err != nil {
		LogDMApi(ctx).Errorf("DebugNotDialogNode failed, error: %v", err)
		return nil, errs.New(tconst.ErrCodeFailed, err.Error())
	}

	LogDMApi(ctx).Infof("DebugWorkflowNode, done")
	return result, nil
}

// DebugWorkflowNodeDialog 对话类的工作流节点调试
func (w WorkflowDmImp) DebugWorkflowNodeDialog(server KEP_WF_DM.WorkflowDm_DebugWorkflowNodeDialogServer) error {

	// 不断接受对话和取消的request，并且只有首个对话的request是有效的，其他的直接忽略。
	req, err := server.Recv()
	if err != nil {
		LogDMApi().Errorf("DebugWorkflowNodeDialog, Recv error: %v", err)
		return sendDebugNodeFailed(server, tconst.ErrCodeFailed, "request error", req.SessionID)
	}

	if req.RequestType == KEP_WF_DM.RequestType_STOP {
		LogDMApi().Errorf("first request cannot be STOP")
		return sendDebugNodeFailed(server, tconst.ErrCodeInvalidParam, "first request cannot be STOP", req.SessionID)
	}
	if err = checkDebugWorkflowNodeDialog(req); err != nil {
		return sendDebugNodeFailed(server, tconst.ErrCodeInvalidParam, err.Error(), req.SessionID)
	}
	node := &KEP_WF.WorkflowNode{}
	if err = (protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(req.NodeJSON), node)); err != nil {
		return sendDebugNodeFailed(server, tconst.ErrCodeInvalidParam, err.Error(), req.SessionID)
	}

	switch node.NodeType {
	case KEP_WF.NodeType_PARAMETER_EXTRACTOR, KEP_WF.NodeType_OPTION_CARD,
		KEP_WF.NodeType_WORKFLOW_REF, KEP_WF.NodeType_ITERATION, KEP_WF.NodeType_BATCH:
	default:
		return sendDebugNodeFailed(server, tconst.ErrCodeInvalidParam, "invalid NodeType", req.SessionID)
	}

	ctx := initCtx(server.Context(), KEP_WF_DM.RunEnvType_SANDBOX, req.SessionID, req.AppID, req.MainModelName)
	var cancel context.CancelFunc
	executeTimeLimit := config.GetMainConfig().Workflow.ExecuteTimeLimit
	if executeTimeLimit > 0 {
		ctx, cancel = context.WithTimeout(ctx, time.Second*time.Duration(executeTimeLimit))
	} else {
		ctx, cancel = context.WithCancel(ctx)
	}
	defer cancel()

	subWorkflowID := util.GetSubWorkflowID(node)
	// 获取session
	session, err := w.store.GetSession(ctx, KEP_WF_DM.RunEnvType_SANDBOX, req.SessionID, req.AppID, subWorkflowID)
	if err != nil {
		LogDMApi(ctx).Errorf("DebugWorkflowNodeDialog get session failed, error: %v", err)
		return sendDebugNodeFailed(server, tconst.ErrCodeFailed, fmt.Sprintf("get session failed, error: %v", err),
			req.SessionID)
	}
	// 计费需要的信息，如果没有，需要从admin处获取
	if session.AppType == "" || session.Uin == 0 || session.SID == 0 {
		sessionScene := dao.SceneSandbox
		if session.RunEnv == KEP_WF_DM.RunEnvType_PRODUCT {
			sessionScene = dao.SceneProduct
		}
		appInfo, err := dao.Default().GetAppInfo(ctx, util.ConvertStringToUint64(session.AppID), uint32(sessionScene))
		if err != nil {
			LogDMApi(ctx).Errorf("get app info failed, error: %v", err)
			return sendDebugNodeFailed(server, tconst.ErrCodeFailed, fmt.Sprintf("get app info failed, error: %v", err),
				req.SessionID)
		}
		session.AppType = appInfo.AppType
		uin, sid, err := dao.Default().GetCorpInfo(ctx, appInfo.CorpId)
		if err != nil {
			LogDMApi(ctx).Errorf("get corp info failed, error: %v", err)
			return sendDebugNodeFailed(server, tconst.ErrCodeFailed, fmt.Sprintf("get corp info failed, error: %v", err),
				req.SessionID)
		}
		session.Uin = uin
		session.SID = sid
		session.FinanceSubBizType = tconst.FinanceSubBizTypeKnowledgeQADialogTest
	}
	session.StartDebugNode(req, node)
	ctx = context.WithValue(ctx, tconst.SessionKey, session)

	LogDMApi(ctx).Infof("DebugWorkflowNodeDialog, req: %v", util.Pb2String(req))
	runWFStep := trace.StartStep(ctx, trace.StepKeyDebugWorkflowNodeDialog, req)
	var firstCostMilliseconds int64 = 0
	var reply *KEP_WF_DM.RunWorkflowReply
	defer func() {
		runWFStep.RecordEnd(map[string]interface{}{
			"Reply":                 reply,
			"FirstCostMilliseconds": firstCostMilliseconds,
		}, err)
		metrics.ReportElapsedMetrics(ctx, map[string]string{metrics.DimensionNameSuccess: metrics.GetSuccessValue(err)},
			runWFStep.StartTime, metrics.MetricsNameWorkflowTotalMS)
	}()

	canceled := false
	go func() {
		for {
			msg, msgErr := server.Recv()
			if msgErr != nil || (req != nil && msg.RequestType == KEP_WF_DM.RequestType_STOP) {
				LogDMApi(ctx).Infof("DebugWorkflowNodeDialog request end. msg: %v, msgErr: %v", msg, msgErr)
				cancel()
				canceled = true
				return
			}
		}
	}()

	replyCh, err := workflow.ExecuteWorkflow(ctx, session, entity.ForgedWorkflowID)
	if err != nil {
		LogDMApi(ctx).Errorf("workflow.ExecuteWorkflow failed, error: %v", err)
		var errO *errs.Error
		if errors.As(err, &errO) {
			return sendDebugNodeFailed(server, errO.Code, errO.Msg, req.SessionID)
		}
		return sendDebugNodeFailed(server, tconst.ErrCodeFailed, fmt.Sprintf("execute workflow failed, error: %v", err),
			req.SessionID)
	}
	var lastReply KEP_WF_DM.RunWorkflowReply
	runningNodeIndex := -1
	nodeSentCount := 0
	for reply = range replyCh {
		// 回复的最大长度
		if config.GetMainConfig().Workflow.MaxReplyLength != 0 &&
			len(reply.GetRespond().GetContent()) > config.GetMainConfig().Workflow.MaxReplyLength {
			return sendDebugNodeFailed(server, tconst.ErrCodeFailed, fmt.Sprintf("reply is too long, error: %v", err),
				req.SessionID)
		}
		// 频率控制
		if !needSend(&lastReply, &runningNodeIndex, &nodeSentCount, reply) {
			continue
		}
		if firstCostMilliseconds == 0 && (len(reply.GetRespond().GetContent()) > 0 || reply.GetIsFinal()) {
			firstCostMilliseconds = time.Now().UnixMilli() - runWFStep.StartTime
			metrics.ReportElapsedMetrics(ctx, nil, runWFStep.StartTime, metrics.MetricsNameWorkflowFirstMS)
		}
		err = server.Send(convertReply(reply))
		if err != nil {
			if !canceled {
				LogDMApi(ctx).Errorf("DebugWorkflowNodeDialog server.Send failed, error: %v", err)
			}
			return err
		}
		// LogDMApi(ctx).Debugf("DebugWorkflowNodeDialog server.Send, reply: %v", reply)
	}

	if canceled {
		LogDMApi(ctx).Warnf("DebugWorkflowNodeDialog is canceled")
		return nil
	}

	session.RequestEnd()
	err = w.store.SaveSession(ctx, session)
	if err != nil {
		LogDMApi(ctx).Errorf("DebugWorkflowNodeDialog sync session failed, error: %v", err)
	}
	LogDMApi(ctx).Infof("DebugWorkflowNodeDialog, done")
	return nil
}

func checkDebugWorkflowNodeDialog(req *KEP_WF_DM.DebugWorkflowNodeDialogRequest) error {
	if _, err := govalidator.ValidateStruct(req); err != nil {
		return err
	}

	return nil
}

func sendDebugNodeFailed(server KEP_WF_DM.WorkflowDm_DebugWorkflowNodeDialogServer, code int32, msg,
	sessionID string) error {
	reply := &KEP_WF_DM.DebugWorkflowNodeDialogReply{
		Code:           code,
		Message:        msg,
		SessionID:      sessionID,
		IsFinal:        true,
		Respond:        nil,
		StatisticInfos: nil,
	}
	err := server.Send(reply)
	return err
}

func convertReply(reply *KEP_WF_DM.RunWorkflowReply) *KEP_WF_DM.DebugWorkflowNodeDialogReply {
	return &KEP_WF_DM.DebugWorkflowNodeDialogReply{
		Code:           reply.GetCode(),
		Message:        reply.GetMessage(),
		SessionID:      reply.GetSessionID(),
		IsFinal:        reply.GetIsFinal(),
		Respond:        reply.GetRespond(),
		StatisticInfos: reply.GetStatisticInfos(),
	}
}
