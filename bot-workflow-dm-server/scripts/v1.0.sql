-- 工作流运行实例表（t_workflow_run）：用于存储工作流异步任务的相关信息。
DROP TABLE IF EXISTS `t_workflow_run`;
CREATE TABLE `t_workflow_run`
(
    `f_id`               bigint        NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_workflow_run_id`  varchar(64)   NOT NULL COMMENT '工作流运行实例ID',
    `f_uin`              varchar(32)   NOT NULL COMMENT '主用户ID',
    `f_sub_uin`          varchar(32)   NOT NULL COMMENT '子用户ID',
    `f_app_id`           varchar(64)   NOT NULL COMMENT '应用ID',
    `f_workflow_id`      varchar(64)   NOT NULL COMMENT '所属工作流ID',
    `f_name`             varchar(255)  NOT NULL COMMENT '实例名称，与工作流名称保持一致',
    `f_workflow_json`    mediumtext    NOT NULL COMMENT '工作流内容，包括工作流和子工作的内容，结构如： {"Workflows": {"<workfflow_id>":"{.....}"}}',
    `f_proto_version`    tinyint       NOT NULL COMMENT '对话树的协议版本号',
    `f_run_env`          ENUM('TEST', 'PROD') NOT NULL DEFAULT 'TEST' COMMENT '运行环境',
    `f_query`            text          NOT NULL COMMENT '用户query',
    `f_main_model_name`  varchar(64)   NOT NULL COMMENT '主模型名称',
    `f_custom_variables` text          NOT NULL COMMENT 'API参数的值，JSON格式',
    -- `f_file_infos`      mediumtext    NOT NULL COMMENT '文件信息，JSON格式',
    `f_state`            ENUM('PENDING', 'RUNNING', 'FAILED', 'SUCCESS', 'CANCELED') NOT NULL DEFAULT 'PENDING' COMMENT '工作流运行状态',
    `f_fail_message`     varchar(4096) NOT NULL DEFAULT '' COMMENT '错误信息',
    `f_total_token`      int unsigned  NOT NULL DEFAULT 0 COMMENT '当前请求消耗token数',
    `f_workflow_output`  mediumtext    NOT NULL COMMENT '工作流输出，JSON格式',
    `f_start_time`       datetime(3)   DEFAULT NULL COMMENT '任务开始时间',
    `f_end_time`         datetime(3)   DEFAULT NULL COMMENT '任务结束时间',
    `f_create_time`      datetime(3)   NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `f_update_time`      datetime(3)   NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `f_is_deleted`       tinyint       NOT NULL DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`f_id`),
    UNIQUE KEY `uk_workflow_run_id` (`f_workflow_run_id`),
    KEY                  `idx_app_id` (`f_app_id`),
    KEY                  `idx_run_env` (`f_run_env`),
    KEY                  `idx_state_create_time` (`f_state`, `f_create_time`),
    KEY                  `idx_workflow_id` (`f_workflow_id`),
    KEY                  `idx_update_time` (`f_update_time`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工作流异步任务表';

-- 工作流的运行节点表（t_node_run）：存储工作流中的节点信息，包括异步节点。
DROP TABLE IF EXISTS `t_node_run`;
CREATE TABLE `t_node_run`
(
    `f_id`                bigint        NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `f_workflow_run_id`   varchar(64)   NOT NULL COMMENT '工作流运行实例ID',
    `f_node_run_id`       varchar(64)   NOT NULL COMMENT '节点ID',
    `f_belong_node_id`    varchar(2048) NOT NULL COMMENT '所属的节点ID，记录子工作流的父工作流节点的ID，也包含循环的序号，分隔符为":"',
    `f_workflow_id`       varchar(64)   NOT NULL COMMENT '所属工作流ID',
    `f_node_id`           varchar(64)   NOT NULL COMMENT '节点ID',
    `f_node_name`         varchar(255)  NOT NULL COMMENT '节点名称',
    `f_node_type`         ENUM('UNKNOWN', 'START', 'PARAMETER_EXTRACTOR', 'LLM', 'LLM_KNOWLEDGE_QA',
    'KNOWLEDGE_RETRIEVER', 'TAG_EXTRACTOR', 'CODE_EXECUTOR', 'TOOL', 'LOGIC_EVALUATOR', 'ANSWER', 'OPTION_CARD', 'ITERATION',
    'INTENT_RECOGNITION', 'WORKFLOW_REF', 'PLUGIN', 'END', 'VAR_AGGREGATION', 'BATCH', 'MQ') NOT NULL COMMENT '节点类型',
    `f_state`             ENUM('INIT', 'RUNNING', 'SUCCESS', 'FAILED', 'CANCELED') NOT NULL DEFAULT 'INIT' COMMENT '节点运行状态',
    `f_fail_code`         varchar(100)  NOT NULL DEFAULT '' COMMENT '错误码',
    `f_fail_message`      varchar(4096) NOT NULL DEFAULT '' COMMENT '错误信息',
    `f_input`             mediumtext    NOT NULL COMMENT '节点的输入，JSON格式',
    `f_output`            mediumtext    NOT NULL COMMENT '节点的输出，JSON格式',
    `f_task_output`       mediumtext    NOT NULL COMMENT '节点任务的原始输出，JSON格式',
    `f_cost_milliseconds` int unsigned  NOT NULL COMMENT '执行耗时(ms)',
    `f_statistic_infos`   varchar(8192) NOT NULL COMMENT 'LLM统计信息，JSON格式',
    `f_start_time`        datetime(3)   DEFAULT NULL COMMENT '运行的开始时间',
    `f_end_time`          datetime(3)   DEFAULT NULL COMMENT '运行的结束时间',
    `f_create_time`       datetime(3)   NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `f_update_time`       datetime(3)   NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `f_is_deleted`        tinyint       NOT NULL DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`f_id`),
    UNIQUE KEY `uk_node_run_id` (`f_node_run_id`),
    KEY                   `idx_workflow_run_id` (`f_workflow_run_id`),
    KEY                   `idx_belong_node_id` (`f_belong_node_id`(200))
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '工作流运行节点表';

-- 应用的工作流配置表
DROP TABLE IF EXISTS `t_app_workflow_config`;
CREATE TABLE `t_app_workflow_config`
(
    `f_id`               bigint      NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `f_uin`              VARCHAR(32) NOT NULL DEFAULT '' COMMENT '主用户ID',
    `f_sub_uin`          VARCHAR(32) NOT NULL DEFAULT '' COMMENT '子用户ID',
    `f_app_id`           varchar(64) NOT NULL COMMENT '机器人/应用ID',
    `f_debug_mode`       ENUM('SYNC', 'ASYNC') NOT NULL DEFAULT 'SYNC' COMMENT '调试模型',
    `f_custom_variables` text        NOT NULL COMMENT 'API参数的值，map的json字符串',
    `f_create_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `f_update_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `f_is_deleted`       tinyint     NOT NULL DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`f_id`),
    UNIQUE KEY `uk_app_id` (`f_app_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '应用的工作流配置表';

