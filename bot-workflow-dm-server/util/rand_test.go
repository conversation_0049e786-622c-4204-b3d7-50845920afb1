package util

import (
	"strings"
	"testing"
)

func TestRandStr(t *testing.T) {
	t.Run("returns string of requested length", func(t *testing.T) {
		lengths := []int{0, 1, 5, 10, 100}
		for _, length := range lengths {
			result := RandStr(length)
			if len(result) != length {
				t.<PERSON><PERSON><PERSON>("RandStr(%d) returned string of length %d, expected %d", length, len(result), length)
			}
		}
	})

	t.Run("returns different strings when called multiple times", func(t *testing.T) {
		length := 10
		result1 := RandStr(length)
		result2 := RandStr(length)
		if result1 == result2 {
			t.<PERSON><PERSON><PERSON>("RandStr(%d) returned the same string twice: %s", length, result1)
		}
	})

	t.Run("only uses characters from letters constant", func(t *testing.T) {
		length := 1000 // Large enough to likely include all possible characters
		result := RandStr(length)
		for i, char := range result {
			if !strings.ContainsRune(letters, char) {
				t.<PERSON><PERSON>("RandStr(%d) returned string with invalid character '%c' at position %d", length, char, i)
			}
		}
	})

	t.Run("handles zero length", func(t *testing.T) {
		result := RandStr(0)
		if result != "" {
			t.<PERSON><PERSON><PERSON>("RandStr(0) returned %q, expected empty string", result)
		}
	})

	t.Run("handles large length", func(t *testing.T) {
		length := 10000
		result := RandStr(length)
		if len(result) != length {
			t.Errorf("RandStr(%d) returned string of length %d, expected %d", length, len(result), length)
		}
	})
}
