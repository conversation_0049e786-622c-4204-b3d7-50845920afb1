package util

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/trpc-go/tnet/log"
	"github.com/jinzhu/copier"
)

// IsArray 是否位数组
func IsArray(value reflect.Value) bool {
	return value.Kind() == reflect.Slice || value.Kind() == reflect.Array
}

// IsString 是否位字符串
func IsString(value reflect.Value) bool {
	return value.Kind() == reflect.String
}

// ConvertValue 根据类型转换值
func ConvertValue(orgValue any, toType KEP_WF.TypeEnum) (any, error) {
	var err error
	value := reflect.ValueOf(orgValue)
	switch toType {
	case KEP_WF.TypeEnum_OBJECT:
		ret := make(map[string]any)
		orgValueStr := ToJsonStringNotNull(orgValue)
		err = json.Unmarshal([]byte(orgValueStr), &ret)
		if err != nil {
			return nil, err
		}
		return ret, nil
	case KEP_WF.TypeEnum_ARRAY_OBJECT:
		ret := make([]map[string]any, 0)
		orgValueStr := ToJsonStringNotNull(orgValue)
		err = json.Unmarshal([]byte(orgValueStr), &ret)
		if err != nil {
			return nil, err
		}
		return ret, nil
	case KEP_WF.TypeEnum_STRING, KEP_WF.TypeEnum_FILE, KEP_WF.TypeEnum_DOCUMENT, KEP_WF.TypeEnum_IMAGE,
		KEP_WF.TypeEnum_AUDIO:
		return GetStringFromValue(value)
	case KEP_WF.TypeEnum_INT:
		return GetIntFromValue(value)
	case KEP_WF.TypeEnum_FLOAT:
		return GetFloatFromValue(value)
	case KEP_WF.TypeEnum_BOOL:
		return GetBoolFromValue(value)
	case KEP_WF.TypeEnum_ARRAY_STRING:
		return GetArrayStringFromValue(value)
	case KEP_WF.TypeEnum_ARRAY_INT:
		return GetArrayIntFromValue(value)
	case KEP_WF.TypeEnum_ARRAY_FLOAT:
		return GetArrayFloatFromValue(value)
	case KEP_WF.TypeEnum_ARRAY_BOOL:
		return GetArrayBoolFromValue(value)
	default:
		return nil, fmt.Errorf("ParseOutputValue failed, invalid type:%v", toType)
	}
}

// ParseOutputValue 解析Output
func ParseOutputValue(data map[string]any, params []*KEP_WF.OutputParam) map[string]any {
	res := make(map[string]any)
	for _, param := range params {
		var err error
		outputValue := data[param.GetTitle()]
		value := reflect.ValueOf(outputValue)
		switch param.GetType() {
		case KEP_WF.TypeEnum_OBJECT:
			if subData, ok := data[param.GetTitle()].(map[string]any); ok {
				// 如果配置了至少一个字段，就按照配置的字段来解析，如果一个都没有，就解析所有。
				if len(param.GetProperties()) > 0 {
					res[param.GetTitle()] = ParseOutputValue(subData, param.GetProperties())
				} else {
					res[param.GetTitle()] = subData
				}
			}
		case KEP_WF.TypeEnum_ARRAY_OBJECT:
			if subData, ok := data[param.GetTitle()].([]any); ok {
				subRes := make([]map[string]any, 0)
				for _, subDataItem := range subData {
					if subData2, ok := subDataItem.(map[string]any); ok {
						subRes = append(subRes, ParseOutputValue(subData2, param.GetProperties()))
					}
				}
				res[param.GetTitle()] = subRes
			}
		case KEP_WF.TypeEnum_STRING, KEP_WF.TypeEnum_FILE, KEP_WF.TypeEnum_DOCUMENT, KEP_WF.TypeEnum_IMAGE,
			KEP_WF.TypeEnum_AUDIO:
			res[param.GetTitle()], err = GetStringFromValue(value)
		case KEP_WF.TypeEnum_INT:
			res[param.GetTitle()], err = GetIntFromValue(value)
		case KEP_WF.TypeEnum_FLOAT:
			res[param.GetTitle()], err = GetFloatFromValue(value)
		case KEP_WF.TypeEnum_BOOL:
			res[param.GetTitle()], err = GetBoolFromValue(value)
		case KEP_WF.TypeEnum_ARRAY_STRING:
			res[param.GetTitle()], err = GetArrayStringFromValue(value)
		case KEP_WF.TypeEnum_ARRAY_INT:
			res[param.GetTitle()], err = GetArrayIntFromValue(value)
		case KEP_WF.TypeEnum_ARRAY_FLOAT:
			res[param.GetTitle()], err = GetArrayFloatFromValue(value)
		case KEP_WF.TypeEnum_ARRAY_BOOL:
			res[param.GetTitle()], err = GetArrayBoolFromValue(value)
		default:
			log.Warnf("ParseOutputValue failed, invalid type:%v", param.GetType())
		}
		if err != nil {
			log.Warnf("ParseOutputValue failed, ouput value:%v,need type:%v", outputValue, param.GetType())
		}
	}
	return res
}

// GetStringFromValue 转换string值
func GetStringFromValue(value reflect.Value) (string, error) {
	if !value.IsValid() {
		return "", fmt.Errorf("string, invalid paramValue: %v", value)
	}
	if !IsArray(value) {
		return ToJsonStringNotNull(value.Interface()), nil
	}
	if value.Len() == 0 {
		return "", nil
	}
	paramValueStr := ToJsonStringNotNull(value.Index(0).Interface())
	for j := 1; j < value.Len(); j++ {
		paramValueStr += fmt.Sprintf("，%v", value.Index(j).Interface())
	}
	return paramValueStr, nil
}

// GetIntFromValue 转成int值
func GetIntFromValue(value reflect.Value) (int64, error) {
	if !value.IsValid() {
		return 0, fmt.Errorf("int, invalid paramValue: %v", value)
	}
	paramValueStr := ToJsonStringNotNull(value.Interface())
	if IsArray(value) {
		if value.Len() != 1 {
			return 0, fmt.Errorf("int, invalid paramValue: %v", value.Interface())
		}
		paramValueStr = ToJsonStringNotNull(value.Index(0).Interface())
	}
	return strconv.ParseInt(paramValueStr, 10, 64)
}

// GetFloatFromValue 转成float值
func GetFloatFromValue(value reflect.Value) (float64, error) {
	if !value.IsValid() {
		return 0, fmt.Errorf("float, invalid paramValue: %v", value)
	}
	paramValueStr := ToJsonStringNotNull(value.Interface())
	if IsArray(value) {
		if value.Len() != 1 {
			return 0, fmt.Errorf("float, invalid paramValue: %v", value.Interface())
		}
		paramValueStr = ToJsonStringNotNull(value.Index(0).Interface())
	}
	return strconv.ParseFloat(paramValueStr, 64)
}

// GetBoolFromValue 转成bool值
func GetBoolFromValue(value reflect.Value) (bool, error) {
	if !value.IsValid() {
		return false, fmt.Errorf("bool, invalid paramValue: %v", value)
	}
	paramValueStr := ToJsonStringNotNull(value.Interface())
	if IsArray(value) {
		if value.Len() != 1 {
			return false, fmt.Errorf("bool, invalid paramValue: %v", value.Interface())
		}
		paramValueStr = ToJsonStringNotNull(value.Index(0).Interface())
	}
	switch paramValueStr {
	case "1", "true", "是", "是的", "对", "对的", "True":
		return true, nil
	case "0", "false", "不是", "不是的", "不对", "不对呀", "False":
		return false, nil
	default:
		return false, fmt.Errorf("invalid bool value: %v", paramValueStr)
	}
}

// GetArrayStringFromValue 转成array<string>值
func GetArrayStringFromValue(value reflect.Value) ([]string, error) {
	if !value.IsValid() {
		return nil, fmt.Errorf("array string, invalid paramValue: %v", value)
	}
	if !IsArray(value) {
		ret := make([]string, 0)
		orgValueStr := ToJsonStringNotNull(value.Interface())
		if orgValueStr == "" {
			return ret, nil
		}
		err := json.Unmarshal([]byte(orgValueStr), &ret)
		if err == nil {
			return ret, nil
		}
		s, _ := GetStringFromValue(value)
		return []string{s}, nil
	}
	res := make([]string, 0, value.Len())
	for i := 0; i < value.Len(); i++ {
		s, err := GetStringFromValue(value.Index(i))
		if err != nil {
			return nil, err
		}
		res = append(res, s)
	}
	return res, nil
}

// GetArrayIntFromValue 转成array<int>值
func GetArrayIntFromValue(value reflect.Value) ([]int64, error) {
	if !value.IsValid() {
		return nil, fmt.Errorf("array int, invalid paramValue: %v", value)
	}
	if !IsArray(value) {
		ret := make([]int64, 0)
		orgValueStr := ToJsonStringNotNull(value.Interface())
		if orgValueStr == "" {
			return ret, nil
		}
		err := json.Unmarshal([]byte(orgValueStr), &ret)
		if err == nil {
			return ret, nil
		}
		s, err := GetIntFromValue(value)
		if err != nil {
			return nil, err
		}
		return []int64{s}, nil
	}
	res := make([]int64, 0, value.Len())
	for i := 0; i < value.Len(); i++ {
		s, err := GetIntFromValue(value.Index(i))
		if err != nil {
			return nil, err
		}
		res = append(res, s)
	}
	return res, nil
}

// GetArrayFloatFromValue 转成array<float>值
func GetArrayFloatFromValue(value reflect.Value) ([]float64, error) {
	if !value.IsValid() {
		return nil, fmt.Errorf("array float, invalid paramValue: %v", value)
	}
	if !IsArray(value) {
		ret := make([]float64, 0)
		orgValueStr := ToJsonStringNotNull(value.Interface())
		if orgValueStr == "" {
			return ret, nil
		}
		err := json.Unmarshal([]byte(orgValueStr), &ret)
		if err == nil {
			return ret, nil
		}
		s, err := GetFloatFromValue(value)
		if err != nil {
			return nil, err
		}
		return []float64{s}, nil
	}
	res := make([]float64, 0, value.Len())
	for i := 0; i < value.Len(); i++ {
		s, err := GetFloatFromValue(value.Index(i))
		if err != nil {
			return nil, err
		}
		res = append(res, s)
	}
	return res, nil
}

// GetArrayBoolFromValue 转成array<bool>值
func GetArrayBoolFromValue(value reflect.Value) ([]bool, error) {
	if !value.IsValid() {
		return nil, fmt.Errorf("array bool, invalid paramValue: %v", value)
	}
	if !IsArray(value) {
		ret := make([]bool, 0)
		orgValueStr := ToJsonStringNotNull(value.Interface())
		if orgValueStr == "" {
			return ret, nil
		}
		err := json.Unmarshal([]byte(orgValueStr), &ret)
		if err == nil {
			return ret, nil
		}
		s, err := GetBoolFromValue(value)
		if err != nil {
			return nil, err
		}
		return []bool{s}, nil
	}
	res := make([]bool, 0, value.Len())
	for i := 0; i < value.Len(); i++ {
		s, err := GetBoolFromValue(value.Index(i))
		if err != nil {
			return nil, err
		}
		res = append(res, s)
	}
	return res, nil
}

// GetValueByPath 从输出中取对应的值
func GetValueByPath(output, path string) any {
	var value any
	err := json.Unmarshal([]byte(output), &value)
	if err != nil {
		return nil
	}
	if path == "" {
		return value
	}
	parts := strings.Split(path, ".")
	for _, part := range parts {
		// 按照jsonPath的格式，获取对应的值
		if value == nil {
			return nil
		}
		switch val := value.(type) {
		case map[string]any:
			value = val[part]
		case []any:
			if len(val) == 0 {
				return nil
			}
			value = val[0]
			switch valItem := value.(type) {
			case map[string]any:
				value = valItem[part]
			default:
				return nil
			}
		default:
			return nil
		}
	}
	return value
}

// CopySlice 复制切片
func CopySlice[T any](src []T) []T {
	dst := make([]T, len(src))
	copy(dst, src)
	return dst
}

// DeepCopySlice 深复制切片
func DeepCopySlice[T any](src []T) []T {
	dst := make([]T, len(src))
	_ = copier.Copy(&dst, src)
	return dst
}
