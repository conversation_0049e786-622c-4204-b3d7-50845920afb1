package util

import "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"

// GetSubWorkflowID 获取子工作流ID
func GetSubWorkflowID(node *KEP_WF.WorkflowNode) string {
	switch node.GetNodeType() {
	case KEP_WF.NodeType_WORKFLOW_REF:
		return node.GetWorkflowRefNodeData().GetWorkflowID()
	case KEP_WF.NodeType_ITERATION:
		return node.GetIterationNodeData().GetWorkflowID()
	case KEP_WF.NodeType_BATCH:
		return node.GetBatchNodeData().GetWorkflowID()
	default:
		return ""
	}
}
