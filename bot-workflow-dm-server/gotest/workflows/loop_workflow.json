{"ProtoVersion": "V2_6", "WorkflowID": "a0c07cee-4277-4528-bb40-a6e2f7d9079e", "WorkflowName": "测试批处理", "WorkflowDesc": "测试批处理", "Nodes": [{"NodeID": "ce163a12-48d0-39ef-4c86-b893e266d697", "NodeName": "开始", "NodeDesc": "", "NodeType": "START", "StartNodeData": {}, "Inputs": [{"Name": "ao", "Type": "ARRAY_OBJECT", "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "[{\"a\":1,\"b\":2},{\"a\":2,\"b\":3},{\"a\":3,\"b\":4},{\"a\":4,\"b\":5},{\"a\":5,\"b\":6}]", "DefaultFileName": ""}], "Outputs": [], "NextNodeIDs": ["e7e4a224-477a-8b00-925d-17aa19ef9991"], "NodeUI": "{\"data\":{\"content\":{\"inputs\":[\"ao\"]},\"isHovering\":false,\"isParallel\":false,\"source\":false,\"target\":true,\"debug\":null,\"error\":false,\"output\":[{\"id\":\"a99179a4-5832-037e-7c5c-0f151f8b14fa\",\"value\":\"ao\",\"label\":\"ao\",\"type\":\"ARRAY_OBJECT\",\"children\":[]}]},\"position\":{\"x\":155,\"y\":252},\"targetPosition\":\"left\",\"sourcePosition\":\"right\",\"selected\":false,\"measured\":{\"width\":250,\"height\":84}}"}, {"NodeID": "fd30326e-0e42-fdee-b506-feaad461ef94", "NodeName": "结束", "NodeDesc": "", "NodeType": "END", "Inputs": [], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Required": [], "Properties": [{"Title": "Results", "Type": "ARRAY_OBJECT", "Required": [], "Properties": [], "Desc": "", "Value": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "6ae22513-b83a-46d5-d1d7-c5552612e652", "JsonPath": "Output.Results"}}, "AnalysisMethod": "COVER"}], "Desc": "输出内容", "AnalysisMethod": "COVER"}], "NextNodeIDs": [], "NodeUI": "{\"data\":{\"content\":{\"outputs\":[\"Output\",\"Output.Results\"]},\"isHovering\":false,\"isParallel\":false,\"source\":true,\"target\":false,\"debug\":null,\"error\":false,\"output\":[{\"id\":\"ba9933be-9bfc-66e5-1205-49c4d98ac5bc\",\"value\":\"Output\",\"label\":\"Output\",\"_uiId\":\"7fbe66c6-c7b3-8d87-3c29-ae476e28b136\",\"type\":\"OBJECT\",\"children\":[{\"id\":\"9b6146bc-c016-7787-eb4a-6afbe5f09e0e\",\"value\":\"Results\",\"label\":\"Results\",\"_uiId\":\"f8ea8989-0094-c61f-d5bb-9e27f6022d7a\",\"type\":\"ARRAY_OBJECT\",\"children\":[]}]}]},\"position\":{\"x\":1055,\"y\":252},\"targetPosition\":\"left\",\"sourcePosition\":\"right\",\"selected\":false,\"measured\":{\"width\":250,\"height\":84}}"}, {"NodeID": "6ae22513-b83a-46d5-d1d7-c5552612e652", "NodeName": "批处理1", "NodeDesc": "", "NodeType": "BATCH", "BatchNodeData": {"BodyType": "WORKFLOW", "WorkflowID": "883f8c7f-e2bd-4f7b-8dc2-3cd5da34d4e9", "RefInputs": [{"Name": "i", "Type": "INT", "Input": {"InputType": "NODE_INPUT_PARAM", "NodeInputParamName": "ai.Item"}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}, {"Name": "o", "Type": "OBJECT", "Input": {"InputType": "NODE_INPUT_PARAM", "NodeInputParamName": "ao.Item"}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}], "MaxParallel": 3, "SpecifiedTraversalVariable": "ao"}, "Inputs": [{"Name": "ai", "Type": "ARRAY_INT", "Input": {"InputType": "CUSTOM_VARIABLE", "CustomVarID": "a6ef3042-26bc-4d59-8085-162bd0a24041"}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}, {"Name": "ao", "Type": "ARRAY_OBJECT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "ce163a12-48d0-39ef-4c86-b893e266d697", "JsonPath": "ao"}}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}], "Outputs": [], "NextNodeIDs": ["fd30326e-0e42-fdee-b506-feaad461ef94"], "NodeUI": "{\"data\":{\"content\":{\"maxParallel\":3,\"workflowRefNodeName\":\"测试批处理子工作流\"},\"isHovering\":false,\"isParallel\":false,\"source\":true,\"target\":true,\"debug\":null,\"error\":false,\"output\":[{\"label\":\"Output\",\"desc\":\"输出内容\",\"optionType\":\"REFERENCE_OUTPUT\",\"type\":\"OBJECT\",\"children\":[]}]},\"position\":{\"x\":755,\"y\":252},\"targetPosition\":\"left\",\"sourcePosition\":\"right\",\"selected\":true,\"measured\":{\"width\":250,\"height\":104},\"dragging\":false}"}, {"NodeID": "e7e4a224-477a-8b00-925d-17aa19ef9991", "NodeName": "循环1", "NodeDesc": "", "NodeType": "ITERATION", "IterationNodeData": {"BodyType": "WORKFLOW", "WorkflowID": "883f8c7f-e2bd-4f7b-8dc2-3cd5da34d4e9", "RefInputs": [{"Name": "i", "Type": "INT", "Input": {"InputType": "NODE_INPUT_PARAM", "NodeInputParamName": "ai.Item"}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}, {"Name": "o", "Type": "OBJECT", "Input": {"InputType": "NODE_INPUT_PARAM", "NodeInputParamName": "ao.Item"}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}], "IterationMode": "ALL", "Condition": {"LogicalOperator": "UNSPECIFIED", "Compound": [], "Comparison": {"Left": {"InputType": "CUSTOM_VARIABLE", "CustomVarID": ""}, "LeftType": "STRING", "Operator": "UNSPECIFIED", "Right": {"InputType": "CUSTOM_VARIABLE", "CustomVarID": ""}, "MatchType": "SEMANTIC"}}, "SpecifiedTraversalVariable": "ao"}, "Inputs": [{"Name": "ai", "Type": "ARRAY_INT", "Input": {"InputType": "CUSTOM_VARIABLE", "CustomVarID": "a6ef3042-26bc-4d59-8085-162bd0a24041"}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}, {"Name": "ao", "Type": "ARRAY_OBJECT", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "ce163a12-48d0-39ef-4c86-b893e266d697", "JsonPath": "ao"}}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}], "Outputs": [], "NextNodeIDs": ["6ae22513-b83a-46d5-d1d7-c5552612e652"]}], "Edge": "[{\"source\":\"6ae22513-b83a-46d5-d1d7-c5552612e652\",\"sourceHandle\":\"6ae22513-b83a-46d5-d1d7-c5552612e652-source\",\"target\":\"fd30326e-0e42-fdee-b506-feaad461ef94\",\"targetHandle\":\"fd30326e-0e42-fdee-b506-feaad461ef94-target\",\"type\":\"custom\",\"data\":{\"connectedNodeIsHovering\":false,\"error\":false,\"isHovering\":false},\"id\":\"xy-edge__6ae22513-b83a-46d5-d1d7-c5552612e6526ae22513-b83a-46d5-d1d7-c5552612e652-source-fd30326e-0e42-fdee-b506-feaad461ef94fd30326e-0e42-fdee-b506-feaad461ef94-target\",\"selected\":false,\"animated\":false},{\"source\":\"ce163a12-48d0-39ef-4c86-b893e266d697\",\"sourceHandle\":\"ce163a12-48d0-39ef-4c86-b893e266d697-source\",\"target\":\"e7e4a224-477a-8b00-925d-17aa19ef9991\",\"type\":\"custom\",\"data\":{\"connectedNodeIsHovering\":false,\"error\":false,\"isHovering\":false},\"id\":\"xy-edge__ce163a12-48d0-39ef-4c86-b893e266d697ce163a12-48d0-39ef-4c86-b893e266d697-source-e7e4a224-477a-8b00-925d-17aa19ef9991\",\"animated\":false},{\"source\":\"e7e4a224-477a-8b00-925d-17aa19ef9991\",\"target\":\"6ae22513-b83a-46d5-d1d7-c5552612e652\",\"type\":\"custom\",\"data\":{\"connectedNodeIsHovering\":false,\"error\":false,\"isHovering\":false},\"sourceHandle\":\"\",\"id\":\"xy-edge__e7e4a224-477a-8b00-925d-17aa19ef9991-6ae22513-b83a-46d5-d1d7-c5552612e652\",\"animated\":false}]", "Mode": "NORMAL", "ReleaseTime": ""}