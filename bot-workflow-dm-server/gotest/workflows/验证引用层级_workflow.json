{"ProtoVersion": "V2_6", "WorkflowID": "b77ca6c7-af5a-4a3a-ae77-c55a6176c994", "WorkflowName": "验证引用层级", "WorkflowDesc": "验证引用层级", "Nodes": [{"NodeID": "caf32fbe-50ef-e71d-5322-76e3f6614c2a", "NodeName": "开始", "NodeDesc": "", "NodeType": "START", "StartNodeData": {}, "Inputs": [], "Outputs": [], "NextNodeIDs": ["f48f9dbd-39da-c5c2-4f5f-b56dbbea3d19"], "NodeUI": "{\"data\":{\"content\":{\"inputs\":[]},\"isHovering\":false,\"isParallel\":false,\"source\":false,\"target\":true,\"debug\":null,\"error\":false,\"output\":[]},\"position\":{\"x\":400,\"y\":282},\"targetPosition\":\"left\",\"sourcePosition\":\"right\",\"selected\":false,\"measured\":{\"width\":250,\"height\":84}}"}, {"NodeID": "d22c4d42-ec20-b980-e505-433113e0b75e", "NodeName": "结束", "NodeDesc": "", "NodeType": "END", "Inputs": [], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Required": [], "Properties": [], "Desc": "输出内容", "AnalysisMethod": "COVER"}], "NextNodeIDs": [], "NodeUI": "{\"data\":{\"content\":\"\",\"isHovering\":false,\"isParallel\":false,\"source\":true,\"target\":false,\"debug\":null,\"error\":false,\"output\":[{\"label\":\"Output\",\"desc\":\"输出内容\",\"optionType\":\"REFERENCE_OUTPUT\",\"type\":\"OBJECT\",\"children\":[]}]},\"position\":{\"x\":1389,\"y\":290},\"targetPosition\":\"left\",\"sourcePosition\":\"right\",\"selected\":false,\"measured\":{\"width\":250,\"height\":84},\"dragging\":false}"}, {"NodeID": "392742b1-1218-1174-0cc6-b73442ab79a4", "NodeName": "循环1", "NodeDesc": "", "NodeType": "ITERATION", "IterationNodeData": {"BodyType": "WORKFLOW", "WorkflowID": "eb581b8b-bd74-4339-823b-3cdde359d8c0", "RefInputs": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "ARRAY_OBJECT", "Input": {"InputType": "NODE_INPUT_PARAM", "NodeInputParamName": "<PERSON><PERSON><PERSON><PERSON>"}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}, {"Name": "str", "Type": "STRING", "Input": {"InputType": "NODE_INPUT_PARAM", "NodeInputParamName": "str"}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}, {"Name": "doc", "Type": "STRING", "Input": {"InputType": "NODE_INPUT_PARAM", "NodeInputParamName": "doc"}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}, {"Name": "item", "Type": "OBJECT", "Input": {"InputType": "NODE_INPUT_PARAM", "NodeInputParamName": "arrob<PERSON><PERSON><PERSON><PERSON>"}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}], "IterationMode": "ALL", "Condition": {"LogicalOperator": "UNSPECIFIED", "Compound": [], "Comparison": {"Left": {"InputType": "CUSTOM_VARIABLE", "CustomVarID": ""}, "LeftType": "STRING", "Operator": "UNSPECIFIED", "Right": {"InputType": "CUSTOM_VARIABLE", "CustomVarID": ""}, "MatchType": "SEMANTIC"}}, "SpecifiedTraversalVariable": "lst"}, "Inputs": [{"Name": "lst", "Type": "ARRAY_STRING", "Input": {"InputType": "REFERENCE_OUTPUT", "Reference": {"NodeID": "f48f9dbd-39da-c5c2-4f5f-b56dbbea3d19", "JsonPath": "Output.lst"}}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "ARRAY_OBJECT", "Input": {"InputType": "CUSTOM_VARIABLE", "CustomVarID": "ec1cf0b5-4ca4-4d45-98a0-8d7b47d254c0"}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}, {"Name": "str", "Type": "STRING", "Input": {"InputType": "CUSTOM_VARIABLE", "CustomVarID": "699dda0b-7f44-486b-9a73-aee6062d3d42"}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}, {"Name": "doc", "Type": "DOCUMENT", "Input": {"InputType": "CUSTOM_VARIABLE", "CustomVarID": "c741c3df-ef4c-409f-9d52-cd410fe9560f"}, "Desc": "", "IsRequired": false, "SubInputs": [], "DefaultValue": "", "DefaultFileName": ""}], "Outputs": [], "NextNodeIDs": ["d22c4d42-ec20-b980-e505-433113e0b75e"], "NodeUI": "{\"data\":{\"content\":{\"iterationType\":\"遍历全部元素\",\"workflowRefNodeName\":\"层级1\"},\"isHovering\":false,\"isParallel\":false,\"source\":true,\"target\":true,\"debug\":null,\"error\":false,\"output\":[{\"label\":\"Output\",\"desc\":\"输出内容\",\"optionType\":\"REFERENCE_OUTPUT\",\"type\":\"OBJECT\",\"children\":[]}]},\"position\":{\"x\":1000,\"y\":282},\"targetPosition\":\"left\",\"sourcePosition\":\"right\",\"selected\":false,\"measured\":{\"width\":250,\"height\":104}}"}, {"NodeID": "f48f9dbd-39da-c5c2-4f5f-b56dbbea3d19", "NodeName": "代码1", "NodeDesc": "", "NodeType": "CODE_EXECUTOR", "CodeExecutorNodeData": {"Code": "\n# 请保存函数名为main,输入输出均为dict；最终结果会以json字符串方式返回，请勿直接返回不支持json.dumps的对象\ndef main(params: dict) -> dict:\n    lst = ['a','b']\n    return {\n        'lst': lst\n    }\n", "Language": "PYTHON3"}, "Inputs": [], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Required": [], "Properties": [{"Title": "lst", "Type": "ARRAY_STRING", "Required": [], "Properties": [], "Desc": "", "AnalysisMethod": "COVER"}], "Desc": "输出内容", "AnalysisMethod": "COVER"}], "NextNodeIDs": ["392742b1-1218-1174-0cc6-b73442ab79a4"], "NodeUI": "{\"data\":{\"content\":{\"outputs\":[\"Output\",\"Output.lst\"],\"inputs\":[]},\"isHovering\":false,\"isParallel\":false,\"source\":true,\"target\":true,\"debug\":null,\"error\":false,\"output\":[{\"id\":\"f32f0a8c-1d6b-bf89-eb30-5f1b5172f41f\",\"value\":\"Output\",\"label\":\"Output\",\"_uiId\":\"b5a19dd9-d379-f660-6177-299a63a0599a\",\"type\":\"OBJECT\",\"children\":[{\"id\":\"7560d1db-6a44-4582-45d3-d73180d1f01f\",\"value\":\"lst\",\"label\":\"lst\",\"_uiId\":\"600deb47-df44-218b-a258-a949be14ab06\",\"type\":\"ARRAY_STRING\",\"children\":[]}]}]},\"position\":{\"x\":700,\"y\":282},\"targetPosition\":\"left\",\"sourcePosition\":\"right\",\"selected\":false,\"measured\":{\"width\":250,\"height\":104}}", "ExceptionHandling": {"Switch": "OFF", "MaxRetries": "3", "RetryInterval": "1", "AbnormalOutputResult": ""}}], "Edge": "[{\"source\":\"caf32fbe-50ef-e71d-5322-76e3f6614c2a\",\"sourceHandle\":\"caf32fbe-50ef-e71d-5322-76e3f6614c2a-source\",\"target\":\"f48f9dbd-39da-c5c2-4f5f-b56dbbea3d19\",\"type\":\"custom\",\"data\":{\"connectedNodeIsHovering\":false,\"error\":false,\"isHovering\":false},\"id\":\"xy-edge__caf32fbe-50ef-e71d-5322-76e3f6614c2acaf32fbe-50ef-e71d-5322-76e3f6614c2a-source-f48f9dbd-39da-c5c2-4f5f-b56dbbea3d19\",\"animated\":false},{\"source\":\"f48f9dbd-39da-c5c2-4f5f-b56dbbea3d19\",\"target\":\"392742b1-1218-1174-0cc6-b73442ab79a4\",\"type\":\"custom\",\"data\":{\"connectedNodeIsHovering\":false,\"error\":false,\"isHovering\":false},\"sourceHandle\":\"\",\"id\":\"xy-edge__f48f9dbd-39da-c5c2-4f5f-b56dbbea3d19-392742b1-1218-1174-0cc6-b73442ab79a4\",\"animated\":false},{\"source\":\"392742b1-1218-1174-0cc6-b73442ab79a4\",\"sourceHandle\":\"392742b1-1218-1174-0cc6-b73442ab79a4-source\",\"target\":\"d22c4d42-ec20-b980-e505-433113e0b75e\",\"targetHandle\":\"d22c4d42-ec20-b980-e505-433113e0b75e-target\",\"type\":\"custom\",\"data\":{\"connectedNodeIsHovering\":false,\"isHovering\":false,\"error\":false},\"id\":\"xy-edge__392742b1-1218-1174-0cc6-b73442ab79a4392742b1-1218-1174-0cc6-b73442ab79a4-source-d22c4d42-ec20-b980-e505-433113e0b75ed22c4d42-ec20-b980-e505-433113e0b75e-target\",\"animated\":false}]", "Mode": "NORMAL", "ReleaseTime": ""}