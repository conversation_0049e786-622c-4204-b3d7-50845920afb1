{"WorkflowID": "loop_workflow_002", "WorkflowName": "循环", "WorkflowDesc": "[握手]", "Nodes": [{"NodeID": "87be04f4-badc-daeb-6f4c-d3c435982b0d", "NodeName": "开始", "NodeType": "START", "NodeDesc": "", "StartNodeData": {}, "NextNodeIDs": ["f5049ac9-c045-cfad-db21-32414782676b"], "Inputs": [], "NodeUI": "{\"data\":{\"content\":\"\",\"isHovering\":false,\"isParallel\":false,\"source\":false,\"target\":true,\"debug\":null,\"error\":false,\"output\":[]},\"position\":{\"x\":400,\"y\":282},\"targetPosition\":\"left\",\"sourcePosition\":\"right\",\"selected\":false,\"measured\":{\"width\":250,\"height\":84}}"}, {"NodeID": "bcedd83f-8356-9572-77f9-033a33766e07", "NodeName": "结束", "NodeType": "END", "NodeDesc": "", "EndNodeData": {}, "NextNodeIDs": [], "Outputs": [{"Title": "Output", "Type": "OBJECT", "Required": [], "Properties": [], "Desc": "输出内容"}], "NodeUI": "{\"data\":{\"content\":\"\",\"isHovering\":false,\"isParallel\":false,\"source\":true,\"target\":false,\"debug\":null,\"error\":false,\"output\":[{\"label\":\"Output\",\"desc\":\"输出内容\",\"optionType\":\"REFERENCE_OUTPUT\",\"type\":\"OBJECT\",\"children\":[]}]},\"position\":{\"x\":1200,\"y\":282},\"targetPosition\":\"left\",\"sourcePosition\":\"right\",\"selected\":null,\"measured\":{\"width\":250,\"height\":84}}"}, {"NodeID": "f5049ac9-c045-cfad-db21-32414782676b", "NodeName": "循环1", "NodeType": "ITERATION", "NodeDesc": "", "Inputs": [{"Name": "Input", "Type": "ARRAY_STRING", "Input": {"InputType": "CUSTOM_VARIABLE", "CustomVarID": "5fffece3-f10b-4517-aed7-75383ef35b49"}, "Desc": "循环数组变量", "SubInputs": []}, {"Name": "arr_obj", "Type": "ARRAY_OBJECT", "Desc": "", "IsRequired": false, "Input": {"InputType": "CUSTOM_VARIABLE", "CustomVarID": "ec150ea1-96c5-4214-8318-d3c77069a477"}, "SubInputs": []}], "IterationNodeData": {"BodyType": "WORKFLOW", "WorkflowID": "a64b5b68-251d-40b0-92e4-e00924f0ffe9", "RefInputs": [{"Name": "input_arr_str", "Type": "ARRAY_STRING", "Desc": "", "IsRequired": false, "SubInputs": [], "Input": {"InputType": "NODE_INPUT_PARAM", "NodeInputParamName": "Input"}}, {"Name": "input_int", "Type": "INT", "Desc": "", "IsRequired": false, "SubInputs": [], "Input": {"InputType": "NODE_INPUT_PARAM", "NodeInputParamName": "Loop.Index"}}, {"Name": "input_obj", "Type": "OBJECT", "Desc": "", "IsRequired": false, "SubInputs": [], "Input": {"InputType": "NODE_INPUT_PARAM", "NodeInputParamName": "Loop.Output"}}, {"Name": "input_arr_obj", "Type": "ARRAY_OBJECT", "Desc": "", "IsRequired": false, "SubInputs": [], "Input": {"InputType": "NODE_INPUT_PARAM", "NodeInputParamName": "arr_obj"}}], "IterationMode": "ALL", "Condition": {"LogicalOperator": "UNSPECIFIED", "Comparison": {"Left": {"InputType": "CUSTOM_VARIABLE", "CustomVarID": ""}, "LeftType": "STRING", "Operator": "UNSPECIFIED", "Right": {"InputType": "CUSTOM_VARIABLE", "CustomVarID": ""}, "MatchType": "SEMANTIC"}}}, "NextNodeIDs": ["bcedd83f-8356-9572-77f9-033a33766e07"]}], "Edge": "[{\"source\":\"87be04f4-badc-daeb-6f4c-d3c435982b0d\",\"sourceHandle\":\"87be04f4-badc-daeb-6f4c-d3c435982b0d-source\",\"target\":\"f5049ac9-c045-cfad-db21-32414782676b\",\"type\":\"custom\",\"data\":{\"connectedNodeIsHovering\":false,\"error\":false,\"isHovering\":false},\"id\":\"xy-edge__87be04f4-badc-daeb-6f4c-d3c435982b0d87be04f4-badc-daeb-6f4c-d3c435982b0d-source-f5049ac9-c045-cfad-db21-32414782676b\",\"animated\":false},{\"source\":\"f5049ac9-c045-cfad-db21-32414782676b\",\"sourceHandle\":\"f5049ac9-c045-cfad-db21-32414782676b-source\",\"target\":\"bcedd83f-8356-9572-77f9-033a33766e07\",\"targetHandle\":\"bcedd83f-8356-9572-77f9-033a33766e07-target\",\"type\":\"custom\",\"data\":{\"connectedNodeIsHovering\":false,\"isHovering\":false,\"error\":false},\"id\":\"xy-edge__f5049ac9-c045-cfad-db21-32414782676bf5049ac9-c045-cfad-db21-32414782676b-source-bcedd83f-8356-9572-77f9-033a33766e07bcedd83f-8356-9572-77f9-033a33766e07-target\",\"animated\":false}]"}