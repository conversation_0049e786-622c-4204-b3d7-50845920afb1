package task_store

import (
	"context"
	"fmt"
	"testing"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/goredis"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestRedis(t *testing.T) (redis.UniversalClient, func()) {
	mr, err := miniredis.Run()
	require.NoError(t, err)

	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})

	return client, func() {
		if err := client.Close(); err != nil {
			// In cleanup functions, we typically just log errors
			fmt.Printf("Error closing Redis client: %v\n", err)
		}
		mr.Close()
	}
}

// func TestNewStore(t *testing.T) {
//	client, cleanup := setupTestRedis(t)
//	defer cleanup()
//
//	store, err := NewStore()
//	require.NoError(t, err)
//	assert.NotNil(t, store)
// }

func TestEnqueue(t *testing.T) {
	// client, cleanup := setupTestRedis(t)
	// defer cleanup()
	client, err := goredis.New("local", &redis.UniversalOptions{Addrs: []string{"9.134.166.84:8081"}})
	if err != nil {
		t.Fatal(err)
	}

	store := &RedisTaskStore{client: client}
	ctx := context.Background()

	tests := []struct {
		name    string
		msg     *entity.TaskMessage
		wantErr bool
	}{
		{
			name: "successful enqueue",
			msg: &entity.TaskMessage{
				TaskID: "test-task-1",
				Queue:  "test-queue",
				Type:   entity.TaskTypeWorkflowRun,
			},
			wantErr: false,
		},
		{
			name: "duplicate task",
			msg: &entity.TaskMessage{
				TaskID: "test-task-1",
				Queue:  "test-queue",
				Type:   entity.TaskTypeWorkflowRun,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := store.Enqueue(ctx, tt.msg)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				// Verify task exists in Redis
				taskKey := GetTaskKey(tt.msg.TaskID)
				exists, err := client.Exists(ctx, taskKey).Result()
				assert.NoError(t, err)
				assert.Equal(t, int64(1), exists)
				data, _ := client.HGet(ctx, taskKey, "Data").Result()
				t.Log(taskKey)
				t.Log(data)
			}
		})
	}
}

func TestDequeue(t *testing.T) {
	client, cleanup := setupTestRedis(t)
	defer cleanup()

	store := &RedisTaskStore{client: client}
	ctx := context.Background()

	// Setup test data
	taskMsg := &entity.TaskMessage{
		TaskID: "test-task-1",
		Queue:  "test-queue",
		Type:   entity.TaskTypeWorkflowRun,
	}
	err := store.Enqueue(ctx, taskMsg)
	require.NoError(t, err)

	tests := []struct {
		name      string
		qNames    []string
		wantTask  bool
		wantError bool
	}{
		{
			name:      "dequeue from existing queue",
			qNames:    []string{"test-queue"},
			wantTask:  true,
			wantError: false,
		},
		{
			name:      "dequeue from non-existing queue",
			qNames:    []string{"non-existing-queue"},
			wantTask:  false,
			wantError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			task, err := store.Dequeue(ctx, tt.qNames)
			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if tt.wantTask {
					assert.NotNil(t, task)
					assert.Equal(t, taskMsg.TaskID, task.TaskID)
				} else {
					assert.Nil(t, task)
				}
			}
		})
	}
}

func TestRequeue(t *testing.T) {
	client, cleanup := setupTestRedis(t)
	defer cleanup()

	store := &RedisTaskStore{client: client}
	ctx := context.Background()

	// Setup test data
	taskMsg := &entity.TaskMessage{
		TaskID: "test-task-1",
		Queue:  "test-queue",
		Type:   entity.TaskTypeWorkflowRun,
	}
	err := store.Enqueue(ctx, taskMsg)
	require.NoError(t, err)

	// Dequeue the task first
	_, err = store.Dequeue(ctx, []string{"test-queue"})
	require.NoError(t, err)

	pendingKey := GetPendingTaskKey(taskMsg.Queue)
	exists, err := client.Exists(ctx, pendingKey).Result()
	require.NoError(t, err)
	require.Equal(t, int64(0), exists)

	tests := []struct {
		name      string
		msg       *entity.TaskMessage
		wantError bool
	}{
		{
			name:      "requeue existing task",
			msg:       taskMsg,
			wantError: false,
		},
		{
			name: "requeue non-existing task",
			msg: &entity.TaskMessage{
				TaskID: "non-existing-task",
				Queue:  "test-queue",
				Type:   entity.TaskTypeWorkflowRun,
			},
			wantError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := store.Requeue(ctx, tt.msg)
			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				// Verify task is back in pending queue
				exists, err := client.Exists(ctx, pendingKey).Result()
				assert.NoError(t, err)
				assert.Equal(t, int64(1), exists)
			}
		})
	}
}

func TestListLeaseExpired(t *testing.T) {
	client, cleanup := setupTestRedis(t)
	defer cleanup()
	// client, err1 := goredis.New("local", &redis.UniversalOptions{Addrs: []string{"9.134.166.84:8081"}})
	// if err1 != nil {
	//	t.Fatal(err1)
	// }

	store := &RedisTaskStore{client: client}
	ctx := context.Background()

	// Setup test data
	taskMsg := &entity.TaskMessage{
		TaskID: "test-task-1",
		Queue:  "test-queue",
		Type:   entity.TaskTypeWorkflowRun,
	}
	err := store.Enqueue(ctx, taskMsg)
	require.NoError(t, err)

	// Dequeue the task
	_, err = store.Dequeue(ctx, []string{"test-queue"})
	require.NoError(t, err)

	tests := []struct {
		name      string
		cutoff    time.Time
		qNames    []string
		wantTasks int
		wantError bool
	}{
		{
			name:      "find expired tasks",
			cutoff:    time.Now().Add(31 * time.Second),
			qNames:    []string{"test-queue"},
			wantTasks: 1,
			wantError: false,
		},
		{
			name:      "no expired tasks",
			cutoff:    time.Now().Add(1 * time.Second),
			qNames:    []string{"test-queue"},
			wantTasks: 0,
			wantError: false,
		},
		{
			name:      "empty queue names",
			cutoff:    time.Now(),
			qNames:    []string{},
			wantTasks: 0,
			wantError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tasks, err := store.ListLeaseExpired(ctx, tt.cutoff, tt.qNames)
			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Len(t, tasks, tt.wantTasks)
				if tt.wantTasks > 0 {
					assert.Equal(t, taskMsg.TaskID, tasks[0].TaskID)
				}
			}
		})
	}
}

func TestExtendLease(t *testing.T) {
	client, cleanup := setupTestRedis(t)
	defer cleanup()

	store := &RedisTaskStore{client: client}
	ctx := context.Background()

	// Setup test data
	taskMsg := &entity.TaskMessage{
		TaskID: "test-task-1",
		Queue:  "test-queue",
		Type:   entity.TaskTypeWorkflowRun,
	}
	err := store.Enqueue(ctx, taskMsg)
	require.NoError(t, err)

	// Dequeue the task
	_, err = store.Dequeue(ctx, []string{"test-queue"})
	require.NoError(t, err)

	tests := []struct {
		name      string
		qName     string
		taskIDs   []string
		wantError bool
	}{
		{
			name:      "extend lease for existing task",
			qName:     "test-queue",
			taskIDs:   []string{"test-task-1"},
			wantError: false,
		},
		{
			name:      "extend lease for non-existing task",
			qName:     "test-queue",
			taskIDs:   []string{"non-existing-task"},
			wantError: false,
		},
		{
			name:      "empty task IDs",
			qName:     "test-queue",
			taskIDs:   []string{},
			wantError: false,
		},
	}

	time.Sleep(2 * time.Second)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := store.ExtendLease(ctx, tt.qName, tt.taskIDs)
			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
	tasks, err := store.ListLeaseExpired(ctx, time.Now().Add(29*time.Second), []string{"test-queue"})
	assert.Nil(t, err)
	assert.Equal(t, len(tasks), 0)
	tasks, err = store.ListLeaseExpired(ctx, time.Now().Add(33*time.Second), []string{"test-queue"})
	assert.Nil(t, err)
	assert.Equal(t, len(tasks), 1)
}

func TestCancellationPubSub(t *testing.T) {
	client, cleanup := setupTestRedis(t)
	defer cleanup()

	store := &RedisTaskStore{client: client}
	ctx := context.Background()

	pubSub, err := store.CancellationPubSub(ctx)
	require.NoError(t, err)
	assert.NotNil(t, pubSub)
	defer func() {
		if err := pubSub.Close(); err != nil {
			t.Logf("Error closing pubSub: %v", err)
		}
	}()
}

func TestPublishCancellation(t *testing.T) {
	// client, cleanup := setupTestRedis(t)
	// defer cleanup()
	client, err1 := goredis.New("local", &redis.UniversalOptions{Addrs: []string{"9.134.166.84:8081"}})
	if err1 != nil {
		t.Fatal(err1)
	}

	store := &RedisTaskStore{client: client}
	ctx := context.Background()

	// Setup pubSub
	pubSub, err := store.CancellationPubSub(ctx)
	require.NoError(t, err)
	defer func() {
		if err := pubSub.Close(); err != nil {
			t.Logf("Error closing pubSub: %v", err)
		}
	}()

	// Subscribe to cancellation channel
	ch := pubSub.Channel()

	// Publish cancellation
	err = store.PublishCancellation(ctx, "test-task-1")
	require.NoError(t, err)

	// Verify message received
	select {
	case msg := <-ch:
		assert.Equal(t, "test-task-1", msg.Payload)
	case <-time.After(2 * time.Second):
		t.Fatal("timeout waiting for cancellation message")
	}
}

func TestIsTaskExpired(t *testing.T) {
	client, cleanup := setupTestRedis(t)
	defer cleanup()

	store := &RedisTaskStore{client: client}
	ctx := context.Background()

	// Setup test data
	taskMsg := &entity.TaskMessage{
		TaskID: "test-task-1",
		Queue:  "test-queue",
		Type:   entity.TaskTypeWorkflowRun,
	}
	err := store.Enqueue(ctx, taskMsg)
	require.NoError(t, err)

	// Dequeue the task to set lease
	_, err = store.Dequeue(ctx, []string{"test-queue"})
	require.NoError(t, err)

	tests := []struct {
		name        string
		queue       string
		taskID      string
		wantExpired bool
		wantError   bool
	}{
		{
			name:        "task not expired",
			queue:       "test-queue",
			taskID:      "test-task-1",
			wantExpired: false,
			wantError:   false,
		},
		{
			name:        "task expired",
			queue:       "test-queue",
			taskID:      "test-task-1",
			wantExpired: true,
			wantError:   false,
		},
		{
			name:        "non-existent task",
			queue:       "test-queue",
			taskID:      "non-existent-task",
			wantExpired: true,
			wantError:   false,
		},
		{
			name:        "non-existent queue",
			queue:       "non-existent-queue",
			taskID:      "test-task-1",
			wantExpired: true,
			wantError:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.name == "task expired" {
				// 等待租约过期
				time.Sleep(LeaseDuration + time.Second)
			}

			expired, err := store.IsTaskExpired(ctx, tt.queue, tt.taskID)
			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantExpired, expired)
			}
		})
	}
}

func TestSetTaskAction(t *testing.T) {
	client, cleanup := setupTestRedis(t)
	defer cleanup()

	store := &RedisTaskStore{client: client}
	ctx := context.Background()

	// Setup test data
	taskMsg := &entity.TaskMessage{
		TaskID: "test-task-1",
		Queue:  "test-queue",
		Type:   entity.TaskTypeWorkflowRun,
	}
	err := store.Enqueue(ctx, taskMsg)
	require.NoError(t, err)

	tests := []struct {
		name      string
		taskID    string
		action    entity.TaskAction
		wantError bool
	}{
		{
			name:      "set cancel action",
			taskID:    "test-task-1",
			action:    entity.TaskActionCancel,
			wantError: false,
		},
		{
			name:      "set pause action",
			taskID:    "test-task-1",
			action:    entity.TaskActionPause,
			wantError: false,
		},
		{
			name:      "set none action",
			taskID:    "test-task-1",
			action:    entity.TaskActionNone,
			wantError: false,
		},
		{
			name:      "non-existent task",
			taskID:    "non-existent-task",
			action:    entity.TaskActionCancel,
			wantError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := store.SetTaskAction(ctx, tt.taskID, tt.action)
			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				// Verify action was set
				action, err := store.GetTaskAction(ctx, tt.taskID)
				assert.NoError(t, err)
				assert.Equal(t, tt.action, action)
			}
		})
	}
}

func TestGetTaskAction(t *testing.T) {
	client, cleanup := setupTestRedis(t)
	defer cleanup()

	store := &RedisTaskStore{client: client}
	ctx := context.Background()

	// Setup test data
	taskMsg := &entity.TaskMessage{
		TaskID: "test-task-1",
		Queue:  "test-queue",
		Type:   entity.TaskTypeWorkflowRun,
	}
	err := store.Enqueue(ctx, taskMsg)
	require.NoError(t, err)

	// Set initial action
	err = store.SetTaskAction(ctx, taskMsg.TaskID, entity.TaskActionCancel)
	require.NoError(t, err)

	tests := []struct {
		name       string
		taskID     string
		wantAction entity.TaskAction
		wantError  bool
	}{
		{
			name:       "get existing action",
			taskID:     "test-task-1",
			wantAction: entity.TaskActionCancel,
			wantError:  false,
		},
		{
			name:       "non-existent task",
			taskID:     "non-existent-task",
			wantAction: entity.TaskActionNone,
			wantError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			action, err := store.GetTaskAction(ctx, tt.taskID)
			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantAction, action)
			}
		})
	}
}

func TestDone(t *testing.T) {
	client, cleanup := setupTestRedis(t)
	defer cleanup()

	store := &RedisTaskStore{client: client}
	ctx := context.Background()

	// Setup test data
	taskMsg := &entity.TaskMessage{
		TaskID: "test-task-1",
		Queue:  "test-queue",
		Type:   entity.TaskTypeWorkflowRun,
	}
	err := store.Enqueue(ctx, taskMsg)
	require.NoError(t, err)

	// Dequeue the task to set lease
	_, err = store.Dequeue(ctx, []string{"test-queue"})
	require.NoError(t, err)

	tests := []struct {
		name      string
		queue     string
		taskID    string
		wantError bool
	}{
		{
			name:      "done existing task",
			queue:     "test-queue",
			taskID:    "test-task-1",
			wantError: false,
		},
		{
			name:      "done non-existent task",
			queue:     "test-queue",
			taskID:    "non-existent-task",
			wantError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := store.Done(ctx, tt.queue, tt.taskID)
			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				// Verify task is completely removed
				taskKey := GetTaskKey(tt.taskID)
				exists, err := client.Exists(ctx, taskKey).Result()
				assert.NoError(t, err)
				assert.Equal(t, int64(0), exists)

				// Verify task is not in any queue
				activeKey := GetActiveTaskKey(tt.queue)
				pendingKey := GetPendingTaskKey(tt.queue)
				leaseKey := GetTaskLeaseKey(tt.queue)

				activeExists, _ := client.LPos(ctx, activeKey, tt.taskID, redis.LPosArgs{}).Result()
				pendingExists, _ := client.LPos(ctx, pendingKey, tt.taskID, redis.LPosArgs{}).Result()
				leaseExists, _ := client.ZScore(ctx, leaseKey, tt.taskID).Result()

				assert.Equal(t, activeExists, int64(0))
				assert.Equal(t, pendingExists, int64(0))
				assert.Equal(t, leaseExists, float64(0))
			}
		})
	}
}

func TestIsTaskExisted(t *testing.T) {
	client, cleanup := setupTestRedis(t)
	defer cleanup()

	store := &RedisTaskStore{client: client}
	ctx := context.Background()

	// Setup test data
	taskMsg := &entity.TaskMessage{
		TaskID: "test-task-1",
		Queue:  "test-queue",
		Type:   entity.TaskTypeWorkflowRun,
	}
	err := store.Enqueue(ctx, taskMsg)
	require.NoError(t, err)

	tests := []struct {
		name       string
		taskID     string
		wantExists bool
		wantError  bool
	}{
		{
			name:       "existing task",
			taskID:     "test-task-1",
			wantExists: true,
			wantError:  false,
		},
		{
			name:       "non-existent task",
			taskID:     "non-existent-task",
			wantExists: false,
			wantError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			exists, err := store.IsTaskExisted(ctx, tt.taskID)
			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantExists, exists)
			}
		})
	}
}
