// Package task_store 任务的存储相关的接口
package task_store

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/goredis"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"github.com/go-redis/redis/v8"
)

// LeaseDuration 延长租约时间，在当前时间的基础上加上这个时间作为任务的过期时间，如果任务在这个时间内没有延长过租约，则会当成失效
const LeaseDuration = 30 * time.Second

// TaskStore 任务存储接口
type TaskStore interface {
	// Enqueue 入队
	Enqueue(ctx context.Context, msg *entity.TaskMessage) error
	// Dequeue 出队
	Dequeue(ctx context.Context, qNames []string) (*entity.TaskMessage, error)
	// Requeue 重新入队
	Requeue(ctx context.Context, msg *entity.TaskMessage) error

	// ListLeaseExpired 获取失效任务
	ListLeaseExpired(ctx context.Context, endTime time.Time, qNames []string) ([]*entity.TaskMessage, error)
	// ExtendLease 延长任务租期
	ExtendLease(ctx context.Context, qName string, taskIDs []string) error

	// CancellationPubSub 获取取消消息订阅
	CancellationPubSub(ctx context.Context) (*redis.PubSub, error)
	// PublishCancellation 发布取消消息
	PublishCancellation(ctx context.Context, taskID string) error

	// IsTaskExpired 判断任务是否失效
	IsTaskExpired(ctx context.Context, queue string, taskID string) (bool, error)

	// SetTaskAction 设置任务操作
	SetTaskAction(ctx context.Context, taskID string, action entity.TaskAction) error
	// GetTaskAction 获取任务操作
	GetTaskAction(ctx context.Context, taskID string) (entity.TaskAction, error)

	// Done 完成任务，清理所有相关存储
	Done(ctx context.Context, queue string, taskID string) error

	// IsTaskExisted 判断任务是否存在
	IsTaskExisted(ctx context.Context, taskID string) (bool, error)

	// GetUinTaskCount 获取单个Uin的任务数
	GetUinTaskCount(ctx context.Context, uin string) (int32, error)

	// GetAllUinTaskCounts 获取所有Uin的任务数
	GetAllUinTaskCounts(ctx context.Context) (map[string]int32, error)
}

// RedisTaskStore redis实现的任务存储
type RedisTaskStore struct {
	client redis.UniversalClient
}

// NewStore 创建任务存储
func NewStore() (TaskStore, error) {
	redisClient, err := goredis.New(tconst.DMRedisServiceName, nil)
	if err != nil {
		return nil, err
	}
	return &RedisTaskStore{
		client: redisClient,
	}, nil
}

// NewStoreByClient 创建任务存储
func NewStoreByClient(client redis.UniversalClient) TaskStore {
	return &RedisTaskStore{
		client: client,
	}
}

// enqueueScript 入队脚本
var enqueueScript = redis.NewScript(`
		local taskKey = KEYS[1]
		local pendingKey = KEYS[2]
		local uinZsetKey = KEYS[3]
		local data = ARGV[1]
		local addTime = ARGV[2]
		local taskID = ARGV[3]
		local uin = ARGV[4]
		-- local status = ARGV[5]
		
		-- 检查任务是否已存在
		if redis.call('EXISTS', taskKey) == 1 then
			return "task already exists"
		end
		
		redis.call('HSET', taskKey, 'Data', data)
		redis.call('HSET', taskKey, 'AddTime', addTime)
		-- redis.call('HSET', taskKey, 'Status', status)
		redis.call('LPUSH', pendingKey, taskID)

		-- 更新Uin任务数ZSET
		redis.call('ZINCRBY', uinZsetKey, 1, uin)
		
		return 'success'
	`)

// Enqueue 将任务加入队列
func (s *RedisTaskStore) Enqueue(ctx context.Context, task *entity.TaskMessage) error {
	// 序列化任务数据
	taskData, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("marshal task data: %w", err)
	}

	// 使用Lua脚本原子性地执行以下操作:
	// 1. 保存任务数据到hash
	// 2. 将任务ID添加到pending队列
	// 3. 设置任务状态为pending
	result, err := enqueueScript.Run(ctx, s.client,
		[]string{
			GetTaskKey(task.TaskID),
			GetPendingTaskKey(task.Queue),
			GetUinTaskNumKey(),
		},
		string(taskData),
		time.Now().Format(time.RFC3339),
		task.TaskID,
		task.Uin,
		// string(entity.TaskStatusPending),
	).Result()

	if err != nil {
		return fmt.Errorf("execute enqueue script: %w", err)
	}
	if result != "success" {
		return fmt.Errorf("enqueue task failed, error: %v", result)
	}

	return nil
}

// dequeueScript 出队脚本
var dequeueScript = redis.NewScript(`
		local pendingKey = KEYS[1]
		local activeKey = KEYS[2]
		local leaseKey = KEYS[3]
		local taskKeyPre = KEYS[4]
		local leaseTime = ARGV[1]

		-- 从pending队列中取出一个任务ID，添加到active队列
		local taskID = redis.call('RPOPLPUSH', pendingKey, activeKey)
		if not taskID then
			return nil
		end

		-- 获取任务数据
		local taskKey = taskKeyPre .. taskID
		local taskData = redis.call('HGET', taskKey, 'Data')
		if not taskData then
			return 'task data not found'
		end

		-- 设置任务租约
		redis.call('ZADD', leaseKey, leaseTime, taskID)

		return taskData
	`)

// Dequeue 从队列中取出一个任务
func (s *RedisTaskStore) Dequeue(ctx context.Context, qNames []string) (*entity.TaskMessage, error) {
	if len(qNames) == 0 {
		return nil, nil
	}

	// 遍历所有队列，尝试获取任务
	for _, queue := range qNames {
		// 使用Lua脚本原子性地执行以下操作:
		// 1. 从pending队列中取出一个任务ID
		// 2. 获取任务数据
		// 3. 将任务ID添加到active队列
		// 4. 设置任务租约
		result, err := dequeueScript.Run(ctx, s.client,
			[]string{
				GetPendingTaskKey(queue),
				GetActiveTaskKey(queue),
				GetTaskLeaseKey(queue),
				GetTaskKey(""), // 占位符，会在Lua脚本中替换
			},
			time.Now().Add(LeaseDuration).Unix(),
		).Result()

		if errors.Is(err, redis.Nil) {
			continue
		}
		if err != nil {
			return nil, fmt.Errorf("execute dequeue script for queue %s: %w", queue, err)
		}

		// 如果没有任务，继续尝试下一个队列
		if result == nil {
			continue
		}

		// 解析任务数据
		taskData := result.(string)
		var taskMsg entity.TaskMessage
		if err := json.Unmarshal([]byte(taskData), &taskMsg); err != nil {
			return nil, fmt.Errorf("unmarshal task data for queue %s, data: %v, error: %w", queue, taskData, err)
		}

		return &taskMsg, nil
	}

	return nil, nil
}

// requeueScript 重新入队脚本
var requeueScript = redis.NewScript(`
		local activeKey = KEYS[1]
		local leaseKey = KEYS[2]
		local taskKey = KEYS[3]
		local pendingKey = KEYS[4]
		local taskID = ARGV[1]
		-- local status = ARGV[2]
		
		-- 检查任务是否在处理中队列
		if redis.call('LREM', activeKey, 0, taskID) == 0 then
			return 'task not found'
		end
		
		-- 从租约中移除
		redis.call('ZREM', leaseKey, taskID)
		
		-- 更新任务状态
		-- redis.call('HSET', taskKey, 'Status', status)
		
		-- 重新入队
		redis.call('RPUSH', pendingKey, taskID)
		
		return 'success'
	`)

// Requeue 将任务重新加入队列
func (s *RedisTaskStore) Requeue(ctx context.Context, task *entity.TaskMessage) error {
	// 使用Lua脚本原子性地执行以下操作:
	// 1. 从active队列中移除任务ID
	// 2. 从租约中移除任务ID
	// 3. 将任务ID重新添加到pending队列
	result, err := requeueScript.Run(ctx, s.client,
		[]string{
			GetActiveTaskKey(task.Queue),
			GetTaskLeaseKey(task.Queue),
			GetTaskKey(task.TaskID),
			GetPendingTaskKey(task.Queue),
		},
		task.TaskID,
		// string(entity.TaskStatusPending),
	).Result()

	if err != nil {
		return fmt.Errorf("execute requeue script: %w", err)
	}
	if result != "success" {
		return fmt.Errorf("requeue task failed, taskID: %s; error: %s", task.TaskID, result)
	}

	return nil
}

// ListLeaseExpired 列出所有租约过期的任务
func (s *RedisTaskStore) ListLeaseExpired(ctx context.Context, endTime time.Time,
	qNames []string) ([]*entity.TaskMessage,
	error) {
	if len(qNames) == 0 {
		return nil, nil
	}

	var allTasks []*entity.TaskMessage
	for _, queue := range qNames {
		// 获取所有租约过期的任务ID
		expiredTaskIDs, err := s.client.ZRangeByScore(ctx, GetTaskLeaseKey(queue), &redis.ZRangeBy{
			Min:    "0",
			Max:    fmt.Sprintf("%d", endTime.Unix()),
			Offset: 0,
			Count:  100,
		}).Result()

		if err != nil {
			return nil, fmt.Errorf("get expired task ids for queue %s: %w", queue, err)
		}

		if len(expiredTaskIDs) == 0 {
			continue
		}

		// 获取任务数据
		for _, taskID := range expiredTaskIDs {
			taskData, err := s.client.HGet(ctx, GetTaskKey(taskID), "Data").Result()
			if err != nil {
				if errors.Is(err, redis.Nil) {
					// TODO log
					continue
				}
				return nil, fmt.Errorf("get task data for task %s: %w", taskID, err)
			}

			var taskMsg entity.TaskMessage
			if err := json.Unmarshal([]byte(taskData), &taskMsg); err != nil {
				return nil, fmt.Errorf("unmarshal task data for task %s: %w", taskID, err)
			}

			allTasks = append(allTasks, &taskMsg)
		}
	}

	return allTasks, nil
}

// ExtendLease 延长任务租约
func (s *RedisTaskStore) ExtendLease(ctx context.Context, queue string, taskIDs []string) error {
	if len(taskIDs) == 0 {
		return nil
	}

	// // 使用Lua脚本原子性地执行以下操作:
	// // 1. 检查任务是否在active队列中
	// // 2. 更新任务租约时间
	// _, err := extendLeaseScript.Run(ctx, s.client,
	//	[]string{
	//		GetActiveTaskKey(queue),
	//		GetTaskLeaseKey(queue),
	//	},
	//	taskIDs,
	//	time.Now().Add(LeaseDuration).Unix(),
	// ).Result()
	// if err != nil {
	//	return fmt.Errorf("execute extend lease script: %w", err)
	// }

	expireAt := time.Now().Add(LeaseDuration)
	var dataList []*redis.Z
	for _, id := range taskIDs {
		dataList = append(dataList, &redis.Z{Member: id, Score: float64(expireAt.Unix())})
	}
	err := s.client.ZAddXX(ctx, GetTaskLeaseKey(queue), dataList...).Err()
	if err != nil {
		return err
	}

	return nil
}

// CancellationPubSub 订阅取消消息
func (s *RedisTaskStore) CancellationPubSub(ctx context.Context) (*redis.PubSub, error) {
	pubSub := s.client.Subscribe(ctx, GetCancelChannelKey())
	// 订阅成功后，才返回pubsub
	_, err := pubSub.Receive(ctx)
	if err != nil {
		return nil, fmt.Errorf("redis pubsub receive error: %w", err)
	}
	return pubSub, nil
}

// PublishCancellation 发布取消消息
func (s *RedisTaskStore) PublishCancellation(ctx context.Context, taskID string) error {
	return s.client.Publish(ctx, GetCancelChannelKey(), taskID).Err()
}

// IsTaskExpired 判断任务是否失效
func (s *RedisTaskStore) IsTaskExpired(ctx context.Context, queue string, taskID string) (bool, error) {
	// 获取任务的租约时间
	score, err := s.client.ZScore(ctx, GetTaskLeaseKey(queue), taskID).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			// 任务不存在于租约中，视为失效
			return true, nil
		}
		return false, fmt.Errorf("get task lease score: %w", err)
	}

	// 将分数转换为时间
	leaseTime := time.Unix(int64(score), 0)

	// 判断是否失效
	return time.Now().After(leaseTime), nil
}

// SetTaskAction 设置任务操作
func (s *RedisTaskStore) SetTaskAction(ctx context.Context, taskID string, action entity.TaskAction) error {
	taskKey := GetTaskKey(taskID)
	exists, err := s.client.Exists(ctx, taskKey).Result()
	if err != nil {
		return fmt.Errorf("check task exists: %w", err)
	}
	if exists == 0 {
		return fmt.Errorf("task not found: %s", taskID)
	}

	err = s.client.HSet(ctx, taskKey, "Action", string(action)).Err()
	if err != nil {
		return fmt.Errorf("set task action: %w", err)
	}

	return nil
}

// GetTaskAction 获取任务操作
func (s *RedisTaskStore) GetTaskAction(ctx context.Context, taskID string) (entity.TaskAction, error) {
	taskKey := GetTaskKey(taskID)
	action, err := s.client.HGet(ctx, taskKey, "Action").Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return entity.TaskActionNone, nil
		}
		return entity.TaskActionNone, fmt.Errorf("get task action: %w", err)
	}

	return entity.TaskAction(action), nil
}

// doneScript 完成任务脚本
var doneScript = redis.NewScript(`
	local activeKey = KEYS[1]
	local leaseKey = KEYS[2]
	local pendingKey = KEYS[3]
	local taskKey = KEYS[4]
	local uinZsetKey = KEYS[5]
	local taskID = ARGV[1]

	-- 首先获取任务数据
	local taskData = redis.call('HGET', taskKey, 'Data')
	if not taskData then
		return 'task data not found'
	end

	-- 解析JSON获取Uin
	local jsonData = cjson.decode(taskData)
	local uin = jsonData['Uin']
	if not uin then
		return 'uin not found in task data'
	end

	-- 从active队列中移除
	redis.call('LREM', activeKey, 0, taskID)
	
	-- 从租约中移除
	redis.call('ZREM', leaseKey, taskID)
	
	-- 从pending队列中移除
	redis.call('LREM', pendingKey, 0, taskID)

	-- 减少Uin任务数
	redis.call('ZINCRBY', uinZsetKey, -1, uin)
	
	-- 删除任务数据
	redis.call('DEL', taskKey)
	
	return 'success'
`)

// Done 完成任务，清理所有相关存储
func (s *RedisTaskStore) Done(ctx context.Context, queue string, taskID string) error {
	result, err := doneScript.Run(ctx, s.client,
		[]string{
			GetActiveTaskKey(queue),
			GetTaskLeaseKey(queue),
			GetPendingTaskKey(queue),
			GetTaskKey(taskID),
			GetUinTaskNumKey(),
		},
		taskID,
	).Result()

	if err != nil {
		return fmt.Errorf("execute done script: %w", err)
	}
	if result != "success" {
		return fmt.Errorf("done task failed: %v", result)
	}

	return nil
}

// IsTaskExisted 判断任务是否存在
func (s *RedisTaskStore) IsTaskExisted(ctx context.Context, taskID string) (bool, error) {
	taskKey := GetTaskKey(taskID)
	exists, err := s.client.Exists(ctx, taskKey).Result()
	if err != nil {
		return false, fmt.Errorf("check task exists: %w", err)
	}
	return exists > 0, nil
}

// GetUinTaskCount 获取单个Uin的任务数
func (s *RedisTaskStore) GetUinTaskCount(ctx context.Context, uin string) (int32, error) {
	// 从ZSET中获取指定Uin的分数(任务数)
	score, err := s.client.ZScore(ctx, GetUinTaskNumKey(), uin).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			// Uin不存在时返回0
			return 0, nil
		}
		return 0, fmt.Errorf("get uin task count: %w", err)
	}
	return int32(score), nil
}

// GetAllUinTaskCounts 获取所有Uin的任务数
func (s *RedisTaskStore) GetAllUinTaskCounts(ctx context.Context) (map[string]int32, error) {
	result, err := s.client.ZRangeWithScores(ctx, GetUinTaskNumKey(), 0, -1).Result()
	if err != nil {
		return nil, fmt.Errorf("get uin task counts: %w", err)
	}

	counts := make(map[string]int32)
	for _, z := range result {
		counts[z.Member.(string)] = int32(z.Score)
	}
	return counts, nil
}
