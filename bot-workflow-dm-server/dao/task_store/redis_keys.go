package task_store

import (
	"fmt"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
)

// GetTaskKey 获取任务Key
func GetTaskKey(taskID string) string {
	return fmt.Sprintf(tconst.TaskKeyFormat, taskID)
}

// GetPendingTaskKey 获取等待队列Key
func GetPendingTaskKey(queue string) string {
	return fmt.Sprintf(tconst.PendingTaskKeyFormat, queue)
}

// GetActiveTaskKey 获取处理中队列Key
func GetActiveTaskKey(queue string) string {
	return fmt.Sprintf(tconst.ActiveTaskKeyFormat, queue)
}

// GetTaskLeaseKey 获取任务租约Key
func GetTaskLeaseKey(queue string) string {
	return fmt.Sprintf(tconst.TaskLeaseKeyFormat, queue)
}

// GetCancelChannelKey 获取取消通道Key
func GetCancelChannelKey() string {
	return tconst.CancelChannelKey
}

// GetUinTaskNumKey 获取用户任务数量的key（zset）
func GetUinTaskNumKey() string {
	return tconst.UinTaskNumKey
}
