package dao

import (
	"context"
	"testing"

	plugin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	. "github.com/smartystreets/goconvey/convey"
)

func Test_MCPCallTool(t *testing.T) {
	Convey("success", t, func() {
		mcpS := &plugin.MCPServerInfo{
			McpServerUrl:   "https://testwss.testsite.woa.com/v1/lke/mcp?action=tavily",
			Headers:        nil,
			Timeout:        30,
			SseReadTimeout: 330,
		}
		header := map[string]any{
			"TAVILY_API_KEY": "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",
		}
		body := map[string]any{
			"days":                       2,
			"exclude_domains":            nil,
			"include_domains":            nil,
			"include_image_descriptions": false,
			"include_images":             false,
			"include_raw_content":        false,
			"search_depth":               "", // 为空就报错，不提交就不报错
			"time_range":                 "", // 为空就报错，不提交就不报错
			"max_results":                10,
			"query":                      "AI新闻",
			"topic":                      "news",
		}
		rsp, err := Default().MCPCallTool(context.Background(), "tavily-search", mcpS, header, body)
		t.Logf("error : %v, rsp: %v", err, rsp)
	})
}
