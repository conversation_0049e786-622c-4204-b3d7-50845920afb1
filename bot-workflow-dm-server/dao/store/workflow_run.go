package store

import (
	"context"
	"fmt"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/model"
	"gorm.io/gorm"
)

// WorkflowRunStore 工作流运行实例存储接口
type WorkflowRunStore interface {
	// UpdateWorkflowRun 更新工作流运行实例
	UpdateWorkflowRun(ctx context.Context, workflowRun *model.WorkflowRun) error
	// SetWorkflowRunResult 更新工作流运行实例的结果
	SetWorkflowRunResult(ctx context.Context, workflowRun *model.WorkflowRun, state model.WorkflowRunState,
		failMessage string) error
	// SetWorkflowRunToken 更新工作流运行实例的token
	SetWorkflowRunToken(ctx context.Context, workflowRunID string, token uint32) error
	// GetWorkflowRunByRunID 根据工作流运行ID获取工作流运行实例
	GetWorkflowRunByRunID(ctx context.Context, runID string) (*model.WorkflowRun, error)
}

// workflowRunStore 工作流运行实例存储实现
type workflowRunStore struct {
	db *gorm.DB
}

// UpdateWorkflowRun 更新工作流运行实例
func (s *workflowRunStore) UpdateWorkflowRun(ctx context.Context, workflowRun *model.WorkflowRun) error {
	LogDB(ctx).Infof("Save workflowRun, WorkflowRunID: %v, State: %v, FailMessage: %v",
		workflowRun.WorkflowRunID, workflowRun.State, workflowRun.FailMessage)
	return s.db.WithContext(ctx).Save(workflowRun).Error
}

// SetWorkflowRunResult 更新工作流运行实例的结果
func (s *workflowRunStore) SetWorkflowRunResult(ctx context.Context, workflowRun *model.WorkflowRun,
	state model.WorkflowRunState, failMessage string) error {
	now := time.Now()
	workflowRun.State = state
	workflowRun.FailMessage = failMessage
	workflowRun.EndTime = &now
	workflowRun.UpdateTime = now
	if state == model.WorkflowRunStateFailed || state == model.WorkflowRunStateCanceled {
		// 把所有Running的节点的状态设置为取消
		now := time.Now()
		err := s.db.WithContext(ctx).Model(&model.NodeRun{}).
			Where("f_workflow_run_id = ? AND f_state = ?", workflowRun.WorkflowRunID, model.NodeRunStateRunning).
			Updates(map[string]interface{}{
				"f_state":       model.NodeRunStateCanceled,
				"f_update_time": now,
			}).Error
		if err != nil {
			LogDB(ctx).Errorf("批量更新节点状态失败, WorkflowRunID: %s, error: %v",
				workflowRun.WorkflowRunID, err)
			return fmt.Errorf("批量更新节点状态失败: %v", err)
		}
	}
	return s.UpdateWorkflowRun(ctx, workflowRun)
}

// SetWorkflowRunToken 更新工作流运行实例的token
func (s *workflowRunStore) SetWorkflowRunToken(ctx context.Context, workflowRunID string, token uint32) error {
	now := time.Now()
	err := s.db.WithContext(ctx).Model(&model.WorkflowRun{}).
		Where("f_workflow_run_id = ?", workflowRunID).
		Updates(map[string]interface{}{
			"f_total_token": token,
			"f_update_time": now,
		}).Error
	if err != nil {
		LogDB(ctx).Errorf("更新工作流运行示例状态失败, WorkflowRunID: %s, error: %v", workflowRunID, err)
		return fmt.Errorf("更新工作流运行示例状态失败: %v", err)
	}
	return nil
}

// GetWorkflowRunByRunID 根据工作流运行ID获取工作流运行实例
func (s *workflowRunStore) GetWorkflowRunByRunID(ctx context.Context, runID string) (*model.WorkflowRun, error) {
	var workflowRun model.WorkflowRun
	err := s.db.WithContext(ctx).Where("f_workflow_run_id = ?", runID).First(&workflowRun).Error
	if err != nil {
		return nil, err
	}
	return &workflowRun, nil
}
