package store

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
)

// CorpInfo 企业信息（计费需要）
type CorpInfo struct {
	UIN uint64 `json:"uin"`
	SID uint32 `json:"sid"`
}

// GetCorpKey 企业信息的存储KEY（计费需要）
func GetCorpKey(corpID uint64) string {
	return fmt.Sprintf(tconst.CorpKeyFormat, corpID)
}

// GetCorpInfo 获取uin和sid
func (r *RedisStore) GetCorpInfo(ctx context.Context, corpID uint64) (uint64, uint32, error) {
	corpCache := r.client.Get(ctx, GetCorpKey(corpID))
	if corpCache.Err() != nil {
		LogRedis(ctx).Warnf("[Redis]GetCorpInfo, get corp cache failed, corpID: %d, err: %v", corpID, corpCache.Err())
		return 0, 0, corpCache.Err()
	}
	var corpInfo CorpInfo
	if err := json.Unmarshal([]byte(corpCache.Val()), &corpInfo); err != nil {
		return 0, 0, err
	}
	LogRedis(ctx).Infof("[Redis]GetCorpInfo, corpID: %d, UIN:%d, SID: %d", corpID, corpInfo.UIN, corpInfo.SID)
	return corpInfo.UIN, corpInfo.SID, nil
}

// SetCorpInfo 缓存uin和sid
func (r *RedisStore) SetCorpInfo(ctx context.Context, corpID uint64, uin uint64, sid uint32) error {
	LogRedis(ctx).Infof("[Redis]SetCorpInfo, corpID: %d, UIN: %d, SID: %d", corpID, uin, sid)
	corpInfo := CorpInfo{
		UIN: uin,
		SID: sid,
	}
	corpInfoBytes, err := json.Marshal(corpInfo)
	if err != nil {
		return err
	}
	// 设置缓存，30天过期
	status := r.client.Set(ctx, GetCorpKey(corpID), string(corpInfoBytes), 30*24*time.Hour)
	if status.Err() != nil {
		LogRedis(ctx).Warnf("[Redis]SetCorpInfo, set corp cache failed, corpID: %d, corpInfo: %+v, err: %v",
			corpID, corpInfo, status.Err())
		return status.Err()
	}
	return nil
}
