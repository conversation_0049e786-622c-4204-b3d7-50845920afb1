package store

import (
	"context"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/model"
	"gorm.io/gorm"
)

// NodeRunStore 工作流运行节点存储接口
type NodeRunStore interface {
	// SaveNodeRun 保存工作流运行节点（存在则更新，不存在则插入）
	SaveNodeRun(ctx context.Context, nodeRun *model.NodeRun) error
	// GetNodeRunsByWorkflowRunID 获取工作流的所有节点
	GetNodeRunsByWorkflowRunID(ctx context.Context, workflowRunID string) ([]*model.NodeRun, error)
	// GetNodeRunsByWorkflowRunIDAndState 根据工作流ID和节点状态获取节点
	GetNodeRunsByWorkflowRunIDAndState(ctx context.Context, workflowRunID string,
		state model.NodeRunState) ([]*model.NodeRun, error)
}

// nodeRunStore 工作流运行节点存储实现
type nodeRunStore struct {
	db *gorm.DB
}

// SaveNodeRun 保存工作流运行节点（存在则更新，不存在则插入）
func (s *nodeRunStore) SaveNodeRun(ctx context.Context, nodeRun *model.NodeRun) error {
	LogDB(ctx).Infof("Save nodeRun, BelongNodeID: %v, NodeID: %v, NodeName: %v, State: %v, StatisticInfos: %v",
		nodeRun.BelongNodeID, nodeRun.NodeID, nodeRun.NodeName, nodeRun.State, nodeRun.StatisticInfos)
	return s.db.WithContext(ctx).Save(nodeRun).Error
}

// GetNodeRunsByWorkflowRunID 获取工作流的所有节点
func (s *nodeRunStore) GetNodeRunsByWorkflowRunID(ctx context.Context, workflowRunID string) ([]*model.NodeRun, error) {
	var nodeRuns []*model.NodeRun
	err := s.db.WithContext(ctx).Where("f_workflow_run_id = ?", workflowRunID).Find(&nodeRuns).Error
	if err != nil {
		return nil, err
	}
	return nodeRuns, nil
}

// GetNodeRunsByWorkflowRunIDAndState 根据工作流ID和节点状态获取节点
func (s *nodeRunStore) GetNodeRunsByWorkflowRunIDAndState(ctx context.Context, workflowRunID string,
	state model.NodeRunState) ([]*model.NodeRun, error) {
	var nodeRuns []*model.NodeRun
	err := s.db.WithContext(ctx).
		Where("f_workflow_run_id = ? AND f_state = ?", workflowRunID, state).
		Find(&nodeRuns).Error
	if err != nil {
		return nil, err
	}
	return nodeRuns, nil
}
