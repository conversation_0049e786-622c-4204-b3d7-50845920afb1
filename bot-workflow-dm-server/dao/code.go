package dao

import (
	"context"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	interpreter "git.woa.com/dialogue-platform/lke_proto/pb-protocol/code_interpreter_dispatcher"
)

const (
	// // UnSupportResultType 不支持类型
	// UnSupportResultType = "unsupport"
	// // FileResultType 文件类型
	// FileResultType = "file"
	// // ImageResultType 图片类型
	// ImageResultType = "image"

	// TextResultType 文本类型
	TextResultType = "text"

	// // WarnResultType 警告类型
	// WarnResultType = "warning"

	// ErrResultType 失败类型
	ErrResultType = "error"
)

// const (
//	// DefaultEntryFuncName 默认代码入口
//	DefaultEntryFuncName = "main"
// )

// var codeExecutor = bot_exec_pycode_server.NewCodeExecutorClientProxy()

// RunCode New 执行代码
func (d *dao) RunCode(ctx context.Context, runSessionID, codeContent, arguments string) (string, error) {
	if arguments == "" {
		arguments = "{}"
	}
	_, finalCode := util.ReplacePlaceholders(config.GetMainConfig().RunCode.CodeTemplate, map[string]any{
		"CodeContent":      codeContent,
		"InputParamObject": strings.ReplaceAll(arguments, "\\", "\\\\"),
		"ResultSeparator":  config.GetMainConfig().RunCode.ResultSeparator,
	})
	req := &interpreter.ExecuteReq{
		SessionID:      runSessionID,
		Code:           finalCode,
		Language:       "python",
		MaxErrorLen:    config.GetMainConfig().RunCode.MaxResultLength,
		NoNeedContext:  true,
		NoNeedInitCode: true,
	}
	step := trace.StartStep(ctx, trace.StepKeyRunCode, req)
	opts := make([]client.Option, 0)
	if d.inAsync {
		opts = append(opts, client.WithTimeout(0))
	}
	dispatchResponse, err := d.codeCli.Execute(ctx, req, opts...)
	step.RecordEnd(dispatchResponse, err)
	if err != nil {
		LogAPI(ctx).Warnf("invokeRunCode failed, err:  %v", err)
		return "", err
	}

	if !dispatchResponse.Success && len(dispatchResponse.Message) > 0 {
		LogAPI(ctx).Warnf("getCodeInterpreterRsp dispatchResponse:  %v", dispatchResponse)
		return "", fmt.Errorf(dispatchResponse.Message)
	}

	var execResult, execMsg string
	// 结果解析
	isFail := false
	for _, output := range dispatchResponse.Outputs {
		if isFail {
			continue
		}
		switch output.Type {
		case ErrResultType:
			isFail = true
			execMsg += output.Content
		case TextResultType:
			execResult += output.Content
		}
	}
	if isFail {
		LogAPI(ctx).Warnf("dispatchResponse.Outputs has failed message:  %v", execMsg)
		return "", fmt.Errorf("%v", execMsg)
	}

	parts := strings.Split(execResult, config.GetMainConfig().RunCode.ResultSeparator)
	if len(parts) != 2 {
		return "", fmt.Errorf("process excution result failed")
	}
	return parts[1], nil
}

// // RunCodeOld 执行代码
// func (d *dao) RunCodeOld(ctx context.Context, runSessionID, codeContent, arguments string) (string, error) {
//	req := new(bot_exec_pycode_server.RunCodeRequest)
//	req.FuncName = DefaultEntryFuncName
//	req.FuncCode = codeContent
//	req.FuncArgs = arguments
//
//	var err error
//	var resp *bot_exec_pycode_server.RunCodeResponse
//	trace.StartStep(ctx, trace.StepKeyRunCode, req).RecordEnd(resp, err)
//	resp, err = codeExecutor.Run(ctx, req)
//	if err != nil {
//		LogAPI(ctx).Errorf("invokeRunCode failed, err:  %v", err)
//		return "", tconst.ErrSystemError
//	}
//	if resp.Code != 0 {
//		LogAPI(ctx).Errorf("codeExecutor.Run failed, resp:  %v", resp)
//		return "", fmt.Errorf("%v", resp.Message)
//	}
//	return resp.Result, nil
// }
