package dao

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/naming/selector"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/metrics"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/common/v3/limiter"
	"git.woa.com/dialogue-platform/go-comm/panicprinter"
	"git.woa.com/dialogue-platform/go-comm/utils"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
)

// WithTrpcSelector 还原为 trpc 默认 Selector
func WithTrpcSelector() client.Option {
	return func(o *client.Options) {
		o.Selector = &selector.TrpcSelector{}
	}
}

// modReqForTest TODO Delete
func modReqForTest(req *llmm.Request, stepKey trace.StepKey) {
	for appID, modelAndParams := range config.GetMainConfig().Model.SpecialR1ModelParams {
		if appID == req.AppKey {
			req.ModelName = modelAndParams.ModelName
			switch stepKey {
			case trace.StepKeyParameterExtract, trace.StepKeyOptionCard, trace.StepKeyNodeIntent,
				trace.StepKeyTagExtract, trace.StepKeyJudgeCondition:
				req.ModelParams = modelAndParams.NoThoughParams
				req.Messages = append(req.Messages, &llmm.Message{
					Role:    llmm.Role_ASSISTANT,
					Content: modelAndParams.AddMessage,
				})
			default:
			}
		}
	}
}

// Chat 流式调用 LLM
func (d *dao) Chat(ctx context.Context, session *entity.Session,
	req *llmm.Request, stepKey trace.StepKey) (chan *llmm.Response, error) {
	isRAGRelated := false // 与RAG无关，自定义模型无需收费
	// TODO Delete
	modReqForTest(req, stepKey)

	// 请求大模型限频
	holder := session.RecordID + "-" + util.RandStr(8)
	for {
		if err := d.llmLock(ctx, session, req.GetModelName(), holder, isRAGRelated); err != nil {
			if errors.Is(err, entity.ErrLLMOverload) {
				go d.reportOverload(ctx, req.ModelName, session, "Chat")
				// 异步工作流的LLM超限后，需要等待一段时间再重试
				if session.IsAsync {
					time.Sleep(time.Second * time.Duration(config.GetMainConfig().AsyncTask.LLMFailedRetryInterval))
					continue
				}
			}
			// 其他错误，如余额不足等，直接返回
			return nil, err
		}
		break
	}

	var err error
	dimension := map[string]string{
		metrics.DimensionNameSuccess:   metrics.GetSuccessValue(nil),
		metrics.DimensionNameModelName: req.ModelName,
		metrics.DimensionNameLLMType:   string(stepKey),
	}
	step := trace.StartStep(ctx, stepKey, req)
	defer func() {
		if err != nil {
			step.RecordEnd(nil, err)
			dimension[metrics.DimensionNameSuccess] = metrics.GetSuccessValue(err)
			metrics.ReportElapsedMetrics(ctx, dimension, step.StartTime, metrics.MetricsNameLLMTotalMS)
		}
	}()

	ch := make(chan *llmm.Response)
	opts := []client.Option{WithTrpcSelector()}
	if d.inAsync {
		opts = append(opts, client.WithTimeout(0))
	}
	cli, err := d.llmmCli.Chat(ctx, opts...)
	if err != nil {
		LogAPI(ctx).Errorf("Chat new stream client error: %+v", err)
		return ch, entity.WrapNodeErr(entity.ErrLLMFail, err.Error())
	}
	if err = cli.Send(req); err != nil {
		LogAPI(ctx).Errorf("Chat send stream request error: %+v, req: %+v", err, req)
		_ = cli.CloseSend()
		return ch, entity.WrapNodeErr(entity.ErrLLMFail, err.Error())
	}

	go func() {
		defer panicprinter.PrintPanic()
		// 大模型限频解锁
		defer d.llmUnlock(ctx, session, req.GetModelName(), holder)
		var rsp *llmm.Response
		var tokenCounter tokenCount
		cancelCtx, cancel := context.WithCancel(ctx)
		tokenCounter.Init(cancelCtx)
		defer func() {
			cancel()
			step.RecordEnd(rsp, err)
			_ = cli.CloseSend()
			close(ch)
			dimension[metrics.DimensionNameSuccess] = metrics.GetSuccessValue(err)
			metrics.ReportElapsedMetrics(ctx, dimension, step.StartTime, metrics.MetricsNameLLMTotalMS)
		}()
		first := true
		for {
			start := time.Now()
			select {
			case <-ctx.Done():
				req.PromptType = llmm.PromptType_CMD_STOP
				_ = cli.Send(req)
				return
			default:
				rsp, err = cli.Recv()
				if err != nil {
					if errors.Is(err, context.Canceled) ||
						strings.Contains(errs.Msg(err), "context canceled") {
						req.PromptType = llmm.PromptType_CMD_STOP
						_ = cli.Send(req)
						return
					}
					if strings.Contains(err.Error(), "timeout") {
						LogAPI(ctx).Warnf("Chat read model response error: %+v, req: %+v", err, req)
					} else {
						LogAPI(ctx).Errorf("Chat read model response error: %+v, req: %+v", err, req)
					}
					rsp = &llmm.Response{Code: -1, ErrMsg: err.Error(), Finished: true}
					ch <- rsp
					return
				}
				if first {
					step.RecordLLMFirst(rsp, nil)
					metrics.ReportElapsedMetrics(ctx, dimension, step.StartTime, metrics.MetricsNameLLMFirstMS)
					first = false
				}

				if rsp.Code != 0 || rsp.Message == nil {
					LogAPI(ctx).Errorf("Chat read model response biz error, rsp: %s", utils.ToJsonString(rsp))
					rsp.Finished = true
					ch <- rsp
					return
				}
				ch <- rsp
				// 更新token计数器(共享并发和专属并发都需要)
				inputTokenCountDiff, outputTokenCountDiff := tokenCounter.Update(cancelCtx, rsp.GetFinished(),
					rsp.GetStatisticInfo().GetInputTokens(), rsp.GetStatisticInfo().GetOutputTokens())
				if !session.IsLLMExclusive(req.ModelName) {
					// 非专属并发需要上报token计费（增量）
					go d.reportLimiter(ctx, req.GetModelName(), session,
						inputTokenCountDiff, outputTokenCountDiff, "Chat")
				}
				if rsp.GetFinished() {
					// 上报计费
					go d.reportFinance(ctx, req.ModelName, session, start,
						tokenCounter.Input, tokenCounter.Output, isRAGRelated, "Chat")
					LogAPI(ctx).Infof("llm Chat, rsp: %v", rsp)
					return
				}
			}
		}
	}()
	return ch, nil
}

// SimpleChat 非流式调用 LLM
func (d *dao) SimpleChat(ctx context.Context, session *entity.Session,
	req *llmm.Request, stepKey trace.StepKey) (*llmm.Response, error) {
	isRAGRelated := false // 与RAG无关，自定义模型无需收费
	// TODO Delete
	modReqForTest(req, stepKey)

	// 请求大模型限频
	holder := session.RecordID + "-" + util.RandStr(8)
	for {
		if err := d.llmLock(ctx, session, req.GetModelName(), holder, isRAGRelated); err != nil {
			if errors.Is(err, entity.ErrLLMOverload) {
				go d.reportOverload(ctx, req.ModelName, session, "SimpleChat")
				// 异步工作流的LLM超限后，需要等待一段时间再重试
				if session.IsAsync {
					time.Sleep(time.Second * time.Duration(config.GetMainConfig().AsyncTask.LLMFailedRetryInterval))
					continue
				}
			}
			// 其他错误，如余额不足等，直接返回
			return nil, err
		}
		break
	}
	defer d.llmUnlock(ctx, session, req.GetModelName(), holder)

	var tokenCounter tokenCount
	cancelCtx, cancel := context.WithCancel(ctx)
	tokenCounter.Init(cancelCtx)
	step := trace.StartStep(ctx, stepKey, req)
	opts := []client.Option{WithTrpcSelector()}
	if d.inAsync {
		opts = append(opts, client.WithTimeout(0))
	}
	start := time.Now()
	rsp, err := d.llmmCli.SimpleChat(ctx, req, opts...)
	defer func() {
		cancel()
		step.RecordEnd(rsp, err)
		dimension := map[string]string{
			metrics.DimensionNameSuccess:   metrics.GetSuccessValue(err),
			metrics.DimensionNameModelName: req.ModelName,
			metrics.DimensionNameLLMType:   string(stepKey),
		}
		metrics.ReportElapsedMetrics(ctx, dimension, step.StartTime, metrics.MetricsNameLLMTotalMS)
	}()
	if err != nil {
		LogAPI(ctx).Errorf("SimpleChat new client error: %+v", err)
		return nil, entity.WrapNodeErr(entity.ErrLLMFail, err.Error())
	}
	// 更新token计数器(共享并发和专属并发都需要)
	inputTokenCountDiff, outputTokenCountDiff := tokenCounter.Update(cancelCtx, true,
		rsp.GetStatisticInfo().GetInputTokens(), rsp.GetStatisticInfo().GetOutputTokens())
	// 非专属并发需要上报token计费（增量）
	if !session.IsLLMExclusive(req.ModelName) {
		go d.reportLimiter(ctx, req.GetModelName(), session,
			inputTokenCountDiff, outputTokenCountDiff, "SimpleChat")
	}
	// 上报计费
	go d.reportFinance(ctx, req.ModelName, session, start,
		tokenCounter.Input, tokenCounter.Output, isRAGRelated, "SimpleChat")
	return rsp, nil
}

// GetAnswerFromKnowledge 从知识中找答案
func (d *dao) GetAnswerFromKnowledge(ctx context.Context, session *entity.Session,
	req *chat.GetAnswerFromKnowledgeRequest, ch chan *chat.GetAnswerFromKnowledgeReply) error {
	isRAGRelated := true // 与RAG相关，自定义模型需要收费

	// 目前改为异步接收响应，因此这里需要在报错返回时关闭channel
	errDeferFunc := func() {
		close(ch)
	}

	// 请求大模型限频
	holder := session.RecordID + "-" + util.RandStr(8)
	for {
		if err := d.llmLock(ctx, session, req.GetModelName(), holder, isRAGRelated); err != nil {
			if errors.Is(err, entity.ErrLLMOverload) {
				go d.reportOverload(ctx, req.ModelName, session, "GetAnswerFromKnowledge")
				// 异步工作流的LLM超限后，需要等待一段时间再重试
				if session.IsAsync {
					time.Sleep(time.Second * time.Duration(config.GetMainConfig().AsyncTask.LLMFailedRetryInterval))
					continue
				}
			}
			// 其他错误，如余额不足等，直接返回
			errDeferFunc()
			return err
		}
		break
	}

	step := trace.StartStep(ctx, trace.StepKeyGetAnswerFromKnowledge, req)
	opts := []client.Option{WithTrpcSelector()}
	if d.inAsync {
		opts = append(opts, client.WithTimeout(0))
	}
	cli, err := d.chatStreamCli.GetAnswerFromKnowledge(ctx, opts...)
	if err != nil {
		LogAPI(ctx).Errorf("GetAnswerFromKnowledge new stream client error: %+v", err)
		errDeferFunc()
		return entity.WrapNodeErr(entity.ErrLLMFail, err.Error())
	}

	LogAPI(ctx).Infof("GetAnswerFromKnowledge stream request: %s", utils.ToJsonString(req))

	if err := cli.Send(req); err != nil {
		LogAPI(ctx).Errorf("GetAnswerFromKnowledge send stream request error: %+v, req: %+v", err, req)
		errDeferFunc()
		return err
	}

	go func() {
		defer panicprinter.PrintPanic()
		// 大模型限频解锁
		defer d.llmUnlock(ctx, session, req.GetModelName(), holder)
		var last *chat.GetAnswerFromKnowledgeReply
		var rsp *chat.GetAnswerFromKnowledgeReply
		var tokenCounter tokenCount
		cancelCtx, cancel := context.WithCancel(ctx)
		tokenCounter.Init(cancelCtx)
		defer func() {
			// 确保流式输出终止 final
			if last != nil && !last.GetFinished() {
				last.Finished = true
				ch <- last
			}
			// 关闭流
			cancel()
			step.RecordEnd(rsp, err)
			_ = cli.CloseSend()
			close(ch)
		}()
		for {
			start := time.Now()
			select {
			case <-ctx.Done():
				req.Type = chat.GetAnswerFromKnowledgeRequest_GET_CANCEL
				_ = cli.Send(req)
				return
			default:
				rsp, err = cli.Recv()
				if err != nil {
					if errors.Is(err, context.Canceled) ||
						strings.Contains(errs.Msg(err), "context canceled") {
						req.Type = chat.GetAnswerFromKnowledgeRequest_GET_CANCEL
						_ = cli.Send(req)
						return
					}
					if strings.Contains(err.Error(), "timeout") {
						LogAPI(ctx).Warnf("GetAnswerFromKnowledge read chat response error: %+v, req: %+v", err, req)
					} else {
						LogAPI(ctx).Errorf("GetAnswerFromKnowledge read chat response error: %+v, req: %+v", err, req)
					}
					return
				}

				if rsp.GetCode() != 0 {
					LogAPI(ctx).Errorf("GetAnswerFromKnowledge response biz error, rsp: %s", utils.ToJsonString(rsp))
					return
				}
				ch <- rsp
				last = rsp
				// 更新token计数器(共享并发和专属并发都需要)
				inputTokenCountDiff, outputTokenCountDiff := tokenCounter.Update(cancelCtx, rsp.GetFinished(),
					rsp.GetStatisticInfo().GetInputTokens(), rsp.GetStatisticInfo().GetOutputTokens())
				if !session.IsLLMExclusive(req.ModelName) {
					// 非专属并发需要上报token计费（增量）
					go d.reportLimiter(ctx, req.GetModelName(), session,
						inputTokenCountDiff, outputTokenCountDiff, "GetAnswerFromKnowledge")
				}
				if rsp.GetFinished() {
					// 上报计费
					go d.reportFinance(ctx, req.ModelName, session, start,
						tokenCounter.Input, tokenCounter.Output, isRAGRelated, "GetAnswerFromKnowledge")
					return
				}
			}
		}
	}()
	return nil
}

// GetAnswerFromBatchKnowledge 从知识中找答案
func (d *dao) GetAnswerFromBatchKnowledge(ctx context.Context, session *entity.Session,
	req *chat.GetAnswerFromBatchKnowledgeRequest, ch chan *chat.GetAnswerFromBatchKnowledgeReply) error {
	isRAGRelated := true // 与RAG相关，自定义模型需要收费

	// 目前改为异步接收响应，因此这里需要在报错返回时关闭channel
	errDeferFunc := func() {
		close(ch)
	}

	// 请求大模型限频
	holder := session.RecordID + "-" + util.RandStr(8)
	for {
		if err := d.llmLock(ctx, session, req.GetModelName(), holder, isRAGRelated); err != nil {
			if errors.Is(err, entity.ErrLLMOverload) {
				go d.reportOverload(ctx, req.ModelName, session, "GetAnswerFromBatchKnowledge")
				// 异步工作流的LLM超限后，需要等待一段时间再重试
				if session.IsAsync {
					time.Sleep(time.Second * time.Duration(config.GetMainConfig().AsyncTask.LLMFailedRetryInterval))
					continue
				}
			}
			// 其他错误，如余额不足等，直接返回
			errDeferFunc()
			return err
		}
		break
	}

	step := trace.StartStep(ctx, trace.StepKeyGetAnswerFromBatchKnowledge, req)
	opts := []client.Option{WithTrpcSelector()}
	if d.inAsync {
		opts = append(opts, client.WithTimeout(0))
	}
	cli, err := d.chatStreamCli.GetAnswerFromBatchKnowledge(ctx, opts...)
	if err != nil {
		LogAPI(ctx).Errorf("GetAnswerFromBatchKnowledge new stream client error: %+v", err)
		errDeferFunc()
		return err
	}

	LogAPI(ctx).Infof("GetAnswerFromBatchKnowledge stream request: %s", utils.ToJsonString(req))

	if err := cli.Send(req); err != nil {
		LogAPI(ctx).Errorf("GetAnswerFromBatchKnowledge send stream request error: %+v, req: %+v", err, req)
		errDeferFunc()
		return err
	}

	go func() {
		defer panicprinter.PrintPanic()
		// 大模型限频解锁
		defer d.llmUnlock(ctx, session, req.GetModelName(), holder)
		var last *chat.GetAnswerFromBatchKnowledgeReply
		var rsp *chat.GetAnswerFromBatchKnowledgeReply
		var tokenCounter tokenCount
		cancelCtx, cancel := context.WithCancel(ctx)
		tokenCounter.Init(cancelCtx)
		defer func() {
			// 确保流式输出终止 final
			if last != nil && !last.GetFinished() {
				last.Finished = true
				ch <- last
			}
			// 关闭流
			cancel()
			step.RecordEnd(rsp, err)
			_ = cli.CloseSend()
			close(ch)
		}()
		for {
			start := time.Now()
			select {
			case <-ctx.Done():
				req.Type = chat.GetAnswerFromBatchKnowledgeRequest_GET_CANCEL
				_ = cli.Send(req)
				return
			default:
				rsp, err = cli.Recv()
				if err != nil {
					if errors.Is(err, context.Canceled) ||
						strings.Contains(errs.Msg(err), "context canceled") {
						req.Type = chat.GetAnswerFromBatchKnowledgeRequest_GET_CANCEL
						_ = cli.Send(req)
						return
					}
					if strings.Contains(err.Error(), "timeout") {
						LogAPI(ctx).Warnf("GetAnswerFromBatchKnowledge read chat response error: %+v, req: %+v", err, req)
					} else {
						LogAPI(ctx).Errorf("GetAnswerFromBatchKnowledge read chat response error: %+v, req: %+v", err, req)
					}
					return
				}

				if rsp.GetCode() != 0 {
					LogAPI(ctx).Errorf("GetAnswerFromBatchKnowledge response biz error, rsp: %s", utils.ToJsonString(rsp))
					return
				}
				ch <- rsp
				last = rsp
				// 更新token计数器(共享并发和专属并发都需要)
				inputTokenCountDiff, outputTokenCountDiff := tokenCounter.Update(cancelCtx, rsp.GetFinished(),
					rsp.GetStatisticInfo().GetInputTokens(), rsp.GetStatisticInfo().GetOutputTokens())
				if !session.IsLLMExclusive(req.ModelName) {
					// 非专属并发需要上报token计费（增量）
					go d.reportLimiter(ctx, req.GetModelName(), session,
						inputTokenCountDiff, outputTokenCountDiff, "GetAnswerFromBatchKnowledge")
				}
				if rsp.GetFinished() {
					// 上报计费
					go d.reportFinance(ctx, req.ModelName, session, start,
						tokenCounter.Input, tokenCounter.Output, isRAGRelated, "GetAnswerFromBatchKnowledge")
					return
				}
			}
		}
	}()
	return nil
}

// IsModelInWhiteList 判断模型是否在白名单内
func (d *dao) IsModelInWhiteList(appID string, modelName string) bool {
	// TODO 工作流模型限时免费。（合并后应该去掉）
	if strings.HasPrefix(modelName, "workflow-pro") {
		return true
	}

	for configAppID, configModelName := range config.GetMainConfig().Model.AppIDToParameterModelName {
		if appID == configAppID && modelName == configModelName {
			return true
		}
	}
	for configAppID, configModelName := range config.GetMainConfig().Model.AppIDToLogicModelName {
		if appID == configAppID && modelName == configModelName {
			return true
		}
	}
	for configAppID, configModelName := range config.GetMainConfig().Model.AppIDToOptionCardModelName {
		if appID == configAppID && modelName == configModelName {
			return true
		}
	}
	return false
}

func (d *dao) reportLimiter(ctx context.Context, modelName string, session *entity.Session,
	inputTokenCountDiff uint32, outputTokenCountDiff uint32, logPrefix string) {
	// kinvo: 判断是否在白名单内，白名单内不需要做并发控制
	if d.IsModelInWhiteList(session.AppID, modelName) {
		return
	}
	newCtx := trpc.CloneContext(ctx)
	token := uint64(inputTokenCountDiff + outputTokenCountDiff)
	if token == 0 {
		// 出现异常情况，即本次的input和output没有上一回大，不需要上报增量，不做处理
		return
	}
	// 上报token并发控制
	reportReq := &limiter.ReportTokenReq{
		Uin:       fmt.Sprintf("%d", session.Uin),
		SID:       session.SID,
		AppBizID:  session.AppID,
		ModelName: modelName,
		Token:     token,
	}
	reportRsp, err := d.limiter.ReportToken(newCtx, reportReq)
	if err != nil {
		// 调用公共库上报token失败，不影响整体流程，告警处理
		LogAPI(newCtx).Warnf("%s invoke limiter.ReportToken failed, err: %+v, report: %+v", logPrefix, err, reportReq)
	} else if !reportRsp.OK {
		// 模型超并发，放过
		LogAPI(newCtx).Warnf("%s model:%s is overload, rsp: %+v", logPrefix, modelName, reportRsp)
	}
}

func (d *dao) reportFinance(ctx context.Context, modelName string, session *entity.Session, startTime time.Time,
	inputTokenCount uint32, outputTokenCount uint32, isRAGRelated bool, logPrefix string) {
	// kinvo: 判断是否在白名单内，白名单内不上报计费
	if d.IsModelInWhiteList(session.AppID, modelName) {
		return
	}
	newCtx := trpc.CloneContext(ctx)
	// 创建基础上报数据
	baseDosage := BaseDosage{
		ModelName:  modelName,
		DosageID:   d.generateDosageID(), // 这里不能使用session的record，会导致同一record下的大模型相关节点上报的数据被覆盖
		StartTime:  startTime,
		EndTime:    time.Now(),
		RAGRelated: isRAGRelated,
	}
	// 上报token计费
	tokenDosage := TokenDosage{
		BaseDosage: baseDosage,
	}
	if inputTokenCount > 0 {
		tokenDosage.InputDosages = append(tokenDosage.InputDosages, inputTokenCount)
	}
	if outputTokenCount > 0 {
		tokenDosage.OutputDosages = append(tokenDosage.OutputDosages, outputTokenCount)
	}
	err := d.ReportTokenDosage(newCtx, session, tokenDosage)
	if err != nil {
		// 调用计费接口失败，不影响整体流程，告警处理
		LogAPI(newCtx).Warnf("%s invoke finance.ReportTokenDosage failed, err: %+v, dosage: %+v", logPrefix,
			err, tokenDosage)
	}
	// 专属并发上报并发计费
	if session.IsLLMExclusive(modelName) {
		concurrencyDosage := ConcurrencyDosage{
			BaseDosage: baseDosage,
			Dosage:     1,
		}
		err = d.ReportConcurrencyDosage(newCtx, session, concurrencyDosage)
		if err != nil {
			// 调用计费接口失败，不影响整体流程，告警处理
			LogAPI(newCtx).Warnf("%s invoke finance.ReportConcurrencyDosage failed, err: %+v, dosage: %+v", logPrefix,
				err, concurrencyDosage)
		}
	}
}

func (d *dao) reportOverload(ctx context.Context, modelName string, session *entity.Session, logPrefix string) {
	// kinvo: 判断是否在白名单内，白名单内不上报计费
	if d.IsModelInWhiteList(session.AppID, modelName) {
		return
	}
	newCtx := trpc.CloneContext(ctx)
	overloadDosage := OverloadDosage{
		ModelName: modelName,
		UsageTime: time.Now(),
	}
	var err error
	// 专属并发上报超并发，共享并发上报超TPM/QPM用量
	if session.IsLLMExclusive(modelName) {
		err = d.ReportOverConcurrencyDosage(newCtx, session, overloadDosage)
	} else {
		err = d.ReportOverMinuteDosage(newCtx, session, overloadDosage)
	}
	if err != nil {
		// 调用计费接口失败，不影响整体流程，告警处理
		LogAPI(newCtx).Warnf("%s invoke finance.ReportOverDosage failed, err: %+v, "+
			"dosage: %+v, exclusive: %v", logPrefix, err, overloadDosage, session.IsLLMExclusive(modelName))
	}
}

func (d *dao) generateDosageID() string {
	// 参考recordID格式，3位随机数_时间戳_8位随机数
	timeStr := time.Now().Format("20060102_150405.000")
	formattedTimeStr := strings.ReplaceAll(timeStr, ".", "_")
	return util.RandStr(3) + "_" + formattedTimeStr + "_" + util.RandStr(8)
}
