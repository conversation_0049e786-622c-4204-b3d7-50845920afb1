package dao

import (
	"testing"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"github.com/golang/mock/gomock"
	"github.com/smartystreets/goconvey/convey"
)

// TestGetAnswerFromKnowledge tests the GetAnswerFromKnowledge function
func TestGetAnswerFromKnowledge(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	convey.Convey("Test GetAnswerFromKnowledge", t, func() {
		// Create a mock dao
		mockDao := &mockGetAnswerDao{
			t:                   ctrl.T,
			llmLockCalled:       false,
			llmUnlockCalled:     false,
			reportLimiterCalled: false,
			reportFinanceCalled: false,
			shouldLockFail:      false,
			shouldStreamFail:    false,
		}

		// Create a test session
		session := &entity.Session{
			AppID:    "test_app_id",
			RecordID: "test_record_id",
		}
		session.InitVar()

		// Create a test request
		req := &chat.GetAnswerFromKnowledgeRequest{
			ModelName: "test_model",
			Question:  "测试问题",
		}

		// Create a channel for replies
		replyCh := make(chan *chat.GetAnswerFromKnowledgeReply, 2)

		convey.Convey("When everything succeeds", func() {
			// Call the function
			err := mockDao.GetAnswerFromKnowledge(t, session, req, replyCh)

			// Verify results
			convey.So(err, convey.ShouldBeNil)
			convey.So(mockDao.llmLockCalled, convey.ShouldBeTrue)

			// Wait for goroutine to complete
			time.Sleep(100 * time.Millisecond)

			// Verify that reporting was called
			convey.So(mockDao.reportLimiterCalled, convey.ShouldBeTrue)
			convey.So(mockDao.reportFinanceCalled, convey.ShouldBeTrue)

			// Collect replies from the channel
			var replies []*chat.GetAnswerFromKnowledgeReply
			timeout := time.After(1 * time.Second)
			done := false

			for !done {
				select {
				case reply, ok := <-replyCh:
					if !ok {
						done = true
						break
					}
					t.Logf("reply: %+v", reply)
					replies = append(replies, reply)
				case <-timeout:
					done = true
				}
			}

			// Wait for goroutine to complete
			time.Sleep(5 * time.Second)

			// Verify that unlock was called
			convey.So(mockDao.llmUnlockCalled, convey.ShouldBeTrue)

			// Verify replies
			convey.So(len(replies), convey.ShouldEqual, 2)
			if len(replies) >= 1 {
				convey.So(replies[0].Finished, convey.ShouldBeFalse)
				convey.So(replies[0].Message.Content, convey.ShouldEqual, "部分回答")
			}
			if len(replies) >= 2 {
				convey.So(replies[1].Finished, convey.ShouldBeTrue)
				convey.So(replies[1].Message.Content, convey.ShouldEqual, "完整回答")
			}
		})

		convey.Convey("When llmLock fails", func() {
			mockDao.shouldLockFail = true
			mockDao.llmLockCalled = false
			mockDao.llmUnlockCalled = false
			mockDao.reportLimiterCalled = false
			mockDao.reportFinanceCalled = false

			// Call the function
			err := mockDao.GetAnswerFromKnowledge(t, session, req, replyCh)

			// Verify results
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldEqual, entity.ErrLLMOverload)
			convey.So(mockDao.llmLockCalled, convey.ShouldBeTrue)
			convey.So(mockDao.llmUnlockCalled, convey.ShouldBeFalse)
			convey.So(mockDao.reportLimiterCalled, convey.ShouldBeFalse)
			convey.So(mockDao.reportFinanceCalled, convey.ShouldBeFalse)
		})

		convey.Convey("When stream creation fails", func() {
			mockDao.shouldLockFail = false
			mockDao.shouldStreamFail = true
			mockDao.llmLockCalled = false
			mockDao.llmUnlockCalled = false
			mockDao.reportLimiterCalled = false
			mockDao.reportFinanceCalled = false

			// Call the function
			err := mockDao.GetAnswerFromKnowledge(t, session, req, replyCh)

			// Verify results
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(mockDao.llmLockCalled, convey.ShouldBeTrue)

			// Wait for goroutine to complete
			time.Sleep(100 * time.Millisecond)

			// Verify that unlock was called but reporting was not
			convey.So(mockDao.llmUnlockCalled, convey.ShouldBeTrue)
			convey.So(mockDao.reportLimiterCalled, convey.ShouldBeFalse)
			convey.So(mockDao.reportFinanceCalled, convey.ShouldBeFalse)
		})
	})
}

// mockGetAnswerDao is a mock implementation of dao for testing GetAnswerFromKnowledge
type mockGetAnswerDao struct {
	t                   gomock.TestReporter
	llmLockCalled       bool
	llmUnlockCalled     bool
	reportLimiterCalled bool
	reportFinanceCalled bool
	shouldLockFail      bool
	shouldStreamFail    bool
}

// Mock implementation of GetAnswerFromKnowledge
func (d *mockGetAnswerDao) GetAnswerFromKnowledge(t *testing.T, session *entity.Session,
	req *chat.GetAnswerFromKnowledgeRequest, ch chan *chat.GetAnswerFromKnowledgeReply) error {

	// 请求大模型限频
	holder := session.RecordID + "-" + util.RandStr(8)
	if err := d.llmLock(t, session, req.GetModelName(), holder); err != nil {
		return err
	}

	// Mock the stream client creation
	if d.shouldStreamFail {
		go func() {
			d.llmUnlock(t, session, req.GetModelName(), holder)
		}()
		return entity.ErrLLMOverload
	}

	// Mock the goroutine that processes responses
	go func() {
		defer close(ch)
		defer d.llmUnlock(t, session, req.GetModelName(), holder)

		// Send first response (not finished)
		ch <- &chat.GetAnswerFromKnowledgeReply{
			Code:     0,
			Message:  &chat.LLMMessage{Content: "部分回答"},
			Finished: false,
			StatisticInfo: &chat.StatisticInfo{
				InputTokens:  10,
				OutputTokens: 5,
			},
		}

		// Mock token reporting for first response
		d.reportLimiter(t, req.GetModelName(), session, time.Now(), 10, 5, "GetAnswerFromKnowledge")

		time.Sleep(50 * time.Millisecond)

		// Send second response (finished)
		ch <- &chat.GetAnswerFromKnowledgeReply{
			Code:     0,
			Message:  &chat.LLMMessage{Content: "完整回答"},
			Finished: true,
			StatisticInfo: &chat.StatisticInfo{
				InputTokens:  10,
				OutputTokens: 10,
			},
		}

		// Mock finance reporting for final response
		d.reportFinance(t, req.GetModelName(), session, time.Now(), 10, 10, "GetAnswerFromKnowledge")
	}()

	return nil
}

// Mock implementation of llmLock
func (d *mockGetAnswerDao) llmLock(t *testing.T, session *entity.Session, modelName string, holder string) error {
	d.llmLockCalled = true
	if d.shouldLockFail {
		t.Logf("Mocking llmLock returning ErrLLMOverload with holder %s", holder)
		return entity.ErrLLMOverload
	}
	t.Logf("Mocking llmLock returning ok with holder %s", holder)
	return nil
}

// Mock implementation of llmUnlock
func (d *mockGetAnswerDao) llmUnlock(t *testing.T, session *entity.Session, modelName string, holder string) {
	d.llmUnlockCalled = true
	t.Logf("Mocking llmUnlock with holder %s", holder)
}

// Mock implementation of reportLimiter
func (d *mockGetAnswerDao) reportLimiter(t *testing.T, modelName string, session *entity.Session, startTime time.Time,
	inputTokenCountDiff uint32, outputTokenCountDiff uint32, logPrefix string) {
	d.reportLimiterCalled = true
}

// Mock implementation of reportFinance
func (d *mockGetAnswerDao) reportFinance(t *testing.T, modelName string, session *entity.Session, startTime time.Time,
	inputTokenCount uint32, outputTokenCount uint32, logPrefix string) {
	d.reportFinanceCalled = true
}
