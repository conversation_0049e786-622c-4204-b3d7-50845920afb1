package dao

import (
	"context"
	"errors"
	"testing"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/common/v3/limiter"
	. "github.com/smartystreets/goconvey/convey"
)

// TestLlmLock tests the llmLock function
func TestLlmLock(t *testing.T) {
	// Create a test context
	ctx := context.Background()

	Convey("Given a test environment for llmLock", t, func() {
		Convey("When model is in whitelist", func() {
			// Setup
			session := &entity.Session{
				AppID: "test-app",
			}
			session.InitVar()

			testDao := &testDao{
				isModelInWhiteListFunc: func(appID, modelName string) bool {
					return true
				},
			}

			modelName := "test-model"
			holder := "test-holder"

			// Execute
			result := testDao.llmLock(ctx, session, modelName, holder)

			// Verify
			So(result, ShouldBeTrue)
		})

		Convey("When it's a main model with count 1", func() {
			// Setup
			session := &entity.Session{
				AppID:         "test-app",
				MainModelName: "test-model",
				Uin:           12345,
			}
			session.InitVar()

			testDao := &testDao{
				isModelInWhiteListFunc: func(appID, modelName string) bool {
					return false
				},
			}

			modelName := "test-model"
			holder := "test-holder"

			// Execute
			result := testDao.llmLock(ctx, session, modelName, holder)

			// Verify
			So(result, ShouldBeTrue)
		})

		Convey("When MultiLock succeeds with model status OK", func() {
			// Setup
			session := &entity.Session{
				AppID:         "test-app",
				MainModelName: "other-model",
				Uin:           12345,
			}
			session.InitVar()

			testDao := &testDao{
				isModelInWhiteListFunc: func(appID, modelName string) bool {
					return false
				},
				multiLockFunc: func(ctx context.Context, req *limiter.MultiLockReq) (*limiter.MultiLockRsp, error) {
					return &limiter.MultiLockRsp{
						OK:          true,
						ModelStatus: true,
						IsExclusive: true,
					}, nil
				},
			}

			modelName := "test-model"
			holder := "test-holder"

			// Execute
			result := testDao.llmLock(ctx, session, modelName, holder)

			// Verify
			So(result, ShouldBeTrue)
			So(session.IsLLMExclusive(modelName), ShouldBeTrue)
			So(session.IsSuccessHolder(holder), ShouldBeTrue)
		})

		Convey("When MultiLock succeeds with model status not OK", func() {
			// Setup
			session := &entity.Session{
				AppID:         "test-app",
				MainModelName: "other-model",
				Uin:           12345,
			}
			session.InitVar()

			testDao := &testDao{
				isModelInWhiteListFunc: func(appID, modelName string) bool {
					return false
				},
				multiLockFunc: func(ctx context.Context, req *limiter.MultiLockReq) (*limiter.MultiLockRsp, error) {
					return &limiter.MultiLockRsp{
						OK:          true,
						ModelStatus: false,
						IsExclusive: false,
					}, nil
				},
			}

			modelName := "test-model"
			holder := "test-holder"

			// Execute
			result := testDao.llmLock(ctx, session, modelName, holder)

			// Verify
			So(result, ShouldBeTrue)
			So(session.IsLLMExclusive(modelName), ShouldBeFalse)
			So(session.IsSuccessHolder(holder), ShouldBeTrue)
		})

		Convey("When MultiLock returns an error", func() {
			// Setup
			session := &entity.Session{
				AppID:         "test-app",
				MainModelName: "other-model",
				Uin:           12345,
			}
			session.InitVar()

			testDao := &testDao{
				isModelInWhiteListFunc: func(appID, modelName string) bool {
					return false
				},
				multiLockFunc: func(ctx context.Context, req *limiter.MultiLockReq) (*limiter.MultiLockRsp, error) {
					return nil, errors.New("lock error")
				},
			}

			modelName := "test-model"
			holder := "test-holder"

			// Execute
			result := testDao.llmLock(ctx, session, modelName, holder)

			// Verify
			So(result, ShouldBeTrue)
		})

		Convey("When MultiLock returns not OK (overload)", func() {
			// Setup
			session := &entity.Session{
				AppID:         "test-app",
				MainModelName: "other-model",
				Uin:           12345,
			}
			session.InitVar()

			testDao := &testDao{
				isModelInWhiteListFunc: func(appID, modelName string) bool {
					return false
				},
				multiLockFunc: func(ctx context.Context, req *limiter.MultiLockReq) (*limiter.MultiLockRsp, error) {
					return &limiter.MultiLockRsp{
						OK:          false,
						ModelStatus: false,
						IsExclusive: false,
					}, nil
				},
			}

			modelName := "test-model"
			holder := "test-holder"

			// Execute
			result := testDao.llmLock(ctx, session, modelName, holder)

			// Verify
			So(result, ShouldBeFalse)
		})
	})
}

// TestLlmUnlock tests the llmUnlock function
func TestLlmUnlock(t *testing.T) {
	// Create a test context
	ctx := context.Background()

	Convey("Given a test environment for llmUnlock", t, func() {
		Convey("When model is in whitelist", func() {
			// Setup
			session := &entity.Session{
				AppID: "test-app",
			}
			session.InitVar()

			testDao := &testDao{
				isModelInWhiteListFunc: func(appID, modelName string) bool {
					return true
				},
			}

			modelName := "test-model"
			holder := "test-holder"

			// Execute - no return value to check
			session.ConsumeLLMResource(ctx)
			testDao.llmUnlock(ctx, session, modelName, holder)

			// No assertions needed as the function doesn't return anything
			// The test passes if no panic occurs
			So(true, ShouldBeTrue) // Just to have an assertion
		})

		Convey("When not a success holder", func() {
			// Setup
			session := &entity.Session{
				AppID: "test-app",
				Uin:   12345,
			}
			session.InitVar()

			// Track if MultiUnLock was called
			multiUnlockCalled := false

			testDao := &testDao{
				isModelInWhiteListFunc: func(appID, modelName string) bool {
					return false
				},
				multiUnlockFunc: func(ctx context.Context, req *limiter.MultiUnlockReq) (*limiter.MultiUnlockRsp, error) {
					multiUnlockCalled = true
					return &limiter.MultiUnlockRsp{}, nil
				},
			}

			modelName := "test-model"
			holder := "test-holder"

			// Execute - no return value to check
			session.ConsumeLLMResource(ctx)
			testDao.llmUnlock(ctx, session, modelName, holder)

			// Verify MultiUnLock was not called
			So(multiUnlockCalled, ShouldBeFalse)
		})

		Convey("When success holder with successful unlock", func() {
			// Setup
			session := &entity.Session{
				AppID: "test-app",
				Uin:   12345,
			}
			session.InitVar()
			session.SetLLMSuccessHolder("test-holder")

			// Track if MultiUnLock was called
			multiUnlockCalled := false

			testDao := &testDao{
				isModelInWhiteListFunc: func(appID, modelName string) bool {
					return false
				},
				multiUnlockFunc: func(ctx context.Context, req *limiter.MultiUnlockReq) (*limiter.MultiUnlockRsp, error) {
					multiUnlockCalled = true
					return &limiter.MultiUnlockRsp{}, nil
				},
			}

			modelName := "test-model"
			holder := "test-holder"

			// Execute - no return value to check
			session.ConsumeLLMResource(ctx)
			testDao.llmUnlock(ctx, session, modelName, holder)

			// Verify MultiUnLock was called
			So(multiUnlockCalled, ShouldBeTrue)
		})

		Convey("When success holder with unlock error", func() {
			// Setup
			session := &entity.Session{
				AppID: "test-app",
				Uin:   12345,
			}
			session.InitVar()
			session.SetLLMSuccessHolder("test-holder")

			// Track if MultiUnLock was called
			multiUnlockCalled := false

			testDao := &testDao{
				isModelInWhiteListFunc: func(appID, modelName string) bool {
					return false
				},
				multiUnlockFunc: func(ctx context.Context, req *limiter.MultiUnlockReq) (*limiter.MultiUnlockRsp, error) {
					multiUnlockCalled = true
					return nil, errors.New("unlock error")
				},
			}

			modelName := "test-model"
			holder := "test-holder"

			// Execute - no return value to check
			session.ConsumeLLMResource(ctx)
			testDao.llmUnlock(ctx, session, modelName, holder)

			// Verify MultiUnLock was called despite the error
			So(multiUnlockCalled, ShouldBeTrue)
		})
	})
}

// testDao is a test implementation of dao for testing llmLock and llmUnlock
type testDao struct {
	isModelInWhiteListFunc func(appID, modelName string) bool
	multiLockFunc          func(ctx context.Context, req *limiter.MultiLockReq) (*limiter.MultiLockRsp, error)
	multiUnlockFunc        func(ctx context.Context, req *limiter.MultiUnlockReq) (*limiter.MultiUnlockRsp, error)
}

// IsModelInWhiteList is a test implementation
func (d *testDao) IsModelInWhiteList(appID, modelName string) bool {
	if d.isModelInWhiteListFunc != nil {
		return d.isModelInWhiteListFunc(appID, modelName)
	}
	return false
}

// llmLock is a test implementation
func (d *testDao) llmLock(ctx context.Context, session *entity.Session, modelName string, holder string) bool {
	if d.IsModelInWhiteList(session.AppID, modelName) {
		return true
	}

	session.ConsumeLLMResource(ctx)

	var lockRsp *limiter.MultiLockRsp
	var err error

	if d.multiLockFunc != nil {
		lockRsp, err = d.multiLockFunc(ctx, &limiter.MultiLockReq{
			Uin:       "12345",
			AppBizID:  session.AppID,
			ModelName: modelName,
			Holder:    holder,
		})
	} else {
		lockRsp = &limiter.MultiLockRsp{
			OK:          true,
			ModelStatus: true,
		}
	}

	if err != nil {
		session.ReleaseLLMResource(ctx)
		return true
	}

	if lockRsp.OK {
		if lockRsp.IsExclusive {
			session.SetLLMExclusive(modelName)
		}
		session.SetLLMSuccessHolder(holder)
		return true
	}

	session.ReleaseLLMResource(ctx)
	return false
}

// llmUnlock is a test implementation
func (d *testDao) llmUnlock(ctx context.Context, session *entity.Session, modelName string, holder string) {
	if d.IsModelInWhiteList(session.AppID, modelName) {
		return
	}

	session.ReleaseLLMResource(ctx)

	if session.IsSuccessHolder(holder) {
		if d.multiUnlockFunc != nil {
			_, _ = d.multiUnlockFunc(ctx, &limiter.MultiUnlockReq{
				Uin:       "12345",
				AppBizID:  session.AppID,
				ModelName: modelName,
				Holder:    holder,
			})
		}
	}
}
