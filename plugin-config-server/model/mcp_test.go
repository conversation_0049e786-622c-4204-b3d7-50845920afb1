package model

import (
	"testing"

	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	"github.com/getkin/kin-openapi/openapi3"
	"github.com/go-playground/assert/v2"
)

func TestMCPToolInputSchema_ToRequestParam(t *testing.T) {
	tests := []struct {
		name    string
		schema  *MCPToolInputSchema
		want    []*pb.RequestParam
		wantErr bool
	}{
		{
			name: "测试基本类型转换",
			schema: &MCPToolInputSchema{
				Type: &openapi3.Types{"object"},
				Properties: map[string]*openapi3.SchemaRef{
					"stringParam": {
						Value: &openapi3.Schema{
							Type:        &openapi3.Types{"string"},
							Description: "字符串参数",
							Default:     "默认值",
						},
					},
					"intParam": {
						Value: &openapi3.Schema{
							Type:    &openapi3.Types{"integer"},
							Default: 123,
						},
					},
				},
				Required: []string{"stringParam"},
			},
			want: []*pb.RequestParam{
				{
					Name:         "stringParam",
					Type:         pb.TypeEnum_STRING,
					Desc:         "字符串参数",
					IsRequired:   true,
					DefaultValue: "默认值",
				},
				{
					Name:         "intParam",
					Type:         pb.TypeEnum_INT,
					IsRequired:   false,
					DefaultValue: "123",
				},
			},
			wantErr: false,
		},
		{
			name: "anyOf类型转换",
			schema: &MCPToolInputSchema{
				Type: &openapi3.Types{"object"},
				Properties: map[string]*openapi3.SchemaRef{
					"amount": {
						Value: &openapi3.Schema{
							AnyOf: []*openapi3.SchemaRef{
								{
									Value: &openapi3.Schema{
										Type: &openapi3.Types{"integer"},
									},
								},
								{
									Value: &openapi3.Schema{
										Type: &openapi3.Types{"null"},
									},
								},
							},
						},
					},
				},
				Required: []string{"amount"},
			},
			wantErr: false,
		},
		/*{
			name: "测试数组类型转换",
			schema: &MCPToolInputSchema{
				Schema: &openapi3.Schema{
					Type: "object",
					Properties: map[string]*openapi3.SchemaRef{
						"stringArray": {
							Value: &openapi3.Schema{
								Type: "array",
								Items: &openapi3.SchemaRef{
									Value: &openapi3.Schema{
										Type: "string",
									},
								},
							},
						},
					},
				},
			},
			want: []*pb.RequestParam{
				{
					Name:       "stringArray",
					Type:       pb.TypeEnum_ARRAY_STRING,
					IsRequired: false,
				},
			},
			wantErr: false,
		},
		{
			name: "测试对象类型转换",
			schema: &MCPToolInputSchema{
				Schema: &openapi3.Schema{
					Type: "object",
					Properties: map[string]*openapi3.SchemaRef{
						"nestedObject": {
							Value: &openapi3.Schema{
								Type: "object",
								Properties: map[string]*openapi3.SchemaRef{
									"nestedField": {
										Value: &openapi3.Schema{
											Type: "string",
										},
									},
								},
							},
						},
					},
				},
			},
			want: []*pb.RequestParam{
				{
					Name:       "nestedObject",
					Type:       pb.TypeEnum_OBJECT,
					IsRequired: false,
					SubParams: []*pb.RequestParam{
						{
							Name:       "nestedField",
							Type:       pb.TypeEnum_STRING,
							IsRequired: false,
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "测试OneOf类型转换",
			schema: &MCPToolInputSchema{
				Schema: &openapi3.Schema{
					Type: "object",
					Properties: map[string]*openapi3.SchemaRef{
						"oneOfParam": {
							Value: &openapi3.Schema{
								OneOf: []*openapi3.SchemaRef{
									{
										Value: &openapi3.Schema{
											Type: "string",
										},
									},
									{
										Value: &openapi3.Schema{
											Type: "integer",
										},
									},
								},
							},
						},
					},
				},
			},
			want: []*pb.RequestParam{
				{
					Name:       "oneOfParam",
					Type:       pb.TypeEnum_UNSPECIFIED,
					IsRequired: false,
					OneOf: []*pb.RequestParam{
						{
							Type: pb.TypeEnum_STRING,
						},
						{
							Type: pb.TypeEnum_INT,
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "测试空属性值",
			schema: &MCPToolInputSchema{
				Schema: &openapi3.Schema{
					Type: "object",
					Properties: map[string]*openapi3.SchemaRef{
						"nilField": nil,
					},
				},
			},
			want:    []*pb.RequestParam{},
			wantErr: false,
		},
		{
			name: "测试无效类型",
			schema: &MCPToolInputSchema{
				Schema: &openapi3.Schema{
					Type: "object",
					Properties: map[string]*openapi3.SchemaRef{
						"invalidType": {
							Value: &openapi3.Schema{
								Type: "invalid",
							},
						},
					},
				},
			},
			want:    []*pb.RequestParam{},
			wantErr: false,
		},
		*/
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.schema.ToRequestParam()
			if (err != nil) != tt.wantErr {
				t.Errorf("ToRequestParam() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("got: %+v", got)
			assert.Equal(t, tt.want, got)
		})
	}
}
