package dao

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/model"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
)

// GetAgentsByPluginRef 获取引用插件的Agent列表
func (d *dao) GetAgentsByPluginRef(ctx context.Context, uin string, pluginId string) ([]*model.PluginRefInfo, error) {
	rsp, err := d.agent.DescribeAgentHasReferencedPluginToolList(ctx,
		&agent_config_server.DescribeAgentHasReferencedPluginToolListReq{
			PluginId: pluginId,
		})
	if err != nil {
		log.ErrorContextf(ctx, "DescribeAgentHasReferencedPluginToolList failed, err: %v", err)
		return nil, model.ErrInternal
	}
	pluginRefs := make([]*model.PluginRefInfo, 0, len(rsp.GetToolRefs()))
	for _, toolRef := range rsp.GetToolRefs() {
		pluginRefs = append(pluginRefs, &model.PluginRefInfo{
			PluginId: toolRef.GetPluginId(),
			AppId:    toolRef.GetAppBizId(),
			RefType:  pb.ToolRef_AGENT,
		})
	}
	return pluginRefs, nil
}
