package dao

import (
	"context"
	"testing"

	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/util/config"
)

func TestCheckWhitelist(t *testing.T) {

	// 创建mock对象
	config.SetMain(config.MainConfig{
		CloudAPI: config.CloudAPIConfig{
			SecretID:  "",
			SecretKey: "",
			//Region:      "ap-guangzhou",
			AccountHost: "account.test.tencentcloudapi.com",
			Version:     "2018-12-25",
		},
	})

	// 测试用例
	tests := []struct {
		name      string
		key       string
		uin       string
		mockSetup func()
		want      bool
		wantErr   bool
	}{
		{
			name:    "在白名单",
			key:     "BangBox_MCP",
			uin:     "************",
			want:    true,
			wantErr: false,
		},
		{
			name:    "不在白名单",
			key:     "BangBox_MCP",
			uin:     "*********",
			want:    false,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建dao实例并注入mock client
			d := &dao{}

			got, err := d.Batch<PERSON>heckWhitelist(context.Background(), tt.key, tt.uin)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchCheckWhitelist() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("BatchCheckWhitelist() = %v, want %v", got, tt.want)
			}
		})
	}
}
